import React, { useRef } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { MkdListTableV2 } from "Components/MkdListTable";

const columns = [
  { header: "Id", accessor: "id" },
  { header: "Subject", accessor: "subject" },
  { header: "From", accessor: "from" },
  { header: "To", accessor: "to" },
  { header: "Created At", accessor: "created_at" },
  { header: "Action", accessor: "" },
];

const UserListEmailLogsTablePage = () => {
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const refreshRef = useRef(null);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: { path: "email_logs" },
    });
  }, [globalDispatch]);

  return (
    <div className="overflow-x-auto rounded bg-white pt-5 shadow md:p-5">
      <MkdListTableV2
        columns={columns}
        tableRole={"user"}
        table={"email_logs"}
        actionId={"id"}
        actions={{
          view: {
            show: true,
            multiple: false,
            action: (ids) => navigate(`/user/email_logs/${ids[0]}`),
          },
          edit: { show: false },
          delete: { show: false },
          select: { show: false },
          add: { show: false },
          export: { show: false },
        }}
        actionPosition={`onTable`}
        refreshRef={refreshRef}
      />
    </div>
  );
};

export default UserListEmailLogsTablePage;
