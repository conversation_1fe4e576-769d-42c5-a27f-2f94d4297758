import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as r}from"./vendor-2ae44a2e.js";import{M as J,G as Q,A as W,t as h}from"./index-d0a8f5da.js";import{o as X}from"./yup-5abd4662.js";import{u as Y}from"./react-hook-form-47c010f8.js";import{c as Z,a as S,f as _}from"./yup-c2e87575.js";import{P as ee}from"./index-132fbad2.js";import{S as se}from"./index-a74110af.js";import{f}from"./react-datepicker-da4cc90f.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";const i=new J,l=[{header:"Call ID",accessor:"call_id"},{header:"User Name",accessor:"first_name"},{header:"Campaign Name",accessor:"name"},{header:"Duration",accessor:"duration"},{header:"Total Cost",accessor:"calculated_cost"},{header:"Deepgram",accessor:"deepgram_cost"},{header:"ElevenLabs",accessor:"elevenlabs_cost"},{header:"Azure",accessor:"azure_cost"},{header:"Claude",accessor:"claude_cost"},{header:"Twilio",accessor:"twilio_cost"},{header:"Date",accessor:"update_at"}],te=Z({userId:S().required("User is required"),campaignId:S(),startDate:_(),endDate:_()}),Le=()=>{const{dispatch:C}=r.useContext(Q),{dispatch:p}=r.useContext(W),[x,P]=r.useState([]),[D,T]=r.useState([]),[d,L]=r.useState([]),[b,y]=r.useState(!1),[m,j]=r.useState(1),[v,E]=r.useState(10),[w,A]=r.useState(0),[ae,I]=r.useState(0),[k,R]=r.useState(!1),[U,M]=r.useState(!1),{register:u,handleSubmit:$,setValue:z,formState:{errors:re}}=Y({resolver:X(te)});r.useEffect(()=>{C({type:"SETPATH",payload:{path:"logs"}}),(async()=>{if(await F(),await G(),x.length>0){const e=x[0];z("userId",e.id),n({user_id:e.id})}else n()})()},[]);const F=async()=>{try{i.setTable("user");const e=await i.callRestAPI({payload:{role:"user"},page:1,limit:50},"GETALL");P(e.list)}catch(e){console.error("Error fetching users",e),h(p,e.message)}},G=async()=>{try{i.setTable("campaign");const e=await i.callRestAPI({},"GETALL");T(e.list)}catch(e){console.error("Error fetching campaigns",e),h(p,e.message)}},n=async e=>{y(!0);try{const t=await i.callRawAPI("/v3/api/custom/voiceoutreach/calls/logs",{...e,page:m,limit:v},"POST"),{logs:a,total:c,num_pages:o}=t;L(a),A(o),I(c),R(m>1),M(m+1<=o)}catch(t){console.error("Error fetching logs",t),h(p,t.message)}y(!1)},V=()=>{let e=[l.map(o=>o.header).join(","),...d.map(o=>l.map(K=>{const g=o[K.accessor];return typeof g=="number"?g.toFixed(2):g}).join(","))].join(`
`);const t=l.map(o=>o.accessor==="calculated_cost"?"Total":"").join(",");e+=`
${t},${N()}`;const a=new Blob([e],{type:"text/csv"}),c=document.createElement("a");c.href=URL.createObjectURL(a),c.download="invoice.csv",c.click()},N=()=>d.reduce((e,t)=>e+parseFloat(t.calculated_cost||0),0).toFixed(2),q=e=>{const t={user_id:e.userId,campaign_id:e.campaignId,start_date:f(new Date(e.startDate),"yyyy-MM-dd"),end_date:f(new Date(e.endDate),"yyyy-MM-dd")};n(t)};function B(e){E(e),n({})}function O(){j(e=>e>1?e-1:1),n({})}function H(){j(e=>e<w?e+1:e),n({})}return s.jsxs("div",{className:"min-h-screen bg-white",children:[s.jsxs("div",{className:"flex justify-between items-center px-8 py-6 admin-input",children:[s.jsxs("form",{onSubmit:$(q),className:"flex gap-4 items-end text-black",children:[s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"User"}),s.jsxs("select",{...u("userId"),className:"h-10 w-48 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none",children:[s.jsx("option",{value:"",children:"Select User"}),x.map(e=>s.jsxs("option",{value:e.id,children:[e.first_name," ",e.last_name]},e.id))]})]}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Campaign"}),s.jsxs("select",{...u("campaignId"),className:"h-10 w-48 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none",children:[s.jsx("option",{value:"",children:"All Campaigns"}),D.map(e=>s.jsx("option",{value:e.id,children:e.name},e.id))]})]}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),s.jsx("input",{type:"date",...u("startDate"),className:"h-10 w-40 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"})]}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),s.jsx("input",{type:"date",style:{filter:"invert"},...u("endDate"),className:"h-10 w-40 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"})]}),s.jsx("button",{type:"submit",className:"h-10 rounded-md bg-[#2cc9d5] px-4 text-white hover:bg-[#28b8c3]",children:"Filter"})]}),s.jsxs("div",{className:"flex gap-4 items-center",children:[s.jsxs("div",{className:"text-lg font-semibold",children:["Total Cost:"," ",s.jsxs("span",{className:"text-[#2cc9d5]",children:["$",N()]})]}),s.jsx("button",{onClick:V,className:"rounded bg-[#2cc9d5] px-4 py-2 text-white hover:bg-[#28b8c3]",children:"Download CSV"})]})]}),b?s.jsx(se,{}):s.jsx("div",{className:"px-8",children:s.jsxs("div",{className:"overflow-x-auto bg-white rounded-lg border border-gray-200 shadow",children:[s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsx("tr",{children:l.map((e,t)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase",children:e.header},t))})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map((e,t)=>s.jsx("tr",{className:"hover:bg-gray-50",children:l.map((a,c)=>s.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 whitespace-nowrap",children:a.accessor==="update_at"?f(new Date(e[a.accessor]),"yyyy-MM-dd"):a.accessor==="calculated_cost"||a.accessor==="deepgram_cost"||a.accessor==="elevenlabs_cost"||a.accessor==="azure_cost"||a.accessor==="claude_cost"||a.accessor==="twilio_cost"?`$${e[a.accessor].toFixed(2)}`:e[a.accessor]},c))},t))})]}),!b&&d.length===0&&s.jsx("div",{className:"px-6 py-4 text-sm text-center text-gray-500",children:"No logs found"})]})}),s.jsx("div",{className:"px-8 py-4",children:s.jsx(ee,{currentPage:m,pageCount:w,pageSize:v,canPreviousPage:k,canNextPage:U,updatePageSize:B,previousPage:O,nextPage:H})})]})};export{Le as default};
