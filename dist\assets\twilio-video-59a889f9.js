import{c as d,g as Fc}from"./vendor-2ae44a2e.js";var Ur={exports:{}},Rt={},Ie={},Lt={},Vc=function(n){var t=n.location,e=n.URL;return[t,e].some(function(r){return!r})?function(i){return Promise.reject(new Error("Failed to import: "+i+": dynamicImport is not supported"))}:(n.__twilioVideoImportedModules={},function(i){return i in n.__twilioVideoImportedModules?Promise.resolve(n.__twilioVideoImportedModules[i]):new Function("scope","return import('"+new e(i,t)+"').then(m => scope.__twilioVideoImportedModules['"+i+"'] = m);")(n)})}(globalThis),na=function(){},re="undefined",Bc=typeof window!==re&&typeof window.navigator!==re&&/Trident\/|MSIE /.test(window.navigator.userAgent),Pr=["trace","debug","info","warn","error"];function Rn(n,t){var e=n[t];if(typeof e.bind=="function")return e.bind(n);try{return Function.prototype.bind.call(e,n)}catch{return function(){return Function.prototype.apply.apply(e,[n,arguments])}}}function Uc(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function Wc(n){return n==="debug"&&(n="log"),typeof console===re?!1:n==="trace"&&Bc?Uc:console[n]!==void 0?Rn(console,n):console.log!==void 0?Rn(console,"log"):na}function ia(n,t){for(var e=0;e<Pr.length;e++){var r=Pr[e];this[r]=e<n?na:this.methodFactory(r,n,t)}this.log=this.debug}function Hc(n,t,e){return function(){typeof console!==re&&(ia.call(this,t,e),this[n].apply(this,arguments))}}function qc(n,t,e){return Wc(n)||Hc.apply(this,arguments)}function aa(n,t,e){var r=this,i,a="loglevel";typeof n=="string"?a+=":"+n:typeof n=="symbol"&&(a=void 0);function o(c){var l=(Pr[c]||"silent").toUpperCase();if(!(typeof window===re||!a)){try{window.localStorage[a]=l;return}catch{}try{window.document.cookie=encodeURIComponent(a)+"="+l+";"}catch{}}}function s(){var c;if(!(typeof window===re||!a)){try{c=window.localStorage[a]}catch{}if(typeof c===re)try{var l=window.document.cookie,f=l.indexOf(encodeURIComponent(a)+"=");f!==-1&&(c=/^([^;]+)/.exec(l.slice(f))[1])}catch{}return r.levels[c]===void 0&&(c=void 0),c}}r.name=n,r.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},r.methodFactory=e||qc,r.getLevel=function(){return i},r.setLevel=function(c,l){if(typeof c=="string"&&r.levels[c.toUpperCase()]!==void 0&&(c=r.levels[c.toUpperCase()]),typeof c=="number"&&c>=0&&c<=r.levels.SILENT){if(i=c,l!==!1&&o(c),ia.call(r,c,n),typeof console===re&&c<r.levels.SILENT)return"No console available for logging"}else throw"log.setLevel() called with invalid level: "+c},r.setDefaultLevel=function(c){s()||r.setLevel(c,!1)},r.enableAll=function(c){r.setLevel(r.levels.TRACE,c)},r.disableAll=function(c){r.setLevel(r.levels.SILENT,c)};var u=s();u==null&&(u=t??"WARN"),r.setLevel(u,!1)}var ne=new aa,Er={};ne.getLogger=function(t){if(typeof t!="symbol"&&typeof t!="string"||t==="")throw new TypeError("You must supply a name when creating a logger.");var e=Er[t];return e||(e=Er[t]=new aa(t,ne.getLevel(),ne.methodFactory)),e};var Qc=typeof window!==re?window.log:void 0;ne.noConflict=function(){return typeof window!==re&&window.log===ne&&(window.log=Qc),ne};ne.getLoggers=function(){return Er};ne.default=ne;var oa=ne,Wr={exports:{}};const Gc="twilio-video",Kc="Twilio Video",zc="Twilio Video JavaScript Library",Yc="2.28.1",Jc="https://twilio.com",Xc="Mark Andrus Roberts <<EMAIL>>",Zc=["Ryan Rowland <<EMAIL>>","Manjesh Malavalli <<EMAIL>>","Makarand Patwardhan <<EMAIL>>"],eu=["twilio","webrtc","library","javascript","video","rooms"],tu={type:"git",url:"https://github.com/twilio/twilio-video.js.git"},ru={"@babel/core":"^7.14.2","@babel/plugin-proposal-class-properties":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/preset-env":"^7.14.2","@babel/preset-typescript":"^7.13.0","@types/express":"^4.11.0","@types/node":"^8.5.1","@types/selenium-webdriver":"^3.0.8","@types/ws":"^3.2.1","@typescript-eslint/eslint-plugin":"^4.13.0","@typescript-eslint/parser":"^4.0.0","babel-cli":"^6.26.0","babel-preset-es2015":"^6.24.1",browserify:"^17.0.0",cheerio:"^0.22.0",cors:"^2.8.5",electron:"^17.2.0",envify:"^4.0.0",eslint:"^6.2.1","eslint-config-standard":"^14.0.0","eslint-plugin-import":"^2.18.2","eslint-plugin-node":"^9.1.0","eslint-plugin-promise":"^4.2.1","eslint-plugin-standard":"^4.0.1",express:"^4.16.2",glob:"^7.1.7","ink-docstrap":"^1.3.2",inquirer:"^7.0.0","is-docker":"^2.0.0",jsdoc:"^3.5.5","jsdoc-babel":"^0.5.0","json-loader":"^0.5.7",karma:"6.4.1","karma-browserify":"^8.0.0","karma-chrome-launcher":"^2.0.0","karma-edgium-launcher":"^4.0.0-0","karma-electron":"^6.1.0","karma-firefox-launcher":"^1.3.0","karma-htmlfile-reporter":"^0.3.8","karma-junit-reporter":"^1.2.0","karma-mocha":"^1.3.0","karma-safari-launcher":"^1.0.0","karma-spec-reporter":"0.0.32","karma-typescript":"^5.5.1","karma-typescript-es6-transform":"^5.5.1",mocha:"^3.2.0","mock-require":"^3.0.3",ncp:"^2.0.0","node-http-server":"^8.1.2","npm-run-all":"^4.0.2",nyc:"^15.1.0",requirejs:"^2.3.3",rimraf:"^2.6.1","simple-git":"^1.126.0",sinon:"^4.0.1","ts-node":"4.0.1",tslint:"5.8.0",twilio:"^3.49.0","twilio-release-tool":"^1.0.2",typescript:"4.2.2","uglify-js":"^2.8.22","vinyl-fs":"^2.4.4","vinyl-source-stream":"^1.1.0",watchify:"^3.11.1","webrtc-adapter":"^7.7.1"},nu={node:">=0.12"},iu="BSD-3-Clause",au="./es5/index.js",ou="./tsdef/index.d.ts",su={"lint:js":"eslint ./lib ./test/*.js ./docker/**/*.js ./test/framework/*.js ./test/lib/*.js ./test/integration/** ./test/unit/** ","lint:ts":"eslint ./tsdef/*.ts ./lib/**/*.ts",lint:"npm-run-all lint:js lint:ts",printVersion:"node --version && npm --version","test:unit":"npm-run-all printVersion build:es5 && nyc --report-dir=./coverage --include=lib/**/* --reporter=html --reporter=lcov --reporter=text mocha -r ts-node/register ./test/unit/*","test:unit:quick":"nyc --report-dir=./coverage --include=lib/**/* --reporter=html --reporter=lcov mocha -r ts-node/register","test:serversiderender":"mocha ./test/serversiderender/index.js","test:integration:adapter":"node ./scripts/karma.js karma/integration.adapter.conf.js","test:integration":"npm run build:es5 && node ./scripts/karma.js karma/integration.conf.js","test:umd:install":"npm install puppeteer@19.2.2","test:umd":"mocha ./test/umd/index.js","test:crossbrowser:build:clean":"rimraf ./test/crossbrowser/lib ./test/crossbrowser/src/browser/index.js","test:crossbrowser:build:lint":"cd ./test/crossbrowser && tslint --project tsconfig.json","test:crossbrowser:build:tsc":"cd ./test/crossbrowser && tsc","test:crossbrowser:build:browser":"cd ./test/crossbrowser && browserify lib/crossbrowser/src/browser/index.js > src/browser/index.js","test:crossbrowser:build":"npm-run-all test:crossbrowser:build:*","test:crossbrowser:test":"cd ./test/crossbrowser && mocha --compilers ts:ts-node/register test/integration/spec/**/*.ts","test:crossbrowser":"npm-run-all test:crossbrowser:*","test:sdkdriver:build:clean":"rimraf ./test/lib/sdkdriver/lib ./test/lib/sdkdriver/test/integration/browser/index.js","test:sdkdriver:build:lint":"cd ./test/lib/sdkdriver && tslint --project tsconfig.json","test:sdkdriver:build:tsc":"cd ./test/lib/sdkdriver && tsc --rootDir src","test:sdkdriver:build":"npm-run-all test:sdkdriver:build:*","test:sdkdriver:test:unit":"cd ./test/lib/sdkdriver && mocha --compilers ts:ts-node/register test/unit/spec/**/*.ts","test:sdkdriver:test:integration:browser":"cd ./test/lib/sdkdriver/test/integration && browserify browser/browser.js > browser/index.js","test:sdkdriver:test:integration:run":"cd ./test/lib/sdkdriver && mocha --compilers ts:ts-node/register test/integration/spec/**/*.ts","test:sdkdriver:test:integration":"npm-run-all test:sdkdriver:test:integration:*","test:sdkdriver:test":"npm-run-all test:sdkdriver:test:*","test:sdkdriver":"npm-run-all test:sdkdriver:*","test:framework:angular:install":"cd ./test/framework/twilio-video-angular && rimraf ./node_modules package-lock.json && npm install","test:framework:angular:run":"mocha ./test/framework/twilio-video-angular.js","test:framework:angular":"npm-run-all test:framework:angular:*","test:framework:no-framework:run":"mocha ./test/framework/twilio-video-no-framework.js","test:framework:no-framework":"npm-run-all test:framework:no-framework:*","test:framework:react:install":"cd ./test/framework/twilio-video-react && rimraf ./node_modules package-lock.json && npm install","test:framework:react:test":"node ./scripts/framework.js twilio-video-react","test:framework:react:build":"cd ./test/framework/twilio-video-react && npm run build","test:framework:react:run":"mocha ./test/framework/twilio-video-react.js","test:framework:react":"npm-run-all test:framework:react:*","test:framework:install":"npm install chromedriver && npm install selenium-webdriver && npm install geckodriver && npm install puppeteer","test:framework":"npm-run-all test:framework:install test:framework:no-framework test:framework:react",test:"npm-run-all test:unit test:integration","build:es5":"rimraf ./es5 && mkdir -p es5 && tsc tsdef/twilio-video-tests.ts --noEmit --lib es2018,dom && tsc","build:js":"node ./scripts/build.js ./src/twilio-video.js ./LICENSE.md ./dist/twilio-video.js","build:min.js":'uglifyjs ./dist/twilio-video.js -o ./dist/twilio-video.min.js --comments "/^! twilio-video.js/" -b beautify=false,ascii_only=true',build:"npm-run-all clean lint docs test:unit test:integration build:es5 build:js build:min.js test:umd","build:quick":"npm-run-all clean lint docs build:es5 build:js build:min.js",docs:"node ./scripts/docs.js ./dist/docs",watch:"tsc -w",clean:"rimraf ./coverage ./es5 ./dist"},cu={events:"^3.3.0",util:"^0.12.4",ws:"^7.4.6",xmlhttprequest:"^1.8.0"},uu={ws:"./src/ws.js",xmlhttprequest:"./src/xmlhttprequest.js"},sa={name:Gc,title:Kc,description:zc,version:Yc,homepage:Jc,author:Xc,contributors:Zc,keywords:eu,repository:tu,devDependencies:ru,engines:nu,license:iu,main:au,types:ou,scripts:su,dependencies:cu,browser:uu};Wr.exports;(function(n){var t=sa;n.exports.SDK_NAME=t.name+".js",n.exports.SDK_VERSION=t.version,n.exports.SDP_FORMAT="unified",n.exports.hardwareDevicePublisheriPad={hwDeviceManufacturer:"Apple",hwDeviceModel:"iPad",hwDeviceType:"tablet",platformName:"iOS"},n.exports.hardwareDevicePublisheriPhone={hwDeviceManufacturer:"Apple",hwDeviceModel:"iPhone",hwDeviceType:"mobile",platformName:"iOS"},n.exports.DEFAULT_ENVIRONMENT="prod",n.exports.DEFAULT_REALM="us1",n.exports.DEFAULT_REGION="gll",n.exports.DEFAULT_LOG_LEVEL="warn",n.exports.DEFAULT_LOGGER_NAME="twilio-video",n.exports.WS_SERVER=function(r,i){return i=i==="gll"?"global":encodeURIComponent(i),r==="prod"?"wss://"+i+".vss.twilio.com/signaling":"wss://"+i+".vss."+r+".twilio.com/signaling"},n.exports.PUBLISH_MAX_ATTEMPTS=5,n.exports.PUBLISH_BACKOFF_JITTER=10,n.exports.PUBLISH_BACKOFF_MS=20;function e(r){return["a","e","i","o","u"].includes(r.toLowerCase()[0])?"an":"a"}n.exports.typeErrors={ILLEGAL_INVOKE:function(r,i){return new TypeError("Illegal call to "+r+": "+i)},INVALID_TYPE:function(r,i){return new TypeError(r+" must be "+e(i)+" "+i)},INVALID_VALUE:function(r,i){return new RangeError(r+" must be one of "+i.join(", "))},REQUIRED_ARGUMENT:function(r){return new TypeError(r+" must be specified")}},n.exports.DEFAULT_FRAME_RATE=24,n.exports.DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS=1e4,n.exports.DEFAULT_ICE_GATHERING_TIMEOUT_MS=15e3,n.exports.DEFAULT_SESSION_TIMEOUT_SEC=30,n.exports.DEFAULT_NQ_LEVEL_LOCAL=1,n.exports.DEFAULT_NQ_LEVEL_REMOTE=0,n.exports.MAX_NQ_LEVEL=3,n.exports.ICE_ACTIVITY_CHECK_PERIOD_MS=1e3,n.exports.ICE_INACTIVITY_THRESHOLD_MS=3e3,n.exports.iceRestartBackoffConfig={factor:1.1,min:1,max:n.exports.DEFAULT_SESSION_TIMEOUT_SEC*1e3,jitter:1},n.exports.reconnectBackoffConfig={factor:1.5,min:80,jitter:1},n.exports.subscriptionMode={MODE_COLLABORATION:"collaboration",MODE_GRID:"grid",MODE_PRESENTATION:"presentation"},n.exports.trackSwitchOffMode={MODE_DISABLED:"disabled",MODE_DETECTED:"detected",MODE_PREDICTED:"predicted"},n.exports.trackPriority={PRIORITY_HIGH:"high",PRIORITY_LOW:"low",PRIORITY_STANDARD:"standard"},n.exports.clientTrackSwitchOffControl={MODE_AUTO:"auto",MODE_MANUAL:"manual"},n.exports.videoContentPreferencesMode={MODE_AUTO:"auto",MODE_MANUAL:"manual"}})(Wr);var U=Wr.exports,lu=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},du=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},fu=oa.getLogger,ca=U,Ln=ca.DEFAULT_LOG_LEVEL,pu=ca.DEFAULT_LOGGER_NAME,gt=U.typeErrors,Ue;function vu(n){if(Ue=Ue||new Map,Ue.has(n))return Ue.get(n);var t=new Set;return Ue.set(n,t),t}var Hr=function(){function n(t,e,r,i,a){if(typeof t!="string")throw gt.INVALID_TYPE("moduleName","string");if(!e)throw gt.REQUIRED_ARGUMENT("component");typeof r!="object"&&(r={}),a=a||fu,xn(r),Object.defineProperties(this,{_component:{value:e},_logLevels:{value:r},_warnings:{value:new Set},_loggerName:{get:function(){var s=i&&typeof i=="string"?i:pu;return this._logLevelsEqual||(s=s+"-"+t),s}},_logger:{get:function(){var s=a(this._loggerName),u=this._logLevels[t]||Ln;return u=u==="off"?"silent":u,s.setDefaultLevel(u),s}},_logLevelsEqual:{get:function(){return new Set(Object.values(this._logLevels)).size===1}},logLevel:{get:function(){return n.getLevelByName(r[t]||Ln)}},name:{get:e.toString.bind(e)}})}return n.getLevelByName=function(t){return isNaN(t)?(t=t.toUpperCase(),da(t),n[t]):parseInt(t,10)},n.prototype.createLog=function(t,e){var r=this._loggerName;return this._logLevelsEqual||(r=r.substring(0,r.lastIndexOf("-"))),new n(t,e,this._logLevels,r)},n.prototype.setLevels=function(t){return xn(t),Object.assign(this._logLevels,t),this},n.prototype.log=function(t,e){var r=n._levels[t];if(!r)throw gt.INVALID_VALUE("logLevel",la);r=r.toLowerCase();var i=[new Date().toISOString(),r,this.name];return(this._logger[r]||function(){}).apply(void 0,du([],lu(i.concat(e)))),this},n.prototype.debug=function(){return this.log(n.DEBUG,[].slice.call(arguments))},n.prototype.deprecated=function(t){var e=vu(this._component.constructor);return e.has(t)?this:(e.add(t),this.warn(t))},n.prototype.info=function(){return this.log(n.INFO,[].slice.call(arguments))},n.prototype.warn=function(){return this.log(n.WARN,[].slice.call(arguments))},n.prototype.warnOnce=function(t){return this._warnings.has(t)?this:(this._warnings.add(t),this.warn(t))},n.prototype.error=function(){return this.log(n.ERROR,[].slice.call(arguments))},n.prototype.throw=function(t,e){throw t.clone&&(t=t.clone(e)),this.log(n.ERROR,t),t},n}();Object.defineProperties(Hr,{DEBUG:{value:0},INFO:{value:1},WARN:{value:2},ERROR:{value:3},OFF:{value:4},_levels:{value:["DEBUG","INFO","WARN","ERROR","OFF"]}});var ua={},la=[],hu=Hr._levels.map(function(n,t){return ua[n]=!0,la.push(t),n});function da(n){if(!(n in ua))throw gt.INVALID_VALUE("level",hu)}function xn(n){Object.keys(n).forEach(function(t){da(n[t].toUpperCase())})}var ce=Hr,_u=d&&d.__awaiter||function(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(l){try{c(r.next(l))}catch(f){o(f)}}function u(l){try{c(r.throw(l))}catch(f){o(f)}}function c(l){l.done?a(l.value):i(l.value).then(s,u)}c((r=r.apply(n,t||[])).next())})},mu=d&&d.__generator||function(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){e.label=c[1];break}if(c[0]===6&&e.label<a[1]){e.label=a[1],a=c;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(c);break}a[2]&&e.ops.pop(),e.trys.pop();continue}c=t.call(n,e)}catch(l){c=[6,l],i=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}};Object.defineProperty(Lt,"__esModule",{value:!0});Lt.createNoiseCancellationAudioProcessor=void 0;var yu=Vc,gu={krisp:{supportedVersion:"1.0.0",pluginFile:"krispsdk.mjs"},rnnoise:{supportedVersion:"0.6.0",pluginFile:"rnnoise_sdk.mjs"}},bu=function(n){var t=n.supportedVersion,e=n.plugin,r=n.log;if(!e.getVersion||!e.isSupported)throw new Error("Plugin does not export getVersion/isSupported api. Are you using old version of the plugin ?");var i=e.getVersion();r.debug("Plugin Version = "+i);var a=t.split(".").map(function(c){return Number(c)}),o=i.split(".").map(function(c){return Number(c)});if(a.length!==3||o.length!==3)throw new Error("Unsupported Plugin version format: "+t+", "+i);if(a[0]!==o[0])throw new Error("Major version mismatch: [Plugin version "+i+"],  [Supported Version "+t+"]");if(o[1]<a[1])throw new Error("Minor version mismatch: [Plugin version "+i+"] < [Supported Version "+t+"]");var s=new AudioContext,u=e.isSupported(s);if(s.close(),!u)throw new Error("Noise Cancellation plugin is not supported on your browser")},An=new Map;function Su(n,t){return _u(this,void 0,void 0,function(){var e,r,i,a,o,s,u,c,l;return mu(this,function(f){switch(f.label){case 0:if(e=An.get(n.vendor),e)return[3,6];if(r=gu[n.vendor],!r)throw new Error("Unsupported NoiseCancellationOptions.vendor: "+n.vendor);i=r.supportedVersion,a=r.pluginFile,o=n.sdkAssetsPath,s=o+"/"+a,f.label=1;case 1:return f.trys.push([1,5,,6]),t.debug("loading noise cancellation sdk: ",s),[4,yu(s)];case 2:return u=f.sent(),t.debug("Loaded noise cancellation sdk:",u),c=u.default,bu({supportedVersion:i,plugin:c,log:t}),c.isInitialized()?[3,4]:(t.debug("initializing noise cancellation sdk: ",o),[4,c.init({rootDir:o})]);case 3:f.sent(),t.debug("noise cancellation sdk initialized!"),f.label=4;case 4:return e={vendor:n.vendor,isInitialized:function(){return c.isInitialized()},isConnected:function(){return c.isConnected()},isEnabled:function(){return c.isEnabled()},disconnect:function(){return c.disconnect()},enable:function(){return c.enable()},disable:function(){return c.disable()},destroy:function(){return c.destroy()},setLogging:function(p){return c.setLogging(p)},connect:function(p){t.debug("connect: ",p.id),c.isConnected()&&c.disconnect();var v=c.connect(new MediaStream([p]));if(!v)throw new Error("Error connecting with noise cancellation sdk");var _=v.getAudioTracks()[0];if(!_)throw new Error("Error getting clean track from noise cancellation sdk");return c.enable(),_}},An.set(n.vendor,e),[3,6];case 5:throw l=f.sent(),t.error("Error loading noise cancellation sdk:"+s,l),l;case 6:return[2,e]}})})}Lt.createNoiseCancellationAudioProcessor=Su;var fa=d&&d.__awaiter||function(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(l){try{c(r.next(l))}catch(f){o(f)}}function u(l){try{c(r.throw(l))}catch(f){o(f)}}function c(l){l.done?a(l.value):i(l.value).then(s,u)}c((r=r.apply(n,t||[])).next())})},pa=d&&d.__generator||function(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){e.label=c[1];break}if(c[0]===6&&e.label<a[1]){e.label=a[1],a=c;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(c);break}a[2]&&e.ops.pop(),e.trys.pop();continue}c=t.call(n,e)}catch(l){c=[6,l],i=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.applyNoiseCancellation=Ie.NoiseCancellationImpl=void 0;var wu=Lt,va=function(){function n(t,e){this._processor=t,this._sourceTrack=e,this._disabledPermanent=!1}return Object.defineProperty(n.prototype,"vendor",{get:function(){return this._processor.vendor},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"sourceTrack",{get:function(){return this._sourceTrack},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"isEnabled",{get:function(){return this._processor.isEnabled()},enumerable:!1,configurable:!0}),n.prototype.enable=function(){if(this._disabledPermanent)throw new Error(this.vendor+" noise cancellation is disabled permanently for this track");return this._processor.enable(),Promise.resolve()},n.prototype.disable=function(){return this._processor.disable(),Promise.resolve()},n.prototype.reacquireTrack=function(t){return fa(this,void 0,void 0,function(){var e,r,i;return pa(this,function(a){switch(a.label){case 0:return e=this._processor.isEnabled(),this._processor.disconnect(),[4,t()];case 1:return r=a.sent(),this._sourceTrack=r,[4,this._processor.connect(r)];case 2:return i=a.sent(),e?this._processor.enable():this._processor.disable(),[2,i]}})})},n.prototype.disablePermanently=function(){return this._disabledPermanent=!0,this.disable()},n.prototype.stop=function(){this._processor.disconnect(),this._sourceTrack.stop()},n}();Ie.NoiseCancellationImpl=va;function ku(n,t,e){return fa(this,void 0,void 0,function(){var r,i,a,o;return pa(this,function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,wu.createNoiseCancellationAudioProcessor(t,e)];case 1:return r=s.sent(),i=r.connect(n),a=new va(r,n),[2,{cleanTrack:i,noiseCancellation:a}];case 2:return o=s.sent(),e.warn("Failed to create noise cancellation. Returning normal audio track: "+o),[2,{cleanTrack:n}];case 3:return[2]}})})}Ie.applyNoiseCancellation=ku;var R={},V={},ha=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Tu=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n};function Pu(){var n={};return n.promise=new Promise(function(t,e){n.resolve=t,n.reject=e}),n}function Eu(n,t,e,r){if(!(r in t)&&!r.match(/^on[a-z]+$/)){var i=!1;try{var a=Object.getOwnPropertyDescriptor(n,r);i=a&&!!a.get}catch{}if(!i){var o;try{o=typeof n[r]}catch{}o==="function"&&(t[r]=function(){return this[e][r].apply(this[e],arguments)})}}}function Ou(n,t,e){for(var r in n)Eu(n,t,e,r)}function Cu(n,t){n=Array.isArray(n)?new Set(n):new Set(n.values()),t=Array.isArray(t)?new Set(t):new Set(t.values());var e=new Set;return n.forEach(function(r){t.has(r)||e.add(r)}),e}function Ru(n,t){var e=n instanceof Map||n instanceof Set?Array.from(n.values()):n;return e.reduce(function(r,i){return r.concat(t(i))},[])}function qr(){return typeof navigator<"u"&&typeof navigator.userAgent=="string"?navigator.userAgent:null}function Qr(n){return typeof n>"u"&&(n=qr()),/Chrome|CriOS/.test(n)?"chrome":/Firefox|FxiOS/.test(n)?"firefox":/Safari|iPhone|iPad|iPod/.test(n)?"safari":null}function Lu(n){typeof n>"u"&&(n=qr());var t={chrome:"Chrome|CriOS",firefox:"Firefox|FxiOS",safari:"Version"}[Qr(n)];if(!t)return null;var e=new RegExp("("+t+")/([^\\s]+)"),r=ha(n.match(e)||[],3),i=r[2];if(!i)return null;var a=i.split(".").map(Number);return{major:isNaN(a[0])?null:a[0],minor:isNaN(a[1])?null:a[1]}}function xu(n){return typeof n>"u"&&(n=qr()),/Mobi/.test(n)&&Qr()==="chrome"&&/iPad|iPhone|iPod/.test(n)}function Au(n,t){var e=null;Object.defineProperty(n,"on"+t,{get:function(){return e},set:function(r){e&&this.removeEventListener(t,e),typeof r=="function"?(e=r,this.addEventListener(t,e)):e=null}})}function Iu(n,t,e){return t?n.then(t,e):n}function Mu(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){var t=Math.random()*16|0,e=n==="x"?t:t&3|8;return e.toString(16)})}function ju(n,t,e){Object.getOwnPropertyNames(n).forEach(function(r){Du(n,t,e,r)})}function Du(n,t,e,r){if(!(r in t)){if(r.match(/^on[a-z]+$/)){Object.defineProperty(t,r,{value:null,writable:!0}),e.addEventListener(r.slice(2),function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];return t.dispatchEvent.apply(t,Tu([],ha(i)))});return}Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})}}function $u(){return typeof navigator=="object"&&typeof navigator.mediaDevices=="object"&&typeof navigator.mediaDevices.getUserMedia=="function"&&typeof RTCPeerConnection=="function"}function Nu(n){if(typeof RTCRtpSender<"u"&&typeof RTCRtpSender.getCapabilities=="function")return Promise.resolve(new Set(RTCRtpSender.getCapabilities(n).codecs.map(function(e){var r=e.mimeType;return r.split("/")[1].toLowerCase()})));if(typeof RTCPeerConnection>"u"||typeof RTCPeerConnection.prototype>"u"||typeof RTCPeerConnection.prototype.addTransceiver!="function"||typeof RTCPeerConnection.prototype.close!="function"||typeof RTCPeerConnection.prototype.createOffer!="function")return Promise.resolve(new Set);var t=new RTCPeerConnection;return t.addTransceiver(n),t.createOffer().then(function(e){var r=e.sdp;return t.close(),new Set((r.match(/^a=rtpmap:.+$/gm)||[]).map(function(i){return i.match(/^a=rtpmap:.+ ([^/]+)/)[1].toLowerCase()}))},function(){return t.close(),new Set})}var Or=new Map;function Fu(n,t){var e=Or.get(t);return e?Promise.resolve(e.has(n.toLowerCase())):Nu(t).then(function(r){return Or.set(t,r),r.has(n.toLowerCase())})}function Vu(){Or.clear()}V.clearCachedSupportedCodecs=Vu;V.defer=Pu;V.delegateMethods=Ou;V.difference=Cu;V.flatMap=Ru;V.guessBrowser=Qr;V.guessBrowserVersion=Lu;V.isCodecSupported=Fu;V.isIOSChrome=xu;V.interceptEvent=Au;V.legacyPromise=Iu;V.makeUUID=Mu;V.proxyProperties=ju;V.support=$u;var xt={},In="1234567890abcdef",Bu=32;function _a(n){for(var t="",e=0;e<Bu;e++)t+=In.charAt(Math.floor(Math.random()*In.length));return""+n+t}xt.sessionSID=_a("SS");xt.createSID=_a;var Uu={recordingMediaLost:"recording-media-lost"},ma=Uu,Wu=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Y=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},ge=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Gr=U,Hu=Gr.typeErrors,zt=Gr.trackPriority,qu=V,Qu=xt.sessionSID,Gu=ma;function Ku(n,t){if(n instanceof t.LocalAudioTrack||n instanceof t.LocalVideoTrack||n instanceof t.LocalDataTrack)return n;if(n instanceof t.MediaStreamTrack)return n.kind==="audio"?new t.LocalAudioTrack(n,t):new t.LocalVideoTrack(n,t);throw Hu.INVALID_TYPE("track","LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack")}function zu(n,t,e,r){var i={audio:r.LocalAudioTrackPublication,video:r.LocalVideoTrackPublication,data:r.LocalDataTrackPublication}[n.kind];return new i(t,n,e,r)}function Yu(n){return n[0].toUpperCase()+n.slice(1)}function Ju(n,t,e,r){var i=new Set;t.on("newListener",function a(o){e.has(o)&&!i.has(o)&&(r.deprecated(n+"#"+o+" has been deprecated and scheduled for removal in twilio-video.js@2.0.0."+(e.get(o)?" Use "+n+"#"+e.get(o)+" instead.":"")),i.add(o)),i.size>=e.size&&t.removeListener("newListener",a)})}function Xu(n,t){n=Array.isArray(n)?new Set(n):new Set(n.values()),t=Array.isArray(t)?new Set(t):new Set(t.values());var e=new Set;return n.forEach(function(r){t.has(r)||e.add(r)}),e}function Zu(n,t){return Object.keys(n).reduce(function(e,r){return n[r]!==t&&(e[r]=n[r]),e},{})}function el(n,t){var e=n instanceof Map||n instanceof Set?Array.from(n.values()):n;return t=t||function(i){return i},e.reduce(function(r,i){var a=t(i);return r.concat(a)},[])}function ya(){return typeof navigator<"u"&&navigator.userAgent?navigator.userAgent:"Unknown"}function tl(){var n=ya(),t=Y(n.match(/\(([^)]+)\)/)||[],2),e=t[1],r=e===void 0?"unknown":e,i=Y(r.split(";").map(function(o){return o.trim()}),1),a=i[0];return a.toLowerCase()}function rl(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){var t=Math.random()*16|0,e=n==="x"?t:t&3|8;return e.toString(16)})}function nl(n){var t=null;function e(){t=null,n()}return function(){t&&clearTimeout(t),t=setTimeout(e)}}function il(n,t,e,r){return new Promise(function(i,a){function o(){var u=[].slice.call(arguments);r&&t.removeListener(r,s),i.apply(void 0,ge([],Y(u)))}function s(){var u=[].slice.call(arguments);t.removeListener(e,o),a.apply(void 0,ge([],Y(u)))}t.once(e,o),r&&t.once(r,s),n()})}function al(n,t){return t.split(".").reduce(function(e,r){return e?e[r]:null},n)}function ol(){var n={};return n.promise=new Promise(function(t,e){n.resolve=t,n.reject=e}),n}function sl(n,t,e,r){if(!(r in t)&&!r.match(/^on[a-z]+$/)){var i;try{i=typeof n[r]}catch{}i==="function"&&(t[r]=function(){for(var a,o=[],s=0;s<arguments.length;s++)o[s]=arguments[s];return(a=this[e])[r].apply(a,ge([],Y(o)))})}}function cl(n,t,e){for(var r in n)sl(n,t,e,r)}function bt(n,t){if(n===t)return!0;if(typeof n!=typeof t)return!1;if(n===null)return t===null;if(t===null)return!1;if(Array.isArray(n))return Array.isArray(t)&&n.length===t.length&&n.every(function(i,a){return bt(i,t[a])});if(typeof n=="object"){var e=Object.keys(n).sort(),r=Object.keys(t).sort();return!Array.isArray(t)&&bt(e,r)&&e.every(function(i){return bt(n[i],t[i])})}return!1}function ga(n){return typeof n=="object"&&!Array.isArray(n)}function ul(n,t,e){Object.getOwnPropertyNames(n).forEach(function(r){ll(n,t,e,r)})}function ll(n,t,e,r){if(!(r in t)){if(r.match(/^on[a-z]+$/)){Object.defineProperty(t,r,{value:null,writable:!0}),e.addEventListener(r.slice(2),function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];t.dispatchEvent.apply(t,ge([],Y(i)))});return}Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})}}function dl(n,t,e){return t?n.then(function(r){t(r)},function(r){e(r)}):n}function fl(n){return typeof n=="string"?{default:n,media:n,signaling:n,webrtc:n}:n}function pl(n,t){return t=t?"Local":"",t+(n.kind||"").replace(/\w{1}/,function(e){return e.toUpperCase()})+"Track"}function vl(n,t){return t=t?"Local":"",t+(n.kind||"").replace(/\w{1}/,function(e){return e.toUpperCase()})+"TrackPublication"}function ba(n){Object.getOwnPropertyNames(n).forEach(function(t){t.startsWith("_")&&Sa(n,t)})}function hl(n,t){return function(e){Wu(r,e);function r(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];var o=e.apply(this,ge([],Y(i)))||this;return ba(o),_l(o,t),o}return r}(n)}function Sa(n,t){var e=Object.getOwnPropertyDescriptor(n,t);e.enumerable=!1,Object.defineProperty(n,t,e)}function _l(n,t){t===void 0&&(t=[]),t.forEach(function(e){n.hasOwnProperty(e)&&Sa(n,e)})}function wa(n){return n.map(At)}function ml(n){return wa(ge([],Y(n)))}function yl(n){return ge([],Y(n.entries())).reduce(function(t,e){var r,i=Y(e,2),a=i[0],o=i[1];return Object.assign((r={},r[a]=At(o),r),t)},{})}function gl(n){return Object.entries(n).reduce(function(t,e){var r,i=Y(e,2),a=i[0],o=i[1];return Object.assign((r={},r[a]=At(o),r),t)},{})}function At(n){return Array.isArray(n)?wa(n):n instanceof Set?ml(n):n instanceof Map?yl(n):n&&typeof n=="object"?gl(n):n}function bl(n){function t(i){return i?"true":"false"}var e={sessionSID:Qu,iceServers:(n.iceServers||[]).length,audioTracks:(n.tracks||[]).filter(function(i){return i.kind==="audio"}).length,videoTracks:(n.tracks||[]).filter(function(i){return i.kind==="video"}).length,dataTracks:(n.tracks||[]).filter(function(i){return i.kind==="data"}).length};if([["audio"],["automaticSubscription"],["enableDscp"],["eventListener"],["preflight"],["video"],["dominantSpeaker","enableDominantSpeaker"]].forEach(function(i){var a=Y(i,2),o=a[0],s=a[1];s=s||o,e[s]=t(!!n[o])}),[["maxVideoBitrate"],["maxAudioBitrate"]].forEach(function(i){var a=Y(i,2),o=a[0],s=a[1];s=s||o,typeof n[o]=="number"?e[s]=n[o]:isNaN(Number(n[o]))||(e[s]=Number(n[o]))}),[["iceTransportPolicy"],["region"],["name","roomName"]].forEach(function(i){var a=Y(i,2),o=a[0],s=a[1];s=s||o,typeof n[o]=="string"?e[s]=n[o]:typeof n[o]=="number"&&o==="name"&&(e[s]=n[o].toString())}),["preferredAudioCodecs","preferredVideoCodecs"].forEach(function(i){i in n&&(e[i]=JSON.stringify(n[i]))}),"networkQuality"in n&&(e.networkQualityConfiguration={},ga(n.networkQuality)?["local","remote"].forEach(function(i){typeof n.networkQuality[i]=="number"&&(e.networkQualityConfiguration[i]=n.networkQuality[i])}):(e.networkQualityConfiguration.remote=0,e.networkQualityConfiguration.local=n.networkQuality?1:0)),n.bandwidthProfile&&n.bandwidthProfile.video){var r=n.bandwidthProfile.video||{};e.bandwidthProfileOptions={},["mode","maxTracks","trackSwitchOffMode","dominantSpeakerPriority","maxSubscriptionBitrate","renderDimensions","contentPreferencesMode","clientTrackSwitchOffControl"].forEach(function(i){typeof r[i]=="number"||typeof r[i]=="string"?e.bandwidthProfileOptions[i]=r[i]:typeof r[i]=="boolean"?e.bandwidthProfileOptions[i]=t(r[i]):typeof r[i]=="object"&&(e.bandwidthProfileOptions[i]=JSON.stringify(r[i]))})}return{group:"room",name:"connect",level:"info",payload:e}}function Sl(n){return It(n,[{prop:"video",type:"object",transform:wl}])}function wl(n){return It(n,[{prop:"dominantSpeakerPriority",type:"string",payloadProp:"active_speaker_priority"},{prop:"maxSubscriptionBitrate",type:"number",payloadProp:"max_subscription_bandwidth"},{prop:"maxTracks",type:"number",payloadProp:"max_tracks"},{prop:"mode",type:"string"},{prop:"renderDimensions",type:"object",payloadProp:"render_dimensions",transform:Tl},{prop:"trackSwitchOffMode",type:"string",payloadProp:"track_switch_off"}])}function kl(n,t,e,r,i,a){var o={transports:[{type:"data-channel"}]};return Object.assign(n?{active_speaker:o}:{},t?{network_quality:o}:{},a?{render_hints:o}:{},i?{publisher_hints:o}:{},e?{track_priority:o}:{},r?{track_switch_off:o}:{})}function Yt(n){return It(n,[{prop:"height",type:"number"},{prop:"width",type:"number"}])}function Tl(n){var t=zt.PRIORITY_HIGH,e=zt.PRIORITY_LOW,r=zt.PRIORITY_STANDARD;return It(n,[{prop:t,type:"object",transform:Yt},{prop:e,type:"object",transform:Yt},{prop:r,type:"object",transform:Yt}])}function It(n,t){return t.reduce(function(e,r){var i,a=r.prop,o=r.type,s=r.payloadProp,u=s===void 0?a:s,c=r.transform,l=c===void 0?function(f){return f}:c;return typeof n[a]===o?Object.assign((i={},i[u]=l(n[a]),i),e):e},{})}function Pl(n){return{rules:[{type:n?"include":"exclude",all:!0}],revision:1}}function El(n){var t,e=(t={},t[Gu.recordingMediaLost]="recordings",t);return n.map(function(r){return e[r]}).filter(function(r){return!!r})}function Ol(n,t){var e=Math.random();return n-t+Math.floor(2*t*e+.5)}function Cl(n,t,e){return t<=n&&n<=e}function Rl(n){return qu.guessBrowser()==="chrome"&&n.kind==="video"&&"displaySurface"in n.getSettings()}function Ll(n){return n===void 0&&(n=10),new Promise(function(t){return setTimeout(t,n)})}function xl(n,t){return new Promise(function(e){n.addEventListener(t,function r(i){n.removeEventListener(t,r),e(i)})})}R.constants=Gr;R.createBandwidthProfilePayload=Sl;R.createMediaSignalingPayload=kl;R.createMediaWarningsPayload=El;R.createRoomConnectEventPayload=bl;R.createSubscribePayload=Pl;R.asLocalTrack=Ku;R.asLocalTrackPublication=zu;R.capitalize=Yu;R.deprecateEvents=Ju;R.difference=Xu;R.filterObject=Zu;R.flatMap=el;R.getPlatform=tl;R.getUserAgent=ya;R.hidePrivateProperties=ba;R.hidePrivateAndCertainPublicPropertiesInClass=hl;R.isDeepEqual=bt;R.isNonArrayObject=ga;R.inRange=Cl;R.makeUUID=rl;R.oncePerTick=nl;R.promiseFromEvents=il;R.getOrNull=al;R.defer=ol;R.delegateMethods=cl;R.proxyProperties=ul;R.legacyPromise=dl;R.buildLogLevels=fl;R.trackClass=pl;R.trackPublicationClass=vl;R.valueToJSON=At;R.withJitter=Ol;R.isChromeScreenShareTrack=Rl;R.waitForSometime=Ll;R.waitForEvent=xl;var J={},ka=V,Al=ka.flatMap,Il=ka.guessBrowser,ve=null;function Ml(){if(typeof ve=="boolean")return ve;if(typeof RTCPeerConnection>"u")return ve=!1,ve;try{new RTCPeerConnection({sdpSemantics:"foo"}),ve=!1}catch{ve=!0}return ve}var Oe=null;function jl(){Oe=null}function Dl(){if(!Oe)if(typeof RTCPeerConnection<"u"&&"addTransceiver"in RTCPeerConnection.prototype){var n=new RTCPeerConnection;try{n.addTransceiver("audio"),Oe="unified"}catch{Oe="planb"}n.close()}else Oe="planb";return Oe}function $l(n){return!n||!Ml()?Dl():{"plan-b":"planb","unified-plan":"unified"}[n]}function Nl(){return typeof RTCRtpTransceiver<"u"&&"currentDirection"in RTCRtpTransceiver.prototype?"unified":"planb"}function Fl(n){return{chrome:$l(n),firefox:"unified",safari:Nl()}[Il()]||null}function Kr(n,t){var e=t.match(new RegExp(n,"gm"))||[];return e.reduce(function(r,i){var a=i.match(new RegExp(n));return a?r.add(a[1]):r},new Set)}function Ta(n,t){return Kr(n,t)}function Pa(n){return Ta("^a=ssrc:[0-9]+ +msid:.+ +(.+) *$",n)}function Ea(n){return Ta("^a=msid:.+ +(.+) *$",n)}function Oa(n,t){var e="^a=ssrc:([0-9]+) +msid:[^ ]+ +"+t+" *$";return Kr(e,n)}function Ca(n,t,e){return t===void 0&&(t=".*"),e===void 0&&(e=".*"),n.split(`\r
m=`).slice(1).map(function(r){return"m="+r}).filter(function(r){var i=new RegExp("m="+t,"gm"),a=new RegExp("a="+e,"gm");return i.test(r)&&a.test(r)})}function Vl(n){return Array.from(Kr("^a=ssrc:([0-9]+) +.*$",n))}function Ra(n,t){var e=Ca(n),r=new RegExp("^a=msid:[^ ]+ +"+t+" *$","gm"),i=e.filter(function(a){return a.match(r)});return new Set(Al(i,Vl))}function La(n,t,e){return new Map(Array.from(n(e)).map(function(r){return[r,t(e,r)]}))}function Bl(n){return La(Pa,Oa,n)}function Ul(n){return La(Ea,Ra,n)}function xa(n,t,e){var r=n(e),i=new Map;r.forEach(function(s,u){if(!t.has(u)){t.set(u,s);return}var c=Array.from(t.get(u)),l=Array.from(s);c.forEach(function(f,p){var v=l[p];i.set(v,f);var _="^a=ssrc:"+v+" (.*)$",m="a=ssrc:"+f+" $1";e=e.replace(new RegExp(_,"gm"),m)})});var a="^(a=ssrc-group:[^ ]+ +)(.*)$",o=e.match(new RegExp(a,"gm"))||[];return o.forEach(function(s){var u=s.match(new RegExp(a));if(u){var c=u[1],l=u[2],f=l.split(" ").map(function(p){var v=i.get(p);return v||p}).join(" ");e=e.replace(u[0],c+f)}}),e}function Wl(n,t){return xa(Bl,n,t)}function Hl(n,t){return xa(Ul,n,t)}J.clearChromeCachedSdpFormat=jl;J.getSdpFormat=Fl;J.getMediaSections=Ca;J.getPlanBTrackIds=Pa;J.getUnifiedPlanTrackIds=Ea;J.getPlanBSSRCs=Oa;J.getUnifiedPlanSSRCs=Ra;J.updatePlanBTrackIdsToSSRCs=Wl;J.updateUnifiedPlanTrackIdsToSSRCs=Hl;var ql=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},zr=V,Aa=zr.flatMap,Ql=zr.guessBrowser,Gl=zr.guessBrowserVersion,Kl=J.getSdpFormat,Yr=Ql(),zl=Gl(),Jr=Yr==="chrome",Ia=Yr==="firefox",Ma=Yr==="safari",Mn=Jr?zl.major:null,Yl=32767;function Jl(n,t){return n&&typeof n.getStats=="function"?Xl(n,t):Promise.reject(new Error("Given PeerConnection does not support getStats"))}function Xl(n,t){var e=ot(n,"audio","local"),r=ot(n,"video","local"),i=ot(n,"audio"),a=ot(n,"video"),o={activeIceCandidatePair:null,localAudioTrackStats:[],localVideoTrackStats:[],remoteAudioTrackStats:[],remoteVideoTrackStats:[]},s=Aa([[e,"localAudioTrackStats",!1],[r,"localVideoTrackStats",!1],[i,"remoteAudioTrackStats",!0],[a,"remoteVideoTrackStats",!0]],function(u){var c=ql(u,3),l=c[0],f=c[1],p=c[2];return l.map(function(v){return rd(n,v,Object.assign({isRemote:p},t)).then(function(_){_.forEach(function(m){m.trackId=v.id,o[f].push(m)})})})});return Promise.all(s).then(function(){return Zl(n,t)}).then(function(u){return o.activeIceCandidatePair=u,o})}function Zl(n,t){return t===void 0&&(t={}),typeof t.testForChrome<"u"||Jr||typeof t.testForSafari<"u"||Ma?n.getStats().then(ed):typeof t.testForFirefox<"u"||Ia?n.getStats().then(td):Promise.reject(new Error("RTCPeerConnection#getStats() not supported"))}function ed(n){var t=Array.from(n.values()).find(function(u){var c=u.nominated,l=u.type;return l==="candidate-pair"&&c});if(!t)return null;var e=n.get(t.localCandidateId),r=n.get(t.remoteCandidateId),i=[{key:"candidateType",type:"string"},{key:"ip",type:"string"},{key:"port",type:"number"},{key:"priority",type:"number"},{key:"protocol",type:"string"},{key:"url",type:"string"}],a=i.concat([{key:"deleted",type:"boolean"},{key:"relayProtocol",type:"string"}]),o=e?a.reduce(function(u,c){var l=c.key,f=c.type;return u[l]=typeof e[l]===f?e[l]:l==="deleted"?!1:null,u},{}):null,s=r?i.reduce(function(u,c){var l=c.key,f=c.type;return u[l]=typeof r[l]===f?r[l]:null,u},{}):null;return[{key:"availableIncomingBitrate",type:"number"},{key:"availableOutgoingBitrate",type:"number"},{key:"bytesReceived",type:"number"},{key:"bytesSent",type:"number"},{key:"consentRequestsSent",type:"number"},{key:"currentRoundTripTime",type:"number"},{key:"lastPacketReceivedTimestamp",type:"number"},{key:"lastPacketSentTimestamp",type:"number"},{key:"nominated",type:"boolean"},{key:"priority",type:"number"},{key:"readable",type:"boolean"},{key:"requestsReceived",type:"number"},{key:"requestsSent",type:"number"},{key:"responsesReceived",type:"number"},{key:"responsesSent",type:"number"},{key:"retransmissionsReceived",type:"number"},{key:"retransmissionsSent",type:"number"},{key:"state",type:"string",fixup:function(u){return u==="inprogress"?"in-progress":u}},{key:"totalRoundTripTime",type:"number"},{key:"transportId",type:"string"},{key:"writable",type:"boolean"}].reduce(function(u,c){var l=c.key,f=c.type,p=c.fixup;return u[l]=typeof t[l]===f?p?p(t[l]):t[l]:null,u},{localCandidate:o,remoteCandidate:s})}function td(n){var t=Array.from(n.values()).find(function(c){var l=c.nominated,f=c.type;return f==="candidate-pair"&&l});if(!t)return null;var e=n.get(t.localCandidateId),r=n.get(t.remoteCandidateId),i=[{key:"candidateType",type:"string"},{key:"ip",ffKeys:["address","ipAddress"],type:"string"},{key:"port",ffKeys:["portNumber"],type:"number"},{key:"priority",type:"number"},{key:"protocol",ffKeys:["transport"],type:"string"},{key:"url",type:"string"}],a=i.concat([{key:"deleted",type:"boolean"},{key:"relayProtocol",type:"string"}]),o={host:"host",peerreflexive:"prflx",relayed:"relay",serverreflexive:"srflx"},s=e?a.reduce(function(c,l){var f=l.ffKeys,p=l.key,v=l.type,_=f&&f.find(function(m){return m in e})||p;return c[p]=typeof e[_]===v?_==="candidateType"&&o[e[_]]||e[_]:_==="deleted"?!1:null,c},{}):null,u=r?i.reduce(function(c,l){var f=l.ffKeys,p=l.key,v=l.type,_=f&&f.find(function(m){return m in r})||p;return c[p]=typeof r[_]===v?_==="candidateType"&&o[r[_]]||r[_]:null,c},{}):null;return[{key:"availableIncomingBitrate",type:"number"},{key:"availableOutgoingBitrate",type:"number"},{key:"bytesReceived",type:"number"},{key:"bytesSent",type:"number"},{key:"consentRequestsSent",type:"number"},{key:"currentRoundTripTime",type:"number"},{key:"lastPacketReceivedTimestamp",type:"number"},{key:"lastPacketSentTimestamp",type:"number"},{key:"nominated",type:"boolean"},{key:"priority",type:"number"},{key:"readable",type:"boolean"},{key:"requestsReceived",type:"number"},{key:"requestsSent",type:"number"},{key:"responsesReceived",type:"number"},{key:"responsesSent",type:"number"},{key:"retransmissionsReceived",type:"number"},{key:"retransmissionsSent",type:"number"},{key:"state",type:"string"},{key:"totalRoundTripTime",type:"number"},{key:"transportId",type:"string"},{key:"writable",type:"boolean"}].reduce(function(c,l){var f=l.key,p=l.type;return c[f]=typeof t[f]===p?t[f]:null,c},{localCandidate:s,remoteCandidate:u})}function ot(n,t,e){var r=e==="local"?"getSenders":"getReceivers";if(n[r])return n[r]().map(function(o){var s=o.track;return s}).filter(function(o){return o&&o.kind===t});var i=e==="local"?"getLocalStreams":"getRemoteStreams",a=t==="audio"?"getAudioTracks":"getVideoTracks";return Aa(n[i](),function(o){return o[a]()})}function rd(n,t,e){return e===void 0&&(e={}),typeof e.testForChrome<"u"||Jr?jn(n,t,e):typeof e.testForFirefox<"u"||Ia?nd(n,t,e):typeof e.testForSafari<"u"||Ma?typeof e.testForSafari<"u"||Kl()==="unified"?jn(n,t,e):Promise.reject(new Error(["getStats() is not supported on this version of Safari","due to this bug: https://bugs.webkit.org/show_bug.cgi?id=192601"].join(" "))):Promise.reject(new Error("RTCPeerConnection#getStats() not supported"))}function jn(n,t,e){return Mn&&Mn<67?new Promise(function(r,i){n.getStats(function(a){r([id(a,t)])},null,i)}):n.getStats(t).then(function(r){return ad(r,e)})}function nd(n,t,e){return n.getStats(t).then(function(r){return[od(r,e)]})}function id(n,t){var e=n.result().find(function(i){return i.type==="ssrc"&&i.stat("googTrackId")===t.id}),r={};return e&&(r.timestamp=Math.round(Number(e.timestamp)),r=e.names().reduce(function(i,a){switch(a){case"googCodecName":i.codecName=e.stat(a);break;case"googRtt":i.roundTripTime=Number(e.stat(a));break;case"googJitterReceived":i.jitter=Number(e.stat(a));break;case"googFrameWidthInput":i.frameWidthInput=Number(e.stat(a));break;case"googFrameHeightInput":i.frameHeightInput=Number(e.stat(a));break;case"googFrameWidthSent":i.frameWidthSent=Number(e.stat(a));break;case"googFrameHeightSent":i.frameHeightSent=Number(e.stat(a));break;case"googFrameWidthReceived":i.frameWidthReceived=Number(e.stat(a));break;case"googFrameHeightReceived":i.frameHeightReceived=Number(e.stat(a));break;case"googFrameRateInput":i.frameRateInput=Number(e.stat(a));break;case"googFrameRateSent":i.frameRateSent=Number(e.stat(a));break;case"googFrameRateReceived":i.frameRateReceived=Number(e.stat(a));break;case"ssrc":i[a]=e.stat(a);break;case"bytesReceived":case"bytesSent":case"packetsLost":case"packetsReceived":case"packetsSent":case"audioInputLevel":case"audioOutputLevel":i[a]=Number(e.stat(a));break}return i},r)),r}function ad(n,t){var e=t.simulateExceptionWhileStandardizingStats,r=e===void 0?!1:e;if(r)throw new Error("Error while gathering stats");var i=null,a=[],o=null,s=null,u=null,c=null,l=null;n.forEach(function(m){var y=m.type;switch(y){case"inbound-rtp":i=m;break;case"outbound-rtp":a.push(m);break;case"media-source":l=m;break;case"track":u=m;break;case"codec":c=m;break;case"remote-inbound-rtp":o=m;break;case"remote-outbound-rtp":s=m;break}});var f=u?u.remoteSource:!l,p=f?[i]:a,v=[],_=f?s:o;return p.forEach(function(m){var y={},P=[m,l,u,c,_&&_.ssrc===m.ssrc?_:null];function h(En){var On=P.find(function(Cn){return Cn&&typeof Cn[En]<"u"})||null;return On?On[En]:null}var b=h("ssrc");typeof b=="number"&&(y.ssrc=String(b));var g=h("timestamp");y.timestamp=Math.round(g);var T=h("mimeType");typeof T=="string"&&(T=T.split("/"),y.codecName=T[T.length-1]);var S=h("roundTripTime");typeof S=="number"&&(y.roundTripTime=Math.round(S*1e3));var w=h("jitter");typeof w=="number"&&(y.jitter=Math.round(w*1e3));var O=h("frameWidth");typeof O=="number"&&(f?y.frameWidthReceived=O:(y.frameWidthSent=O,y.frameWidthInput=u?u.frameWidth:l.width));var D=h("frameHeight");typeof D=="number"&&(f?y.frameHeightReceived=D:(y.frameHeightSent=D,y.frameHeightInput=u?u.frameHeight:l.height));var F=h("framesPerSecond");typeof F=="number"&&(y[f?"frameRateReceived":"frameRateSent"]=F);var q=h("bytesReceived");typeof q=="number"&&(y.bytesReceived=q);var E=h("bytesSent");typeof E=="number"&&(y.bytesSent=E);var I=h("packetsLost");typeof I=="number"&&(y.packetsLost=I);var k=h("packetsReceived");typeof k=="number"&&(y.packetsReceived=k);var x=h("packetsSent");typeof x=="number"&&(y.packetsSent=x);var C=h("audioLevel");typeof C=="number"&&(C=Math.round(C*Yl),f?y.audioOutputLevel=C:y.audioInputLevel=C);var $=h("totalPacketSendDelay");typeof $=="number"&&(y.totalPacketSendDelay=$);var N=h("totalEncodeTime");typeof N=="number"&&(y.totalEncodeTime=N);var W=h("framesEncoded");typeof W=="number"&&(y.framesEncoded=W);var G=h("estimatedPlayoutTimestamp");typeof G=="number"&&(y.estimatedPlayoutTimestamp=G);var H=h("totalDecodeTime");typeof H=="number"&&(y.totalDecodeTime=H);var ae=h("framesDecoded");typeof ae=="number"&&(y.framesDecoded=ae);var Q=h("jitterBufferDelay");typeof Q=="number"&&(y.jitterBufferDelay=Q);var ee=h("jitterBufferEmittedCount");typeof ee=="number"&&(y.jitterBufferEmittedCount=ee),v.push(y)}),v}function od(n,t){n===void 0&&(n=new Map);var e=t.isRemote,r=t.simulateExceptionWhileStandardizingStats,i=r===void 0?!1:r;if(i)throw new Error("Error while gathering stats");var a=null,o=null;n.forEach(function(k){var x=k.isRemote,C=k.remoteId,$=k.type;if(!x)switch($){case"inbound-rtp":a=k,o=n.get(C);break;case"outbound-rtp":o=k,a=n.get(C);break}});var s=e?a:o,u=e?o:a;function c(k){return s&&typeof s[k]<"u"?s[k]:u&&typeof u[k]<"u"?u[k]:null}var l={},f=c("timestamp");l.timestamp=Math.round(f);var p=c("ssrc");typeof p=="number"&&(l.ssrc=String(p));var v=c("bytesSent");typeof v=="number"&&(l.bytesSent=v);var _=c("packetsLost");typeof _=="number"&&(l.packetsLost=_);var m=c("packetsSent");typeof m=="number"&&(l.packetsSent=m);var y=c("roundTripTime");typeof y=="number"&&(l.roundTripTime=Math.round(y*1e3));var P=c("jitter");typeof P=="number"&&(l.jitter=Math.round(P*1e3));var h=c("framerateMean");typeof h=="number"&&(l.frameRateSent=Math.round(h));var b=c("bytesReceived");typeof b=="number"&&(l.bytesReceived=b);var g=c("packetsReceived");typeof g=="number"&&(l.packetsReceived=g);var T=c("framerateMean");typeof T=="number"&&(l.frameRateReceived=Math.round(T));var S=c("totalPacketSendDelay");typeof S=="number"&&(l.totalPacketSendDelay=S);var w=c("totalEncodeTime");typeof w=="number"&&(l.totalEncodeTime=w);var O=c("framesEncoded");typeof O=="number"&&(l.framesEncoded=O);var D=c("estimatedPlayoutTimestamp");typeof D=="number"&&(l.estimatedPlayoutTimestamp=D);var F=c("totalDecodeTime");typeof F=="number"&&(l.totalDecodeTime=F);var q=c("framesDecoded");typeof q=="number"&&(l.framesDecoded=q);var E=c("jitterBufferDelay");typeof E=="number"&&(l.jitterBufferDelay=E);var I=c("jitterBufferEmittedCount");return typeof I=="number"&&(l.jitterBufferEmittedCount=I),l}var sd=Jl;function cd(n){return n===void 0&&(n={audio:!0,video:!0}),typeof navigator=="object"&&typeof navigator.mediaDevices=="object"&&typeof navigator.mediaDevices.getUserMedia=="function"?navigator.mediaDevices.getUserMedia(n):Promise.reject(new Error("getUserMedia is not supported"))}var ud=cd,Cr={exports:{}};typeof MediaStream=="function"?Cr.exports=MediaStream:Cr.exports=function(){throw new Error("MediaStream is not supported")};var ja=Cr.exports,Rr={exports:{}};typeof MediaStreamTrack=="function"?Rr.exports=MediaStreamTrack:Rr.exports=function(){throw new Error("MediaStreamTrack is not supported")};var ld=Rr.exports,Lr={exports:{}};typeof RTCIceCandidate=="function"?Lr.exports=RTCIceCandidate:Lr.exports=function(){throw new Error("RTCIceCandidate is not supported")};var dd=Lr.exports,Ce={exports:{}},Jt,Dn;function Da(){if(Dn)return Jt;Dn=1;var n=function(){function t(e){this.descriptionInitDict=e;var r=e&&e.type==="rollback"?null:new RTCSessionDescription(e);Object.defineProperties(this,{_description:{get:function(){return r}}})}return Object.defineProperty(t.prototype,"sdp",{get:function(){return this._description?this._description.sdp:this.descriptionInitDict.sdp},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){return this._description?this._description.type:this.descriptionInitDict.type},enumerable:!1,configurable:!0}),t}();return Jt=n,Jt}var Xr={exports:{}},xe=typeof Reflect=="object"?Reflect:null,$n=xe&&typeof xe.apply=="function"?xe.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)},St;xe&&typeof xe.ownKeys=="function"?St=xe.ownKeys:Object.getOwnPropertySymbols?St=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:St=function(t){return Object.getOwnPropertyNames(t)};function fd(n){console&&console.warn&&console.warn(n)}var $a=Number.isNaN||function(t){return t!==t};function B(){B.init.call(this)}Xr.exports=B;Xr.exports.once=_d;B.EventEmitter=B;B.prototype._events=void 0;B.prototype._eventsCount=0;B.prototype._maxListeners=void 0;var Nn=10;function Mt(n){if(typeof n!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n)}Object.defineProperty(B,"defaultMaxListeners",{enumerable:!0,get:function(){return Nn},set:function(n){if(typeof n!="number"||n<0||$a(n))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+n+".");Nn=n}});B.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};B.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||$a(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function Na(n){return n._maxListeners===void 0?B.defaultMaxListeners:n._maxListeners}B.prototype.getMaxListeners=function(){return Na(this)};B.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i=t==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var o;if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var u=a[t];if(u===void 0)return!1;if(typeof u=="function")$n(u,this,e);else for(var c=u.length,l=Wa(u,c),r=0;r<c;++r)$n(l[r],this,e);return!0};function Fa(n,t,e,r){var i,a,o;if(Mt(e),a=n._events,a===void 0?(a=n._events=Object.create(null),n._eventsCount=0):(a.newListener!==void 0&&(n.emit("newListener",t,e.listener?e.listener:e),a=n._events),o=a[t]),o===void 0)o=a[t]=e,++n._eventsCount;else if(typeof o=="function"?o=a[t]=r?[e,o]:[o,e]:r?o.unshift(e):o.push(e),i=Na(n),i>0&&o.length>i&&!o.warned){o.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=n,s.type=t,s.count=o.length,fd(s)}return n}B.prototype.addListener=function(t,e){return Fa(this,t,e,!1)};B.prototype.on=B.prototype.addListener;B.prototype.prependListener=function(t,e){return Fa(this,t,e,!0)};function pd(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Va(n,t,e){var r={fired:!1,wrapFn:void 0,target:n,type:t,listener:e},i=pd.bind(r);return i.listener=e,r.wrapFn=i,i}B.prototype.once=function(t,e){return Mt(e),this.on(t,Va(this,t,e)),this};B.prototype.prependOnceListener=function(t,e){return Mt(e),this.prependListener(t,Va(this,t,e)),this};B.prototype.removeListener=function(t,e){var r,i,a,o,s;if(Mt(e),i=this._events,i===void 0)return this;if(r=i[t],r===void 0)return this;if(r===e||r.listener===e)--this._eventsCount===0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||e));else if(typeof r!="function"){for(a=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,a=o;break}if(a<0)return this;a===0?r.shift():vd(r,a),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,s||e)}return this};B.prototype.off=B.prototype.removeListener;B.prototype.removeAllListeners=function(t){var e,r,i;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var a=Object.keys(r),o;for(i=0;i<a.length;++i)o=a[i],o!=="removeListener"&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=r[t],typeof e=="function")this.removeListener(t,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(t,e[i]);return this};function Ba(n,t,e){var r=n._events;if(r===void 0)return[];var i=r[t];return i===void 0?[]:typeof i=="function"?e?[i.listener||i]:[i]:e?hd(i):Wa(i,i.length)}B.prototype.listeners=function(t){return Ba(this,t,!0)};B.prototype.rawListeners=function(t){return Ba(this,t,!1)};B.listenerCount=function(n,t){return typeof n.listenerCount=="function"?n.listenerCount(t):Ua.call(n,t)};B.prototype.listenerCount=Ua;function Ua(n){var t=this._events;if(t!==void 0){var e=t[n];if(typeof e=="function")return 1;if(e!==void 0)return e.length}return 0}B.prototype.eventNames=function(){return this._eventsCount>0?St(this._events):[]};function Wa(n,t){for(var e=new Array(t),r=0;r<t;++r)e[r]=n[r];return e}function vd(n,t){for(;t+1<n.length;t++)n[t]=n[t+1];n.pop()}function hd(n){for(var t=new Array(n.length),e=0;e<t.length;++e)t[e]=n[e].listener||n[e];return t}function _d(n,t){return new Promise(function(e,r){function i(o){n.removeListener(t,a),r(o)}function a(){typeof n.removeListener=="function"&&n.removeListener("error",i),e([].slice.call(arguments))}Ha(n,t,a,{once:!0}),t!=="error"&&md(n,i,{once:!0})})}function md(n,t,e){typeof n.on=="function"&&Ha(n,"error",t,e)}function Ha(n,t,e,r){if(typeof n.on=="function")r.once?n.once(t,e):n.on(t,e);else if(typeof n.addEventListener=="function")n.addEventListener(t,function i(a){r.once&&n.removeEventListener(t,i),e(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof n)}var K=Xr.exports,Xt,Fn;function Zr(){if(Fn)return Xt;Fn=1;var n=d&&d.__read||function(i,a){var o=typeof Symbol=="function"&&i[Symbol.iterator];if(!o)return i;var s=o.call(i),u,c=[],l;try{for(;(a===void 0||a-- >0)&&!(u=s.next()).done;)c.push(u.value)}catch(f){l={error:f}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(l)throw l.error}}return c},t=d&&d.__spreadArray||function(i,a){for(var o=0,s=a.length,u=i.length;o<s;o++,u++)i[u]=a[o];return i},e=K.EventEmitter,r=function(){function i(){Object.defineProperties(this,{_eventEmitter:{value:new e}})}return i.prototype.dispatchEvent=function(a){return this._eventEmitter.emit(a.type,a)},i.prototype.addEventListener=function(){var a;return(a=this._eventEmitter).addListener.apply(a,t([],n(arguments)))},i.prototype.removeEventListener=function(){var a;return(a=this._eventEmitter).removeListener.apply(a,t([],n(arguments)))},i}();return Xt=r,Xt}var Zt,Vn;function qa(){if(Vn)return Zt;Vn=1;var n=V.defer,t={high:new Set(["low"]),low:new Set(["high"])},e=function(){function i(a){a===void 0&&(a="low");var o=a;Object.defineProperties(this,{_state:{set:function(s){var u=this;if(o!==s){o=s;var c=this._whenDeferreds.get(o);c.forEach(function(l){return l.resolve(u)}),c.clear()}},get:function(){return o}},_whenDeferreds:{value:new Map([["high",new Set],["low",new Set]])}})}return Object.defineProperty(i.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),i.prototype.lower=function(){return this.transition("low")},i.prototype.raise=function(){return this.transition("high")},i.prototype.transition=function(a){if(!t[this.state].has(a))throw r(this.state,a);return this._state=a,this},i.prototype.when=function(a){if(this.state===a)return Promise.resolve(this);if(!t[this.state].has(a))return Promise.reject(r(this.state,a));var o=n();return this._whenDeferreds.get(a).add(o),o.promise},i}();function r(i,a){return new Error('Cannot transition from "'+i+'" to "'+a+'"')}return Zt=e,Zt}var er,Bn;function yd(){if(Bn)return er;Bn=1;var n=function(){function t(e){Object.defineProperties(this,{track:{enumerable:!0,value:e,writable:!0}})}return t}();return er=n,er}var tr,Un;function gd(){if(Un)return tr;Un=1;var n=d&&d.__extends||function(){var E=function(I,k){return E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(x,C){x.__proto__=C}||function(x,C){for(var $ in C)Object.prototype.hasOwnProperty.call(C,$)&&(x[$]=C[$])},E(I,k)};return function(I,k){if(typeof k!="function"&&k!==null)throw new TypeError("Class extends value "+String(k)+" is not a constructor or null");E(I,k);function x(){this.constructor=I}I.prototype=k===null?Object.create(k):(x.prototype=k.prototype,new x)}}(),t=d&&d.__read||function(E,I){var k=typeof Symbol=="function"&&E[Symbol.iterator];if(!k)return E;var x=k.call(E),C,$=[],N;try{for(;(I===void 0||I-- >0)&&!(C=x.next()).done;)$.push(C.value)}catch(W){N={error:W}}finally{try{C&&!C.done&&(k=x.return)&&k.call(x)}finally{if(N)throw N.error}}return $},e=d&&d.__spreadArray||function(E,I){for(var k=0,x=I.length,C=E.length;k<x;k++,C++)E[C]=I[k];return E},r=Da(),i=Zr(),a=qa(),o=ja,s=yd(),u=J,c=u.getSdpFormat,l=u.updatePlanBTrackIdsToSSRCs,f=u.updateUnifiedPlanTrackIdsToSSRCs,p=V,v=p.delegateMethods,_=p.interceptEvent,m=p.isIOSChrome,y=p.legacyPromise,P=p.proxyProperties,h=c()==="unified",b=function(E){n(I,E);function I(k,x){k===void 0&&(k={});var C=E.call(this)||this,$=Object.assign(k.iceTransportPolicy?{iceTransports:k.iceTransportPolicy}:{},k);_(C,"datachannel"),_(C,"signalingstatechange");var N=c($.sdpSemantics),W=new RTCPeerConnection($,x);return Object.defineProperties(C,{_appliedTracksToSSRCs:{value:new Map,writable:!0},_localStream:{value:new o},_peerConnection:{value:W},_pendingLocalOffer:{value:null,writable:!0},_pendingRemoteOffer:{value:null,writable:!0},_rolledBackTracksToSSRCs:{value:new Map,writable:!0},_sdpFormat:{value:N},_senders:{value:new Map},_signalingStateLatch:{value:new a},_tracksToSSRCs:{value:new Map,writable:!0}}),W.addEventListener("datachannel",function(G){F(G.channel),C.dispatchEvent(G)}),W.addEventListener("signalingstatechange",function(){for(var G=[],H=0;H<arguments.length;H++)G[H]=arguments[H];W.signalingState==="stable"&&(C._appliedTracksToSSRCs=new Map(C._tracksToSSRCs)),!C._pendingLocalOffer&&!C._pendingRemoteOffer&&C.dispatchEvent.apply(C,e([],t(G)))}),W.ontrack=function(){},typeof W.addTrack!="function"&&W.addStream(C._localStream),P(RTCPeerConnection.prototype,C,W),C}return Object.defineProperty(I.prototype,"localDescription",{get:function(){return this._pendingLocalOffer?this._pendingLocalOffer:this._peerConnection.localDescription},enumerable:!1,configurable:!0}),Object.defineProperty(I.prototype,"remoteDescription",{get:function(){return this._pendingRemoteOffer?this._pendingRemoteOffer:this._peerConnection.remoteDescription},enumerable:!1,configurable:!0}),Object.defineProperty(I.prototype,"signalingState",{get:function(){return this._pendingLocalOffer?"have-local-offer":this._pendingRemoteOffer?"have-remote-offer":this._peerConnection.signalingState},enumerable:!1,configurable:!0}),I.prototype.addTrack=function(k){for(var x,C=[],$=1;$<arguments.length;$++)C[$-1]=arguments[$];if(typeof this._peerConnection.addTrack=="function")return(x=this._peerConnection).addTrack.apply(x,e([k],t(C)));if(this._peerConnection.signalingState==="closed")throw new Error("Cannot add MediaStreamTrack ["+k.id+`, 
        `+k.kind+"]: RTCPeerConnection is closed");var N=this._senders.get(k);if(N&&N.track)throw new Error("Cannot add MediaStreamTrack ['"+k.id+`, 
        `+k.kind+"]: RTCPeerConnection already has it");return this._peerConnection.removeStream(this._localStream),this._localStream.addTrack(k),this._peerConnection.addStream(this._localStream),N=new s(k),this._senders.set(k,N),N},I.prototype.removeTrack=function(k){if(this._peerConnection.signalingState==="closed")throw new Error("Cannot remove MediaStreamTrack: RTCPeerConnection is closed");if(typeof this._peerConnection.addTrack=="function")try{return this._peerConnection.removeTrack(k)}catch{}else{var x=k.track;if(!x)return;k=this._senders.get(x),k&&k.track&&(k.track=null,this._peerConnection.removeStream(this._localStream),this._localStream.removeTrack(x),this._peerConnection.addStream(this._localStream))}},I.prototype.getSenders=function(){return typeof this._peerConnection.addTrack=="function"?this._peerConnection.getSenders():Array.from(this._senders.values())},I.prototype.addIceCandidate=function(k){for(var x=this,C=[],$=1;$<arguments.length;$++)C[$-1]=arguments[$];var N;return this.signalingState==="have-remote-offer"?N=this._signalingStateLatch.when("low").then(function(){return x._peerConnection.addIceCandidate(k)}):N=this._peerConnection.addIceCandidate(k),C.length>0?y.apply(void 0,e([N],t(C))):N},I.prototype.close=function(){this.signalingState!=="closed"&&(this._pendingLocalOffer=null,this._pendingRemoteOffer=null,this._peerConnection.close())},I.prototype.createAnswer=function(){for(var k=this,x=[],C=0;C<arguments.length;C++)x[C]=arguments[C];var $;return this._pendingRemoteOffer?$=this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then(function(){return k._signalingStateLatch.lower(),k._peerConnection.createAnswer()}).then(function(N){return k._pendingRemoteOffer=null,k._rolledBackTracksToSSRCs.clear(),new r({type:"answer",sdp:q(k._sdpFormat,k._tracksToSSRCs,N.sdp)})},function(N){throw k._pendingRemoteOffer=null,N}):$=this._peerConnection.createAnswer().then(function(N){return k._rolledBackTracksToSSRCs.clear(),new r({type:"answer",sdp:q(k._sdpFormat,k._tracksToSSRCs,N.sdp)})}),x.length>1?y.apply(void 0,e([$],t(x))):$},I.prototype.createOffer=function(){for(var k=this,x=[],C=0;C<arguments.length;C++)x[C]=arguments[C];var $=t(x,3),N=$[0],W=$[1],G=$[2],H=G||N||{};if(m()){if(H.offerToReceiveVideo&&!this._audioTransceiver&&!(h&&S(this,"audio"))){delete H.offerToReceiveAudio;try{this._audioTransceiver=h?this.addTransceiver("audio",{direction:"recvonly"}):this.addTransceiver("audio")}catch(Q){return Promise.reject(Q)}}if(H.offerToReceiveVideo&&!this._videoTransceiver&&!(h&&S(this,"video"))){delete H.offerToReceiveVideo;try{this._videoTransceiver=h?this.addTransceiver("video",{direction:"recvonly"}):this.addTransceiver("video")}catch(Q){return Promise.reject(Q)}}}var ae=this._peerConnection.createOffer(H).then(function(Q){return k._rolledBackTracksToSSRCs.clear(),new r({type:Q.type,sdp:q(k._sdpFormat,k._tracksToSSRCs,Q.sdp)})});return x.length>1?y(ae,N,W):ae},I.prototype.createDataChannel=function(k,x){x=D(x);var C=this._peerConnection.createDataChannel(k,x);return F(C),C},I.prototype.setLocalDescription=function(){for(var k=[],x=0;x<arguments.length;x++)k[x]=arguments[x];var C=t(k,3),$=C[0],N=C[1],W=C[2];this._rolledBackTracksToSSRCs.size>0&&(this._tracksToSSRCs=new Map(this._rolledBackTracksToSSRCs),this._rolledBackTracksToSSRCs.clear());var G=g(this,!0,$);return k.length>1?y(G,N,W):G},I.prototype.setRemoteDescription=function(){for(var k=[],x=0;x<arguments.length;x++)k[x]=arguments[x];var C=t(k,3),$=C[0],N=C[1],W=C[2];this._rolledBackTracksToSSRCs.clear();var G=g(this,!1,$);return k.length>1?y(G,N,W):G},I}(i);v(RTCPeerConnection.prototype,b.prototype,"_peerConnection");function g(E,I,k){function x(Q){I?E._pendingLocalOffer=Q:E._pendingRemoteOffer=Q}function C(){I?E._pendingLocalOffer=null:E._pendingRemoteOffer=null}var $=I?E._pendingLocalOffer:E._pendingRemoteOffer,N=I?E._pendingRemoteOffer:E._pendingLocalOffer,W=I?"have-local-offer":"have-remote-offer",G=I?"setLocalDescription":"setRemoteDescription",H;if(!I&&N&&k.type==="answer")H=T(E,k);else if(k.type==="offer"){if(E.signalingState!==W&&E.signalingState!=="stable")return Promise.reject(new Error("Cannot set "+(I?"local":"remote")+" offer in state "+E.signalingState));!$&&E._signalingStateLatch.state==="low"&&E._signalingStateLatch.raise();var ae=E.signalingState;x(w(k)),H=Promise.resolve(),E.signalingState!==ae&&H.then(function(){return E.dispatchEvent(new Event("signalingstatechange"))})}else k.type==="rollback"&&(E.signalingState!==W?H=Promise.reject(new Error("Cannot rollback "+(I?"local":"remote")+" description in "+E.signalingState)):(C(),E._rolledBackTracksToSSRCs=new Map(E._tracksToSSRCs),E._tracksToSSRCs=new Map(E._appliedTracksToSSRCs),H=Promise.resolve(),H.then(function(){return E.dispatchEvent(new Event("signalingstatechange"))})));return H||E._peerConnection[G](w(k))}function T(E,I){var k=E._pendingLocalOffer;return E._peerConnection.setLocalDescription(k).then(function(){return E._pendingLocalOffer=null,E.setRemoteDescription(I)}).then(function(){E._signalingStateLatch.lower()})}function S(E,I){return!!E.getTransceivers().find(function(k){var x=k.receiver,C=x===void 0?{}:x,$=C.track,N=$===void 0?{}:$;return N.kind===I})}function w(E){return E instanceof r&&E._description?E._description:new RTCSessionDescription(E)}function O(){return"maxRetransmitTime"in RTCDataChannel.prototype&&!("maxPacketLifeTime"in RTCDataChannel.prototype)}function D(E){return E=Object.assign({},E),O()&&"maxPacketLifeTime"in E&&(E.maxRetransmitTime=E.maxPacketLifeTime),E}function F(E){return Object.defineProperty(E,"maxRetransmits",{value:E.maxRetransmits===65535?null:E.maxRetransmits}),O()&&Object.defineProperty(E,"maxPacketLifeTime",{value:E.maxRetransmitTime===65535?null:E.maxRetransmitTime}),E}function q(E,I,k){return E==="unified"?f(I,k):l(I,k)}return tr=b,tr}var rr,Wn;function Qa(){return Wn||(Wn=1,rr=RTCSessionDescription),rr}var nr,Hn;function bd(){if(Hn)return nr;Hn=1;var n=d&&d.__extends||function(){var m=function(y,P){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,b){h.__proto__=b}||function(h,b){for(var g in b)Object.prototype.hasOwnProperty.call(b,g)&&(h[g]=b[g])},m(y,P)};return function(y,P){if(typeof P!="function"&&P!==null)throw new TypeError("Class extends value "+String(P)+" is not a constructor or null");m(y,P);function h(){this.constructor=y}y.prototype=P===null?Object.create(P):(h.prototype=P.prototype,new h)}}(),t=d&&d.__read||function(m,y){var P=typeof Symbol=="function"&&m[Symbol.iterator];if(!P)return m;var h=P.call(m),b,g=[],T;try{for(;(y===void 0||y-- >0)&&!(b=h.next()).done;)g.push(b.value)}catch(S){T={error:S}}finally{try{b&&!b.done&&(P=h.return)&&P.call(h)}finally{if(T)throw T.error}}return g},e=d&&d.__spreadArray||function(m,y){for(var P=0,h=y.length,b=m.length;P<h;P++,b++)m[b]=y[P];return m},r=Zr(),i=Qa(),a=J.updateUnifiedPlanTrackIdsToSSRCs,o=V,s=o.delegateMethods,u=o.interceptEvent,c=o.legacyPromise,l=o.proxyProperties,f=function(m){n(y,m);function y(P){var h=m.call(this)||this;u(h,"signalingstatechange");var b=new RTCPeerConnection(P);Object.defineProperties(h,{_initiallyNegotiatedDtlsRole:{value:null,writable:!0},_isClosed:{value:!1,writable:!0},_peerConnection:{value:b},_rollingBack:{value:!1,writable:!0},_tracksToSSRCs:{value:new Map},peerIdentity:{enumerable:!0,value:Promise.resolve({idp:"",name:""})}});var g;return b.addEventListener("signalingstatechange",function(){for(var T=[],S=0;S<arguments.length;S++)T[S]=arguments[S];!h._rollingBack&&h.signalingState!==g&&(g=h.signalingState,h._isClosed?setTimeout(function(){return h.dispatchEvent.apply(h,e([],t(T)))}):h.dispatchEvent.apply(h,e([],t(T))))}),l(RTCPeerConnection.prototype,h,b),h}return Object.defineProperty(y.prototype,"iceGatheringState",{get:function(){return this._isClosed?"complete":this._peerConnection.iceGatheringState},enumerable:!1,configurable:!0}),Object.defineProperty(y.prototype,"localDescription",{get:function(){return _(this._peerConnection.localDescription,this._initiallyNegotiatedDtlsRole)},enumerable:!1,configurable:!0}),Object.defineProperty(y.prototype,"signalingState",{get:function(){return this._isClosed?"closed":this._peerConnection.signalingState},enumerable:!1,configurable:!0}),y.prototype.createAnswer=function(){for(var P=this,h=[],b=0;b<arguments.length;b++)h[b]=arguments[b];var g;return g=this._peerConnection.createAnswer().then(function(T){return v(P,T),_(T,P._initiallyNegotiatedDtlsRole)}),typeof h[0]=="function"?c.apply(void 0,e([g],t(h))):g},y.prototype.createOffer=function(){for(var P=this,h=[],b=0;b<arguments.length;b++)h[b]=arguments[b];var g=t(h,3),T=g[0],S=g[1],w=g[2],O=w||T||{},D;if(this.signalingState==="have-local-offer"||this.signalingState==="have-remote-offer"){var F=this.signalingState==="have-local-offer";D=p(this,F,function(){return P.createOffer(O)})}else D=this._peerConnection.createOffer(O);return D=D.then(function(q){return new i({type:q.type,sdp:a(P._tracksToSSRCs,q.sdp)})}),h.length>1?c(D,T,S):D},y.prototype.setLocalDescription=function(){for(var P,h=[],b=0;b<arguments.length;b++)h[b]=arguments[b];var g=t(h),T=g[0],S=g.slice(1),w;return T&&T.type==="answer"&&this.signalingState==="have-local-offer"&&(w=Promise.reject(new Error("Cannot set local answer in state have-local-offer"))),w?h.length>1?c.apply(void 0,e([w],t(S))):w:(P=this._peerConnection).setLocalDescription.apply(P,e([],t(h)))},y.prototype.setRemoteDescription=function(){for(var P=this,h=[],b=0;b<arguments.length;b++)h[b]=arguments[b];var g=t(h),T=g[0],S=g.slice(1),w;return T&&this.signalingState==="have-remote-offer"&&(T.type==="answer"?w=Promise.reject(new Error("Cannot set remote answer in state have-remote-offer")):T.type==="offer"&&(w=p(this,!1,function(){return P._peerConnection.setRemoteDescription(T)}))),w||(w=this._peerConnection.setRemoteDescription(T)),w=w.then(function(){return v(P,T,!0)}),h.length>1?c.apply(void 0,e([w],t(S))):w},y.prototype.close=function(){this.signalingState!=="closed"&&(this._isClosed=!0,this._peerConnection.close())},y}(r);s(RTCPeerConnection.prototype,f.prototype,"_peerConnection");function p(m,y,P){var h=y?"setLocalDescription":"setRemoteDescription";return m._rollingBack=!0,m._peerConnection[h](new i({type:"rollback"})).then(P).then(function(b){return m._rollingBack=!1,b},function(b){throw m._rollingBack=!1,b})}function v(m,y,P){if(!(m._initiallyNegotiatedDtlsRole||y.type==="offer")){var h=y.sdp.match(/a=setup:([a-z]+)/);if(h){var b=h[1];m._initiallyNegotiatedDtlsRole=P?{active:"passive",passive:"active"}[b]:b}}}function _(m,y){return m&&m.type==="answer"&&y?new i({type:m.type,sdp:m.sdp.replace(/a=setup:[a-z]+/g,"a=setup:"+y)}):m}return nr=f,nr}var ir,qn;function Sd(){if(qn)return ir;qn=1;var n=d&&d.__extends||function(){var g=function(T,S){return g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(w,O){w.__proto__=O}||function(w,O){for(var D in O)Object.prototype.hasOwnProperty.call(O,D)&&(w[D]=O[D])},g(T,S)};return function(T,S){if(typeof S!="function"&&S!==null)throw new TypeError("Class extends value "+String(S)+" is not a constructor or null");g(T,S);function w(){this.constructor=T}T.prototype=S===null?Object.create(S):(w.prototype=S.prototype,new w)}}(),t=d&&d.__read||function(g,T){var S=typeof Symbol=="function"&&g[Symbol.iterator];if(!S)return g;var w=S.call(g),O,D=[],F;try{for(;(T===void 0||T-- >0)&&!(O=w.next()).done;)D.push(O.value)}catch(q){F={error:q}}finally{try{O&&!O.done&&(S=w.return)&&S.call(w)}finally{if(F)throw F.error}}return D},e=d&&d.__spreadArray||function(g,T){for(var S=0,w=T.length,O=g.length;S<w;S++,O++)g[O]=T[S];return g},r=Zr(),i=qa(),a=J,o=a.getSdpFormat,s=a.updatePlanBTrackIdsToSSRCs,u=a.updateUnifiedPlanTrackIdsToSSRCs,c=V,l=c.delegateMethods,f=c.interceptEvent,p=c.proxyProperties,v=o()==="unified",_=v?u:s,m=function(g){n(T,g);function T(S){var w=g.call(this)||this;f(w,"datachannel"),f(w,"iceconnectionstatechange"),f(w,"signalingstatechange"),f(w,"track");var O=new RTCPeerConnection(S);return Object.defineProperties(w,{_appliedTracksToSSRCs:{value:new Map,writable:!0},_audioTransceiver:{value:null,writable:!0},_isClosed:{value:!1,writable:!0},_peerConnection:{value:O},_pendingLocalOffer:{value:null,writable:!0},_pendingRemoteOffer:{value:null,writable:!0},_rolledBackTracksToSSRCs:{value:new Map,writable:!0},_signalingStateLatch:{value:new i},_tracksToSSRCs:{value:new Map,writable:!0},_videoTransceiver:{value:null,writable:!0}}),O.addEventListener("datachannel",function(D){b(D.channel),w.dispatchEvent(D)}),O.addEventListener("iceconnectionstatechange",function(){for(var D=[],F=0;F<arguments.length;F++)D[F]=arguments[F];w._isClosed||w.dispatchEvent.apply(w,e([],t(D)))}),O.addEventListener("signalingstatechange",function(){for(var D=[],F=0;F<arguments.length;F++)D[F]=arguments[F];w._isClosed||(O.signalingState==="stable"&&(w._appliedTracksToSSRCs=new Map(w._tracksToSSRCs)),!w._pendingLocalOffer&&!w._pendingRemoteOffer&&w.dispatchEvent.apply(w,e([],t(D))))}),O.addEventListener("track",function(D){w._pendingRemoteOffer=null,w.dispatchEvent(D)}),p(RTCPeerConnection.prototype,w,O),w}return Object.defineProperty(T.prototype,"localDescription",{get:function(){return this._pendingLocalOffer||this._peerConnection.localDescription},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"iceConnectionState",{get:function(){return this._isClosed?"closed":this._peerConnection.iceConnectionState},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"iceGatheringState",{get:function(){return this._isClosed?"complete":this._peerConnection.iceGatheringState},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"remoteDescription",{get:function(){return this._pendingRemoteOffer||this._peerConnection.remoteDescription},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"signalingState",{get:function(){return this._isClosed?"closed":this._pendingLocalOffer?"have-local-offer":this._pendingRemoteOffer?"have-remote-offer":this._peerConnection.signalingState},enumerable:!1,configurable:!0}),T.prototype.addIceCandidate=function(S){var w=this;return this.signalingState==="have-remote-offer"?this._signalingStateLatch.when("low").then(function(){return w._peerConnection.addIceCandidate(S)}):this._peerConnection.addIceCandidate(S)},T.prototype.createOffer=function(S){var w=this;if(S=Object.assign({},S),S.offerToReceiveVideo&&!this._audioTransceiver&&!(v&&h(this,"audio"))){delete S.offerToReceiveAudio;try{this._audioTransceiver=v?this.addTransceiver("audio",{direction:"recvonly"}):this.addTransceiver("audio")}catch(O){return Promise.reject(O)}}if(S.offerToReceiveVideo&&!this._videoTransceiver&&!(v&&h(this,"video"))){delete S.offerToReceiveVideo;try{this._videoTransceiver=v?this.addTransceiver("video",{direction:"recvonly"}):this.addTransceiver("video")}catch(O){return Promise.reject(O)}}return this._peerConnection.createOffer(S).then(function(O){return w._rolledBackTracksToSSRCs.clear(),new RTCSessionDescription({type:O.type,sdp:_(w._tracksToSSRCs,O.sdp)})})},T.prototype.createAnswer=function(S){var w=this;return this._pendingRemoteOffer?this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then(function(){return w._signalingStateLatch.lower(),w._peerConnection.createAnswer()}).then(function(O){return w._pendingRemoteOffer=null,w._rolledBackTracksToSSRCs.clear(),v?new RTCSessionDescription({type:O.type,sdp:_(w._tracksToSSRCs,O.sdp)}):O},function(O){throw w._pendingRemoteOffer=null,O}):this._peerConnection.createAnswer(S).then(function(O){return w._rolledBackTracksToSSRCs.clear(),v?new RTCSessionDescription({type:O.type,sdp:_(w._tracksToSSRCs,O.sdp)}):O})},T.prototype.createDataChannel=function(S,w){var O=this._peerConnection.createDataChannel(S,w);return b(O),O},T.prototype.removeTrack=function(S){S.replaceTrack(null),this._peerConnection.removeTrack(S)},T.prototype.setLocalDescription=function(S){return this._rolledBackTracksToSSRCs.size>0&&(this._tracksToSSRCs=new Map(this._rolledBackTracksToSSRCs),this._rolledBackTracksToSSRCs.clear()),y(this,!0,S)},T.prototype.setRemoteDescription=function(S){return this._rolledBackTracksToSSRCs.clear(),y(this,!1,S)},T.prototype.close=function(){var S=this;this._isClosed||(this._isClosed=!0,this._peerConnection.close(),setTimeout(function(){S.dispatchEvent(new Event("iceconnectionstatechange")),S.dispatchEvent(new Event("signalingstatechange"))}))},T}(r);l(RTCPeerConnection.prototype,m.prototype,"_peerConnection");function y(g,T,S){function w(k){T?g._pendingLocalOffer=k:g._pendingRemoteOffer=k}function O(){T?g._pendingLocalOffer=null:g._pendingRemoteOffer=null}var D=T?g._pendingLocalOffer:g._pendingRemoteOffer,F=T?g._pendingRemoteOffer:g._pendingLocalOffer,q=T?"have-local-offer":"have-remote-offer",E=T?"setLocalDescription":"setRemoteDescription";if(!T&&F&&S.type==="answer")return P(g,S);if(S.type==="offer"){if(g.signalingState!==q&&g.signalingState!=="stable")return Promise.reject(new Error("Cannot set "+(T?"local":"remote")+`
        offer in state `+g.signalingState));!D&&g._signalingStateLatch.state==="low"&&g._signalingStateLatch.raise();var I=g.signalingState;return w(S),g.signalingState!==I?Promise.resolve().then(function(){return g.dispatchEvent(new Event("signalingstatechange"))}):Promise.resolve()}else if(S.type==="rollback")return g.signalingState!==q?Promise.reject(new Error(`Cannot rollback 
        `+(T?"local":"remote")+" description in "+g.signalingState)):(O(),g._rolledBackTracksToSSRCs=new Map(g._tracksToSSRCs),g._tracksToSSRCs=new Map(g._appliedTracksToSSRCs),Promise.resolve().then(function(){return g.dispatchEvent(new Event("signalingstatechange"))}));return g._peerConnection[E](S)}function P(g,T){var S=g._pendingLocalOffer;return g._peerConnection.setLocalDescription(S).then(function(){return g._pendingLocalOffer=null,g.setRemoteDescription(T)}).then(function(){return g._signalingStateLatch.lower()})}function h(g,T){return!!g.getTransceivers().find(function(S){var w=S.receiver,O=w===void 0?{}:w,D=O.track,F=D===void 0?{}:D;return F.kind===T})}function b(g){return Object.defineProperties(g,{maxPacketLifeTime:{value:g.maxPacketLifeTime===65535?null:g.maxPacketLifeTime},maxRetransmits:{value:g.maxRetransmits===65535?null:g.maxRetransmits}})}return ir=m,ir}if(typeof RTCPeerConnection=="function"){var wd=V.guessBrowser;switch(wd()){case"chrome":Ce.exports=gd();break;case"firefox":Ce.exports=bd();break;case"safari":Ce.exports=Sd();break;default:Ce.exports=RTCPeerConnection;break}}else Ce.exports=function(){throw new Error("RTCPeerConnection is not supported")};var kd=Ce.exports,qe={exports:{}};if(typeof RTCSessionDescription=="function"){var Td=V.guessBrowser;switch(Td()){case"chrome":qe.exports=Da();break;case"firefox":qe.exports=Qa();break;default:qe.exports=RTCSessionDescription;break}}else qe.exports=function(){throw new Error("RTCSessionDescription is not supported")};var Pd=qe.exports,Ga={};Object.defineProperties(Ga,{getStats:{enumerable:!0,value:sd},getUserMedia:{enumerable:!0,value:ud},MediaStream:{enumerable:!0,value:ja},MediaStreamTrack:{enumerable:!0,value:ld},RTCIceCandidate:{enumerable:!0,value:dd},RTCPeerConnection:{enumerable:!0,value:kd},RTCSessionDescription:{enumerable:!0,value:Pd}});var be=Ga,en=function(t,e){if(t&&e)if(t.super_=e,typeof Object.create=="function")t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}});else{var r=function(){function i(){}return i}();r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}},Ed=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a};function Od(){return/Android/.test(navigator.userAgent)}function Ka(){return!!(navigator&&navigator.maxTouchPoints&&navigator.maxTouchPoints>2)}function za(){return Ka()&&window.screen.width>=744&&(/Macintosh/i.test(navigator.userAgent)||/iPad/.test(navigator.userAgent)||/iPad/.test(navigator.platform))}function Ya(){return Ka()&&window.screen.width<=476&&(/Macintosh/i.test(navigator.userAgent)||/iPhone/.test(navigator.userAgent)||/iPhone/.test(navigator.platform))}function Cd(){return za()||Ya()}function Rd(){return/Mobi/.test(navigator.userAgent)}function Ld(n){return n==="chrome"&&/Edge/.test(navigator.userAgent)&&(typeof chrome>"u"||typeof chrome.runtime>"u")}function xd(n){if(n!=="chrome")return null;if("brave"in navigator)return"brave";var t=Id(navigator.userAgent),e=t.reduce(function(o,s){return o.replace(s,"")},navigator.userAgent),r=e.match(/[^\s]+/g)||[],i=Ed(r.map(function(o){return o.split("/")[0].toLowerCase()})),a=i.slice(2);return a.find(function(o){return!["chrome","mobile","safari"].includes(o)})||null}function Ad(n){return n!=="safari"?null:"brave"in navigator?"brave":["edge","edg"].find(function(t){return navigator.userAgent.toLowerCase().includes(t)})||null}function Id(n){for(var t=[],e=[],r=0;r<n.length;r++)if(n[r]==="(")t.push(r);else if(n[r]===")"&&t.length>0){var i=t.pop();t.length===0&&e.push(n.substring(i,r+1))}return e}var Se={isAndroid:Od,isIOS:Cd,isIpad:za,isIphone:Ya,isMobile:Rd,isNonChromiumEdge:Ld,mobileWebKitBrowser:Ad,rebrandedChromeBrowser:xd};function Md(n){return n=typeof n=="number"?n:0,new Promise(function(t){return setTimeout(t,n)})}function jd(n,t,e){e=typeof e=="number"?e:250;var r=n.createMediaStreamSource(t),i=n.createAnalyser();i.fftSize=2048,r.connect(i);var a=new Uint8Array(i.fftSize),o=!1;setTimeout(function(){o=!0},e);function s(){return o?Promise.resolve(!0):(i.getByteTimeDomainData(a),a.some(function(u){return u!==128&&u!==0})?Promise.resolve(!1):Md().then(s))}return s().then(function(u){return r.disconnect(),u},function(u){throw r.disconnect(),u})}var Ja=jd,ar,Qn;function jt(){if(Qn)return ar;Qn=1;var n=typeof AudioContext<"u"?AudioContext:typeof webkitAudioContext<"u"?webkitAudioContext:null,t=function(){function e(r){r=Object.assign({AudioContext:n},r),Object.defineProperties(this,{_AudioContext:{value:r.AudioContext},_audioContext:{value:null,writable:!0},_holders:{value:new Set},AudioContextFactory:{enumerable:!0,value:e}})}return e.prototype.getOrCreate=function(r){if(!this._holders.has(r)&&(this._holders.add(r),this._AudioContext&&!this._audioContext))try{this._audioContext=new this._AudioContext}catch{}return this._audioContext},e.prototype.release=function(r){this._holders.has(r)&&(this._holders.delete(r),!this._holders.size&&this._audioContext&&(this._audioContext.close(),this._audioContext=null))},e}();return ar=new t,ar}var Dd=Ja,$d=3,Nd=250;function Fd(n){var t=jt(),e={},r=t.getOrCreate(e),i=$d;function a(){return i--,Dd(r,n.srcObject,Nd).then(function(o){return o?i>0?a():!0:!1}).catch(function(){return!0})}return a().finally(function(){t.release(e)})}var Xa=Fd,st=R.defer,Vd=function(){function n(){Object.defineProperties(this,{_audio:{value:st(),writable:!0},_video:{value:st(),writable:!0}}),this._audio.resolve(),this._video.resolve()}return n.prototype.resolveDeferred=function(t){t==="audio"?this._audio.resolve():this._video.resolve()},n.prototype.startDeferred=function(t){t==="audio"?this._audio=st():this._video=st()},n.prototype.whenResolved=function(t){return t==="audio"?this._audio.promise:this._video.promise},n}(),Za=new Vd,Bd=K.EventEmitter,Ud=R.hidePrivateAndCertainPublicPropertiesInClass,et=Ud(Bd,["domain"]),Wd=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Hd=et,eo=R,qd=eo.buildLogLevels,Qd=eo.valueToJSON,Gd=U.DEFAULT_LOG_LEVEL,Kd=ce,zd=0,Yd=function(n){Wd(t,n);function t(e,r,i){var a=this;i=Object.assign({name:e,log:null,logLevel:Gd},i),a=n.call(this)||this;var o=String(i.name),s=qd(i.logLevel),u=i.log?i.log.createLog("media",a):new Kd("media",a,s,i.loggerName);return Object.defineProperties(a,{_instanceId:{value:++zd},_log:{value:u},kind:{enumerable:!0,value:r},name:{enumerable:!0,value:o}}),a}return t.prototype.toJSON=function(){return Qd(this)},t}(Hd),tn=Yd,Jd=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Xd=Se.isIOS,Zd=be.MediaStream,to=R,ef=to.waitForEvent,tf=to.waitForSometime,rf=Za,nf=tn,af=function(n){Jd(t,n);function t(e,r){var i=this;r=Object.assign({playPausedElementsIfNotBackgrounded:Xd()&&typeof document=="object"&&typeof document.addEventListener=="function"&&typeof document.visibilityState=="string"},r),i=n.call(this,e.id,e.kind,r)||this;var a=!1;return r=Object.assign({MediaStream:Zd},r),Object.defineProperties(i,{_attachments:{value:new Set},_dummyEl:{value:null,writable:!0},_elShims:{value:new WeakMap},_isStarted:{get:function(){return a},set:function(o){a=o}},_playPausedElementsIfNotBackgrounded:{value:r.playPausedElementsIfNotBackgrounded},_shouldShimAttachedElements:{value:r.workaroundWebKitBug212780||r.playPausedElementsIfNotBackgrounded},_unprocessedTrack:{value:null,writable:!0},_MediaStream:{value:r.MediaStream},isStarted:{enumerable:!0,get:function(){return a}},mediaStreamTrack:{enumerable:!0,get:function(){return this._unprocessedTrack||e.track}},processedTrack:{enumerable:!0,value:null,writable:!0}}),i._initialize(),i}return t.prototype._start=function(){this._log.debug("Started"),this._isStarted=!0,this._dummyEl&&(this._dummyEl.oncanplay=null),this.emit("started",this)},t.prototype._initialize=function(){var e=this;this._log.debug("Initializing"),this._dummyEl=this._createElement(),this.mediaStreamTrack.addEventListener("ended",function r(){e._end(),e.mediaStreamTrack.removeEventListener("ended",r)}),this._dummyEl&&(this._dummyEl.muted=!0,this._dummyEl.oncanplay=this._start.bind(this,this._dummyEl),this._attach(this._dummyEl,this.mediaStreamTrack),this._attachments.delete(this._dummyEl))},t.prototype._end=function(){this._log.debug("Ended"),this._dummyEl&&(this._dummyEl.remove(),this._dummyEl.srcObject=null,this._dummyEl.oncanplay=null,this._dummyEl=null)},t.prototype.attach=function(e){var r=this;if(typeof e=="string"?e=this._selectElement(e):e||(e=this._createElement()),this._log.debug("Attempting to attach to element:",e),e=this._attach(e),this._shouldShimAttachedElements&&!this._elShims.has(e)){var i=this._playPausedElementsIfNotBackgrounded?function(){return of(e,r._log)}:null;this._elShims.set(e,sf(e,i))}return e},t.prototype._attach=function(e,r){r===void 0&&(r=this.processedTrack||this.mediaStreamTrack);var i=e.srcObject;i instanceof this._MediaStream||(i=new this._MediaStream);var a=r.kind==="audio"?"getAudioTracks":"getVideoTracks";return i[a]().forEach(function(o){i.removeTrack(o)}),i.addTrack(r),e.srcObject=i,e.autoplay=!0,e.playsInline=!0,this._attachments.has(e)||this._attachments.add(e),e},t.prototype._selectElement=function(e){var r=document.querySelector(e);if(!r)throw new Error("Selector matched no element: "+e);return r},t.prototype._updateElementsMediaStreamTrack=function(){var e=this;this._log.debug("Reattaching all elements to update mediaStreamTrack"),this._getAllAttachedElements().forEach(function(r){return e._attach(r)})},t.prototype._createElement=function(){return typeof document<"u"?document.createElement(this.kind):null},t.prototype.detach=function(e){var r;return typeof e=="string"?r=[this._selectElement(e)]:e?r=[e]:r=this._getAllAttachedElements(),this._log.debug("Attempting to detach from elements:",r),this._detachElements(r),e?r[0]:r},t.prototype._detachElements=function(e){return e.map(this._detachElement.bind(this))},t.prototype._detachElement=function(e){if(!this._attachments.has(e))return e;var r=e.srcObject;if(r instanceof this._MediaStream&&r.removeTrack(this.processedTrack||this.mediaStreamTrack),this._attachments.delete(e),this._shouldShimAttachedElements&&this._elShims.has(e)){var i=this._elShims.get(e);i.unShim(),this._elShims.delete(e)}return e},t.prototype._getAllAttachedElements=function(){var e=[];return this._attachments.forEach(function(r){e.push(r)}),e},t}(nf);function of(n,t){var e=n.tagName.toLowerCase();t.warn("Unintentionally paused:",n),Promise.race([ef(document,"visibilitychange"),tf(1e3)]).then(function(){document.visibilityState==="visible"&&rf.whenResolved("audio").then(function(){return t.info("Playing unintentionally paused <"+e+"> element"),t.debug("Element:",n),n.play()}).then(function(){t.info("Successfully played unintentionally paused <"+e+"> element"),t.debug("Element:",n)}).catch(function(r){t.warn("Error while playing unintentionally paused <"+e+"> element:",{error:r,el:n})})})}function sf(n,t){t===void 0&&(t=null);var e=n.pause,r=n.play,i=!1;n.pause=function(){return i=!0,e.call(n)},n.play=function(){return i=!1,r.call(n)};var a=t?function(){i||t()}:null;return a&&n.addEventListener("pause",a),{pausedIntentionally:function(){return i},unShim:function(){n.pause=e,n.play=r,a&&n.removeEventListener("pause",a)}}}var ro=af,cf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),uf=ro,lf=function(n){cf(t,n);function t(e,r){return n.call(this,e,r)||this}return t.prototype.attach=function(){return n.prototype.attach.apply(this,arguments)},t.prototype.detach=function(){return n.prototype.detach.apply(this,arguments)},t}(uf),no=lf,xr=null,df=3,Gn=50,Kn=250,zn=50;function ff(n){try{var t=xr.getContext("2d");t.drawImage(n,0,0,zn,Gn);var e=t.getImageData(0,0,zn,Gn),r=e.data.filter(function(a,o){return(o+1)%4}),i=Math.max.apply(Math,r);return i===0}catch(a){return console.log("Error checking silence: ",a),!1}}function pf(n){return xr=xr||document.createElement("canvas"),new Promise(function(t){var e=df;setTimeout(function r(){return e--,ff(n)?e>0?setTimeout(r,Kn):t(!0):t(!1)},Kn)})}var io=pf,vf=function(){function n(t){var e=this;t===void 0&&(t=1),Object.defineProperties(this,{_listeners:{value:[]},_onVisibilityChange:{value:function(){e._emitVisible(document.visibilityState==="visible")}}});for(var r=0;r<t;r++)this._listeners.push([])}return n.prototype.clear=function(){for(var t=this._listeners.length,e=0;e<t;e++)this._listeners[e]=[]},n.prototype._listenerCount=function(){return this._listeners.reduce(function(t,e){return t+e.length},0)},n.prototype._emitVisible=function(t){for(var e=this,r=Promise.resolve(),i=function(o){r=r.then(function(){return e._emitVisiblePhase(o,t)})},a=1;a<=this._listeners.length;a++)i(a);return r},n.prototype._emitVisiblePhase=function(t,e){var r=this._listeners[t-1];return Promise.all(r.map(function(i){var a=i(e);return a instanceof Promise?a:Promise.resolve(a)}))},n.prototype._start=function(){document.addEventListener("visibilitychange",this._onVisibilityChange)},n.prototype._stop=function(){document.removeEventListener("visibilitychange",this._onVisibilityChange)},n.prototype.onVisibilityChange=function(t,e){if(typeof t!="number"||t<=0||t>this._listeners.length)throw new Error("invalid phase: ",t);var r=this._listeners[t-1];return r.push(e),this._listenerCount()===1&&this._start(),this},n.prototype.offVisibilityChange=function(t,e){if(typeof t!="number"||t<=0||t>this._listeners.length)throw new Error("invalid phase: ",t);var r=this._listeners[t-1],i=r.indexOf(e);return i!==-1&&(r.splice(i,1),this._listenerCount()===0&&this._stop()),this},n}(),rn=new vf(2),hf=Ja;function _f(n,t,e,r,i){r=typeof r=="number"?r:3;var a=0,o=jt(),s={},u=o.getOrCreate(s);function c(){return t(e).then(function(l){var f=e.audio?hf(u,l,i).catch(function(p){return n.warn("Encountered an error while detecting silence",p),!0}):Promise.resolve(!1);return f.then(function(p){if(p){if(r<=0)return n.warn("Got a silent audio MediaStreamTrack. Normally we would try to get a new one, but we've run out of retries; returning it anyway."),l}else return n.info("Got a non-silent audio MediaStreamTrack; returning it."),l;return n.warn("Got a silent audio MediaStreamTrack. Stopping all MediaStreamTracks and calling getUserMedia again. This is retry #"+ ++a+"."),l.getTracks().forEach(function(v){return v.stop()}),r--,c()})})}return c().then(function(l){return o.release(s),l},function(l){throw o.release(s),l})}var ao=_f,mf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Yn=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Jn=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},yf=K.EventEmitter,gf=function(n){mf(t,n);function t(){var e=n.call(this)||this;return Object.defineProperties(e,{_queuedEvents:{value:new Map}}),e}return t.prototype.dequeue=function(e){var r=this,i=!0;if(!e)return this._queuedEvents.forEach(function(o,s){i=this.dequeue(s)&&i},this),i;var a=this._queuedEvents.get(e)||[];return this._queuedEvents.delete(e),a.reduce(function(o,s){return r.emit.apply(r,Jn([],Yn([e].concat(s))))&&o},i)},t.prototype.queue=function(){var e=[].slice.call(arguments);if(this.emit.apply(this,Jn([],Yn(e))))return!0;var r=e[0];return this._queuedEvents.has(r)||this._queuedEvents.set(r,[]),this._queuedEvents.get(r).push(e.slice(1)),!1},t}(yf),oo=gf,bf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Sf=oo,wf=function(n){bf(t,n);function t(e,r){var i=n.call(this)||this;return Object.defineProperties(i,{id:{enumerable:!0,value:e},kind:{enumerable:!0,value:r}}),i}return t.prototype.stop=function(){this.emit("stopped")},t}(Sf),so=wf,kf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Tf=so,Pf=function(n){kf(t,n);function t(e,r){var i=n.call(this,e,r.kind)||this;return Object.defineProperties(i,{_track:{value:r,writable:!0},enabled:{enumerable:!0,get:function(){return this._track.enabled}},readyState:{enumerable:!0,get:function(){return this._track.readyState}},track:{enumerable:!0,get:function(){return this._track}}}),i}return t.prototype.stop=function(){this.track.stop(),n.prototype.stop.call(this)},t}(Tf),co=Pf,Ef=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Of=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Cf=co,Rf=function(n){Ef(t,n);function t(e){var r=n.call(this,e.id,e)||this;return Object.defineProperties(r,{_clones:{value:new Set},_eventsToReemitters:{value:new Map([["mute",function(){return r.queue("muted")}],["unmute",function(){return r.queue("unmuted")}]])},_senders:{value:new Set},_senderToPublisherHintCallbacks:{value:new Map},isPublishing:{enumerable:!0,get:function(){return!!this._clones.size}},muted:{enumerable:!0,get:function(){return this._track.muted}}}),r._reemitMediaStreamTrackEvents(),r}return t.prototype._reemitMediaStreamTrackEvents=function(e){e===void 0&&(e=this._track);var r=this,i=r._eventsToReemitters,a=r._track;if(i.forEach(function(s,u){return e.addEventListener(u,s)}),a!==e&&(i.forEach(function(s,u){return a.removeEventListener(u,s)}),a.muted!==e.muted)){var o=i.get(e.muted?"mute":"unmute");o()}},t.prototype.clone=function(){var e=new t(this.track.clone());return this._clones.add(e),e},t.prototype.removeClone=function(e){this._clones.delete(e)},t.prototype.setMediaStreamTrack=function(e){var r=this,i=Array.from(this._clones),a=Array.from(this._senders);return Promise.all(i.map(function(o){return o.setMediaStreamTrack(e.clone())}).concat(a.map(function(o){return r._replaceTrack(o,e)}))).finally(function(){r._reemitMediaStreamTrackEvents(e),r._track=e})},t.prototype.addSender=function(e,r){return this._senders.add(e),r&&this._senderToPublisherHintCallbacks.set(e,r),this},t.prototype.removeSender=function(e){return this._senders.delete(e),this._senderToPublisherHintCallbacks.delete(e),this},t.prototype.setPublisherHint=function(e){var r=Of(Array.from(this._senderToPublisherHintCallbacks.values()),1),i=r[0];return i?i(e):Promise.resolve("COULD_NOT_APPLY_HINT")},t.prototype._replaceTrack=function(e,r){var i=this;return e.replaceTrack(r).then(function(a){return i.setPublisherHint(null).catch(function(){}),i.emit("replaced"),a})},t}(Cf),uo=Rf,Lf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),xf=be.getUserMedia,Af=Se.isIOS,Dt=R,Xn=Dt.capitalize,If=Dt.defer,Mf=Dt.waitForSometime,jf=Dt.waitForEvent,Df=U.typeErrors.ILLEGAL_INVOKE,$f=Xa,Nf=io,Zn=rn,ei=Za,Ff=ao,Vf=uo;function Bf(n){return function(t){Lf(e,t);function e(r,i){var a=this,o=Af()&&typeof document=="object"&&typeof document.addEventListener=="function"&&typeof document.visibilityState=="string";i=Object.assign({getUserMedia:xf,isCreatedByCreateLocalTracks:!1,workaroundWebKitBug1208516:o,gUMSilentTrackWorkaround:Ff},i);var s=new Vf(r),u=s.kind;return a=t.call(this,s,i)||this,Object.defineProperties(a,{_constraints:{value:typeof i[u]=="object"?i[u]:{},writable:!0},_getUserMedia:{value:i.getUserMedia},_gUMSilentTrackWorkaround:{value:i.gUMSilentTrackWorkaround},_eventsToReemitters:{value:new Map([["muted",function(){return a.emit("muted",a)}],["unmuted",function(){return a.emit("unmuted",a)}]])},_workaroundWebKitBug1208516:{value:i.workaroundWebKitBug1208516},_workaroundWebKitBug1208516Cleanup:{value:null,writable:!0},_didCallEnd:{value:!1,writable:!0},_isCreatedByCreateLocalTracks:{value:i.isCreatedByCreateLocalTracks},_noiseCancellation:{value:i.noiseCancellation||null},_trackSender:{value:s},id:{enumerable:!0,value:s.id},isEnabled:{enumerable:!0,get:function(){return s.enabled}},isMuted:{enumerable:!0,get:function(){return s.muted}},isStopped:{enumerable:!0,get:function(){return s.readyState==="ended"}}}),a._workaroundWebKitBug1208516&&(a._workaroundWebKitBug1208516Cleanup=ti(a)),a._reemitTrackSenderEvents(),a}return e.prototype._end=function(){var r=this;this._didCallEnd||(t.prototype._end.call(this),this._didCallEnd=!0,this._eventsToReemitters.forEach(function(i,a){return r._trackSender.removeListener(a,i)}),this.emit("stopped",this))},e.prototype._initialize=function(){this._didCallEnd&&(this._didCallEnd=!1),this._eventsToReemitters&&this._reemitTrackSenderEvents(),t.prototype._initialize.call(this)},e.prototype._reacquireTrack=function(r){var i,a=this,o=a._getUserMedia,s=a._gUMSilentTrackWorkaround,u=a._log,c=a.mediaStreamTrack.kind;u.info("Re-acquiring the MediaStreamTrack"),u.debug("Constraints:",r);var l=Object.assign({audio:!1,video:!1},(i={},i[c]=r,i)),f=this._workaroundWebKitBug1208516Cleanup?s(u,o,l):o(l);return f.then(function(p){return p.getTracks()[0]})},e.prototype._reemitTrackSenderEvents=function(){var r=this;this._eventsToReemitters.forEach(function(i,a){return r._trackSender.on(a,i)}),this._trackSender.dequeue("muted"),this._trackSender.dequeue("unmuted")},e.prototype._restart=function(r){var i=this,a=this._log;return r=r||this._constraints,this._stop(),this._reacquireTrack(r).catch(function(o){throw a.error("Failed to re-acquire the MediaStreamTrack:",{error:o,constraints:r}),o}).then(function(o){return a.info("Re-acquired the MediaStreamTrack"),a.debug("MediaStreamTrack:",o),i._constraints=Object.assign({},r),i._setMediaStreamTrack(o)})},e.prototype._setMediaStreamTrack=function(r){var i=this;return r.enabled=this.mediaStreamTrack.enabled,this._stop(),(this._unprocessedTrack?Promise.resolve().then(function(){i._unprocessedTrack=r}):this._trackSender.setMediaStreamTrack(r).catch(function(a){i._log.warn("setMediaStreamTrack failed:",{error:a,mediaStreamTrack:r})})).then(function(){i._initialize(),i._getAllAttachedElements().forEach(function(a){return i._attach(a)})})},e.prototype._stop=function(){return this.mediaStreamTrack.stop(),this._end(),this},e.prototype.enable=function(r){return r=typeof r=="boolean"?r:!0,r!==this.mediaStreamTrack.enabled&&(this._log.info((r?"En":"Dis")+"abling"),this.mediaStreamTrack.enabled=r,this.emit(r?"enabled":"disabled",this)),this},e.prototype.disable=function(){return this.enable(!1)},e.prototype.restart=function(r){var i=this,a=this.kind;if(!this._isCreatedByCreateLocalTracks)return Promise.reject(Df("restart","can only be called on a"+(" Local"+Xn(a)+"Track that is created using createLocalTracks")+(" or createLocal"+Xn(a)+"Track.")));this._workaroundWebKitBug1208516Cleanup&&(this._workaroundWebKitBug1208516Cleanup(),this._workaroundWebKitBug1208516Cleanup=null);var o=this._restart(r);return this._workaroundWebKitBug1208516&&(o=o.finally(function(){i._workaroundWebKitBug1208516Cleanup=ti(i)})),o},e.prototype.stop=function(){return this._log.info("Stopping"),this._workaroundWebKitBug1208516Cleanup&&(this._workaroundWebKitBug1208516Cleanup(),this._workaroundWebKitBug1208516Cleanup=null),this._stop()},e}(n)}function ti(n){var t=n._log,e=n.kind,r=n._noiseCancellation,i={audio:$f,video:Nf}[e],a=function(){return r?r.sourceTrack:n.mediaStreamTrack},o=n._dummyEl,s=a(),u=null;function c(){return o.play().then(function(){return i(o)}).then(function(y){return y?t.warn("Silence detected"):t.info("Non-silence detected"),y}).catch(function(y){t.warn("Failed to detect silence:",y)}).finally(function(){n.processedTrack||o.pause()})}function l(){var y=n._workaroundWebKitBug1208516Cleanup,P=n.isStopped,h=P&&!!y,b=a().muted;return Promise.resolve().then(function(){return document.visibilityState==="visible"&&!u&&(b||h||c())})}function f(){return Promise.race([jf(s,"unmute"),Mf(50)]).then(function(){return l()}).then(function(y){y&&!u&&(u=If(),n._restart().finally(function(){o=n._dummyEl,_(),s=a(),v(),u.resolve(),u=null}).catch(function(h){t.error("failed to restart track: ",h)}));var P=u&&u.promise||Promise.resolve();return P.finally(function(){return ei.resolveDeferred(e)})}).catch(function(y){t.error("error in maybeRestart: "+y.message)})}function p(){var y=n._log,P=n.kind;y.info("Muted"),y.debug("LocalMediaTrack:",n),ei.startDeferred(P)}function v(){s.addEventListener("ended",f),s.addEventListener("mute",p),s.addEventListener("unmute",f)}function _(){s.removeEventListener("ended",f),s.removeEventListener("mute",p),s.removeEventListener("unmute",f)}var m=function(y){return y?f():!1};return Zn.onVisibilityChange(1,m),v(),function(){Zn.offVisibilityChange(1,m),_()}}var lo=Bf,Uf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Wf=Se.isIOS,Hf=Xa,qf=V.isIOSChrome,Qf=no,Gf=lo,Kf=Gf(Qf),zf=function(n){Uf(t,n);function t(e,r){var i=this,a=(r==null?void 0:r.noiseCancellation)||null;i=n.call(this,e,r)||this;var o=i._log,s=e.label,u=s===void 0?"":s,c=e.getSettings(),l=c.deviceId,f=l===void 0?"":l,p=c.groupId,v=p===void 0?"":p;return Object.defineProperties(i,{_currentDefaultDeviceInfo:{value:{deviceId:f,groupId:v,label:u},writable:!0},_defaultDeviceCaptureMode:{value:!Wf()&&i._isCreatedByCreateLocalTracks&&typeof navigator=="object"&&typeof navigator.mediaDevices=="object"&&typeof navigator.mediaDevices.addEventListener=="function"&&typeof navigator.mediaDevices.enumerateDevices=="function"?(r==null?void 0:r.defaultDeviceCaptureMode)||"auto":"manual"},_onDeviceChange:{value:function(){navigator.mediaDevices.enumerateDevices().then(function(_){var m=_.find(function(y){var P=y.deviceId,h=y.kind;return h==="audioinput"&&P!=="default"});m&&["deviceId","groupId"].some(function(y){return m[y]!==i._currentDefaultDeviceInfo[y]})&&(o.info("Default device changed, restarting the LocalAudioTrack"),o.debug('Old default device: "'+i._currentDefaultDeviceInfo.deviceId+'" => "'+i._currentDefaultDeviceInfo.label+'"'),o.debug('New default device: "'+m.deviceId+'" => "'+m.label+'"'),i._currentDefaultDeviceInfo=m,i._restartDefaultDevice().catch(function(y){return o.warn("Failed to restart: "+y.message)}))},function(_){o.warn("Failed to run enumerateDevices(): "+_.message)})}},_restartOnDefaultDeviceChangeCleanup:{value:null,writable:!0},noiseCancellation:{enumerable:!0,value:a,writable:!1}}),o.debug("defaultDeviceCaptureMode:",i._defaultDeviceCaptureMode),i._maybeRestartOnDefaultDeviceChange(),i}return t.prototype.toString=function(){return"[LocalAudioTrack #"+this._instanceId+": "+this.id+"]"},t.prototype.attach=function(e){return e=n.prototype.attach.call(this,e),e.muted=!0,e},t.prototype._end=function(){return n.prototype._end.apply(this,arguments)},t.prototype._maybeRestartOnDefaultDeviceChange=function(){var e=this,r=this,i=r._constraints,a=r._defaultDeviceCaptureMode,o=r._log,s=this.noiseCancellation?this.noiseCancellation.sourceTrack:this.mediaStreamTrack,u=s.getSettings().deviceId,c=function(f){return f!==u||f==="default"},l=function f(p){return p===void 0&&(p={}),typeof p=="string"?c(p):Array.isArray(p)?p.every(c):p.exact?f(p.exact):p.ideal?f(p.ideal):!0}(i.deviceId);a==="auto"&&l?this._restartOnDefaultDeviceChangeCleanup||(o.info("LocalAudioTrack will be restarted if the default device changes"),navigator.mediaDevices.addEventListener("devicechange",this._onDeviceChange),this._restartOnDefaultDeviceChangeCleanup=function(){o.info("Cleaning up the listener to restart the LocalAudioTrack if the default device changes"),navigator.mediaDevices.removeEventListener("devicechange",e._onDeviceChange),e._restartOnDefaultDeviceChangeCleanup=null}):(o.info("LocalAudioTrack will NOT be restarted if the default device changes"),this._restartOnDefaultDeviceChangeCleanup&&this._restartOnDefaultDeviceChangeCleanup())},t.prototype._reacquireTrack=function(e){var r=this;return this._log.debug("_reacquireTrack: ",e),this.noiseCancellation?this.noiseCancellation.reacquireTrack(function(){return n.prototype._reacquireTrack.call(r,e)}):n.prototype._reacquireTrack.call(this,e)},t.prototype._restartDefaultDevice=function(){var e=this,r=Object.assign({},this._constraints),i=Object.assign({},r,{deviceId:this._currentDefaultDeviceInfo.deviceId});return this.restart(i).then(function(){e._constraints=r,e._maybeRestartOnDefaultDeviceChange()})},t.prototype._setMediaStreamTrack=function(e){var r=this,i=this,a=i._log,o=i.noiseCancellation,s=n.prototype._setMediaStreamTrack.call(this,e);return qf()&&o&&(a.debug("iOS Chrome detected, checking if the restarted Krisp audio is silent"),s=s.then(function(){return Hf(r._dummyEl)}).then(function(u){return a.debug("Krisp audio is "+(u?"silent, using source audio":"not silent")),u&&o.disablePermanently().then(function(){return n.prototype._setMediaStreamTrack.call(r,o.sourceTrack)})})),s},t.prototype.disable=function(){return n.prototype.disable.apply(this,arguments)},t.prototype.enable=function(){return n.prototype.enable.apply(this,arguments)},t.prototype.restart=function(){return n.prototype.restart.apply(this,arguments)},t.prototype.stop=function(){return this.noiseCancellation&&this.noiseCancellation.stop(),this._restartOnDefaultDeviceChangeCleanup&&this._restartOnDefaultDeviceChangeCleanup(),n.prototype.stop.apply(this,arguments)},t}(Kf),Yf=zf,Jf=en,fo=Yf;function nn(n,t){var e=new fo(n,t);return Object.setPrototypeOf(e,nn.prototype),e}Jf(nn,fo);var Xf=nn,Zf=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),ep=K.EventEmitter,tp=U.DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS,rp=function(n){Zf(t,n);function t(e){var r=n.call(this)||this;return Object.defineProperties(r,{_lastStatsSaveTime:{value:null,writable:!0},_lastStatsPublishTime:{value:null,writable:!0},_log:{value:e},_processorInfo:{value:null,writable:!0},_stats:{value:null,writable:!0}}),r.on("add",function(i){r._lastStatsSaveTime=Date.now(),r._lastStatsPublishTime=Date.now(),r._processorInfo=i,r._stats=[],r._reemitEvent("add",r._getEventData())}),r.on("remove",function(){var i=r._getEventData();r._lastStatsSaveTime=null,r._lastStatsPublishTime=null,r._processorInfo=null,r._stats=null,r._reemitEvent("remove",i)}),r.on("start",function(){r._reemitEvent("start",r._getEventData())}),r.on("stop",function(i){r._reemitEvent("stop",Object.assign({message:i},r._getEventData()))}),r.on("stats",function(){return r._maybeEmitStats()}),r}return t.prototype._getEventData=function(){if(!this._processorInfo)return{};var e=this._processorInfo,r=e.processor,i=e.captureHeight,a=e.captureWidth,o=e.inputFrameRate,s=e.isRemoteVideoTrack,u=e.inputFrameBufferType,c=e.outputFrameBufferContextType,l={captureHeight:i,captureWidth:a,inputFrameRate:o,isRemoteVideoTrack:s,inputFrameBufferType:u,outputFrameBufferContextType:c};return l.name=r._name||"VideoProcessor",["assetsPath","blurFilterRadius","debounce","fitType","isSimdEnabled","maskBlurRadius","pipeline","version"].forEach(function(f){var p=r["_"+f];typeof p<"u"&&(l[f]=p)}),Object.keys(l).forEach(function(f){var p=l[f];typeof p=="boolean"&&(l[f]=p?"true":"false")}),l},t.prototype._maybeEmitStats=function(){if(!(!this._stats||!this._processorInfo)){var e=this._processorInfo.processor._benchmark;if(e){var r=Date.now();if(!(r-this._lastStatsSaveTime<1e3)){var i={outputFrameRate:e.getRate("totalProcessingDelay")};if(["captureFrameDelay","imageCompositionDelay","inputImageResizeDelay","processFrameDelay","segmentationDelay"].forEach(function(s){i[s]=e.getAverageDelay(s)}),this._lastStatsSaveTime=r,this._stats.push(i),!(r-this._lastStatsPublishTime<tp)){this._lastStatsPublishTime=r;var a=this._stats.splice(0),o=a.reduce(function(s,u,c){return Object.keys(i).forEach(function(l){s[l]||(s[l]=0),s[l]=(s[l]*c+u[l])/(c+1)}),s},{});Object.keys(o).forEach(function(s){o[s]=parseFloat(o[s].toFixed(2))}),this._reemitEvent("stats",Object.assign({},o,this._getEventData()))}}}}},t.prototype._reemitEvent=function(e,r){this._log.debug("VideoProcessor:"+e,r),this.emit("event",{name:e,data:r})},t}(ep),np=rp,ip=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),ap=ro,op=np,ri=U.DEFAULT_FRAME_RATE,po=function(n){ip(t,n);function t(e,r){var i=n.call(this,e,r)||this;return Object.defineProperties(i,{_captureTimeoutId:{value:null,writable:!0},_isCapturing:{value:!1,writable:!0},_inputFrame:{value:null,writable:!0},_outputFrame:{value:null,writable:!0},_processorEventObserver:{value:null,writable:!0},_processorOptions:{value:{},writable:!0},_unmuteHandler:{value:null,writable:!0},dimensions:{enumerable:!0,value:{width:null,height:null}},processor:{enumerable:!0,value:null,writable:!0}}),i._processorEventObserver=new(r.VideoProcessorEventObserver||op)(i._log),i}return t.prototype._checkIfCanCaptureFrames=function(e){e===void 0&&(e=!1);var r=!0,i="",a=this.mediaStreamTrack,o=a.enabled,s=a.readyState;return o||(r=!1,i="MediaStreamTrack is disabled"),s==="ended"&&(r=!1,i="MediaStreamTrack is ended"),this.processor||(r=!1,i="VideoProcessor not detected."),!this._attachments.size&&!e&&(r=!1,i="VideoTrack is not publishing and there is no attached element."),i&&this._log.debug(i),{canCaptureFrames:r,message:i}},t.prototype._captureFrames=function(){var e=this;if(this._isCapturing){this._log.debug("Ignoring captureFrames call. Capture is already in progress");return}if(!this._checkIfCanCaptureFrames().canCaptureFrames){this._isCapturing=!1,this._log.debug("Cannot capture frames. Ignoring captureFrames call.");return}this._isCapturing=!0,this._processorEventObserver.emit("start"),this._log.debug("Start capturing frames");var r=Date.now(),i;this._dummyEl.play().then(function(){var a=function(s){clearTimeout(e._captureTimeoutId);var u=e.mediaStreamTrack.getSettings().frameRate,c=u===void 0?ri:u,l=Math.floor(1e3/c),f=l-i;(f<0||typeof i!="number")&&(f=0),e._captureTimeoutId=setTimeout(s,f)},o=function(){var s=e._checkIfCanCaptureFrames();if(!s.canCaptureFrames){e._isCapturing=!1,e._processorEventObserver.emit("stop",s.message),e._log.debug("Cannot capture frames. Stopping capturing frames.");return}r=Date.now();var u=e.mediaStreamTrack.getSettings(),c=u.width,l=c===void 0?0:c,f=u.height,p=f===void 0?0:f;e._outputFrame&&e._outputFrame.width!==l&&(e._outputFrame.width=l,e._outputFrame.height=p),e._inputFrame&&(e._inputFrame.width!==l&&(e._inputFrame.width=l,e._inputFrame.height=p),e._inputFrame.getContext("2d").drawImage(e._dummyEl,0,0,l,p));var v=null;try{var _=e._processorOptions.inputFrameBufferType==="video"?e._dummyEl:e._inputFrame;v=e.processor.processFrame(_,e._outputFrame)}catch(m){e._log.debug("Exception detected after calling processFrame.",m)}(v instanceof Promise?v:Promise.resolve(v)).then(function(){e._outputFrame&&(typeof e.processedTrack.requestFrame=="function"&&e.processedTrack.requestFrame(),e._processorEventObserver.emit("stats"))}).finally(function(){i=Date.now()-r,a(o)})};a(o)}).catch(function(a){return e._log.error("Video element cannot be played",{error:a,track:e})})},t.prototype._initialize=function(){var e=this;n.prototype._initialize.call(this),this._dummyEl&&(this._dummyEl.onloadedmetadata=function(){ni(e,e._dummyEl)&&(e.dimensions.width=e._dummyEl.videoWidth,e.dimensions.height=e._dummyEl.videoHeight)},this._dummyEl.onresize=function(){ni(e,e._dummyEl)&&(e.dimensions.width=e._dummyEl.videoWidth,e.dimensions.height=e._dummyEl.videoHeight,e.isStarted&&(e._log.debug("Dimensions changed:",e.dimensions),e.emit(t.DIMENSIONS_CHANGED,e)))})},t.prototype._restartProcessor=function(){var e=this.processor;if(e){var r=Object.assign({},this._processorOptions);this.removeProcessor(e),this.addProcessor(e,r)}},t.prototype._start=function(e){return this.dimensions.width=e.videoWidth,this.dimensions.height=e.videoHeight,this._log.debug("Dimensions:",this.dimensions),this.emit(t.DIMENSIONS_CHANGED,this),n.prototype._start.call(this,e)},t.prototype.addProcessor=function(e,r){var i=this;if(!e||typeof e.processFrame!="function")throw new Error("Received an invalid VideoProcessor from addProcessor.");if(this.processor)throw new Error("A VideoProcessor has already been added.");if(!this._dummyEl)throw new Error("VideoTrack has not been initialized.");this._log.debug("Adding VideoProcessor to the VideoTrack",e),this._unmuteHandler||(this._unmuteHandler=function(){i._log.debug("mediaStreamTrack unmuted"),i.processedTrack.muted&&(i._log.debug("mediaStreamTrack is unmuted but processedTrack is muted. Restarting processor."),i._restartProcessor())},this.mediaStreamTrack.addEventListener("unmute",this._unmuteHandler)),this._processorOptions=r||{};var a=this._processorOptions,o=a.inputFrameBufferType,s=a.outputFrameBufferContextType;if(typeof OffscreenCanvas>"u"&&o==="offscreencanvas")throw new Error("OffscreenCanvas is not supported by this browser.");if(o&&o!=="video"&&o!=="canvas"&&o!=="offscreencanvas")throw new Error("Invalid inputFrameBufferType of "+o);o||(o=typeof OffscreenCanvas>"u"?"canvas":"offscreencanvas");var u=this.mediaStreamTrack.getSettings(),c=u.width,l=c===void 0?0:c,f=u.height,p=f===void 0?0:f,v=u.frameRate,_=v===void 0?ri:v;o==="offscreencanvas"&&(this._inputFrame=new OffscreenCanvas(l,p)),o==="canvas"&&(this._inputFrame=document.createElement("canvas")),this._inputFrame&&(this._inputFrame.width=l,this._inputFrame.height=p),this._outputFrame=document.createElement("canvas"),this._outputFrame.width=l,this._outputFrame.height=p,s=s||"2d";var m=this._outputFrame.getContext(s);if(!m)throw new Error("Cannot get outputFrameBufferContextType: "+s+".");var y=typeof CanvasCaptureMediaStreamTrack<"u"&&CanvasCaptureMediaStreamTrack.prototype&&typeof CanvasCaptureMediaStreamTrack.prototype.requestFrame=="function"?0:void 0;return this.processedTrack=this._outputFrame.captureStream(y).getTracks()[0],this.processedTrack.enabled=this.mediaStreamTrack.enabled,this.processor=e,this._processorEventObserver.emit("add",{processor:e,captureHeight:p,captureWidth:l,inputFrameRate:_,isRemoteVideoTrack:this.toString().includes("RemoteVideoTrack"),inputFrameBufferType:o,outputFrameBufferContextType:s}),this._updateElementsMediaStreamTrack(),this._captureFrames(),this},t.prototype.attach=function(){var e=n.prototype.attach.apply(this,arguments);return this.processor&&this._captureFrames(),e},t.prototype.detach=function(){return n.prototype.detach.apply(this,arguments)},t.prototype.removeProcessor=function(e){if(!e)throw new Error("Received an invalid VideoProcessor from removeProcessor.");if(!this.processor)throw new Error("No existing VideoProcessor detected.");if(e!==this.processor)throw new Error("The provided VideoProcessor is different than the existing one.");return this._processorEventObserver.emit("remove"),this._log.debug("Removing VideoProcessor from the VideoTrack",e),clearTimeout(this._captureTimeoutId),this.mediaStreamTrack.removeEventListener("unmute",this._unmuteHandler),this._processorOptions={},this._unmuteHandler=null,this._isCapturing=!1,this.processor=null,this.processedTrack=null,this._inputFrame=null,this._outputFrame=null,this._updateElementsMediaStreamTrack(),this},t}(ap);po.DIMENSIONS_CHANGED="dimensionsChanged";function ni(n,t){return n.dimensions.width!==t.videoWidth||n.dimensions.height!==t.videoHeight}var vo=po,sp=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),cp=Se.isIOS,up=io,lp=lo,dp=vo,fp=lp(dp),pp=function(n){sp(t,n);function t(e,r){var i=this;return r=Object.assign({workaroundSilentLocalVideo:cp()&&typeof document<"u"&&typeof document.createElement=="function"},r),i=n.call(this,e,r)||this,Object.defineProperties(i,{_workaroundSilentLocalVideo:{value:r.workaroundSilentLocalVideo?vp:null},_workaroundSilentLocalVideoCleanup:{value:null,writable:!0}}),i._workaroundSilentLocalVideo&&(i._workaroundSilentLocalVideoCleanup=i._workaroundSilentLocalVideo(i,document)),i}return t.prototype.toString=function(){return"[LocalVideoTrack #"+this._instanceId+": "+this.id+"]"},t.prototype._checkIfCanCaptureFrames=function(){return n.prototype._checkIfCanCaptureFrames.call(this,this._trackSender.isPublishing)},t.prototype._end=function(){return n.prototype._end.apply(this,arguments)},t.prototype._setSenderMediaStreamTrack=function(e){var r=this,i=this.mediaStreamTrack,a=e?this.processedTrack:i;return this._trackSender.setMediaStreamTrack(a).catch(function(o){return r._log.warn("setMediaStreamTrack failed on LocalVideoTrack RTCRtpSender",{error:o,mediaStreamTrack:a})}).then(function(){r._unprocessedTrack=e?i:null})},t.prototype.addProcessor=function(){this._log.debug("Adding VideoProcessor to the LocalVideoTrack");var e=n.prototype.addProcessor.apply(this,arguments);return this.processedTrack?(this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the processed MediaStreamTrack",this.processedTrack),this._setSenderMediaStreamTrack(!0),e):this._log.warn("Unable to add a VideoProcessor to the LocalVideoTrack")},t.prototype.removeProcessor=function(){var e=this;this._log.debug("Removing VideoProcessor from the LocalVideoTrack");var r=n.prototype.removeProcessor.apply(this,arguments);return this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the original MediaStreamTrack"),this._setSenderMediaStreamTrack().then(function(){return e._updateElementsMediaStreamTrack()}),r},t.prototype.disable=function(){var e=n.prototype.disable.apply(this,arguments);return this.processedTrack&&(this.processedTrack.enabled=!1),e},t.prototype.enable=function(e){e===void 0&&(e=!0);var r=n.prototype.enable.apply(this,arguments);return this.processedTrack&&(this.processedTrack.enabled=e,e&&(this._captureFrames(),this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the processed MediaStreamTrack",this.processedTrack),this._setSenderMediaStreamTrack(!0))),r},t.prototype.restart=function(){var e=this;this._workaroundSilentLocalVideoCleanup&&(this._workaroundSilentLocalVideoCleanup(),this._workaroundSilentLocalVideoCleanup=null);var r=n.prototype.restart.apply(this,arguments);return this.processor&&r.then(function(){e._restartProcessor()}),this._workaroundSilentLocalVideo&&r.finally(function(){e._workaroundSilentLocalVideoCleanup=e._workaroundSilentLocalVideo(e,document)}),r},t.prototype.stop=function(){return this._workaroundSilentLocalVideoCleanup&&(this._workaroundSilentLocalVideoCleanup(),this._workaroundSilentLocalVideoCleanup=null),n.prototype.stop.apply(this,arguments)},t}(fp);function vp(n,t){var e=n._log,r=n._dummyEl,i=n.mediaStreamTrack;function a(){n.isEnabled&&(e.info("Unmuted, checking silence"),r.play().then(function(){return up(r)}).then(function(o){if(!o){e.info("Non-silent frames detected, so no need to restart");return}return e.warn("Silence detected, restarting"),n._stop(),n._restart()}).catch(function(o){e.warn("Failed to detect silence and restart:",o)}).finally(function(){r=n._dummyEl,r&&!r.paused&&!n.processedTrack&&r.pause(),i.removeEventListener("unmute",a),i=n.mediaStreamTrack,i.addEventListener("unmute",a)}))}return i.addEventListener("unmute",a),function(){i.removeEventListener("unmute",a)}}var hp=pp,_p=en,ho=hp;function an(n,t){var e=new ho(n,t);return Object.setPrototypeOf(e,an.prototype),e}_p(an,ho);var mp=an,yp=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),gp=so,bp=function(n){yp(t,n);function t(e,r,i,a){var o=n.call(this,e,"data")||this;return Object.defineProperties(o,{maxPacketLifeTime:{enumerable:!0,value:r},maxRetransmits:{enumerable:!0,value:i},ordered:{enumerable:!0,value:a}}),o}return t}(gp),_o=bp,Sp=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),wp=_o,kp=R.makeUUID,Tp=function(n){Sp(t,n);function t(e,r,i){var a=n.call(this,kp(),e,r,i)||this;return Object.defineProperties(a,{_clones:{value:new Set},_dataChannels:{value:new Set}}),a}return t.prototype._addClone=function(e){this._clones.add(e)},t.prototype.removeClone=function(e){this._clones.delete(e)},t.prototype.addDataChannel=function(e){return this._dataChannels.add(e),this},t.prototype.clone=function(){var e=this,r=new t(this.maxPacketLifeTime,this.maxRetransmits,this.ordered);return this._addClone(r),r.once("stopped",function(){return e.removeClone(r)}),r},t.prototype.removeDataChannel=function(e){return this._dataChannels.delete(e),this},t.prototype.send=function(e){return this._dataChannels.forEach(function(r){try{r.send(e)}catch{}}),this._clones.forEach(function(r){try{r.send(e)}catch{}}),this},t.prototype.stop=function(){this._dataChannels.forEach(function(e){return e.close()}),this._clones.forEach(function(e){return e.stop()}),n.prototype.stop.call(this)},t}(wp),Pp=Tp,Ep=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Op=tn,Cp=Pp,Rp=function(n){Ep(t,n);function t(e){var r=this;e=Object.assign({DataTrackSender:Cp,maxPacketLifeTime:null,maxRetransmits:null,ordered:!0},e);var i=e.DataTrackSender,a=new i(e.maxPacketLifeTime,e.maxRetransmits,e.ordered);return r=n.call(this,a.id,"data",e)||this,Object.defineProperties(r,{_trackSender:{value:a},id:{enumerable:!0,value:a.id},maxPacketLifeTime:{enumerable:!0,value:e.maxPacketLifeTime},maxRetransmits:{enumerable:!0,value:e.maxRetransmits},ordered:{enumerable:!0,value:e.ordered},reliable:{enumerable:!0,value:e.maxPacketLifeTime===null&&e.maxRetransmits===null}}),r}return t.prototype.send=function(e){this._trackSender.send(e)},t}(Op),Lp=Rp,xp=en,mo=Lp;function on(n){var t=new mo(n);return Object.setPrototypeOf(t,on.prototype),t}xp(on,mo);var Ap=on,Ae={LocalAudioTrack:Xf,LocalVideoTrack:mp,LocalDataTrack:Ap},te=d&&d.__assign||function(){return te=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},te.apply(this,arguments)},ii=d&&d.__awaiter||function(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(l){try{c(r.next(l))}catch(f){o(f)}}function u(l){try{c(r.throw(l))}catch(f){o(f)}}function c(l){l.done?a(l.value):i(l.value).then(s,u)}c((r=r.apply(n,t||[])).next())})},ai=d&&d.__generator||function(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){e.label=c[1];break}if(c[0]===6&&e.label<a[1]){e.label=a[1],a=c;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(c);break}a[2]&&e.ops.pop(),e.trys.pop();continue}c=t.call(n,e)}catch(l){c=[6,l],i=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}},oi=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},si=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.createLocalTracks=void 0;var Ip=Ie,Mp=R.buildLogLevels,yo=be,jp=yo.getUserMedia,Dp=yo.MediaStreamTrack,sn=Ae,$p=sn.LocalAudioTrack,Np=sn.LocalDataTrack,Fp=sn.LocalVideoTrack,Vp=ce,cn=U,Bp=cn.DEFAULT_LOG_LEVEL,Up=cn.DEFAULT_LOGGER_NAME,Wp=cn.typeErrors.INVALID_VALUE,Hp=ao,qp=0;function Qp(n){return ii(this,void 0,void 0,function(){var t,e,r,i,a,o,s,u,c,l,f,p,v,_=this;return ai(this,function(m){switch(m.label){case 0:if(t=!(n&&("audio"in n||"video"in n)),e=te({audio:t,getUserMedia:jp,loggerName:Up,logLevel:Bp,LocalAudioTrack:$p,LocalDataTrack:Np,LocalVideoTrack:Fp,MediaStreamTrack:Dp,Log:Vp,video:t},n),r="[createLocalTracks #"+ ++qp+"]",i=Mp(e.logLevel),a=new e.Log("default",r,i,e.loggerName),o=Object.assign({log:a},e),delete o.name,e.audio===!1&&e.video===!1)return a.info("Neither audio nor video requested, so returning empty LocalTracks"),[2,[]];if(e.tracks)return a.info("Adding user-provided LocalTracks"),a.debug("LocalTracks:",e.tracks),[2,e.tracks];if(s={audio:typeof e.audio=="object"&&e.audio.name?{name:e.audio.name}:{defaultDeviceCaptureMode:"auto"},video:typeof e.video=="object"&&e.video.name?{name:e.video.name}:{}},s.audio.isCreatedByCreateLocalTracks=!0,s.video.isCreatedByCreateLocalTracks=!0,typeof e.audio=="object")if(typeof e.audio.workaroundWebKitBug1208516=="boolean"&&(s.audio.workaroundWebKitBug1208516=e.audio.workaroundWebKitBug1208516),"noiseCancellationOptions"in e.audio&&(u=e.audio.noiseCancellationOptions,delete e.audio.noiseCancellationOptions),!("defaultDeviceCaptureMode"in e.audio))s.audio.defaultDeviceCaptureMode="auto";else{if(["auto","manual"].every(function(y){return y!==e.audio.defaultDeviceCaptureMode}))throw Wp("CreateLocalAudioTrackOptions.defaultDeviceCaptureMode",["auto","manual"]);s.audio.defaultDeviceCaptureMode=e.audio.defaultDeviceCaptureMode}typeof e.video=="object"&&typeof e.video.workaroundWebKitBug1208516=="boolean"&&(s.video.workaroundWebKitBug1208516=e.video.workaroundWebKitBug1208516),typeof e.audio=="object"&&delete e.audio.name,typeof e.video=="object"&&delete e.video.name,c={audio:e.audio,video:e.video},l=typeof e.audio=="object"&&e.audio.workaroundWebKitBug180748,m.label=1;case 1:return m.trys.push([1,4,,5]),[4,l?Hp(a,e.getUserMedia,c):e.getUserMedia(c)];case 2:return f=m.sent(),p=si(si([],oi(f.getAudioTracks())),oi(f.getVideoTracks())),a.info("Call to getUserMedia successful; got tracks:",p),[4,Promise.all(p.map(function(y){return ii(_,void 0,void 0,function(){var P,h,b;return ai(this,function(g){switch(g.label){case 0:return y.kind==="audio"&&u?[4,Ip.applyNoiseCancellation(y,u,a)]:[3,2];case 1:return P=g.sent(),h=P.cleanTrack,b=P.noiseCancellation,[2,new o.LocalAudioTrack(h,te(te(te({},s.audio),o),{noiseCancellation:b}))];case 2:if(y.kind==="audio")return[2,new o.LocalAudioTrack(y,te(te({},s.audio),o))];g.label=3;case 3:return[2,new o.LocalVideoTrack(y,te(te({},s.video),o))]}})})}))];case 3:return[2,m.sent()];case 4:throw v=m.sent(),a.warn("Call to getUserMedia failed:",v),v;case 5:return[2]}})})}Rt.createLocalTracks=Qp;var Me={},$t={};Object.defineProperty($t,"__esModule",{value:!0});$t.Timer=void 0;var Gp=function(){function n(){this._end=void 0,this.start()}return n.prototype.start=function(){return this._start=Date.now(),this},n.prototype.stop=function(){return this._end=Date.now(),this},n.prototype.getTimeMeasurement=function(){return{start:this._start,end:this._end,duration:this._end===void 0?void 0:this._end-this._start}},n}();$t.Timer=Gp;var je={};Object.defineProperty(je,"__esModule",{value:!0});je.mosToScore=je.calculateMOS=void 0;var ci=94.768;function Kp(n,t,e){var r=n+t*2+10,i=0;switch(!0){case r<160:i=ci-r/40;break;case r<1e3:i=ci-(r-120)/10;break}switch(!0){case e<=i/2.5:i=Math.max(i-e*2.5,6.52);break;default:i=0;break}var a=1+.035*i+7e-6*i*(i-60)*(100-i);return a}je.calculateMOS=Kp;function zp(n){var t=0;return n?n>4.2?t=5:n>4?t=4:n>3.6?t=3:n>3?t=2:t=1:t=0,t}je.mosToScore=zp;var Nt={},Yp=d&&d.__awaiter||function(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(l){try{c(r.next(l))}catch(f){o(f)}}function u(l){try{c(r.throw(l))}catch(f){o(f)}}function c(l){l.done?a(l.value):i(l.value).then(s,u)}c((r=r.apply(n,t||[])).next())})},Jp=d&&d.__generator||function(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){e.label=c[1];break}if(c[0]===6&&e.label<a[1]){e.label=a[1],a=c;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(c);break}a[2]&&e.ops.pop(),e.trys.pop();continue}c=t.call(n,e)}catch(l){c=[6,l],i=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}},Xp=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.getCombinedConnectionStats=void 0;function de(n,t,e,r){var i=[];return n.forEach(function(a){(r.length===0||r.includes(a.type))&&(e.length===0||e.includes(a.kind))&&typeof a[t]=="number"&&i.push(a[t])}),i}function Zp(n){var t=n.publisher,e=n.subscriber;return Yp(this,void 0,void 0,function(){var r,i,a,o,s,u,c,l,f,p,v,_,m,y,P;return Jp(this,function(h){switch(h.label){case 0:return[4,Promise.all([t,e].map(function(b){return b.getStats()}))];case 1:return r=Xp.apply(void 0,[h.sent(),2]),i=r[0],a=r[1],o=de(a,"timestamp",["audio"],["inbound-rtp"]),s=o.length>0?o[0]:0,u=de(a,"jitter",["audio"],["inbound-rtp"]).reduce(function(b,g){return Math.max(b,g)},0),c=de(a,"packetsReceived",["audio","video"],["inbound-rtp"]).reduce(function(b,g){return b+g},0),l=de(a,"packetsLost",["audio","video"],["inbound-rtp"]).reduce(function(b,g){return b+g},0),f=de(i,"roundTripTime",["audio","video"],["remote-inbound-rtp"]).reduce(function(b,g){return Math.max(b,g)},0),p=de(a,"currentRoundTripTime",[],["candidate-pair"]).reduce(function(b,g){return Math.max(b,g)},0),v=(p||f)*1e3,_=de(i,"bytesSent",[],["candidate-pair"]).reduce(function(b,g){return b+g},0),m=de(a,"bytesReceived",[],["candidate-pair"]).reduce(function(b,g){return b+g},0),y=ev(a),P=[],a.forEach(function(b){(b.type==="local-candidate"||b.type==="remote-candidate")&&P.push(Ar(b))}),[2,{timestamp:s,jitter:u,packets:c,packetsLost:l,roundTripTime:v,bytesSent:_,bytesReceived:m,selectedIceCandidatePairStats:y,iceCandidateStats:P}]}})})}Nt.getCombinedConnectionStats=Zp;function Ar(n){var t=[{key:"transportId",type:"string"},{key:"candidateType",type:"string"},{key:"port",altKeys:["portNumber"],type:"number"},{key:"address",altKeys:["ip","ipAddress"],type:"string"},{key:"priority",type:"number"},{key:"protocol",altKeys:["transport"],type:"string"},{key:"url",type:"string"},{key:"relayProtocol",type:"string"}];return t.reduce(function(e,r){var i=[r.key];r.altKeys&&(i=i.concat(r.altKeys));var a=i.find(function(o){return o in n});return a&&typeof n[a]===r.type&&(e[r.key]=n[a]),e},{})}function ev(n){var t=null,e=[];n.forEach(function(s){s.type==="transport"&&s.selectedCandidatePairId?t=s.selectedCandidatePairId:s.type==="candidate-pair"&&e.push(s)});var r=e.find(function(s){return s.selected||t&&s.id===t});if(!r)return null;var i=r,a=n.get(i.localCandidateId),o=n.get(i.remoteCandidateId);return!a||!o?null:{localCandidate:Ar(a),remoteCandidate:Ar(o)}}var Ft={},tv=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),rv=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},nv=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},iv=K.EventEmitter,go=R,av=function(n){tv(t,n);function t(e,r){var i=n.call(this)||this,a=null,o=e;return r=cv(r),Object.defineProperties(i,{_lock:{get:function(){return a},set:function(s){a=s}},_reachableStates:{value:sv(r)},_state:{get:function(){return o},set:function(s){o=s}},_states:{value:r},_whenDeferreds:{value:new Set},isLocked:{enumerable:!0,get:function(){return a!==null}},state:{enumerable:!0,get:function(){return o}}}),i.on("stateChanged",function(s){i._whenDeferreds.forEach(function(u){u.when(s,u.resolve,u.reject)})}),i}return t.prototype._whenPromise=function(e){var r=this;if(typeof e!="function")return Promise.reject(new Error("when() executor must be a function"));var i=go.defer();return i.when=e,this._whenDeferreds.add(i),i.promise.then(function(a){return r._whenDeferreds.delete(i),a},function(a){throw r._whenDeferreds.delete(i),a})},t.prototype.bracket=function(e,r){var i,a=this;function o(s){if(a.hasLock(i)&&a.releaseLockCompletely(i),s)throw s}return this.takeLock(e).then(function(u){return i=u,r(i)}).then(function(u){return o(),u},o)},t.prototype.hasLock=function(e){return this._lock===e},t.prototype.preempt=function(e,r,i){if(!ct(this._states,this.state,e))throw new Error('Cannot transition from "'+this.state+'" to "'+e+'"');var a;this.isLocked&&(a=this._lock,this._lock=null);var o=null;r&&(o=this.takeLockSync(r));var s=o?null:this.takeLockSync("preemption");return this.transition(e,o||s,i),a&&a.resolve(),s&&this.releaseLock(s),o},t.prototype.releaseLock=function(e){if(this.isLocked){if(!this.hasLock(e))throw new Error("Could not release the lock for "+e.name+" because "+this._lock.name+" has the lock")}else throw new Error("Could not release the lock for "+e.name+" because the StateMachine is not locked");e.depth===0?(this._lock=null,e.resolve()):e.depth--},t.prototype.releaseLockCompletely=function(e){if(this.isLocked){if(!this.hasLock(e))throw new Error("Could not release the lock for "+e.name+" because "+this._lock.name+" has the lock")}else throw new Error("Could not release the lock for "+e.name+" because the StateMachine is not locked");e.depth=0,this._lock=null,e.resolve()},t.prototype.takeLock=function(e){var r=this;if(typeof e=="object"){var i=e;return new Promise(function(s){s(r.takeLockSync(i))})}var a=e;if(this.isLocked){var o=this.takeLock.bind(this,a);return this._lock.promise.then(o)}return Promise.resolve(this.takeLockSync(a))},t.prototype.takeLockSync=function(e){var r=typeof e=="string"?null:e,i=r?r.name:e;if(r&&!this.hasLock(r)||!r&&this.isLocked)throw new Error("Could not take the lock for "+i+" because the lock for "+this._lock.name+" was not released");if(r)return r.depth++,r;var a=ov(i);return this._lock=a,a},t.prototype.transition=function(e,r,i){if(i=i||[],this.isLocked)if(r){if(!this.hasLock(r))throw new Error("Could not transition using the key for "+r.name+" because "+this._lock.name+" has the lock")}else throw new Error("You must provide the key in order to transition");else if(r)throw new Error("Key provided for "+r.name+", but the StateMachine was not locked (possibly due to preemption)");if(!ct(this._states,this.state,e))throw new Error('Cannot transition from "'+this.state+'" to "'+e+'"');this._state=e,this.emit.apply(this,nv([],rv(["stateChanged",e].concat(i))))},t.prototype.tryTransition=function(e,r,i){try{this.transition(e,r,i)}catch{return!1}return!0},t.prototype.when=function(e){var r=this;return this.state===e?Promise.resolve(this):ct(this._reachableStates,this.state,e)?this._whenPromise(function(i,a,o){i===e?a(r):ct(r._reachableStates,i,e)||o(ui(i,e))}):Promise.reject(ui(this.state,e))},t}(iv);function ct(n,t,e){return n.get(t).has(e)}function ov(n){var t=go.defer();return t.name=n,t.depth=0,t}function sv(n){return Array.from(n.keys()).reduce(function(t,e){return t.set(e,bo(n,e))},new Map)}function bo(n,t,e){return e=e||new Set,n.get(t).forEach(function(r){e.has(r)||(e.add(r),bo(n,r,e).forEach(e.add,e))}),e}function cv(n){var t=new Map;for(var e in n)t.set(e,new Set(n[e]));return t}function ui(n,t){return new Error('"'+t+'" cannot be reached from "'+n+'"')}var $e=av,uv=function(){function n(t,e){var r=this;e=Object.assign({navigator,window},e);var i=e.navigator,a=i.connection||{type:null},o=a.type,s=a.type?{_events:{value:["change","typechange"]},_listener:{value:function(){var f=o!==r.type&&r.isOnline;o=r.type,f&&t()}},_target:{value:a}}:{_events:{value:["online"]},_listener:{value:t},_target:{value:e.window}},u=s._events,c=s._listener,l=s._target;Object.defineProperties(this,{isOnline:{enumerable:!0,get:function(){return typeof i.onLine=="boolean"?i.onLine:!0}},type:{enumerable:!0,get:function(){return a.type||null}},_listener:c,_events:u,_target:l})}return n.prototype.start=function(){var t=this;this._events.forEach(function(e){t._target.addEventListener(e,t._listener)})},n.prototype.stop=function(){var t=this;this._events.forEach(function(e){t._target.removeEventListener(e,t._listener)})},n}(),lv=uv,dv=function(){function n(t,e,r){r===void 0&&(r=!0),Object.defineProperties(this,{_delay:{value:e,writable:!0},_fn:{value:t},_timeout:{value:null,writable:!0}}),r&&this.start()}return Object.defineProperty(n.prototype,"delay",{get:function(){return this._delay},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"isSet",{get:function(){return!!this._timeout},enumerable:!1,configurable:!0}),n.prototype.setDelay=function(t){this._delay=t},n.prototype.start=function(){var t=this;this.isSet||(this._timeout=setTimeout(function(){var e=t._fn;t.clear(),e()},this._delay))},n.prototype.clear=function(){clearTimeout(this._timeout),this._timeout=null},n.prototype.reset=function(){this.clear(),this.start()},n}(),we=dv,or,li;function So(){return li||(li=1,or=WebSocket),or}var fv=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),di=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},pv=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},vv=$e,wo=R,hv=wo.buildLogLevels,_v=wo.makeUUID,mv=ce,yv=lv,Te=we,gv=0,bv={closed:[],connecting:["closed","open","waiting"],early:["closed","connecting"],open:["closed"],waiting:["closed","connecting","early","open"]},fi={closed:"close",open:"open",waiting:"waiting"},Sv=2,wv=3,kv=3,Tv=5e3,Pv=15e3,Ev=5e3,Ov=200,pi=1e3,Ir=3e3,Mr=3001,jr=3002,Dr=3003,ko=3004,ut=3005,$r=3006,To=3007,vi=globalThis,Cv=vi.WebSocket?vi.WebSocket:So(),X={BUSY:"busy",FAILED:"failed",LOCAL:"local",REMOTE:"remote",TIMEOUT:"timeout"},Rv=new Map([[Ir,X.TIMEOUT],[Mr,X.TIMEOUT],[jr,X.FAILED],[Dr,X.FAILED],[ko,X.TIMEOUT],[$r,X.BUSY],[To,X.TIMEOUT]]),Po=function(n){fv(t,n);function t(e,r){var i=n.call(this,"early",bv)||this;r=Object.assign({helloBody:null,maxConsecutiveFailedHellos:kv,maxConsecutiveMissedHeartbeats:wv,requestedHeartbeatTimeout:Tv,openTimeout:Pv,welcomeTimeout:Ev,Log:mv,WebSocket:Cv},r);var a=hv(r.logLevel),o=new r.Log("default",i,a,r.loggerName),s=r.networkMonitor?new yv(function(){var c=s.type,l="Network changed"+(c?" to "+c:"");o.debug(l),i._close({code:ko,reason:l})}):null;Object.defineProperties(i,{_busyWaitTimeout:{value:null,writable:!0},_consecutiveHeartbeatsMissed:{value:0,writable:!0},_cookie:{value:null,writable:!0},_eventObserver:{value:r.eventObserver},_heartbeatTimeout:{value:null,writable:!0},_hellosLeft:{value:r.maxConsecutiveFailedHellos,writable:!0},_instanceId:{value:++gv},_log:{value:o},_messageQueue:{value:[]},_networkMonitor:{value:s},_options:{value:r},_openTimeout:{value:null,writable:!0},_sendHeartbeatTimeout:{value:null,writable:!0},_serverUrl:{value:e},_welcomeTimeout:{value:null,writable:!0},_ws:{value:null,writable:!0}});var u={connecting:"info",early:"info",open:"info",waiting:"warning",closed:"info"};return i.on("stateChanged",function(c){for(var l=[],f=1;f<arguments.length;f++)l[f-1]=arguments[f];c in fi&&i.emit.apply(i,pv([fi[c]],di(l)));var p={name:c,group:"signaling",level:u[i.state]};if(c==="closed"){var v=di(l,1),_=v[0];p.payload={reason:_},p.level=_===X.LOCAL?"info":"error"}i._eventObserver.emit("event",p)}),i._eventObserver.emit("event",{name:i.state,group:"signaling",level:u[i.state]}),i._connect(),i}return t.prototype.toString=function(){return"[TwilioConnection #"+this._instanceId+": "+this._ws.url+"]"},t.prototype._close=function(e){var r=e.code,i=e.reason;if(this.state!=="closed"){this._openTimeout&&this._openTimeout.clear(),this._welcomeTimeout&&this._welcomeTimeout.clear(),this._heartbeatTimeout&&this._heartbeatTimeout.clear(),this._sendHeartbeatTimeout&&this._sendHeartbeatTimeout.clear(),this._networkMonitor&&this._networkMonitor.stop(),this._busyWaitTimeout&&r!==ut&&this._busyWaitTimeout.clear(),this._messageQueue.splice(0);var a=this._log;r===pi?(a.debug("Closed"),this.transition("closed",null,[X.LOCAL])):(a.warn("Closed: "+r+" - "+i),r!==ut&&this.transition("closed",null,[Rv.get(r)||X.REMOTE]));var o=this._ws.readyState,s=this._options.WebSocket;o!==s.CLOSING&&o!==s.CLOSED&&this._ws.close(r,i)}},t.prototype._connect=function(){var e=this,r=this._log;if(this.state==="waiting")this.transition("early");else if(this.state!=="early"){r.warn('Unexpected state "'+this.state+'" for connecting to the TCMP server.');return}this._ws=new this._options.WebSocket(this._serverUrl);var i=this._ws;r.debug("Created a new WebSocket:",i),i.addEventListener("close",function(o){return e._close(o)});var a=this._options.openTimeout;this._openTimeout=new Te(function(){var o="Failed to open in "+a+" ms";e._close({code:To,reason:o})},a),i.addEventListener("open",function(){r.debug("WebSocket opened:",i),e._openTimeout.clear(),e._startHandshake(),e._networkMonitor&&e._networkMonitor.start()}),i.addEventListener("message",function(o){r.debug("Incoming: "+o.data);try{o=JSON.parse(o.data)}catch(s){e.emit("error",s);return}switch(o.type){case"bad":e._handleBad(o);break;case"busy":e._handleBusy(o);break;case"bye":break;case"msg":e._handleMessage(o);case"heartbeat":e._handleHeartbeat();break;case"welcome":e._handleWelcome(o);break;default:e._log.debug("Unknown message type: "+o.type),e.emit("error",new Error("Unknown message type: "+o.type));break}})},t.prototype._handleBad=function(e){var r=e.reason,i=this._log;if(!["connecting","open"].includes(this.state)){i.warn('Unexpected state "'+this.state+'" for handling a "bad" message from the TCMP server.');return}if(this.state==="connecting"){i.warn("Closing: "+jr+" - "+r),this._close({code:jr,reason:r});return}i.debug("Error: "+r),this.emit("error",new Error(r))},t.prototype._handleBusy=function(e){var r=this,i=e.cookie,a=e.keepAlive,o=e.retryAfter,s=this._log;if(!["connecting","waiting"].includes(this.state)){s.warn('Unexpected state "'+this.state+'" for handling a "busy" message from the TCMP server.');return}this._busyWaitTimeout&&this._busyWaitTimeout.clear(),this._welcomeTimeout&&this._welcomeTimeout.clear();var u=o<0?'Received terminal "busy" message':'Received "busy" message, retrying after '+o+" ms";if(o<0){s.warn("Closing: "+$r+" - "+u),this._close({code:$r,reason:u});return}var c=this._options.maxConsecutiveFailedHellos;this._hellosLeft=c,this._cookie=i||null,a?(s.warn(u),this._busyWaitTimeout=new Te(function(){return r._startHandshake()},o)):(s.warn("Closing: "+ut+" - "+u),this._close({code:ut,reason:u}),this._busyWaitTimeout=new Te(function(){return r._connect()},o)),this.transition("waiting",null,[a,o])},t.prototype._handleHeartbeat=function(){if(this.state!=="open"){this._log.warn('Unexpected state "'+this.state+'" for handling a "heartbeat" message from the TCMP server.');return}this._heartbeatTimeout.reset()},t.prototype._handleHeartbeatTimeout=function(){if(this.state==="open"){var e=this._log,r=this._options.maxConsecutiveMissedHeartbeats;e.debug("Consecutive heartbeats missed: "+r);var i="Missed "+r+' "heartbeat" messages';e.warn("Closing: "+Mr+" - "+i),this._close({code:Mr,reason:i})}},t.prototype._handleMessage=function(e){var r=e.body;if(this.state!=="open"){this._log.warn('Unexpected state "'+this.state+'" for handling a "msg" message from the TCMP server.');return}this.emit("message",r)},t.prototype._handleWelcome=function(e){var r=this,i=e.negotiatedTimeout,a=this._log;if(!["connecting","waiting"].includes(this.state)){a.warn('Unexpected state "'+this.state+'" for handling a "welcome" message from the TCMP server.');return}this.state==="waiting"&&(a.debug('Received "welcome" message, no need to retry connection.'),this._busyWaitTimeout.clear());var o=this._options.maxConsecutiveMissedHeartbeats,s=i*o,u=i-Ov;this._welcomeTimeout.clear(),this._heartbeatTimeout=new Te(function(){return r._handleHeartbeatTimeout()},s),this._messageQueue.splice(0).forEach(function(c){return r._send(c)}),this._sendHeartbeatTimeout=new Te(function(){return r._sendHeartbeat()},u),this.transition("open")},t.prototype._handleWelcomeTimeout=function(){if(this.state==="connecting"){var e=this._log;if(this._hellosLeft<=0){var r="All handshake attempts failed";e.warn("Closing: "+Ir+" - "+r),this._close({code:Ir,reason:r});return}var i=this._options.maxConsecutiveFailedHellos;e.warn("Handshake attempt "+(i-this._hellosLeft)+" failed"),this._startHandshake()}},t.prototype._send=function(e){var r=this._ws.readyState,i=this._options.WebSocket;if(r===i.OPEN){var a=JSON.stringify(e);this._log.debug("Outgoing: "+a);try{this._ws.send(a),this._sendHeartbeatTimeout&&this._sendHeartbeatTimeout.reset()}catch{var o="Failed to send message";this._log.warn("Closing: "+Dr+" - "+o),this._close({code:Dr,reason:o})}}},t.prototype._sendHeartbeat=function(){this.state!=="closed"&&this._send({type:"heartbeat"})},t.prototype._sendHello=function(){var e=this._options,r=e.helloBody,i=e.requestedHeartbeatTimeout,a={id:_v(),timeout:i,type:"hello",version:Sv};this._cookie&&(a.cookie=this._cookie),r&&(a.body=r),this._send(a)},t.prototype._sendOrEnqueue=function(e){var r=this;if(this.state!=="closed"){var i=this.state==="open"?function(a){return r._send(a)}:function(a){return r._messageQueue.push(a)};i(e)}},t.prototype._startHandshake=function(){var e=this;if(["early","waiting"].includes(this.state)&&this.transition("connecting"),this.state==="connecting"){this._hellosLeft--,this._sendHello();var r=this._options.welcomeTimeout;this._welcomeTimeout=new Te(function(){return e._handleWelcomeTimeout()},r)}},t.prototype.close=function(){this.state!=="closed"&&(this._sendOrEnqueue({type:"bye"}),this._close({code:pi,reason:"Normal"}))},t.prototype.sendMessage=function(e){this._sendOrEnqueue({body:e,type:"msg"})},t}(vv);Po.CloseReason=X;var Eo=Po,L={},Lv=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),xv=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Av=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Iv=function(n){Lv(t,n);function t(e){var r=this,i=[].slice.call(arguments,1);r=n.apply(this,Av([],xv(i)))||this,Object.setPrototypeOf(r,t.prototype);var a=Error.apply(r,i);return a.name="TwilioError",Object.defineProperty(r,"code",{value:e,enumerable:!0}),Object.getOwnPropertyNames(a).forEach(function(o){Object.defineProperty(this,o,{value:a[o],enumerable:!0})},r),r}return t.prototype.toString=function(){var e=this.message?": "+this.message:"";return this.name+" "+this.code+e},t}(Error),Mv=Iv,j=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),M=Mv,A={};L.createTwilioError=function(t,e){return t=typeof t=="number"?t:0,e=typeof e=="string"&&e?e:"Unknown error",A[t]?new A[t]:new M(t,e)};var Oo=function(n){j(t,n);function t(){var e=n.call(this,20101,"Invalid Access Token")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenInvalidError=Oo;Object.defineProperty(A,20101,{value:Oo});var Co=function(n){j(t,n);function t(){var e=n.call(this,20102,"Invalid Access Token header")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenHeaderInvalidError=Co;Object.defineProperty(A,20102,{value:Co});var Ro=function(n){j(t,n);function t(){var e=n.call(this,20103,"Invalid Access Token issuer/subject")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenIssuerInvalidError=Ro;Object.defineProperty(A,20103,{value:Ro});var Lo=function(n){j(t,n);function t(){var e=n.call(this,20104,"Access Token expired or expiration date invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenExpiredError=Lo;Object.defineProperty(A,20104,{value:Lo});var xo=function(n){j(t,n);function t(){var e=n.call(this,20105,"Access Token not yet valid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenNotYetValidError=xo;Object.defineProperty(A,20105,{value:xo});var Ao=function(n){j(t,n);function t(){var e=n.call(this,20106,"Invalid Access Token grants")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenGrantsInvalidError=Ao;Object.defineProperty(A,20106,{value:Ao});var Io=function(n){j(t,n);function t(){var e=n.call(this,20107,"Invalid Access Token signature")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.AccessTokenSignatureInvalidError=Io;Object.defineProperty(A,20107,{value:Io});var Mo=function(n){j(t,n);function t(){var e=n.call(this,53e3,"Signaling connection error")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingConnectionError=Mo;Object.defineProperty(A,53e3,{value:Mo});var jo=function(n){j(t,n);function t(){var e=n.call(this,53001,"Signaling connection disconnected")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingConnectionDisconnectedError=jo;Object.defineProperty(A,53001,{value:jo});var Do=function(n){j(t,n);function t(){var e=n.call(this,53002,"Signaling connection timed out")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingConnectionTimeoutError=Do;Object.defineProperty(A,53002,{value:Do});var $o=function(n){j(t,n);function t(){var e=n.call(this,53003,"Client received an invalid signaling message")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingIncomingMessageInvalidError=$o;Object.defineProperty(A,53003,{value:$o});var No=function(n){j(t,n);function t(){var e=n.call(this,53004,"Client sent an invalid signaling message")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingOutgoingMessageInvalidError=No;Object.defineProperty(A,53004,{value:No});var Fo=function(n){j(t,n);function t(){var e=n.call(this,53006,"Video server is busy")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.SignalingServerBusyError=Fo;Object.defineProperty(A,53006,{value:Fo});var Vo=function(n){j(t,n);function t(){var e=n.call(this,53100,"Room name is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomNameInvalidError=Vo;Object.defineProperty(A,53100,{value:Vo});var Bo=function(n){j(t,n);function t(){var e=n.call(this,53101,"Room name is too long")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomNameTooLongError=Bo;Object.defineProperty(A,53101,{value:Bo});var Uo=function(n){j(t,n);function t(){var e=n.call(this,53102,"Room name contains invalid characters")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomNameCharsInvalidError=Uo;Object.defineProperty(A,53102,{value:Uo});var Wo=function(n){j(t,n);function t(){var e=n.call(this,53103,"Unable to create Room")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomCreateFailedError=Wo;Object.defineProperty(A,53103,{value:Wo});var Ho=function(n){j(t,n);function t(){var e=n.call(this,53104,"Unable to connect to Room")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomConnectFailedError=Ho;Object.defineProperty(A,53104,{value:Ho});var qo=function(n){j(t,n);function t(){var e=n.call(this,53105,"Room contains too many Participants")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomMaxParticipantsExceededError=qo;Object.defineProperty(A,53105,{value:qo});var Qo=function(n){j(t,n);function t(){var e=n.call(this,53106,"Room not found")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomNotFoundError=Qo;Object.defineProperty(A,53106,{value:Qo});var Go=function(n){j(t,n);function t(){var e=n.call(this,53107,"MaxParticipants is out of range")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomMaxParticipantsOutOfRangeError=Go;Object.defineProperty(A,53107,{value:Go});var Ko=function(n){j(t,n);function t(){var e=n.call(this,53108,"RoomType is not valid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomTypeInvalidError=Ko;Object.defineProperty(A,53108,{value:Ko});var zo=function(n){j(t,n);function t(){var e=n.call(this,53109,"Timeout is out of range")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomTimeoutOutOfRangeError=zo;Object.defineProperty(A,53109,{value:zo});var Yo=function(n){j(t,n);function t(){var e=n.call(this,53110,"StatusCallbackMethod is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomStatusCallbackMethodInvalidError=Yo;Object.defineProperty(A,53110,{value:Yo});var Jo=function(n){j(t,n);function t(){var e=n.call(this,53111,"StatusCallback is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomStatusCallbackInvalidError=Jo;Object.defineProperty(A,53111,{value:Jo});var Xo=function(n){j(t,n);function t(){var e=n.call(this,53112,"Status is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomStatusInvalidError=Xo;Object.defineProperty(A,53112,{value:Xo});var Zo=function(n){j(t,n);function t(){var e=n.call(this,53113,"Room exists")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomRoomExistsError=Zo;Object.defineProperty(A,53113,{value:Zo});var es=function(n){j(t,n);function t(){var e=n.call(this,53114,"Room creation parameter(s) incompatible with the Room type")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomInvalidParametersError=es;Object.defineProperty(A,53114,{value:es});var ts=function(n){j(t,n);function t(){var e=n.call(this,53115,"MediaRegion is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomMediaRegionInvalidError=ts;Object.defineProperty(A,53115,{value:ts});var rs=function(n){j(t,n);function t(){var e=n.call(this,53116,"There are no media servers available in the MediaRegion")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomMediaRegionUnavailableError=rs;Object.defineProperty(A,53116,{value:rs});var ns=function(n){j(t,n);function t(){var e=n.call(this,53117,"The subscription operation requested is not supported for the Room type")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomSubscriptionOperationNotSupportedError=ns;Object.defineProperty(A,53117,{value:ns});var is=function(n){j(t,n);function t(){var e=n.call(this,53118,"Room completed")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomCompletedError=is;Object.defineProperty(A,53118,{value:is});var as=function(n){j(t,n);function t(){var e=n.call(this,53124,"The AudioOnly flag is not supported for the Room type")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomAudioOnlyFlagNotSupportedError=as;Object.defineProperty(A,53124,{value:as});var os=function(n){j(t,n);function t(){var e=n.call(this,53125,"The track kind is not supported by the Room")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.RoomTrackKindNotSupportedError=os;Object.defineProperty(A,53125,{value:os});var ss=function(n){j(t,n);function t(){var e=n.call(this,53200,"Participant identity is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantIdentityInvalidError=ss;Object.defineProperty(A,53200,{value:ss});var cs=function(n){j(t,n);function t(){var e=n.call(this,53201,"Participant identity is too long")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantIdentityTooLongError=cs;Object.defineProperty(A,53201,{value:cs});var us=function(n){j(t,n);function t(){var e=n.call(this,53202,"Participant identity contains invalid characters")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantIdentityCharsInvalidError=us;Object.defineProperty(A,53202,{value:us});var ls=function(n){j(t,n);function t(){var e=n.call(this,53203,"The maximum number of published tracks allowed in the Room at the same time has been reached")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantMaxTracksExceededError=ls;Object.defineProperty(A,53203,{value:ls});var ds=function(n){j(t,n);function t(){var e=n.call(this,53204,"Participant not found")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantNotFoundError=ds;Object.defineProperty(A,53204,{value:ds});var fs=function(n){j(t,n);function t(){var e=n.call(this,53205,"Participant disconnected because of duplicate identity")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ParticipantDuplicateIdentityError=fs;Object.defineProperty(A,53205,{value:fs});var ps=function(n){j(t,n);function t(){var e=n.call(this,53300,"Track is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackInvalidError=ps;Object.defineProperty(A,53300,{value:ps});var vs=function(n){j(t,n);function t(){var e=n.call(this,53301,"Track name is invalid")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackNameInvalidError=vs;Object.defineProperty(A,53301,{value:vs});var hs=function(n){j(t,n);function t(){var e=n.call(this,53302,"Track name is too long")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackNameTooLongError=hs;Object.defineProperty(A,53302,{value:hs});var _s=function(n){j(t,n);function t(){var e=n.call(this,53303,"Track name contains invalid characters")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackNameCharsInvalidError=_s;Object.defineProperty(A,53303,{value:_s});var ms=function(n){j(t,n);function t(){var e=n.call(this,53304,"Track name is duplicated")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackNameIsDuplicatedError=ms;Object.defineProperty(A,53304,{value:ms});var ys=function(n){j(t,n);function t(){var e=n.call(this,53305,"The server has reached capacity and cannot fulfill this request")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.TrackServerTrackCapacityReachedError=ys;Object.defineProperty(A,53305,{value:ys});var gs=function(n){j(t,n);function t(){var e=n.call(this,53400,"Client is unable to create or apply a local media description")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaClientLocalDescFailedError=gs;Object.defineProperty(A,53400,{value:gs});var bs=function(n){j(t,n);function t(){var e=n.call(this,53401,"Server is unable to create or apply a local media description")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaServerLocalDescFailedError=bs;Object.defineProperty(A,53401,{value:bs});var Ss=function(n){j(t,n);function t(){var e=n.call(this,53402,"Client is unable to apply a remote media description")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaClientRemoteDescFailedError=Ss;Object.defineProperty(A,53402,{value:Ss});var ws=function(n){j(t,n);function t(){var e=n.call(this,53403,"Server is unable to apply a remote media description")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaServerRemoteDescFailedError=ws;Object.defineProperty(A,53403,{value:ws});var ks=function(n){j(t,n);function t(){var e=n.call(this,53404,"No supported codec")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaNoSupportedCodecError=ks;Object.defineProperty(A,53404,{value:ks});var Ts=function(n){j(t,n);function t(){var e=n.call(this,53405,"Media connection failed or Media activity ceased")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaConnectionError=Ts;Object.defineProperty(A,53405,{value:Ts});var Ps=function(n){j(t,n);function t(){var e=n.call(this,53407,"Media connection failed due to DTLS handshake failure")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.MediaDTLSTransportFailedError=Ps;Object.defineProperty(A,53407,{value:Ps});var Es=function(n){j(t,n);function t(){var e=n.call(this,53500,"Unable to acquire configuration")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ConfigurationAcquireFailedError=Es;Object.defineProperty(A,53500,{value:Es});var Os=function(n){j(t,n);function t(){var e=n.call(this,53501,"Unable to acquire TURN credentials")||this;return Object.setPrototypeOf(e,t.prototype),e}return t}(M);L.ConfigurationAcquireTurnFailedError=Os;Object.defineProperty(A,53501,{value:Os});Object.defineProperty(Ft,"__esModule",{value:!0});Ft.getTurnCredentials=void 0;var jv=Eo,Dv=U.ICE_VERSION,Cs=L,$v=Cs.createTwilioError,Nv=Cs.SignalingConnectionError,Fv=K;function Vv(n,t){return new Promise(function(e,r){var i=new Fv.EventEmitter,a={networkMonitor:null,eventObserver:i,helloBody:{edge:"roaming",preflight:!0,token:n,type:"ice",version:Dv}},o=new jv(t,a),s=!1;o.once("close",function(){s||(s=!0,r(new Nv))}),o.on("message",function(u){var c=u.code,l=u.message,f=u.ice_servers,p=u.type;(p==="iced"||p==="error")&&!s&&(s=!0,p==="iced"?e(f):r($v(c,l)),o.close())})})}Ft.getTurnCredentials=Vv;var Vt={},hi=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},_i=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n};Object.defineProperty(Vt,"__esModule",{value:!0});Vt.makeStat=void 0;function Bv(n){if(n&&n.length){var t=Math.min.apply(Math,_i([],hi(n))),e=Math.max.apply(Math,_i([],hi(n))),r=n.reduce(function(i,a){return i+a},0)/n.length;return{min:t,max:e,average:r}}return null}Vt.makeStat=Bv;var Bt={};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.syntheticAudio=void 0;function Uv(){var n=jt(),t={},e=n.getOrCreate(t),r=e.createOscillator(),i=r.connect(e.createMediaStreamDestination());r.start();var a=i.stream.getAudioTracks()[0],o=a.stop;return a.stop=function(){o.call(a),n.release(t)},a}Bt.syntheticAudio=Uv;var Ut={};Object.defineProperty(Ut,"__esModule",{value:!0});Ut.syntheticVideo=void 0;function Wv(n){var t=n===void 0?{}:n,e=t.width,r=e===void 0?640:e,i=t.height,a=i===void 0?480:i,o=Object.assign(document.createElement("canvas"),{width:r,height:a}),s=o.getContext("2d");s.fillStyle="green",s.fillRect(0,0,o.width,o.height);var u=!1;requestAnimationFrame(function p(){if(!u){var v=Math.round(Math.random()*255),_=Math.round(Math.random()*255),m=Math.round(Math.random()*255),y=Math.round(Math.random()*255);s.fillStyle="rgba("+v+", "+_+", "+m+", "+y+")",s.fillRect(Math.random()*r,Math.random()*a,50,50),requestAnimationFrame(p)}});var c=o.captureStream(30),l=c.getTracks()[0],f=l.stop;return l.stop=function(){u=!0,f.call(l)},l}Ut.syntheticVideo=Wv;var Hv=function(){function n(){Object.defineProperties(this,{_samples:{value:[{denominator:0,numerator:0},{denominator:0,numerator:0}]}})}return n.prototype.get=function(){var t=this._samples,e=t[1].denominator-t[0].denominator||1/0,r=t[1].numerator-t[0].numerator;return r/e},n.prototype.putSample=function(t,e){var r=this._samples;r.shift(),r.push({denominator:e,numerator:t})},n}(),Rs=Hv,qv=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Qv=K.EventEmitter,Gv=["signaling","room","media","quality","video-processor","preflight"],Kv=["debug","error","info","warning"],zv=function(n){qv(t,n);function t(e,r,i,a){a===void 0&&(a=null);var o=n.call(this)||this;return o.on("event",function(s){var u=s.name,c=s.group,l=s.level,f=s.payload;if(typeof u!="string")throw i.error("Unexpected name: ",u),new Error("Unexpected name: ",u);if(!Gv.includes(c))throw i.error("Unexpected group: ",c),new Error("Unexpected group: ",c);if(!Kv.includes(l))throw i.error("Unexpected level: ",l),new Error("Unexpected level: ",l);var p=Date.now(),v=p-r,_=Object.assign({elapsedTime:v,level:l},f||{});e.publish(c,u,_);var m=Object.assign({elapsedTime:v,group:c,level:l,name:u,timestamp:p},f?{payload:f}:{}),y={debug:"debug",error:"error",info:"info",warning:"warn"}[l];i[y]("event",m),a&&c==="signaling"&&a.emit("event",m)}),o}return t}(Qv),Ls=zv,Yv=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Le=d&&d.__assign||function(){return Le=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},Le.apply(this,arguments)},Jv=K.EventEmitter,Xv=R.getUserAgent,Zv=5,eh=50,th=1e3,mi=globalThis,rh=mi.WebSocket?mi.WebSocket:So(),xs=U,nh=xs.hardwareDevicePublisheriPad,ih=xs.hardwareDevicePublisheriPhone,ah=R,yi=Se,oh=function(n){Yv(t,n);function t(e,r,i,a,o,s){var u=n.call(this)||this;return s=Object.assign({gateway:sh(a,o)+"/v1/VideoEvents",maxReconnectAttempts:Zv,reconnectIntervalMs:eh,userAgent:Xv(),WebSocket:rh},s),Object.defineProperties(u,{_connectTimestamp:{value:0,writable:!0},_eventQueue:{value:[]},_readyToConnect:{value:ah.defer()},_reconnectAttemptsLeft:{value:s.maxReconnectAttempts,writable:!0},_ws:{value:null,writable:!0},_WebSocket:{value:s.WebSocket}}),u._readyToConnect.promise.then(function(c){var l=c.roomSid,f=c.participantSid,p=u;u.on("disconnected",function v(_){if(p._session=null,_&&p._reconnectAttemptsLeft>0){p.emit("reconnecting"),uh(p,e,r,i,l,f,s);return}p.removeListener("disconnected",v)}),Nr(u,e,r,i,l,f,s)}).catch(function(){}),u}return t.prototype.connect=function(e,r){this._readyToConnect.resolve({roomSid:e,participantSid:r})},t.prototype._publish=function(e){e.session=this._session,this._ws.send(JSON.stringify(e))},t.prototype.disconnect=function(){if(this._ws===null||this._ws.readyState===this._WebSocket.CLOSING||this._ws.readyState===this._WebSocket.CLOSED)return!1;try{this._ws.close()}catch{}return this.emit("disconnected"),!0},t.prototype.publish=function(e,r,i){if(this._ws!==null&&(this._ws.readyState===this._WebSocket.CLOSING||this._ws.readyState===this._WebSocket.CLOSED))return!1;var a=typeof this._session=="string"?this._publish.bind(this):this._eventQueue.push.bind(this._eventQueue);return a({group:e,name:r,payload:i,timestamp:Date.now(),type:"event",version:1}),!0},t}(Jv);function Nr(n,t,e,r,i,a,o){n._connectTimestamp=Date.now(),n._reconnectAttemptsLeft--,n._ws=new o.WebSocket(o.gateway);var s=n._ws;s.addEventListener("close",function(u){if(u.code===th){n.emit("disconnected");return}n.emit("disconnected",new Error("WebSocket Error "+u.code+": "+u.reason))}),s.addEventListener("message",function(u){ch(n,JSON.parse(u.data),o)}),s.addEventListener("open",function(){var u={type:"connect",token:t,version:1};u.publisher={name:e,sdkVersion:r,userAgent:o.userAgent,participantSid:a,roomSid:i},yi.isIpad()?u.publisher=Le(Le({},u.publisher),nh):yi.isIphone()&&(u.publisher=Le(Le({},u.publisher),ih)),s.send(JSON.stringify(u))})}function sh(n,t){return n==="prod"?"wss://sdkgw."+t+".twilio.com":"wss://sdkgw."+n+"-"+t+".twilio.com"}function ch(n,t,e){switch(t.type){case"connected":n._session=t.session,n._reconnectAttemptsLeft=e.maxReconnectAttempts,n._eventQueue.splice(0).forEach(n._publish,n),n.emit("connected");break;case"error":n._ws.close(),n.emit("disconnected",new Error(t.message));break}}function uh(n,t,e,r,i,a,o){var s=Date.now()-n._connectTimestamp,u=o.reconnectIntervalMs-s;if(u>0){setTimeout(function(){Nr(n,t,e,r,i,a,o)},u);return}Nr(n,t,e,r,i,a,o)}var As=oh,lh=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),kt=d&&d.__assign||function(){return kt=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},kt.apply(this,arguments)},We=d&&d.__awaiter||function(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(l){try{c(r.next(l))}catch(f){o(f)}}function u(l){try{c(r.throw(l))}catch(f){o(f)}}function c(l){l.done?a(l.value):i(l.value).then(s,u)}c((r=r.apply(n,t||[])).next())})},He=d&&d.__generator||function(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){e.label=c[1];break}if(c[0]===6&&e.label<a[1]){e.label=a[1],a=c;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(c);break}a[2]&&e.ops.pop(),e.trys.pop();continue}c=t.call(n,e)}catch(l){c=[6,l],i=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}};Object.defineProperty(Me,"__esModule",{value:!0});Me.runPreflight=Me.PreflightTest=void 0;var Pe=U,Ee=$t,dh=je,fh=Nt,ph=Ft,lt=Vt,vh=Bt,hh=Ut,_h=R,mh=U.WS_SERVER,yh=ce,gh=et,sr=Rs,bh=Ls,Sh=As,Is=xt,wh=Is.createSID,kh=Is.sessionSID,Ms=L,Th=Ms.SignalingConnectionTimeoutError,gi=Ms.MediaConnectionError,js=1e3,Ph=10*js,he={mediaAcquired:"mediaAcquired",connected:"connected",mediaSubscribed:"mediaSubscribed",mediaStarted:"mediaStarted",dtlsConnected:"dtlsConnected",peerConnectionConnected:"peerConnectionConnected",iceConnected:"iceConnected"};function Eh(n){return n!==null&&typeof n<"u"}var Oh=0,Ds=function(n){lh(t,n);function t(e,r){var i=n.call(this)||this;i._testTiming=new Ee.Timer,i._dtlsTiming=new Ee.Timer,i._iceTiming=new Ee.Timer,i._peerConnectionTiming=new Ee.Timer,i._mediaTiming=new Ee.Timer,i._connectTiming=new Ee.Timer,i._sentBytesMovingAverage=new sr,i._packetLossMovingAverage=new sr,i._progressEvents=[],i._receivedBytesMovingAverage=new sr;var a=r,o=a.environment,s=o===void 0?"prod":o,u=a.region,c=u===void 0?"gll":u,l=a.duration,f=l===void 0?Ph:l,p=a.wsServer||mh(s,c);return i._log=new yh("default",i,Pe.DEFAULT_LOG_LEVEL,Pe.DEFAULT_LOGGER_NAME),i._testDuration=f,i._instanceId=Oh++,i._testTiming.start(),i._runPreflightTest(e,s,p),i}return t.prototype.toString=function(){return"[Preflight #"+this._instanceId+"]"},t.prototype.stop=function(){this._stopped=!0},t.prototype._generatePreflightReport=function(e){return this._testTiming.stop(),{testTiming:this._testTiming.getTimeMeasurement(),networkTiming:{dtls:this._dtlsTiming.getTimeMeasurement(),ice:this._iceTiming.getTimeMeasurement(),peerConnection:this._peerConnectionTiming.getTimeMeasurement(),connect:this._connectTiming.getTimeMeasurement(),media:this._mediaTiming.getTimeMeasurement()},stats:{jitter:lt.makeStat(e==null?void 0:e.jitter),rtt:lt.makeStat(e==null?void 0:e.rtt),packetLoss:lt.makeStat(e==null?void 0:e.packetLoss)},selectedIceCandidatePairStats:e?e.selectedIceCandidatePairStats:null,iceCandidateStats:e?e.iceCandidateStats:[],progressEvents:this._progressEvents,mos:lt.makeStat(e==null?void 0:e.mos)}},t.prototype._executePreflightStep=function(e,r,i){return We(this,void 0,void 0,function(){var a,o,s,u,c;return He(this,function(l){switch(l.label){case 0:if(this._log.debug("Executing step: ",e),a=this._testDuration+10*js,this._stopped)throw new Error("stopped");o=Promise.resolve().then(r),s=null,u=new Promise(function(f,p){s=setTimeout(function(){p(i||new Error(e+" timeout."))},a)}),l.label=1;case 1:return l.trys.push([1,,3,4]),[4,Promise.race([u,o])];case 2:return c=l.sent(),[2,c];case 3:return s!==null&&clearTimeout(s),[7];case 4:return[2]}})})},t.prototype._collectNetworkTimings=function(e){var r=this;return new Promise(function(i){var a;e.addEventListener("iceconnectionstatechange",function(){e.iceConnectionState==="checking"&&r._iceTiming.start(),e.iceConnectionState==="connected"&&(r._iceTiming.stop(),r._updateProgress(he.iceConnected),(!a||a&&a.state==="connected")&&i())}),e.addEventListener("connectionstatechange",function(){e.connectionState==="connecting"&&r._peerConnectionTiming.start(),e.connectionState==="connected"&&(r._peerConnectionTiming.stop(),r._updateProgress(he.peerConnectionConnected))});var o=e.getSenders(),s=o.map(function(u){return u.transport}).find(Eh);typeof s<"u"&&(a=s,a.addEventListener("statechange",function(){a.state==="connecting"&&r._dtlsTiming.start(),a.state==="connected"&&(r._dtlsTiming.stop(),r._updateProgress(he.dtlsConnected),e.iceConnectionState==="connected"&&i())}))})},t.prototype._setupInsights=function(e){var r=e.token,i=e.environment,a=i===void 0?Pe.DEFAULT_ENVIRONMENT:i,o=e.realm,s=o===void 0?Pe.DEFAULT_REALM:o,u={},c=new Sh(r,Pe.SDK_NAME,Pe.SDK_VERSION,a,s,u);c.connect("PREFLIGHT_ROOM_SID","PREFLIGHT_PARTICIPANT");var l=new bh(c,Date.now(),this._log),f=void 0;return{reportToInsights:function(p){var v,_,m=p.report,y=m.stats.jitter||f,P=m.stats.rtt||f,h=m.stats.packetLoss||f,b=m.mos||f,g=new Map;m.iceCandidateStats.forEach(function(w){if(w.candidateType&&w.protocol){var O=g.get(w.candidateType)||[];O.indexOf(w.protocol)<0&&O.push(w.protocol),g.set(w.candidateType,O)}});var T=JSON.stringify(Object.fromEntries(g)),S={name:"report",group:"preflight",level:m.error?"error":"info",payload:{sessionSID:kh,preflightSID:wh("PF"),progressEvents:JSON.stringify(m.progressEvents),testTiming:m.testTiming,dtlsTiming:m.networkTiming.dtls,iceTiming:m.networkTiming.ice,peerConnectionTiming:m.networkTiming.peerConnection,connectTiming:m.networkTiming.connect,mediaTiming:m.networkTiming.media,selectedLocalCandidate:(v=m.selectedIceCandidatePairStats)===null||v===void 0?void 0:v.localCandidate,selectedRemoteCandidate:(_=m.selectedIceCandidatePairStats)===null||_===void 0?void 0:_.remoteCandidate,iceCandidateStats:T,jitterStats:y,rttStats:P,packetLossStats:h,mosStats:b,error:m.error}};l.emit("event",S),setTimeout(function(){return c.disconnect()},2e3)}}},t.prototype._runPreflightTest=function(e,r,i){return We(this,void 0,void 0,function(){var a,o,s,u,c,l,f,p,v,_,m,y,P=this;return He(this,function(h){switch(h.label){case 0:a=[],o=[],s=this._setupInsights({token:e,environment:r}).reportToInsights,h.label=1;case 1:return h.trys.push([1,8,9,10]),u=[],[4,this._executePreflightStep("Acquire media",function(){return[vh.syntheticAudio(),hh.syntheticVideo({width:640,height:480})]})];case 2:return a=h.sent(),this._updateProgress(he.mediaAcquired),this.emit("debug",{localTracks:a}),this._connectTiming.start(),[4,this._executePreflightStep("Get turn credentials",function(){return ph.getTurnCredentials(e,i)},new Th)];case 3:return c=h.sent(),this._connectTiming.stop(),this._updateProgress(he.connected),l=new RTCPeerConnection({iceServers:c,iceTransportPolicy:"relay",bundlePolicy:"max-bundle"}),f=new RTCPeerConnection({iceServers:c,bundlePolicy:"max-bundle"}),o.push(l),o.push(f),this._mediaTiming.start(),[4,this._executePreflightStep("Setup Peer Connections",function(){return We(P,void 0,void 0,function(){var b,g,T,S;return He(this,function(w){switch(w.label){case 0:return l.addEventListener("icecandidate",function(O){return O.candidate&&f.addIceCandidate(O.candidate)}),f.addEventListener("icecandidate",function(O){return O.candidate&&l.addIceCandidate(O.candidate)}),a.forEach(function(O){return l.addTrack(O)}),b=new Promise(function(O){var D=[];f.addEventListener("track",function(F){D.push(F.track),D.length===a.length&&O(D)})}),[4,l.createOffer()];case 1:return g=w.sent(),T=g,[4,l.setLocalDescription(T)];case 2:return w.sent(),[4,f.setRemoteDescription(T)];case 3:return w.sent(),[4,f.createAnswer()];case 4:return S=w.sent(),[4,f.setLocalDescription(S)];case 5:return w.sent(),[4,l.setRemoteDescription(S)];case 6:return w.sent(),[4,this._collectNetworkTimings(l)];case 7:return w.sent(),[2,b]}})})},new gi)];case 4:return p=h.sent(),this.emit("debug",{remoteTracks:p}),p.forEach(function(b){b.addEventListener("ended",function(){return P._log.warn(b.kind+":ended")}),b.addEventListener("mute",function(){return P._log.warn(b.kind+":muted")}),b.addEventListener("unmute",function(){return P._log.warn(b.kind+":unmuted")})}),this._updateProgress(he.mediaSubscribed),[4,this._executePreflightStep("Wait for tracks to start",function(){return new Promise(function(b){var g=document.createElement("video");g.autoplay=!0,g.playsInline=!0,g.muted=!0,g.srcObject=new MediaStream(p),u.push(g),P.emit("debugElement",g),g.oncanplay=b})},new gi)];case 5:return h.sent(),this._mediaTiming.stop(),this._updateProgress(he.mediaStarted),[4,this._executePreflightStep("Collect stats for duration",function(){return P._collectRTCStatsForDuration(P._testDuration,Ch(),l,f)})];case 6:return v=h.sent(),[4,this._executePreflightStep("Generate report",function(){return P._generatePreflightReport(v)})];case 7:return _=h.sent(),s({report:_}),this.emit("completed",_),[3,10];case 8:return m=h.sent(),y=this._generatePreflightReport(),s({report:kt(kt({},y),{error:m==null?void 0:m.toString()})}),this.emit("failed",m,y),[3,10];case 9:return o.forEach(function(b){return b.close()}),a.forEach(function(b){return b.stop()}),[7];case 10:return[2]}})})},t.prototype._collectRTCStats=function(e,r,i){return We(this,void 0,void 0,function(){var a,o,s,u,c,l,f,p,v,_,m,y,P,h;return He(this,function(b){switch(b.label){case 0:return[4,fh.getCombinedConnectionStats({publisher:r,subscriber:i})];case 1:return a=b.sent(),o=a.timestamp,s=a.bytesSent,u=a.bytesReceived,c=a.packets,l=a.packetsLost,f=a.roundTripTime,p=a.jitter,v=a.selectedIceCandidatePairStats,_=a.iceCandidateStats,m=e.jitter.length>0,e.jitter.push(p),e.rtt.push(f),this._sentBytesMovingAverage.putSample(s,o),this._receivedBytesMovingAverage.putSample(u,o),this._packetLossMovingAverage.putSample(l,c),m&&(e.outgoingBitrate.push(this._sentBytesMovingAverage.get()*1e3*8),e.incomingBitrate.push(this._receivedBytesMovingAverage.get()*1e3*8),y=this._packetLossMovingAverage.get(),P=Math.min(100,y*100),e.packetLoss.push(P),h=dh.calculateMOS(f,p,y),e.mos.push(h)),e.selectedIceCandidatePairStats||(e.selectedIceCandidatePairStats=v),e.iceCandidateStats.length===0&&(e.iceCandidateStats=_),[2]}})})},t.prototype._collectRTCStatsForDuration=function(e,r,i,a){return We(this,void 0,void 0,function(){var o,s,u;return He(this,function(c){switch(c.label){case 0:return o=Date.now(),s=Math.min(1e3,e),[4,_h.waitForSometime(s)];case 1:return c.sent(),[4,this._collectRTCStats(r,i,a)];case 2:return c.sent(),u=e-(Date.now()-o),u>0?[4,this._collectRTCStatsForDuration(u,r,i,a)]:[3,4];case 3:r=c.sent(),c.label=4;case 4:return[2,r]}})})},t.prototype._updateProgress=function(e){var r=Date.now()-this._testTiming.getTimeMeasurement().start;this._progressEvents.push({duration:r,name:e}),this.emit("progress",e)},t}(gh);Me.PreflightTest=Ds;function Ch(){return{mos:[],jitter:[],rtt:[],outgoingBitrate:[],incomingBitrate:[],packetLoss:[],selectedIceCandidatePairStats:null,iceCandidateStats:[]}}function Rh(n,t){t===void 0&&(t={});var e=new Ds(n,t);return e}Me.runPreflight=Rh;var cr=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},ur=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Lh=function(){function n(t,e){var r=this;Object.defineProperties(this,{_isCancelable:{writable:!0,value:!0},_isCanceled:{writable:!0,value:!1},_onCancel:{value:e}}),Object.defineProperty(this,"_promise",{value:new Promise(function(i,a){t(function(o){r._isCancelable=!1,i(o)},function(o){r._isCancelable=!1,a(o)},function(){return r._isCanceled})})})}return n.reject=function(t){return new n(function(r,i){i(t)},function(){})},n.resolve=function(t){return new n(function(r){r(t)},function(){})},n.prototype.cancel=function(){return this._isCancelable&&(this._isCanceled=!0,this._onCancel()),this},n.prototype.catch=function(){var t=[].slice.call(arguments),e=this._promise;return new n(function(i,a){e.catch.apply(e,ur([],cr(t))).then(i,a)},this._onCancel)},n.prototype.then=function(){var t=[].slice.call(arguments),e=this._promise;return new n(function(i,a){e.then.apply(e,ur([],cr(t))).then(i,a)},this._onCancel)},n.prototype.finally=function(){var t=[].slice.call(arguments),e=this._promise;return new n(function(i,a){e.finally.apply(e,ur([],cr(t))).then(i,a)},this._onCancel)},n}(),un=Lh,bi=un;function xh(n,t,e,r){var i,a=new Error("Canceled");return new bi(function(s,u,c){var l;n(function(p){return c()?bi.reject(a):(l=t(p),e(l).then(function(_){if(c())throw a;return i=_(),i}))}).then(function(p){if(c())throw p.disconnect(),a;s(r(l,p))}).catch(function(p){u(p)})},function(){i&&i.cancel()})}var Ah=xh,Ih=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Mh=K.EventEmitter,jh=function(n){Ih(t,n);function t(e,r){var i=n.call(this)||this;return e=Object.assign({maxAudioBitrate:null,maxVideoBitrate:null},e),Object.defineProperties(i,{maxAudioBitrate:{value:e.maxAudioBitrate,writable:!0},maxVideoBitrate:{value:e.maxVideoBitrate,writable:!0},adaptiveSimulcast:{value:r}}),i}return t.prototype.toJSON=function(){return{maxAudioBitrate:this.maxAudioBitrate,maxVideoBitrate:this.maxVideoBitrate}},t.prototype.update=function(e){var r=this;e=Object.assign({maxAudioBitrate:this.maxAudioBitrate,maxVideoBitrate:this.maxVideoBitrate},e);var i=["maxAudioBitrate","maxVideoBitrate"].reduce(function(a,o){return r[o]!==e[o]&&(r[o]=e[o],a=!0),a},!1);i&&this.emit("changed")},t}(Mh),Dh=jh,tt={},$h=R.isNonArrayObject,Ne=U,Qe=Ne.typeErrors,Nh=Ne.clientTrackSwitchOffControl,Fh=Ne.videoContentPreferencesMode,Vh=Ne.subscriptionMode,$s=Ne.trackPriority,Bh=Ne.trackSwitchOffMode;function Uh(n){var t=ze(n,"options.bandwidthProfile");return!n||t||(t=ze(n.video,"options.bandwidthProfile.video",[{prop:"contentPreferencesMode",values:Object.values(Fh)},{prop:"dominantSpeakerPriority",values:Object.values($s)},{prop:"maxSubscriptionBitrate",type:"number"},{prop:"maxTracks",type:"number"},{prop:"mode",values:Object.values(Vh)},{prop:"clientTrackSwitchOffControl",values:Object.values(Nh)},{prop:"trackSwitchOffMode",values:Object.values(Bh)}]),t)?t:n.video?"maxTracks"in n.video&&"clientTrackSwitchOffControl"in n.video?new TypeError("options.bandwidthProfile.video.maxTracks is deprecated. Use options.bandwidthProfile.video.clientTrackSwitchOffControl instead."):"renderDimensions"in n.video&&"contentPreferencesMode"in n.video?new TypeError("options.bandwidthProfile.video.renderDimensions is deprecated. Use options.bandwidthProfile.video.contentPreferencesMode instead."):Hh(n.video.renderDimensions):null}function Wh(n,t){if(!(n instanceof t.LocalAudioTrack||n instanceof t.LocalDataTrack||n instanceof t.LocalVideoTrack||n instanceof t.MediaStreamTrack))throw Qe.INVALID_TYPE("track","LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack")}function ze(n,t,e){return e===void 0&&(e=[]),typeof n>"u"?null:n===null||!$h(n)?Qe.INVALID_TYPE(t,"object"):e.reduce(function(r,i){var a=i.prop,o=i.type,s=i.values;if(r||!(a in n))return r;var u=n[a];return o&&typeof u!==o||o==="number"&&isNaN(u)?Qe.INVALID_TYPE(t+"."+a,o):Array.isArray(s)&&!s.includes(u)?Qe.INVALID_VALUE(t+"."+a,s):r},null)}function Hh(n){var t="options.bandwidthProfile.video.renderDimensions",e=ze(n,t);return n?e||Object.values($s).reduce(function(r,i){return r||ze(n[i],t+"."+i,[{prop:"height",type:"number"},{prop:"width",type:"number"}])},null):e}tt.validateBandwidthProfile=Uh;tt.validateLocalTrack=Wh;tt.validateObject=ze;var qh=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Qh=et,Ns=R,Gh=Ns.buildLogLevels,Kh=Ns.valueToJSON,zh=U.DEFAULT_LOG_LEVEL,Yh=ce,Jh=0,Xh=function(n){qh(t,n);function t(e,r,i){var a=n.call(this)||this;i=Object.assign({logLevel:zh},i);var o=Gh(i.logLevel);return Object.defineProperties(a,{_instanceId:{value:Jh++},_log:{value:i.log?i.log.createLog("default",a):new Yh("default",a,o,i.loggerName)},trackName:{enumerable:!0,value:e},trackSid:{enumerable:!0,value:r}}),a}return t.prototype.toJSON=function(){return Kh(this)},t.prototype.toString=function(){return"[TrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(Qh),Fs=Xh,Zh=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),e_=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},t_=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},r_=Fs,Vs=U,n_=Vs.typeErrors,i_=Vs.trackPriority,a_=function(n){Zh(t,n);function t(e,r,i,a){var o=n.call(this,r.name,e.sid,a)||this;return Object.defineProperties(o,{_reemitSignalingEvent:{value:function(){for(var s=[],u=0;u<arguments.length;u++)s[u]=arguments[u];return o.emit.apply(o,t_([s&&s.length?"warning":"warningsCleared"],e_(s)))}},_reemitTrackEvent:{value:function(){return o.emit(o.isTrackEnabled?"trackEnabled":"trackDisabled")}},_signaling:{value:e},_unpublish:{value:i},isTrackEnabled:{enumerable:!0,get:function(){return this.track.kind==="data"?!0:this.track.isEnabled}},kind:{enumerable:!0,value:r.kind},priority:{enumerable:!0,get:function(){return e.updatedPriority}},track:{enumerable:!0,value:r}}),["disabled","enabled"].forEach(function(s){return r.on(s,o._reemitTrackEvent)}),["warning","warningsCleared"].forEach(function(s){return e.on(s,o._reemitSignalingEvent)}),o}return t.prototype.toString=function(){return"[LocalTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t.prototype.setPriority=function(e){var r=Object.values(i_);if(!r.includes(e))throw n_.INVALID_VALUE("priority",r);return this._signaling.setPriority(e),this},t.prototype.unpublish=function(){var e=this;return["disabled","enabled"].forEach(function(r){return e.track.removeListener(r,e._reemitTrackEvent)}),["warning","warningsCleared"].forEach(function(r){return e._signaling.removeListener(r,e._reemitSignalingEvent)}),this._unpublish(this),this},t}(r_),ln=a_,o_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),s_=ln,c_=function(n){o_(t,n);function t(e,r,i,a){return n.call(this,e,r,i,a)||this}return t.prototype.toString=function(){return"[LocalAudioTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(s_),u_=c_,l_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),d_=ln,f_=function(n){l_(t,n);function t(e,r,i,a){return n.call(this,e,r,i,a)||this}return t.prototype.toString=function(){return"[LocalDataTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(d_),p_=f_,v_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),h_=ln,__=function(n){v_(t,n);function t(e,r,i,a){return n.call(this,e,r,i,a)||this}return t.prototype.toString=function(){return"[LocalVideoTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(h_),m_=__,y_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),g_=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},b_=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Bs=U,S_=Bs.typeErrors,w_=Bs.trackPriority,k_=Se.isIOS,Si=rn;function T_(n){return function(t){y_(e,t);function e(r,i,a,o,s,u,c){var l=this;return c=Object.assign({workaroundWebKitBug212780:k_()&&typeof document=="object"&&typeof document.addEventListener=="function"&&typeof document.visibilityState=="string"},c),l=t.call(this,i,c)||this,Object.defineProperties(l,{_isEnabled:{value:a,writable:!0},_isSwitchedOff:{value:o,writable:!0},_priority:{value:null,writable:!0},_setPriority:{value:s},_setRenderHint:{value:function(f){l._log.debug("updating render hint:",f),u(f)}},isEnabled:{enumerable:!0,get:function(){return this._isEnabled}},isSwitchedOff:{enumerable:!0,get:function(){return this._isSwitchedOff}},priority:{enumerable:!0,get:function(){return this._priority}},sid:{enumerable:!0,value:r},_workaroundWebKitBug212780:{value:c.workaroundWebKitBug212780},_workaroundWebKitBug212780Cleanup:{value:null,writable:!0}}),l}return e.prototype.setPriority=function(r){var i=b_([null],g_(Object.values(w_)));if(!i.includes(r))throw S_.INVALID_VALUE("priority",i);return this._priority!==r&&(this._priority=r,this._setPriority(r)),this},e.prototype._setEnabled=function(r){this._isEnabled!==r&&(this._isEnabled=r,this.emit(this._isEnabled?"enabled":"disabled",this))},e.prototype._setSwitchedOff=function(r){this._isSwitchedOff!==r&&(this._isSwitchedOff=r,this.emit(r?"switchedOff":"switchedOn",this))},e.prototype.attach=function(r){var i=t.prototype.attach.call(this,r);return this.mediaStreamTrack.enabled!==!0&&(this.mediaStreamTrack.enabled=!0,this.processedTrack&&(this.processedTrack.enabled=!0),this.processor&&this._captureFrames()),this._workaroundWebKitBug212780&&(this._workaroundWebKitBug212780Cleanup=this._workaroundWebKitBug212780Cleanup||P_(this)),i},e.prototype.detach=function(r){var i=t.prototype.detach.call(this,r);return this._attachments.size===0&&(this.mediaStreamTrack.enabled=!1,this.processedTrack&&(this.processedTrack.enabled=!1),this._workaroundWebKitBug212780Cleanup&&(this._workaroundWebKitBug212780Cleanup(),this._workaroundWebKitBug212780Cleanup=null)),i},e}(n)}function P_(n){var t=n._log,e=n.kind;function r(i){i&&n._attachments.forEach(function(a){var o=n._elShims.get(a),s=a.paused&&o&&!o.pausedIntentionally();s&&(t.info("Playing inadvertently paused <"+e+"> element"),t.debug("Element:",a),t.debug("RemoteMediaTrack:",n),a.play().then(function(){t.info("Successfully played inadvertently paused <"+e+"> element"),t.debug("Element:",a),t.debug("RemoteMediaTrack:",n)}).catch(function(u){t.warn("Error while playing inadvertently paused <"+e+"> element:",{err:u,el:a,remoteMediaTrack:n})}))})}return Si.onVisibilityChange(2,r),function(){Si.offVisibilityChange(2,r)}}var Us=T_,E_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),O_=no,C_=Us,R_=C_(O_),L_=function(n){E_(t,n);function t(e,r,i,a,o,s,u){return n.call(this,e,r,i,a,o,s,u)||this}return t.prototype.toString=function(){return"[RemoteAudioTrack #"+this._instanceId+": "+this.sid+"]"},t.prototype._start=function(){n.prototype._start.call(this),this._dummyEl&&(this._dummyEl.srcObject=null,this._dummyEl=null)},t.prototype.setPriority=function(e){return n.prototype.setPriority.call(this,e)},t}(R_),x_=L_,A_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),I_=Fs,M_=function(n){A_(t,n);function t(e,r){var i=n.call(this,e.name,e.sid,r)||this;Object.defineProperties(i,{_signaling:{value:e},_track:{value:null,writable:!0},isSubscribed:{enumerable:!0,get:function(){return!!this._track}},isTrackEnabled:{enumerable:!0,get:function(){return e.isEnabled}},kind:{enumerable:!0,value:e.kind},publishPriority:{enumerable:!0,get:function(){return e.priority}},track:{enumerable:!0,get:function(){return this._track}}});var a=e.error,o=e.isEnabled,s=e.isSwitchedOff,u=e.priority;return e.on("updated",function(){if(a!==e.error){a=e.error,i.emit("subscriptionFailed",e.error);return}o!==e.isEnabled&&(o=e.isEnabled,i.track&&i.track._setEnabled(e.isEnabled),i.emit(e.isEnabled?"trackEnabled":"trackDisabled")),s!==e.isSwitchedOff&&(i._log.debug(i.trackSid+": "+(s?"OFF":"ON")+" => "+(e.isSwitchedOff?"OFF":"ON")),s=e.isSwitchedOff,i.track?(i.track._setSwitchedOff(e.isSwitchedOff),i.emit(s?"trackSwitchedOff":"trackSwitchedOn",i.track)):s&&i._log.warn("Track was not subscribed when switched Off.")),u!==e.priority&&(u=e.priority,i.emit("publishPriorityChanged",u))}),i}return t.prototype.toString=function(){return"[RemoteTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t.prototype._subscribed=function(e){!this._track&&e&&(this._track=e,this.emit("subscribed",e))},t.prototype._unsubscribe=function(){if(this._track){var e=this._track;this._track=null,this.emit("unsubscribed",e)}},t}(I_),dn=M_,j_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),D_=dn,$_=function(n){j_(t,n);function t(e,r){return n.call(this,e,r)||this}return t.prototype.toString=function(){return"[RemoteAudioTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(D_),N_=$_,F_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),V_=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},B_=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},U_=tn,Ws=U,W_=Ws.typeErrors,H_=Ws.trackPriority,q_=function(n){F_(t,n);function t(e,r,i){var a=n.call(this,r.id,"data",i)||this;return Object.defineProperties(a,{_isSwitchedOff:{value:!1,writable:!0},_priority:{value:null,writable:!0},isEnabled:{enumerable:!0,value:!0},isSwitchedOff:{enumerable:!0,get:function(){return this._isSwitchedOff}},maxPacketLifeTime:{enumerable:!0,value:r.maxPacketLifeTime},maxRetransmits:{enumerable:!0,value:r.maxRetransmits},ordered:{enumerable:!0,value:r.ordered},priority:{enumerable:!0,get:function(){return this._priority}},reliable:{enumerable:!0,value:r.maxPacketLifeTime===null&&r.maxRetransmits===null},sid:{enumerable:!0,value:e}}),r.on("message",function(o){a.emit("message",o,a)}),a}return t.prototype.setPriority=function(e){var r=B_([null],V_(Object.values(H_)));if(!r.includes(e))throw W_.INVALID_VALUE("priority",r);return this._priority=e,this},t.prototype._setEnabled=function(){},t.prototype._setSwitchedOff=function(e){this._isSwitchedOff!==e&&(this._isSwitchedOff=e,this.emit(e?"switchedOff":"switchedOn",this))},t}(U_),Q_=q_,G_=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),K_=dn,z_=function(n){G_(t,n);function t(e,r){return n.call(this,e,r)||this}return t.prototype.toString=function(){return"[RemoteDataTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(K_),Y_=z_,Hs=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),fn=function(){function n(t){Object.defineProperties(this,{_callback:{value:t}})}return n.prototype.observe=function(){},n.prototype.unobserve=function(){},n.prototype.makeVisible=function(t){var e=this._makeFakeEntry(t,!0);this._callback([e])},n.prototype.makeInvisible=function(t){var e=this._makeFakeEntry(t,!1);this._callback([e])},n.prototype._makeFakeEntry=function(t,e){return{target:t,isIntersecting:e}},n}(),J_=function(n){Hs(t,n);function t(){return n!==null&&n.apply(this,arguments)||this}return t}(fn),X_=function(n){Hs(t,n);function t(){return n!==null&&n.apply(this,arguments)||this}return t.prototype.resize=function(e){var r=this._makeFakeEntry(e,!0);this._callback([r])},t}(fn),Z_={NullIntersectionObserver:J_,NullResizeObserver:X_,NullObserver:fn},em=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),tm=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},rm=Us,nm=vo,wi=rn,ki=Z_.NullObserver,im=we,am=rm(nm),om=50,sm=function(n){em(t,n);function t(e,r,i,a,o,s,u){var c=this;return u=Object.assign({clientTrackSwitchOffControl:"auto",contentPreferencesMode:"auto",enableDocumentVisibilityTurnOff:!0},u),u=Object.assign({IntersectionObserver:typeof IntersectionObserver>"u"||u.clientTrackSwitchOffControl!=="auto"?ki:IntersectionObserver,ResizeObserver:typeof ResizeObserver>"u"||u.contentPreferencesMode!=="auto"?ki:ResizeObserver},u),c=n.call(this,e,r,i,a,o,s,u)||this,Object.defineProperties(c,{_enableDocumentVisibilityTurnOff:{value:u.enableDocumentVisibilityTurnOff===!0&&u.clientTrackSwitchOffControl==="auto"},_documentVisibilityTurnOffCleanup:{value:null,writable:!0},_clientTrackSwitchOffControl:{value:u.clientTrackSwitchOffControl},_contentPreferencesMode:{value:u.contentPreferencesMode},_invisibleElements:{value:new WeakSet},_elToPipCallbacks:{value:new WeakMap},_elToPipWindows:{value:new WeakMap},_turnOffTimer:{value:new im(function(){c._setRenderHint({enabled:!1})},om,!1)},_resizeObserver:{value:new u.ResizeObserver(function(l){var f=l.find(function(p){return!c._invisibleElements.has(p.target)});f&&dt(c)})},_intersectionObserver:{value:new u.IntersectionObserver(function(l){var f=!1;l.forEach(function(p){var v=!c._invisibleElements.has(p.target);v!==p.isIntersecting&&(p.isIntersecting?(c._log.debug("intersectionObserver detected: Off => On"),c._invisibleElements.delete(p.target)):(c._log.debug("intersectionObserver detected: On => Off"),c._invisibleElements.add(p.target)),f=!0)}),f&&(Re(c),dt(c))},{threshold:.25})}}),c}return t.prototype._start=function(e){var r=n.prototype._start.call(this,e);return Re(this),r},t.prototype.switchOn=function(){if(this._clientTrackSwitchOffControl!=="manual")throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');return this._setRenderHint({enabled:!0}),this},t.prototype.switchOff=function(){if(this._clientTrackSwitchOffControl!=="manual")throw new Error('Invalid state. You can call switchOff only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');return this._setRenderHint({enabled:!1}),this},t.prototype.setContentPreferences=function(e){if(this._contentPreferencesMode!=="manual")throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.contentPreferencesMode is set to "manual"');return e.renderDimensions&&this._setRenderHint({renderDimensions:e.renderDimensions}),this},t.prototype._unObservePip=function(e){var r=this._elToPipCallbacks.get(e);r&&(e.removeEventListener("enterpictureinpicture",r.onEnterPip),e.removeEventListener("leavepictureinpicture",r.onLeavePip),this._elToPipCallbacks.delete(e))},t.prototype._observePip=function(e){var r=this,i=this._elToPipCallbacks.get(e);if(!i){var a=function(u){return r._onEnterPip(u,e)},o=function(u){return r._onLeavePip(u,e)},s=function(u){return r._onResizePip(u,e)};e.addEventListener("enterpictureinpicture",a),e.addEventListener("leavepictureinpicture",o),this._elToPipCallbacks.set(e,{onEnterPip:a,onLeavePip:o,onResizePip:s})}},t.prototype._onEnterPip=function(e,r){this._log.debug("onEnterPip");var i=e.pictureInPictureWindow;this._elToPipWindows.set(r,i);var a=this._elToPipCallbacks.get(r).onResizePip;i.addEventListener("resize",a),Re(this)},t.prototype._onLeavePip=function(e,r){this._log.debug("onLeavePip"),this._elToPipWindows.delete(r);var i=this._elToPipCallbacks.get(r).onResizePip,a=e.pictureInPictureWindow;a.removeEventListener("resize",i),Re(this)},t.prototype._onResizePip=function(){dt(this)},t.prototype.attach=function(e){var r=n.prototype.attach.call(this,e);return this._clientTrackSwitchOffControl==="auto"&&this._invisibleElements.add(r),this._intersectionObserver.observe(r),this._resizeObserver.observe(r),this._enableDocumentVisibilityTurnOff&&(this._documentVisibilityTurnOffCleanup=this._documentVisibilityTurnOffCleanup||cm(this)),this._observePip(r),r},t.prototype.detach=function(e){var r=this,i=n.prototype.detach.call(this,e),a=Array.isArray(i)?i:[i];return a.forEach(function(o){r._intersectionObserver.unobserve(o),r._resizeObserver.unobserve(o),r._invisibleElements.delete(o),r._unObservePip(o)}),this._attachments.size===0&&this._documentVisibilityTurnOffCleanup&&(this._documentVisibilityTurnOffCleanup(),this._documentVisibilityTurnOffCleanup=null),Re(this),dt(this),i},t.prototype.addProcessor=function(){return n.prototype.addProcessor.apply(this,arguments)},t.prototype.removeProcessor=function(){return n.prototype.removeProcessor.apply(this,arguments)},t.prototype.toString=function(){return"[RemoteVideoTrack #"+this._instanceId+": "+this.sid+"]"},t.prototype.setPriority=function(e){return n.prototype.setPriority.call(this,e)},t}(am);function cm(n){function t(){Re(n)}return wi.onVisibilityChange(1,t),function(){wi.offVisibilityChange(1,t)}}function Re(n){if(n._clientTrackSwitchOffControl==="auto"){var t=n._getAllAttachedElements().filter(function(i){return!n._invisibleElements.has(i)}),e=n._getAllAttachedElements().filter(function(i){return n._elToPipWindows.has(i)}),r=e.length>0||document.visibilityState==="visible"&&t.length>0;r===!0?(n._turnOffTimer.clear(),n._setRenderHint({enabled:!0})):n._turnOffTimer.isSet||n._turnOffTimer.start()}}function dt(n){if(n._contentPreferencesMode==="auto"){var t=n._getAllAttachedElements().filter(function(c){return!n._invisibleElements.has(c)}),e=n._getAllAttachedElements().map(function(c){var l=n._elToPipWindows.get(c);return l?{clientHeight:l.height,clientWidth:l.width}:{clientHeight:0,clientWidth:0}}),r=t.concat(e);if(r.length>0){var i=tm(r.sort(function(c,l){return l.clientHeight+l.clientWidth-c.clientHeight-c.clientWidth-1}),1),a=i[0],o=a.clientHeight,s=a.clientWidth,u={height:o,width:s};n._setRenderHint({renderDimensions:u})}}}var um=sm,lm=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),dm=dn,fm=function(n){lm(t,n);function t(e,r){return n.call(this,e,r)||this}return t.prototype.toString=function(){return"[RemoteVideoTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(dm),pm=fm,vm=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Ye=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Fr=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},hm=et,_m=x_,mm=N_,ym=Q_,gm=Y_,bm=um,Sm=pm,oe=R,wm=0,km=function(n){vm(t,n);function t(e,r){var i=n.call(this)||this;r=Object.assign({RemoteAudioTrack:_m,RemoteAudioTrackPublication:mm,RemoteDataTrack:ym,RemoteDataTrackPublication:gm,RemoteVideoTrack:bm,RemoteVideoTrackPublication:Sm,tracks:[]},r);var a=Tm(r.tracks),o=r.log.createLog("default",i),s=new Map(a.audioTracks),u=new Map(a.dataTracks),c=new Map(a.tracks),l=new Map(a.videoTracks);return Object.defineProperties(i,{_RemoteAudioTrack:{value:r.RemoteAudioTrack},_RemoteAudioTrackPublication:{value:r.RemoteAudioTrackPublication},_RemoteDataTrack:{value:r.RemoteDataTrack},_RemoteDataTrackPublication:{value:r.RemoteDataTrackPublication},_RemoteVideoTrack:{value:r.RemoteVideoTrack},_RemoteVideoTrackPublication:{value:r.RemoteVideoTrackPublication},_audioTracks:{value:s},_dataTracks:{value:u},_instanceId:{value:++wm},_clientTrackSwitchOffControl:{value:r.clientTrackSwitchOffControl},_contentPreferencesMode:{value:r.contentPreferencesMode},_log:{value:o},_signaling:{value:e},_tracks:{value:c},_trackEventReemitters:{value:new Map},_trackPublicationEventReemitters:{value:new Map},_trackSignalingUpdatedEventCallbacks:{value:new Map},_videoTracks:{value:l},audioTracks:{enumerable:!0,value:new Map},dataTracks:{enumerable:!0,value:new Map},identity:{enumerable:!0,get:function(){return e.identity}},networkQualityLevel:{enumerable:!0,get:function(){return e.networkQualityLevel}},networkQualityStats:{enumerable:!0,get:function(){return e.networkQualityStats}},sid:{enumerable:!0,get:function(){return e.sid}},state:{enumerable:!0,get:function(){return e.state}},tracks:{enumerable:!0,value:new Map},videoTracks:{enumerable:!0,value:new Map}}),i._tracks.forEach(Ti.bind(null,i)),e.on("networkQualityLevelChanged",function(){return i.emit("networkQualityLevelChanged",i.networkQualityLevel,i.networkQualityStats&&(i.networkQualityStats.audio||i.networkQualityStats.video)?i.networkQualityStats:null)}),Pm(i,e),o.info("Created a new Participant"+(i.identity?": "+i.identity:"")),i}return t.prototype._getTrackEvents=function(){return[["dimensionsChanged","trackDimensionsChanged"],["message","trackMessage"],["started","trackStarted"]]},t.prototype._getTrackPublicationEvents=function(){return[]},t.prototype.toString=function(){return"[Participant #"+this._instanceId+": "+this.sid+"]"},t.prototype._addTrack=function(e,r){var i=this._log;if(this._tracks.has(r))return null;this._tracks.set(r,e);var a={audio:this._audioTracks,video:this._videoTracks,data:this._dataTracks}[e.kind];return a.set(r,e),Ti(this,e,r),i.info("Added a new "+oe.trackClass(e)+":",r),i.debug(oe.trackClass(e)+":",e),e},t.prototype._addTrackPublication=function(e){var r=this._log;if(this.tracks.has(e.trackSid))return null;this.tracks.set(e.trackSid,e);var i={audio:this.audioTracks,data:this.dataTracks,video:this.videoTracks}[e.kind];return i.set(e.trackSid,e),Em(this,e),r.info("Added a new "+oe.trackPublicationClass(e)+":",e.trackSid),r.debug(oe.trackPublicationClass(e)+":",e),e},t.prototype._handleTrackSignalingEvents=function(){var e=this,r=e._log,i=e._clientTrackSwitchOffControl,a=e._contentPreferencesMode,o=this;if(this.state==="disconnected")return;var s=this._RemoteAudioTrack,u=this._RemoteAudioTrackPublication,c=this._RemoteVideoTrack,l=this._RemoteVideoTrackPublication,f=this._RemoteDataTrack,p=this._RemoteDataTrackPublication,v=this._signaling;function _(h){var b={audio:u,data:p,video:l}[h.kind],g=new b(h,{log:r});o._addTrackPublication(g);var T=h.isSubscribed;T&&y(h),o._trackSignalingUpdatedEventCallbacks.set(h.sid,function(){if(T!==h.isSubscribed){if(T=h.isSubscribed,T){y(h);return}P(h)}}),h.on("updated",o._trackSignalingUpdatedEventCallbacks.get(h.sid))}function m(h){h.isSubscribed&&h.setTrackTransceiver(null);var b=o._trackSignalingUpdatedEventCallbacks.get(h.sid);b&&(h.removeListener("updated",b),o._trackSignalingUpdatedEventCallbacks.delete(h.sid));var g=o.tracks.get(h.sid);g&&o._removeTrackPublication(g)}function y(h){var b=h.isEnabled,g=h.name,T=h.kind,S=h.sid,w=h.trackTransceiver,O=h.isSwitchedOff,D={audio:s,video:c,data:f}[T],F=o.tracks.get(S);if(!(!D||T!==w.kind)){var q={log:r,name:g,clientTrackSwitchOffControl:i,contentPreferencesMode:a},E=function(x){return v.updateSubscriberTrackPriority(S,x)},I=function(x){h.isSubscribed&&v.updateTrackRenderHint(S,x)},k=T==="data"?new D(S,w,q):new D(S,w,b,O,E,I,q);o._addTrack(k,F,w.id)}}function P(h){var b=Ye(Array.from(o._tracks.entries()).find(function(w){var O=Ye(w,2),D=O[1];return D.sid===h.sid}),2),g=b[0],T=b[1],S=o.tracks.get(h.sid);T&&o._removeTrack(T,S,g)}v.on("trackAdded",_),v.on("trackRemoved",m),v.tracks.forEach(_),v.on("stateChanged",function h(b){b==="disconnected"?(r.debug("Removing event listeners"),v.removeListener("stateChanged",h),v.removeListener("trackAdded",_),v.removeListener("trackRemoved",m)):b==="connected"&&(r.info("reconnected"),setTimeout(function(){return o.emit("reconnected")},0))})},t.prototype._removeTrack=function(e,r){if(!this._tracks.has(r))return null;this._tracks.delete(r);var i={audio:this._audioTracks,video:this._videoTracks,data:this._dataTracks}[e.kind];i.delete(r);var a=this._trackEventReemitters.get(r)||new Map;a.forEach(function(s,u){e.removeListener(u,s)});var o=this._log;return o.info("Removed a "+oe.trackClass(e)+":",r),o.debug(oe.trackClass(e)+":",e),e},t.prototype._removeTrackPublication=function(e){if(e=this.tracks.get(e.trackSid),!e)return null;this.tracks.delete(e.trackSid);var r={audio:this.audioTracks,data:this.dataTracks,video:this.videoTracks}[e.kind];r.delete(e.trackSid);var i=this._trackPublicationEventReemitters.get(e.trackSid)||new Map;i.forEach(function(o,s){e.removeListener(s,o)});var a=this._log;return a.info("Removed a "+oe.trackPublicationClass(e)+":",e.trackSid),a.debug(oe.trackPublicationClass(e)+":",e),e},t.prototype.toJSON=function(){return oe.valueToJSON(this)},t}(hm);function Tm(n){var t=n.map(function(a){return[a.id,a]}),e=t.filter(function(a){return a[1].kind==="audio"}),r=t.filter(function(a){return a[1].kind==="video"}),i=t.filter(function(a){return a[1].kind==="data"});return{audioTracks:e,dataTracks:i,tracks:t,videoTracks:r}}function Pm(n,t){var e=n._log;n.state!=="disconnected"&&t.on("stateChanged",function r(i){e.debug("Transitioned to state:",i),n.emit(i,n),i==="disconnected"&&(e.debug("Removing Track event reemitters"),t.removeListener("stateChanged",r),n._tracks.forEach(function(a){var o=n._trackEventReemitters.get(a.id);a&&o&&o.forEach(function(s,u){a.removeListener(u,s)})}),t.tracks.forEach(function(a){var o=n._tracks.get(a.id),s=n._trackEventReemitters.get(a.id);o&&s&&s.forEach(function(u,c){o.removeListener(c,u)})}),n._trackEventReemitters.clear(),n.tracks.forEach(function(a){n._trackPublicationEventReemitters.get(a.trackSid).forEach(function(o,s){a.removeListener(s,o)})}),n._trackPublicationEventReemitters.clear())})}function Ti(n,t,e){var r=new Map;n.state!=="disconnected"&&(n._getTrackEvents().forEach(function(i){var a=i[0],o=i[1];r.set(a,function(){var s=[o].concat([].slice.call(arguments));return n.emit.apply(n,Fr([],Ye(s)))}),t.on(a,r.get(a))}),n._trackEventReemitters.set(e,r))}function Em(n,t){var e=new Map;n.state!=="disconnected"&&(n._getTrackPublicationEvents().forEach(function(r){var i=Ye(r,2),a=i[0],o=i[1];e.set(a,function(){for(var s=[],u=0;u<arguments.length;u++)s[u]=arguments[u];n.emit.apply(n,Fr(Fr([o],Ye(s)),[t]))}),t.on(a,e.get(a))}),n._trackPublicationEventReemitters.set(t.trackSid,e))}var qs=km,Om=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Cm=be.MediaStreamTrack,pn=R,Rm=pn.asLocalTrack,Lm=pn.asLocalTrackPublication,_e=pn.trackClass,Qs=U,fe=Qs.typeErrors,lr=Qs.trackPriority,xm=tt.validateLocalTrack,vn=Ae,Am=vn.LocalAudioTrack,Im=vn.LocalDataTrack,Mm=vn.LocalVideoTrack,jm=u_,Dm=p_,$m=m_,Nm=qs,Fm=function(n){Om(t,n);function t(e,r,i){var a=this;i=Object.assign({LocalAudioTrack:Am,LocalVideoTrack:Mm,LocalDataTrack:Im,MediaStreamTrack:Cm,LocalAudioTrackPublication:jm,LocalVideoTrackPublication:$m,LocalDataTrackPublication:Dm,shouldStopLocalTracks:!1,tracks:r},i);var o=i.shouldStopLocalTracks?new Set(r.filter(function(s){return s.kind!=="data"})):new Set;return a=n.call(this,e,i)||this,Object.defineProperties(a,{_eventObserver:{value:i.eventObserver},_LocalAudioTrack:{value:i.LocalAudioTrack},_LocalDataTrack:{value:i.LocalDataTrack},_LocalVideoTrack:{value:i.LocalVideoTrack},_MediaStreamTrack:{value:i.MediaStreamTrack},_LocalAudioTrackPublication:{value:i.LocalAudioTrackPublication},_LocalDataTrackPublication:{value:i.LocalDataTrackPublication},_LocalVideoTrackPublication:{value:i.LocalVideoTrackPublication},_tracksToStop:{value:o},signalingRegion:{enumerable:!0,get:function(){return e.signalingRegion}}}),a._handleTrackSignalingEvents(),a}return t.prototype._addTrack=function(e,r,i){var a=n.prototype._addTrack.call(this,e,r);return a&&this.state!=="disconnected"&&this._addLocalTrack(e,i),a},t.prototype._addLocalTrack=function(e,r){var i,a=(i=e.noiseCancellation)===null||i===void 0?void 0:i.vendor;this._signaling.addTrack(e._trackSender,e.name,r,a),this._log.info("Added a new "+_e(e,!0)+":",e.id),this._log.debug(_e(e,!0)+":",e)},t.prototype._removeTrack=function(e,r){var i=n.prototype._removeTrack.call(this,e,r);return i&&this.state!=="disconnected"&&(this._signaling.removeTrack(e._trackSender),this._log.info("Removed a "+_e(e,!0)+":",e.id),this._log.debug(_e(e,!0)+":",e)),i},t.prototype._getTrackEvents=function(){return n.prototype._getTrackEvents.call(this).concat([["disabled","trackDisabled"],["enabled","trackEnabled"],["stopped","trackStopped"]])},t.prototype.toString=function(){return"[LocalParticipant #"+this._instanceId+(this.sid?": "+this.sid:"")+"]"},t.prototype._handleTrackSignalingEvents=function(){var e=this,r=this._log;if(this.state!=="disconnected"){var i=function(u){var c=e._signaling.getPublication(u._trackSender);c&&(c.disable(),r.debug("Disabled the "+_e(u,!0)+":",u.id))},a=function(u){var c=e._signaling.getPublication(u._trackSender);c&&(c.enable(),r.debug("Enabled the "+_e(u,!0)+":",u.id))},o=function(u){var c=e._signaling.getPublication(u._trackSender);return c&&c.stop(),c},s=function(u){r.debug("Transitioned to state:",u),u==="disconnected"?(r.debug("Removing LocalTrack event listeners"),e._signaling.removeListener("stateChanged",s),e.removeListener("trackDisabled",i),e.removeListener("trackEnabled",a),e.removeListener("trackStopped",o),e._tracks.forEach(function(c){var l=o(c);l&&c._trackSender.removeClone(l._trackTransceiver)}),r.info("LocalParticipant disconnected. Stopping "+e._tracksToStop.size+" automatically-acquired LocalTracks"),e._tracksToStop.forEach(function(c){c.stop()})):u==="connected"&&(r.info("reconnected"),setTimeout(function(){return e.emit("reconnected")},0))};this.on("trackDisabled",i),this.on("trackEnabled",a),this.on("trackStopped",o),this._signaling.on("stateChanged",s),this._tracks.forEach(function(u){e._addLocalTrack(u,lr.PRIORITY_STANDARD),e._getOrCreateLocalTrackPublication(u).catch(function(c){r.warn("Failed to get or create LocalTrackPublication for "+u+":",c)})})}},t.prototype._getOrCreateLocalTrackPublication=function(e){var r=ft(this.tracks,e);if(r)return Promise.resolve(r);var i=this._log,a=this,o=this._signaling.getPublication(e._trackSender);return o?new Promise(function(s,u){function c(){var l=o.error;if(l){o.removeListener("updated",c),i.warn("Failed to publish the "+_e(e,!0)+": "+l.message),a._removeTrack(e,e.id),setTimeout(function(){a.emit("trackPublicationFailed",l,e)}),u(l);return}if(!a._tracks.has(e.id)){o.removeListener("updated",c),u(new Error("The "+e+" was unpublished"));return}var f=o.sid;if(f){o.removeListener("updated",c);var p={log:i,LocalAudioTrackPublication:a._LocalAudioTrackPublication,LocalDataTrackPublication:a._LocalDataTrackPublication,LocalVideoTrackPublication:a._LocalVideoTrackPublication};r=ft(a.tracks,e);var v=function(P){return a.emit("trackWarning",P,r)},_=function(){return a.emit("trackWarningsCleared",r)},m=function(P){r.removeListener("trackWarning",v),r.removeListener("trackWarningsCleared",_),a.unpublishTrack(P.track)};r||(r=Lm(e,o,m,p),a._addTrackPublication(r)),r.on("warning",v),r.on("warningsCleared",_);var y=a._signaling.state;(y==="connected"||y==="connecting")&&(e._processorEventObserver&&e._processorEventObserver.on("event",function(P){a._eventObserver.emit("event",{name:P.name,payload:P.data,group:"video-processor",level:"info"})}),e.processedTrack&&(e._captureFrames(),e._setSenderMediaStreamTrack(!0))),y==="connected"&&setTimeout(function(){a.emit("trackPublished",r)}),s(r)}}o.on("updated",c)}):Promise.reject(new Error("Unexpected error: The "+e+" cannot be published"))},t.prototype.publishTrack=function(e,r){var i=ft(this.tracks,e);if(i)return Promise.resolve(i);r=Object.assign({log:this._log,priority:lr.PRIORITY_STANDARD,LocalAudioTrack:this._LocalAudioTrack,LocalDataTrack:this._LocalDataTrack,LocalVideoTrack:this._LocalVideoTrack,MediaStreamTrack:this._MediaStreamTrack},r);var a;try{a=Rm(e,r)}catch(l){return Promise.reject(l)}var o=a.noiseCancellation,s=this._signaling.audioProcessors;o&&!s.includes(o.vendor)&&(this._log.warn(o.vendor+" is not supported in this room. disabling it permanently"),o.disablePermanently());var u=Object.values(lr);if(!u.includes(r.priority))return Promise.reject(fe.INVALID_VALUE("LocalTrackPublishOptions.priority",u));var c=this._addTrack(a,a.id,r.priority)||this._tracks.get(a.id);return this._getOrCreateLocalTrackPublication(c)},t.prototype.publishTracks=function(e){if(!Array.isArray(e))throw fe.INVALID_TYPE("tracks","Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack");return Promise.all(e.map(this.publishTrack,this))},t.prototype.setBandwidthProfile=function(){this._log.warn("setBandwidthProfile is not implemented yet and may be available in future versions of twilio-video.js")},t.prototype.setNetworkQualityConfiguration=function(e){if(typeof e!="object"||e===null)throw fe.INVALID_TYPE("networkQualityConfiguration","NetworkQualityConfiguration");return["local","remote"].forEach(function(r){if(r in e&&(typeof e[r]!="number"||isNaN(e[r])))throw fe.INVALID_TYPE("networkQualityConfiguration."+r,"number")}),this._signaling.setNetworkQualityConfiguration(e),this},t.prototype.setParameters=function(e){if(typeof e<"u"&&typeof e!="object")throw fe.INVALID_TYPE("encodingParameters","EncodingParameters, null or undefined");if(e){if(this._signaling.getParameters().adaptiveSimulcast&&e.maxVideoBitrate)throw fe.INVALID_TYPE("encodingParameters",'encodingParameters.maxVideoBitrate is not compatible with "preferredVideoCodecs=auto"');["maxAudioBitrate","maxVideoBitrate"].forEach(function(r){if(typeof e[r]<"u"&&typeof e[r]!="number"&&e[r]!==null)throw fe.INVALID_TYPE("encodingParameters."+r,"number, null or undefined")})}else e===null&&(e={maxAudioBitrate:null,maxVideoBitrate:null});return this._signaling.setParameters(e),this},t.prototype.unpublishTrack=function(e){xm(e,{LocalAudioTrack:this._LocalAudioTrack,LocalDataTrack:this._LocalDataTrack,LocalVideoTrack:this._LocalVideoTrack,MediaStreamTrack:this._MediaStreamTrack});var r=this._tracks.get(e.id);if(!r)return null;var i=this._signaling.getPublication(r._trackSender);if(i.publishFailed(new Error("The "+r+" was unpublished")),r=this._removeTrack(r,r.id),!r)return null;var a=ft(this.tracks,r);return a&&this._removeTrackPublication(a),a},t.prototype.unpublishTracks=function(e){var r=this;if(!Array.isArray(e))throw fe.INVALID_TYPE("tracks","Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack");return e.reduce(function(i,a){var o=r.unpublishTrack(a);return o?i.concat(o):i},[])},t}(Nm);function ft(n,t){return Array.from(n.values()).find(function(e){return e.track===t||e.track.mediaStreamTrack===t})||null}var Vm=Fm,Bm=function(){function n(){Object.defineProperties(this,{_connected:{writable:!0,value:!0}})}return n.prototype.connect=function(){},n.prototype.disconnect=function(){return this._connected?(this._connected=!1,!0):!1},n.prototype.publish=function(){return this._connected},n}(),Um=Bm,Wm=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Hm=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},qm=K.EventEmitter,hn=U,pt=hn.DEFAULT_NQ_LEVEL_LOCAL,vt=hn.DEFAULT_NQ_LEVEL_REMOTE,Pi=hn.MAX_NQ_LEVEL,dr=R.inRange,Qm=function(n){Wm(t,n);function t(e){var r=n.call(this)||this;return e=Object.assign({local:pt,remote:vt},e),Object.defineProperties(r,{local:{value:dr(e.local,pt,Pi)?e.local:pt,writable:!0},remote:{value:dr(e.remote,vt,Pi)?e.remote:vt,writable:!0}}),r}return t.prototype.update=function(e){var r=this;e=Object.assign({local:this.local,remote:this.remote},e),[["local",pt,3],["remote",vt,3]].forEach(function(i){var a=Hm(i,3),o=a[0],s=a[1],u=a[2];r[o]=typeof e[o]=="number"&&dr(e[o],s,u)?e[o]:s})},t}(qm),Gm=Qm,Km=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),zm=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Ei=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Ym=qs,Jm=function(n){Km(t,n);function t(e,r){var i=n.call(this,e,r)||this;return i._handleTrackSignalingEvents(),i.once("disconnected",i._unsubscribeTracks.bind(i)),i}return t.prototype.toString=function(){return"[RemoteParticipant #"+this._instanceId+(this.sid?": "+this.sid:"")+"]"},t.prototype._addTrack=function(e,r,i){return n.prototype._addTrack.call(this,e,i)?(r._subscribed(e),this.emit("trackSubscribed",e,r),e):null},t.prototype._addTrackPublication=function(e){var r=n.prototype._addTrackPublication.call(this,e);return r?(this.emit("trackPublished",r),r):null},t.prototype._getTrackPublicationEvents=function(){return Ei(Ei([],zm(n.prototype._getTrackPublicationEvents.call(this))),[["subscriptionFailed","trackSubscriptionFailed"],["trackDisabled","trackDisabled"],["trackEnabled","trackEnabled"],["publishPriorityChanged","trackPublishPriorityChanged"],["trackSwitchedOff","trackSwitchedOff"],["trackSwitchedOn","trackSwitchedOn"]])},t.prototype._unsubscribeTracks=function(){var e=this;this.tracks.forEach(function(r){if(r.isSubscribed){var i=r.track;r._unsubscribe(),e.emit("trackUnsubscribed",i,r)}})},t.prototype._removeTrack=function(e,r,i){var a=this._tracks.get(i);return a?(n.prototype._removeTrack.call(this,a,i),r._unsubscribe(),this.emit("trackUnsubscribed",a,r),a):null},t.prototype._removeTrackPublication=function(e){this._signaling.clearTrackHint(e.trackSid);var r=n.prototype._removeTrackPublication.call(this,e);return r?(this.emit("trackUnpublished",r),r):null},t}(Ym),Xm=Jm,Zm=function(){function n(t,e){if(typeof t!="string")throw new Error("Track id must be a string");Object.defineProperties(this,{trackId:{value:t,enumerable:!0},trackSid:{value:e.trackSid,enumerable:!0},timestamp:{value:e.timestamp,enumerable:!0},ssrc:{value:e.ssrc,enumerable:!0},packetsLost:{value:typeof e.packetsLost=="number"?e.packetsLost:null,enumerable:!0},codec:{value:typeof e.codecName=="string"?e.codecName:null,enumerable:!0}})}return n}(),Gs=Zm,ey=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),ty=Gs,ry=function(n){ey(t,n);function t(e,r,i){var a=n.call(this,e,r)||this;return Object.defineProperties(a,{bytesSent:{value:typeof r.bytesSent=="number"?r.bytesSent:i?0:null,enumerable:!0},packetsSent:{value:typeof r.packetsSent=="number"?r.packetsSent:i?0:null,enumerable:!0},roundTripTime:{value:typeof r.roundTripTime=="number"?r.roundTripTime:i?0:null,enumerable:!0}}),a}return t}(ty),Ks=ry,ny=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),iy=Ks,ay=function(n){ny(t,n);function t(e,r,i){var a=n.call(this,e,r,i)||this;return Object.defineProperties(a,{audioLevel:{value:typeof r.audioInputLevel=="number"?r.audioInputLevel:null,enumerable:!0},jitter:{value:typeof r.jitter=="number"?r.jitter:null,enumerable:!0}}),a}return t}(iy),oy=ay,sy=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),cy=Ks,uy=function(n){sy(t,n);function t(e,r,i){var a=n.call(this,e,r,i)||this,o=null;typeof r.frameWidthInput=="number"&&typeof r.frameHeightInput=="number"&&(o={},Object.defineProperties(o,{width:{value:r.frameWidthInput,enumerable:!0},height:{value:r.frameHeightInput,enumerable:!0}}));var s=null;return typeof r.frameWidthSent=="number"&&typeof r.frameHeightSent=="number"&&(s={},Object.defineProperties(s,{width:{value:r.frameWidthSent,enumerable:!0},height:{value:r.frameHeightSent,enumerable:!0}})),Object.defineProperties(a,{captureDimensions:{value:o,enumerable:!0},dimensions:{value:s,enumerable:!0},captureFrameRate:{value:typeof r.frameRateInput=="number"?r.frameRateInput:null,enumerable:!0},frameRate:{value:typeof r.frameRateSent=="number"?r.frameRateSent:null,enumerable:!0}}),a}return t}(cy),ly=uy,dy=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),fy=Gs,py=function(n){dy(t,n);function t(e,r){var i=n.call(this,e,r)||this;return Object.defineProperties(i,{bytesReceived:{value:typeof r.bytesReceived=="number"?r.bytesReceived:null,enumerable:!0},packetsReceived:{value:typeof r.packetsReceived=="number"?r.packetsReceived:null,enumerable:!0}}),i}return t}(fy),zs=py,vy=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),hy=zs,_y=function(n){vy(t,n);function t(e,r){var i=n.call(this,e,r)||this;return Object.defineProperties(i,{audioLevel:{value:typeof r.audioOutputLevel=="number"?r.audioOutputLevel:null,enumerable:!0},jitter:{value:typeof r.jitter=="number"?r.jitter:null,enumerable:!0}}),i}return t}(hy),my=_y,yy=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),gy=zs,by=function(n){yy(t,n);function t(e,r){var i=n.call(this,e,r)||this,a=null;return typeof r.frameWidthReceived=="number"&&typeof r.frameHeightReceived=="number"&&(a={},Object.defineProperties(a,{width:{value:r.frameWidthReceived,enumerable:!0},height:{value:r.frameHeightReceived,enumerable:!0}})),Object.defineProperties(i,{dimensions:{value:a,enumerable:!0},frameRate:{value:typeof r.frameRateReceived=="number"?r.frameRateReceived:null,enumerable:!0}}),i}return t}(gy),Sy=by,wy=oy,ky=ly,Ty=my,Py=Sy,Ey=function(){function n(t,e,r){if(typeof t!="string")throw new Error("RTCPeerConnection id must be a string");Object.defineProperties(this,{peerConnectionId:{value:t,enumerable:!0},localAudioTrackStats:{value:e.localAudioTrackStats.map(function(i){return new wy(i.trackId,i,r)}),enumerable:!0},localVideoTrackStats:{value:e.localVideoTrackStats.map(function(i){return new ky(i.trackId,i,r)}),enumerable:!0},remoteAudioTrackStats:{value:e.remoteAudioTrackStats.map(function(i){return new Ty(i.trackId,i)}),enumerable:!0},remoteVideoTrackStats:{value:e.remoteVideoTrackStats.map(function(i){return new Py(i.trackId,i)}),enumerable:!0}})}return n}(),Ys=Ey,Oy=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Je=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},wt=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Cy=et,Ry=Xm,Ly=Ys,Js=R,xy=Js.flatMap,Ay=Js.valueToJSON,Iy=0,My=function(n){Oy(t,n);function t(e,r,i){var a=n.call(this)||this,o=i.log.createLog("default",a),s=new Map;return Object.defineProperties(a,{_log:{value:o},_clientTrackSwitchOffControl:{value:i.clientTrackSwitchOffControl||"disabled"},_contentPreferencesMode:{value:i.contentPreferencesMode||"disabled"},_instanceId:{value:++Iy},_options:{value:i},_participants:{value:s},_signaling:{value:r},dominantSpeaker:{enumerable:!0,get:function(){return this.participants.get(r.dominantSpeakerSid)||null}},isRecording:{enumerable:!0,get:function(){return r.recording.isEnabled||!1}},localParticipant:{enumerable:!0,value:e},name:{enumerable:!0,value:r.name},participants:{enumerable:!0,value:s},sid:{enumerable:!0,value:r.sid},state:{enumerable:!0,get:function(){return r.state}},mediaRegion:{enumerable:!0,value:r.mediaRegion}}),Dy(a,e),$y(a,r.recording),Ny(a,r),jy(a),o.info("Created a new Room:",a.name),o.debug("Initial RemoteParticipants:",Array.from(a._participants.values())),a}return t.prototype.toString=function(){return"[Room #"+this._instanceId+": "+this.sid+"]"},t.prototype.disconnect=function(){return this._log.info("Disconnecting"),this._signaling.disconnect(),this},t.prototype.getStats=function(){var e=this;return this._signaling.getStats().then(function(r){return Array.from(r).map(function(i){var a=Je(i,2),o=a[0],s=a[1];return new Ly(o,Object.assign({},s,{localAudioTrackStats:Oi(e,s.localAudioTrackStats),localVideoTrackStats:Oi(e,s.localVideoTrackStats)}))})})},t.prototype.refreshInactiveMedia=function(){var e=this.localParticipant.tracks,r=Array.from(e.values()).filter(function(u){var c=u.track.kind;return c!=="data"}).map(function(u){var c=u.track;return c}),i=xy(this.participants,function(u){return Array.from(u.tracks.values())}).filter(function(u){var c=u.track;return c&&c.kind!=="data"}).map(function(u){var c=u.track;return c}),a=r.concat(i),o=new Event("unmute");r.forEach(function(u){var c=u.isMuted,l=u.mediaStreamTrack;c&&l.dispatchEvent(o)});var s=new Event("pause");return a.forEach(function(u){var c=u._attachments,l=u._elShims;return c.forEach(function(f){var p=l.get(f),v=f.paused&&p&&!p.pausedIntentionally();v&&f.dispatchEvent(s)})}),this},t.prototype.toJSON=function(){return Ay(this)},t}(Cy);function jy(n){var t=n.localParticipant._signaling.audioProcessors;n.localParticipant.audioTracks.forEach(function(e){var r=e.track,i=r.noiseCancellation;i&&!t.includes(i.vendor)&&(n._log.warn(i.vendor+" is not supported in this room. disabling it permanently"),i.disablePermanently())})}function Oi(n,t){var e=n.localParticipant._signaling;return t.reduce(function(r,i){var a=e.tracks.get(i.trackId),o=e.getSender(a);return o?[Object.assign({},i,{trackId:o.id})].concat(r):r},[])}function Ci(n,t){var e=n._log,r=n._clientTrackSwitchOffControl,i=n._contentPreferencesMode,a=new Ry(t,{log:e,clientTrackSwitchOffControl:r,contentPreferencesMode:i});e.info("A new RemoteParticipant connected:",a),n._participants.set(a.sid,a),n.emit("participantConnected",a);var o=[["reconnected","participantReconnected"],["reconnecting","participantReconnecting"],"trackDimensionsChanged","trackDisabled","trackEnabled","trackMessage","trackPublished","trackPublishPriorityChanged","trackStarted","trackSubscribed","trackSubscriptionFailed","trackSwitchedOff","trackSwitchedOn","trackUnpublished","trackUnsubscribed"].map(function(s){var u=Je(Array.isArray(s)?s:[s,s],2),c=u[0],l=u[1];function f(){var p=[].slice.call(arguments);p.unshift(l),p.push(a),n.emit.apply(n,wt([],Je(p)))}return a.on(c,f),[c,f]});a.once("disconnected",function(){var u=n.dominantSpeaker;e.info("RemoteParticipant disconnected:",a),n._participants.delete(a.sid),o.forEach(function(c){a.removeListener(c[0],c[1])}),n.emit("participantDisconnected",a),a===u&&n.emit("dominantSpeakerChanged",n.dominantSpeaker)})}function Dy(n,t){var e=["trackWarning","trackWarningsCleared"].map(function(r){return{eventName:r,handler:function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];return n.emit.apply(n,wt([r],Je(wt(wt([],Je(i)),[t]))))}}});e.forEach(function(r){var i=r.eventName,a=r.handler;return t.on(i,a)}),n.once("disconnected",function(){return e.forEach(function(r){var i=r.eventName,a=r.handler;return t.removeListener(i,a)})})}function $y(n,t){t.on("updated",function(){var r=t.isEnabled;n._log.info("Recording "+(r?"started":"stopped")),n.emit("recording"+(r?"Started":"Stopped"))})}function Ny(n,t){var e=n._log;e.debug("Creating a new RemoteParticipant for each ParticipantSignaling in the RoomSignaling"),t.participants.forEach(Ci.bind(null,n)),e.debug("Setting up RemoteParticipant creation for all subsequent ParticipantSignalings that connect to the RoomSignaling"),t.on("participantConnected",Ci.bind(null,n)),t.on("dominantSpeakerChanged",function(){return n.emit("dominantSpeakerChanged",n.dominantSpeaker)}),t.on("stateChanged",function r(i,a){switch(e.info("Transitioned to state:",i),i){case"disconnected":n.participants.forEach(function(o){o._unsubscribeTracks()}),n.emit(i,n,a),n.localParticipant.tracks.forEach(function(o){o.unpublish()}),t.removeListener("stateChanged",r);break;case"reconnecting":setTimeout(function(){return n.emit("reconnecting",a)},0);break;default:setTimeout(function(){return n.emit("reconnected")},0)}})}var Fy=My,Vy=function(){function n(t){Object.defineProperties(this,{_min:{value:t.min||100},_max:{value:t.max||1e4},_jitter:{value:t.jitter>0&&t.jitter<=1?t.jitter:0},_factor:{value:t.factor||2},_attempts:{value:0,writable:!0},_duration:{enumerable:!1,get:function(){var e=this._min*Math.pow(this._factor,this._attempts);if(this._jitter){var r=Math.random(),i=Math.floor(r*this._jitter*e);e=Math.floor(r*10)&1?e+i:e-i}return Math.min(e,this._max)|0}},_timeoutID:{value:null,writable:!0}})}return n.prototype.backoff=function(t){var e=this,r=this._duration;this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null),this._timeoutID=setTimeout(function(){e._attempts++,t()},r)},n.prototype.reset=function(){this._attempts=0,this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null)},n}(),Xs=Vy,z={},By=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},Zs=R,fr=Zs.difference,se=Zs.flatMap;function pr(){var n=4294967295;return String(Math.floor(Math.random()*n))}var Uy=function(){function n(t,e,r){Object.defineProperties(this,{cName:{enumerable:!0,value:r},isSimulcastEnabled:{enumerable:!0,value:!1,writable:!0},primarySSRCs:{enumerable:!0,value:new Set},rtxPairs:{enumerable:!0,value:new Map},streamId:{enumerable:!0,value:e},trackId:{enumerable:!0,value:t}})}return n.prototype.addSimulcastSSRCs=function(){if(!this.isSimulcastEnabled){var t=[pr(),pr()];t.forEach(function(e){this.primarySSRCs.add(e)},this),this.rtxPairs.size&&t.forEach(function(e){this.rtxPairs.set(pr(),e)},this)}},n.prototype.addSSRC=function(t,e,r){e?this.rtxPairs.set(t,e):this.primarySSRCs.add(t),this.isSimulcastEnabled=this.isSimulcastEnabled||r},n.prototype.toSdpLines=function(t){var e=this,r=t?[]:Array.from(this.rtxPairs.entries()).map(function(c){return c.reverse()}),i=Array.from(this.primarySSRCs.values()),a=r.length?se(r):i,o=se(a,function(c){return["a=ssrc:"+c+" cname:"+e.cName,"a=ssrc:"+c+" msid:"+e.streamId+" "+e.trackId]}),s=r.map(function(c){return"a=ssrc-group:FID "+c.join(" ")}),u=["a=ssrc-group:SIM "+i.join(" ")];return s.concat(o).concat(u)},n}();function Tt(n,t){var e=n.match(new RegExp(t,"gm"))||[];return e.map(function(r){var i=r.match(new RegExp(t))||[];return i.slice(1)})}function Wy(n){var t="^a=ssrc-group:SIM ([0-9]+) ([0-9]+) ([0-9]+)$";return new Set(se(Tt(n,t)))}function Hy(n,t,e){var r="a=ssrc:"+t+" "+e+":(.+)";return n.match(new RegExp(r))[1]}function qy(n){var t="^a=ssrc-group:FID ([0-9]+) ([0-9]+)$";return new Map(Tt(n,t).map(function(e){return e.reverse()}))}function Qy(n){var t=By(se(Tt(n,"^a=msid:(.+) (.+)$")),2),e=t[0],r=t[1],i=se(Tt(n,"^a=ssrc:(.+) cname:.+$"));return i.map(function(a){return[a,e,r]})}function Gy(n){var t=Wy(n),e=qy(n),r=Qy(n);return r.reduce(function(i,a){var o=a[0],s=a[1],u=a[2],c=i.get(u)||new Uy(u,s,Hy(n,o,"cname")),l=e.get(o)||null;return c.addSSRC(o,l,t.has(o)),i.set(u,c)},new Map)}function Ky(n,t){var e=Gy(n),r=Array.from(e.keys()),i=Array.from(t.keys()),a=fr(r,i),o=fr(i,r),s=se(a,function(_){return e.get(_)});s.forEach(function(_){_.addSimulcastSSRCs(),t.set(_.trackId,_)}),i=Array.from(t.keys());var u=fr(i,o),c=se(u,function(_){return t.get(_)}),l=!n.match(/a=rtpmap:[0-9]+ rtx/),f=se(c,function(_){return _.toSdpLines(l)}),p=se(new Set(n.split(`\r
`).concat(f))),v="a=x-google-flag:conference";return n.match(v)||p.push(v),p.join(`\r
`)}var zy=Ky,rt=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},ec=R,Yy=ec.difference,Vr=ec.flatMap,Jy=zy,Ri={0:"PCMU",8:"PCMA"};function Fe(n){return Array.from(Wt(n)).reduce(function(t,e){var r=e[0],i=e[1],a=t.get(i)||[];return t.set(i,a.concat(r))},new Map)}function Br(n){return ie(n).reduce(function(t,e){var r=De(e);return r?t.set(r,e):t},new Map)}function Wt(n){return nt(n).reduce(function(t,e){var r=new RegExp("a=rtpmap:"+e+" ([^/]+)"),i=n.match(r),a=i?i[1].toLowerCase():Ri[e]?Ri[e].toLowerCase():"";return t.set(e,a)},new Map)}function Pt(n,t){var e=new RegExp("^a=fmtp:"+n+" (.+)$","m"),r=t.match(e);return r&&r[1].split(";").reduce(function(i,a){var o=rt(a.split("="),2),s=o[0],u=o[1];return i[s]=isNaN(u)?u:parseInt(u,10),i},{})}function De(n){var t=n.match(/^a=mid:(.+)$/m);return t&&t[1]}function ie(n,t,e){return n.replace(/\r\n\r\n$/,`\r
`).split(`\r
m=`).slice(1).map(function(r){return"m="+r}).filter(function(r){var i=new RegExp("m="+(t||".*"),"gm"),a=new RegExp("a="+(e||".*"),"gm");return i.test(r)&&a.test(r)})}function nt(n){var t=n.split(`\r
`)[0],e=t.match(/([0-9]+)/g);return e?e.slice(1).map(function(r){return parseInt(r,10)}):[]}function Xy(n,t){t=t.map(function(a){var o=a.codec;return o.toLowerCase()});var e=Vr(t,function(a){return n.get(a)||[]}),r=Yy(Array.from(n.keys()),t),i=Vr(r,function(a){return n.get(a)});return e.concat(i)}function _n(n,t){var e=t.split(`\r
`),r=e[0],i=e.slice(1);return r=r.replace(/([0-9]+\s?)+$/,n.join(" ")),[r].concat(i).join(`\r
`)}function Zy(n,t,e){var r=ie(n),i=n.split(`\r
m=`)[0];return[i].concat(r.map(function(a){if(!/^m=(audio|video)/.test(a))return a;var o=a.match(/^m=(audio|video)/)[1],s=Fe(a),u=o==="audio"?t:e,c=Xy(s,u),l=_n(c,a),f=s.get("pcma")||[],p=s.get("pcmu")||[],v=o==="audio"?new Set(f.concat(p)):new Set;return v.has(c[0])?l.replace(/\r\nb=(AS|TIAS):([0-9]+)/g,""):l})).join(`\r
`)}function eg(n,t){var e=ie(n),r=n.split(`\r
m=`)[0];return[r].concat(e.map(function(i){if(i=i.replace(/\r\n$/,""),!/^m=video/.test(i))return i;var a=Fe(i),o=nt(i),s=new Set(a.get("vp8")||[]),u=o.some(function(c){return s.has(c)});return u?Jy(i,t):i})).concat("").join(`\r
`)}function tg(n,t,e,r,i){var a=e.get(n)||[];if(a.length<=1)return a;var o=Pt(t,i);if(!o)return a;var s=a.find(function(u){var c=Pt(u,r);return c&&Object.keys(o).every(function(l){return o[l]===c[l]})});return typeof s=="number"?[s]:a}function rg(n,t,e){if(!/^m=(audio|video)/.test(n))return n;var r=De(n),i=r&&t.get(r);if(!i)return n;var a=Wt(i),o=Fe(n),s=Vr(Array.from(a),function(f){var p=rt(f,2),v=p[0],_=p[1];return _!=="rtx"&&!e.includes(_)?tg(_,v,o,n,i):[]}),u=o.get("rtx")||[];s=s.concat(u.filter(function(f){var p=Pt(f,n);return p&&s.includes(p.apt)}));var c=n.split(`\r
`).filter(function(f){var p=f.match(/^a=(rtpmap|fmtp|rtcp-fb):(.+) .+$/),v=p&&p[2];return!p||v&&s.includes(parseInt(v,10))}),l=nt(n).filter(function(f){return s.includes(f)});return _n(l,c.join(`\r
`))}function ng(n,t){var e=ie(n),r=n.split(`\r
m=`)[0],i=Br(t);return[r].concat(e.map(function(a){return rg(a,i,[])})).join(`\r
`)}function ig(n,t,e,r){r===void 0&&(r=!1);var i=Br(e),a=Br(t),o=ie(n),s=n.split(`\r
m=`)[0];return[s].concat(o.map(function(u){if(u=u.replace(/\r\n$/,""),!/^m=video/.test(u))return u;var c=u.match(/^a=mid:(.+)$/m),l=c&&c[1];if(!l)return u;var f=i.get(l),p=Wt(f),v=nt(f),_=v.length&&p.get(v[0])==="vp8",m=r||!_;return m?a.get(l).replace(/\r\n$/,""):u})).concat("").join(`\r
`)}function ag(n,t,e){var r=Array.from(e).reduce(function(i,a){var o=rt(a,2),s=o[0],u=o[1],c=ie(n,s,"send(only|recv)"),l=c.map(De).filter(function(f){return!t.has(f)});return l.forEach(function(f,p){return i.set(f,u[p])}),i},new Map);return tc(n,r)}function tc(n,t){var e=ie(n),r=n.split(`\r
m=`)[0];return[r].concat(e.map(function(i){if(!/^m=(audio|video)/.test(i))return i;var a=De(i);if(!a)return i;var o=t.get(a);if(!o)return i;var s=(i.match(/^a=msid:(.+)$/m)||[])[1];if(!s)return i;var u=rt(s.split(" "),2),c=u[0],l=u[1],f=new RegExp("msid:"+c+(l?" "+l:"")+"$","gm");return i.replace(f,"msid:"+c+" "+o)})).join(`\r
`)}function og(n,t){return n.split(`\r
`).filter(function(e){return!t.find(function(r){return new RegExp("a=ssrc:.*"+r+":","g").test(e)})}).join(`\r
`)}function sg(n){var t=ie(n),e=n.split(`\r
m=`)[0];return[e].concat(t.map(function(r){if(!/^m=video/.test(r))return r;var i=Fe(r),a=i.get("rtx");if(!a)return r;var o=new Set(nt(r));a.forEach(function(l){return o.delete(l)});var s=r.match(/a=ssrc-group:FID [0-9]+ ([0-9]+)/),u=s&&s[1],c=[/^a=fmtp:.+ apt=.+$/,/^a=rtpmap:.+ rtx\/.+$/,/^a=ssrc-group:.+$/].concat(u?[new RegExp("^a=ssrc:"+u+" .+$")]:[]);return r=r.split(`\r
`).filter(function(l){return c.every(function(f){return!f.test(l)})}).join(`\r
`),_n(Array.from(o),r)})).join(`\r
`)}function Li(n,t){var e=Object.entries(t).map(function(r){var i=rt(r,2),a=i[0],o=i[1];return a+"="+o}).join(";");return"a=fmtp:"+n+" "+e}function cg(n,t){var e=ie(n),r=n.split(`\r
m=`)[0];return t=t||e.filter(function(i){return/^m=audio/.test(i)}).map(De),[r].concat(e.map(function(i){if(!/^m=audio/.test(i))return i;var a=Fe(i),o=a.get("opus");if(!o)return i;var s=Pt(o,i);if(!s)return i;var u=Li(o,s),c=new RegExp(u),l=De(i);t.includes(l)?s.usedtx=1:delete s.usedtx;var f=Li(o,s);return i.replace(c,f)})).join(`\r
`)}z.addOrRewriteNewTrackIds=ag;z.addOrRewriteTrackIds=tc;z.createCodecMapForMediaSection=Fe;z.createPtToCodecName=Wt;z.disableRtx=sg;z.enableDtxForOpus=cg;z.filterLocalCodecs=ng;z.getMediaSections=ie;z.removeSSRCAttributes=og;z.revertSimulcast=ig;z.setCodecPreferences=Zy;z.setSimulcast=eg;var ug=function(){function n(t){t=Object.assign({getKey:function(r){return r},getValue:function(r){return r},isLessThanOrEqualTo:function(r,i){return r<=i}},t),Object.defineProperties(this,{_getKey:{value:t.getKey},_getValue:{value:t.getValue},_isLessThanOrEqualTo:{value:t.isLessThanOrEqualTo},_map:{value:new Map}})}return n.prototype.toMap=function(){return new Map(this._map)},n.prototype.updateAndFilter=function(t){return t.filter(this.update,this)},n.prototype.update=function(t){var e=this._getKey(t),r=this._getValue(t);return this._map.has(e)&&this._isLessThanOrEqualTo(r,this._map.get(e))?!1:(this._map.set(e,r),!0)},n}(),lg=ug,dg=lg,fg=function(){function n(){Object.defineProperties(this,{_filter:{value:new dg({getKey:function(e){return e.ufrag},isLessThanOrEqualTo:function(e,r){return e.revision<=r.revision}})},_ufrag:{writable:!0,value:null},ufrag:{enumerable:!0,get:function(){return this._ufrag}}})}return n.prototype.setUfrag=function(t){this._ufrag=t;var e=this._filter.toMap().get(t);return e?e.candidates:[]},n.prototype.update=function(t){t.candidates=t.candidates||[];var e=this._filter.toMap().get(t.ufrag),r=e?e.candidates:[];return this._filter.update(t)&&this._ufrag===t.ufrag?t.candidates.slice(r.length):[]},n}(),pg=fg,rc=U,vg=rc.ICE_ACTIVITY_CHECK_PERIOD_MS,hg=rc.ICE_INACTIVITY_THRESHOLD_MS,_g=function(){function n(t,e){e=Object.assign({activityCheckPeriodMs:vg,inactivityThresholdMs:hg},e),Object.defineProperties(this,{_activityCheckPeriodMs:{value:e.activityCheckPeriodMs},_inactivityThresholdMs:{value:e.inactivityThresholdMs},_lastActivity:{value:null,writable:!0},_peerConnection:{value:t},_timer:{value:null,writable:!0},_onIceConnectionStateChanged:{value:null,writable:!0}})}return n.prototype._getActivePairStat=function(t){var e=Array.from(t.values()),r=e.find(function(i){return i.type==="candidate-pair"&&i.nominated});return r||{bytesReceived:0,timestamp:Math.round(new Date().getTime())}},n.prototype._getIceConnectionStats=function(){var t=this;return this._peerConnection.getStats().then(function(e){return t._getActivePairStat(e)}).catch(function(){return null})},n.prototype._scheduleInactivityCallback=function(t){var e=this;t&&this._onIceConnectionStateChanged===null?(this._onIceConnectionStateChanged=function(){e._peerConnection.iceConnectionState==="disconnected"&&t()},this._peerConnection.addEventListener("iceconnectionstatechange",this._onIceConnectionStateChanged)):!t&&this._onIceConnectionStateChanged&&(this._peerConnection.removeEventListener("iceconnectionstatechange",this._onIceConnectionStateChanged),this._onIceConnectionStateChanged=null)},n.prototype.start=function(t){var e=this;this.stop(),this._timer=setInterval(function(){e._getIceConnectionStats().then(function(r){r&&((!e._lastActivity||e._lastActivity.bytesReceived!==r.bytesReceived)&&(e._lastActivity=r,e._scheduleInactivityCallback(null)),r.timestamp-e._lastActivity.timestamp>=e._inactivityThresholdMs&&(e._peerConnection.iceConnectionState==="disconnected"?t():e._onIceConnectionStateChanged===null&&e._scheduleInactivityCallback(t)))})},this._activityCheckPeriodMs)},n.prototype.stop=function(){this._scheduleInactivityCallback(null),this._timer!==null&&(clearInterval(this._timer),this._timer=null,this._lastActivity=null)},n}(),mg=_g,yg=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),gg=K.EventEmitter,bg=function(n){yg(t,n);function t(e){var r=n.call(this)||this;return Object.defineProperties(r,{_dataChannel:{value:e},_messageQueue:{value:[]}}),e.addEventListener("open",function(){r._messageQueue.splice(0).forEach(function(i){return r._publish(i)})}),e.addEventListener("message",function(i){var a=i.data;try{var o=JSON.parse(a);r.emit("message",o)}catch{}}),r.publish({type:"ready"}),r}return t.prototype._publish=function(e){var r=JSON.stringify(e);try{this._dataChannel.send(r)}catch{}},t.prototype.publish=function(e){var r=this._dataChannel;return r.readyState==="closing"||r.readyState==="closed"?!1:r.readyState==="connecting"?(this._messageQueue.push(e),!0):(this._publish(e),!0)},t}(gg),Sg=bg,wg=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),kg=_o,Tg=Sg,Pg=function(n){wg(t,n);function t(e){var r=n.call(this,e.label,e.maxPacketLifeTime,e.maxRetransmits,e.ordered)||this;return Object.defineProperties(r,{_dataChannel:{value:e}}),e.binaryType="arraybuffer",e.addEventListener("message",function(i){r.emit("message",i.data)}),e.addEventListener("close",function(){r.emit("close")}),r}return t.prototype.stop=function(){this._dataChannel.close(),n.prototype.stop.call(this)},t.prototype.toDataTransport=function(){return new Tg(this._dataChannel)},t}(kg),Eg=Pg,Og=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Cg=co,Rg=function(n){Og(t,n);function t(e,r){return n.call(this,e,r)||this}return t}(Cg),Lg=Rg,xg=z.getMediaSections,Ag=function(){function n(){Object.defineProperties(this,{_midsToTrackIds:{value:new Map,writable:!0}})}return n.prototype.match=function(t){return this._midsToTrackIds.get(t.transceiver.mid)||null},n.prototype.update=function(t){var e=xg(t,"(audio|video)");this._midsToTrackIds=e.reduce(function(r,i){var a=i.match(/^a=mid:(.+)$/m)||[],o=i.match(/^a=msid:.+ (.+)$/m)||[],s=a[1],u=o[1];return s&&u?r.set(s,u):r},this._midsToTrackIds)},n}(),Ig=Ag,Mg=be.RTCSessionDescription,nc=z,jg=nc.createPtToCodecName,Dg=nc.getMediaSections;function $g(n){var t={type:n.type};return n.type!=="rollback"&&(t.sdp=Ng(n.sdp)),new Mg(t)}function Ng(n){var t=Dg(n),e=n.split(`\r
m=`)[0];return[e].concat(t.map(Fg)).join(`\r
`)}function Fg(n){var t=jg(n);n=Vg(n,t);var e=Bg(t),r=e.get("rtx")||new Set,i=new Set,a=Ug(n,t,r,i),o=Wg(a,i),s=Array.from(i),u=["h264","vp8","vp9"],c=u.reduce(function(l,f){var p=e.get(f)||new Set;return Array.from(p).reduce(function(v,_){return o.has(_)?v:v.add(_)},l)},new Set);return c.forEach(function(l){if(s.length){var f=s.shift();n=xi(n,f),n=qg(n,f,l)}}),s.forEach(function(l){n=xi(n,l),n=Hg(n,l)}),n}function Vg(n,t){return Array.from(t.keys()).reduce(function(e,r){var i=new RegExp("^a=rtpmap:"+r+" rtx.+$","gm");return(e.match(i)||[]).slice(t.get(r)==="rtx"?1:0).reduce(function(a,o){var s=new RegExp(`\r
`+o),u=new RegExp(`\r
a=fmtp:`+r+" apt=[0-9]+");return a.replace(s,"").replace(u,"")},e)},n)}function Bg(n){var t=new Map;return n.forEach(function(e,r){var i=t.get(e)||new Set;return t.set(e,i.add(r))}),t}function Ug(n,t,e,r){return Array.from(e).reduce(function(i,a){var o=new RegExp("a=fmtp:"+a+" apt=(\\d+)"),s=n.match(o);if(!s)return r.add(a),i;var u=Number.parseInt(s[1]);if(!t.has(u))return r.add(a),i;var c=t.get(u);return c==="rtx"?(r.add(a),i):i.set(a,u)},new Map)}function Wg(n,t){var e=Array.from(n).reduce(function(r,i){var a=i[0],o=i[1],s=r.get(o)||new Set;return r.set(o,s.add(a))},new Map);return Array.from(e).reduce(function(r,i){var a=i[0],o=Array.from(i[1]);return o.length>1?(o.forEach(function(s){t.add(s)}),r):r.set(a,o[0])},new Map)}function xi(n,t){var e=new RegExp("a=fmtp:"+t+`.*\r
`,"gm");return n.replace(e,"")}function Hg(n,t){var e=new RegExp("a=rtpmap:"+t+`.*\r
`,"gm");return n.replace(e,"")}function qg(n,t,e){return n.endsWith(`\r
`)?n+"a=fmtp:"+t+" apt="+e+`\r
`:n+`\r
a=fmtp:`+t+" apt="+e}var Qg=$g,Gg=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Kg=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},zg=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Yg=Xs,Ht=be,Jg=Ht.RTCIceCandidate,Xg=Ht.RTCPeerConnection,Zg=Ht.RTCSessionDescription,eb=Ht.getStats,ic=V,qt=U,tb=qt.DEFAULT_ICE_GATHERING_TIMEOUT_MS,rb=qt.DEFAULT_LOG_LEVEL,nb=qt.DEFAULT_SESSION_TIMEOUT_SEC,ib=qt.iceRestartBackoffConfig,Z=z,ab=Z.addOrRewriteNewTrackIds,ob=Z.addOrRewriteTrackIds,ac=Z.createCodecMapForMediaSection,Ai=Z.disableRtx,ht=Z.enableDtxForOpus,sb=Z.filterLocalCodecs,Ke=Z.getMediaSections,Ii=Z.removeSSRCAttributes,cb=Z.revertSimulcast,ub=Z.setCodecPreferences,lb=Z.setSimulcast,db=we,oc=L,vr=oc.MediaClientLocalDescFailedError,hr=oc.MediaClientRemoteDescFailedError,it=R,fb=it.buildLogLevels,pb=it.getPlatform,vb=it.isChromeScreenShareTrack,hb=it.oncePerTick,_b=it.defer,mb=pg,yb=mg,gb=Eg,bb=Lg,Sb=$e,wb=ce,kb=Ig,Mi=Qg,mn=ic.guessBrowser(),Tb=pb(),Pb=/android/.test(Tb),Eb=mn==="chrome",ye=mn==="firefox",Ob=mn==="safari",Cb=0,Rb={open:["closed","updating"],updating:["closed","open"],closed:[]},Lb=function(n){Gg(t,n);function t(e,r,i,a){var o=n.call(this,"open",Rb)||this;a=Object.assign({enableDscp:!1,dummyAudioMediaStreamTrack:null,isChromeScreenShareTrack:vb,iceServers:[],logLevel:rb,offerOptions:{},revertSimulcast:cb,sessionTimeout:nb*1e3,setCodecPreferences:ub,setSimulcast:lb,Backoff:Yg,IceConnectionMonitor:yb,RTCIceCandidate:Jg,RTCPeerConnection:Xg,RTCSessionDescription:Zg,Timeout:db},a);var s=Ni(a),u=fb(a.logLevel),c=a.RTCPeerConnection;a.enableDscp===!0&&(a.chromeSpecificConstraints=a.chromeSpecificConstraints||{},a.chromeSpecificConstraints.optional=a.chromeSpecificConstraints.optional||[],a.chromeSpecificConstraints.optional.push({googDscp:!0}));var l=a.log?a.log.createLog("webrtc",o):new wb("webrtc",o,u,a.loggerName),f=new c(s,a.chromeSpecificConstraints);a.dummyAudioMediaStreamTrack&&f.addTrack(a.dummyAudioMediaStreamTrack),Object.defineProperties(o,{_appliedTrackIdsToAttributes:{value:new Map,writable:!0},_dataChannels:{value:new Map},_dataTrackReceivers:{value:new Set},_descriptionRevision:{writable:!0,value:0},_didGenerateLocalCandidates:{writable:!0,value:!1},_enableDscp:{value:a.enableDscp},_encodingParameters:{value:r},_isChromeScreenShareTrack:{value:a.isChromeScreenShareTrack},_iceGatheringFailed:{value:!1,writable:!0},_iceGatheringTimeout:{value:new a.Timeout(function(){return o._handleIceGatheringTimeout()},tb,!1)},_iceRestartBackoff:{value:new a.Backoff(ib)},_instanceId:{value:++Cb},_isIceConnectionInactive:{writable:!0,value:!1},_isIceLite:{writable:!0,value:!1},_isIceRestartBackoffInProgress:{writable:!0,value:!1},_isRestartingIce:{writable:!0,value:!1},_lastIceConnectionState:{writable:!0,value:null},_lastStableDescriptionRevision:{writable:!0,value:0},_localCandidates:{writable:!0,value:[]},_localCodecs:{value:new Set},_localCandidatesRevision:{writable:!0,value:1},_localDescriptionWithoutSimulcast:{writable:!0,value:null},_localDescription:{writable:!0,value:null},_localUfrag:{writable:!0,value:null},_log:{value:l},_eventObserver:{value:a.eventObserver},_remoteCodecMaps:{value:new Map},_rtpSenders:{value:new Map},_rtpNewSenders:{value:new Set},_iceConnectionMonitor:{value:new a.IceConnectionMonitor(f)},_mediaTrackReceivers:{value:new Set},_needsAnswer:{writable:!0,value:!1},_negotiationRole:{writable:!0,value:null},_offerOptions:{writable:!0,value:a.offerOptions},_onEncodingParametersChanged:{value:hb(function(){o._needsAnswer||sc(o)})},_peerConnection:{value:f},_preferredAudioCodecs:{value:i.audio},_preferredVideoCodecs:{value:i.video},_shouldApplyDtx:{value:i.audio.every(function(v){var _=v.codec;return _!=="opus"})||i.audio.some(function(v){var _=v.codec,m=v.dtx;return _==="opus"&&m})},_queuedDescription:{writable:!0,value:null},_iceReconnectTimeout:{value:new a.Timeout(function(){l.debug("ICE reconnect timed out"),o.close()},a.sessionTimeout,!1)},_recycledTransceivers:{value:{audio:[],video:[]}},_replaceTrackPromises:{value:new Map},_remoteCandidates:{writable:!0,value:new mb},_setCodecPreferences:{value:ye&&Pb&&i.video[0]&&i.video[0].codec.toLowerCase()!=="h264"?function(v){return v}:a.setCodecPreferences},_setSimulcast:{value:a.setSimulcast},_revertSimulcast:{value:a.revertSimulcast},_RTCIceCandidate:{value:a.RTCIceCandidate},_RTCPeerConnection:{value:a.RTCPeerConnection},_RTCSessionDescription:{value:a.RTCSessionDescription},_shouldOffer:{writable:!0,value:!1},_shouldRestartIce:{writable:!0,value:!1},_trackIdsToAttributes:{value:new Map,writable:!0},_trackMatcher:{writable:!0,value:null},_mediaTrackSenderToPublisherHints:{value:new Map},id:{enumerable:!0,value:e}}),r.on("changed",o._onEncodingParametersChanged),f.addEventListener("connectionstatechange",o._handleConnectionStateChange.bind(o)),f.addEventListener("datachannel",o._handleDataChannelEvent.bind(o)),f.addEventListener("icecandidate",o._handleIceCandidateEvent.bind(o)),f.addEventListener("iceconnectionstatechange",o._handleIceConnectionStateChange.bind(o)),f.addEventListener("icegatheringstatechange",o._handleIceGatheringStateChange.bind(o)),f.addEventListener("signalingstatechange",o._handleSignalingStateChange.bind(o)),f.addEventListener("track",o._handleTrackEvent.bind(o));var p=o;return o.on("stateChanged",function v(_){_==="closed"&&(p.removeListener("stateChanged",v),p._dataChannels.forEach(function(m,y){p.removeDataTrackSender(y)}))}),o}return t.prototype.toString=function(){return"[PeerConnectionV2 #"+this._instanceId+": "+this.id+"]"},t.prototype.setEffectiveAdaptiveSimulcast=function(e){this._log.debug("Setting setEffectiveAdaptiveSimulcast: ",e),this._preferredVideoCodecs.forEach(function(r){"adaptiveSimulcast"in r&&(r.adaptiveSimulcast=e)})},Object.defineProperty(t.prototype,"_shouldApplySimulcast",{get:function(){if(!Eb&&!Ob)return!1;var e=this._preferredVideoCodecs.some(function(r){return r.codec.toLowerCase()==="vp8"&&r.simulcast&&r.adaptiveSimulcast!==!1});return e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"connectionState",{get:function(){return this.iceConnectionState==="failed"?"failed":this._peerConnection.connectionState||this.iceConnectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._isIceConnectionInactive&&this._peerConnection.iceConnectionState==="disconnected"||this._iceGatheringFailed?"failed":this._peerConnection.iceConnectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isApplicationSectionNegotiated",{get:function(){return this._peerConnection.signalingState!=="closed"?this._peerConnection.localDescription?Ke(this._peerConnection.localDescription.sdp,"application").length>0:!1:!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_isAdaptiveSimulcastEnabled",{get:function(){var e=this._preferredVideoCodecs.find(function(r){return"adaptiveSimulcast"in r});return e&&e.adaptiveSimulcast===!0},enumerable:!1,configurable:!0}),t.prototype._maybeUpdateEncodings=function(e,r,i){if(i===void 0&&(i=!1),e.kind!=="video"||e.readyState==="ended")return!1;var a=e.getSettings(),o=a.height,s=a.width;if(typeof o!="number"||typeof s!="number")return!1;var u=ic.guessBrowser();return u==="safari"||u==="chrome"&&this._isAdaptiveSimulcastEnabled?(this._updateEncodings(e,r,i),!0):!1},t.prototype._updateEncodings=function(e,r,i){if(this._isChromeScreenShareTrack(e)){var a=[{scaleResolutionDownBy:1},{scaleResolutionDownBy:1}];r.forEach(function(v,_){var m=a[_];m?(v.scaleResolutionDownBy=m.scaleResolutionDownBy,i&&delete v.active):(v.active=!1,delete v.scaleResolutionDownBy)})}else{var o=e.getSettings(),s=o.width,u=o.height,c=[{pixels:960*540,maxActiveLayers:3},{pixels:480*270,maxActiveLayers:2},{pixels:0,maxActiveLayers:1}],l=s*u,f=c.find(function(v){return l>=v.pixels}),p=Math.min(r.length,f.maxActiveLayers);r.forEach(function(v,_){var m=_<p;m?(v.scaleResolutionDownBy=1<<p-_-1,i&&(v.active=!0)):(v.active=!1,delete v.scaleResolutionDownBy)})}this._log.debug("_updateEncodings:",r.map(function(v,_){var m=v.active,y=v.scaleResolutionDownBy;return"["+_+": "+m+", "+(y||0)+"]"}).join(", "))},t.prototype._addIceCandidate=function(e){var r=this;return Promise.resolve().then(function(){return e=new r._RTCIceCandidate(e),r._peerConnection.addIceCandidate(e)}).catch(function(i){r._log.warn("Failed to add RTCIceCandidate "+(e?'"'+e.candidate+'"':"null")+": "+i.message)})},t.prototype._addIceCandidates=function(e){return Promise.all(e.map(this._addIceCandidate,this)).then(function(){})},t.prototype._addOrUpdateTransceiver=function(e){var r=this,i=jb(this,e.kind);if(i&&i.sender){var a=i.sender.track?i.sender.track.id:null;return a&&this._log.warn("Reusing transceiver: "+i.mid+"] "+a+" => "+e.id),this._replaceTrackPromises.set(i,i.sender.replaceTrack(e).then(function(){i.direction="sendrecv"},function(){}).finally(function(){r._replaceTrackPromises.delete(i)})),i}return this._peerConnection.addTransceiver(e)},t.prototype._checkIceBox=function(e){var r=$i(e);if(!r)return Promise.resolve();var i=this._remoteCandidates.setUfrag(r);return this._addIceCandidates(i)},t.prototype._answer=function(e){var r=this;return Promise.resolve().then(function(){return r._negotiationRole||(r._negotiationRole="answerer"),r._setRemoteDescription(e)}).catch(function(){throw new hr}).then(function(){return r._peerConnection.createAnswer()}).then(function(i){ye?i=new r._RTCSessionDescription({sdp:Ai(i.sdp),type:i.type}):i=Mi(i);var a=Ii(i.sdp,["mslabel","label"]);if(r._shouldApplySimulcast){var o=a;a=r._setSimulcast(o,r._trackIdsToAttributes),a=r._revertSimulcast(a,o,e.sdp)}return a=a.replace(/42e015/g,"42e01f"),r._setLocalDescription({type:i.type,sdp:a})}).then(function(){return r._checkIceBox(e)}).then(function(){return r._queuedDescription&&r._updateDescription(r._queuedDescription)}).then(function(){return r._queuedDescription=null,r._maybeReoffer(r._peerConnection.localDescription)}).catch(function(i){var a=i instanceof hr?i:new vr;throw r._publishMediaWarning({message:"Failed to _answer",code:a.code,error:i}),a})},t.prototype._close=function(){return this._iceConnectionMonitor.stop(),this._peerConnection.signalingState!=="closed"?(this._peerConnection.close(),this.preempt("closed"),this._encodingParameters.removeListener("changed",this._onEncodingParametersChanged),!0):!1},t.prototype._handleConnectionStateChange=function(){this.emit("connectionStateChanged")},t.prototype._handleDataChannelEvent=function(e){var r=this,i=e.channel,a=new gb(i);this._dataTrackReceivers.add(a),i.addEventListener("close",function(){r._dataTrackReceivers.delete(a)}),this.emit("trackAdded",a)},t.prototype._handleGlare=function(e){var r=this;return this._log.debug("Glare detected; rolling back"),this._isRestartingIce&&(this._log.debug("An ICE restart was in progress; we'll need to restart ICE again after rolling back"),this._isRestartingIce=!1,this._shouldRestartIce=!0),Promise.resolve().then(function(){return r._trackIdsToAttributes=new Map(r._appliedTrackIdsToAttributes),r._setLocalDescription({type:"rollback"})}).then(function(){return r._needsAnswer=!1,r._answer(e)}).then(function(i){return i?Promise.resolve():r._offer()})},t.prototype._publishMediaWarning=function(e){var r=e.message,i=e.code,a=e.error,o=e.sdp;this._eventObserver.emit("event",{level:"warning",name:"error",group:"media",payload:{message:r,code:i,context:JSON.stringify({error:a.message,sdp:o})}})},t.prototype._handleIceCandidateEvent=function(e){e.candidate&&(this._log.debug("Clearing ICE gathering timeout"),this._didGenerateLocalCandidates=!0,this._iceGatheringTimeout.clear(),this._localCandidates.push(e.candidate));var r={ice:{candidates:this._isIceLite?[]:this._localCandidates.slice(),ufrag:this._localUfrag},id:this.id};e.candidate||(r.ice.complete=!0),this._isIceLite&&e.candidate||(r.ice.revision=this._localCandidatesRevision++,this.emit("candidates",r))},t.prototype._handleIceConnectionStateChange=function(){var e=this,r=this._peerConnection.iceConnectionState,i=["connected","completed"].includes(r),a=this._log;a.debug('ICE connection state is "'+r+'"'),i&&(this._iceReconnectTimeout.clear(),this._iceRestartBackoff.reset()),this._lastIceConnectionState!=="failed"&&r==="failed"&&!this._shouldRestartIce&&!this._isRestartingIce?(a.warn("ICE failed"),this._initiateIceRestartBackoff()):["disconnected","failed"].includes(this._lastIceConnectionState)&&i&&a.debug("ICE reconnected"),r==="connected"?(this._isIceConnectionInactive=!1,this._iceConnectionMonitor.start(function(){e._iceConnectionMonitor.stop(),!e._shouldRestartIce&&!e._isRestartingIce&&(a.warn("ICE Connection Monitor detected inactivity"),e._isIceConnectionInactive=!0,e._initiateIceRestartBackoff(),e.emit("iceConnectionStateChanged"),e.emit("connectionStateChanged"))})):["disconnected","completed"].includes(r)||(this._iceConnectionMonitor.stop(),this._isIceConnectionInactive=!1),this._lastIceConnectionState=r,this.emit("iceConnectionStateChanged")},t.prototype._handleIceGatheringTimeout=function(){this._log.warn("ICE failed to gather any local candidates"),this._iceGatheringFailed=!0,this._initiateIceRestartBackoff(),this.emit("iceConnectionStateChanged"),this.emit("connectionStateChanged")},t.prototype._handleIceGatheringStateChange=function(){var e=this._peerConnection.iceGatheringState,r=this._log;r.debug('ICE gathering state is "'+e+'"');var i=this._iceGatheringTimeout,a=i.delay,o=i.isSet;e==="gathering"&&!this._didGenerateLocalCandidates&&!o&&(r.debug("Starting ICE gathering timeout: "+a),this._iceGatheringFailed=!1,this._iceGatheringTimeout.start())},t.prototype._handleSignalingStateChange=function(){this._peerConnection.signalingState==="stable"&&(this._appliedTrackIdsToAttributes=new Map(this._trackIdsToAttributes))},t.prototype._handleTrackEvent=function(e){var r=this,i=this._peerConnection.remoteDescription?this._peerConnection.remoteDescription.sdp:null;this._trackMatcher=this._trackMatcher||new kb,this._trackMatcher.update(i);var a=e.track,o=this._trackMatcher.match(e)||a.id,s=new bb(o,a);this._mediaTrackReceivers.forEach(function(u){u.track.id===s.track.id&&r._mediaTrackReceivers.delete(u)}),this._mediaTrackReceivers.add(s),a.addEventListener("ended",function(){return r._mediaTrackReceivers.delete(s)}),this.emit("trackAdded",s)},t.prototype._initiateIceRestart=function(){if(this._peerConnection.signalingState!=="closed"){var e=this._log;e.warn("Attempting to restart ICE"),this._didGenerateLocalCandidates=!1,this._isIceRestartBackoffInProgress=!1,this._shouldRestartIce=!0;var r=this._iceReconnectTimeout,i=r.delay,a=r.isSet;a||(e.debug("Starting ICE reconnect timeout: "+i),this._iceReconnectTimeout.start()),this.offer().catch(function(o){e.error("offer failed in _initiateIceRestart with: "+o.message)})}},t.prototype._initiateIceRestartBackoff=function(){var e=this;this._peerConnection.signalingState==="closed"||this._isIceRestartBackoffInProgress||(this._log.warn("An ICE restart has been scheduled"),this._isIceRestartBackoffInProgress=!0,this._iceRestartBackoff.backoff(function(){return e._initiateIceRestart()}))},t.prototype._maybeReoffer=function(e){var r=this._shouldOffer;if(e&&e.sdp){var i=this._peerConnection.getSenders().filter(function(c){return c.track});r=["audio","video"].reduce(function(c,l){var f=Ke(e.sdp,l,"(sendrecv|sendonly)"),p=i.filter(Ab.bind(null,l));return c||f.length<p.length},r);var a=this._dataChannels.size>0,o=Ke(e.sdp,"application").length>0,s=a&&!o;r=r||s}var u=r?this._offer():Promise.resolve();return u.then(function(){return r})},t.prototype._offer=function(){var e=this,r=Object.assign({},this._offerOptions);return this._needsAnswer=!0,this._shouldRestartIce&&(this._shouldRestartIce=!1,this._isRestartingIce=!0,r.iceRestart=!0),Promise.all(this._replaceTrackPromises.values()).then(function(){return e._peerConnection.createOffer(r)}).catch(function(i){var a=new vr;throw e._publishMediaWarning({message:"Failed to create offer",code:a.code,error:i}),a}).then(function(i){ye?i=new e._RTCSessionDescription({sdp:Ai(i.sdp),type:i.type}):i=Mi(i);var a=Ii(i.sdp,["mslabel","label"]);a=e._peerConnection.remoteDescription?sb(a,e._peerConnection.remoteDescription.sdp):a;var o=e._setCodecPreferences(a,e._preferredAudioCodecs,e._preferredVideoCodecs);return e._shouldOffer=!1,e._negotiationRole||(e._negotiationRole="offerer"),e._shouldApplySimulcast&&(e._localDescriptionWithoutSimulcast={type:"offer",sdp:o},o=e._setSimulcast(o,e._trackIdsToAttributes)),e._setLocalDescription({type:"offer",sdp:o})})},t.prototype._getMediaTrackSenderId=function(e){var r=Array.from(this._rtpSenders.keys()).find(function(i){var a=i.track.id;return a===e});return r?r.id:e},t.prototype._addOrRewriteLocalTrackIds=function(e){var r=this,i=this._peerConnection.getTransceivers(),a=i.filter(function(p){var v=p.sender,_=p.stopped;return!_&&v&&v.track}),o=a.filter(function(p){var v=p.mid;return v}),s=new Map(o.map(function(p){var v=p.mid,_=p.sender;return[v,r._getMediaTrackSenderId(_.track.id)]})),u=ob(e.sdp,s),c=a.filter(function(p){var v=p.mid;return!v}),l=new Map(["audio","video"].map(function(p){return[p,c.filter(function(v){var _=v.sender;return _.track.kind===p}).map(function(v){var _=v.sender;return r._getMediaTrackSenderId(_.track.id)})]})),f=ab(u,s,l);return new this._RTCSessionDescription({sdp:f,type:e.type})},t.prototype._rollbackAndApplyOffer=function(e){var r=this;return this._setLocalDescription({type:"rollback"}).then(function(){return r._setLocalDescription(e)})},t.prototype._setLocalDescription=function(e){var r=this;return e.type!=="rollback"&&this._shouldApplyDtx&&(e=new this._RTCSessionDescription({sdp:ht(e.sdp),type:e.type})),this._peerConnection.setLocalDescription(e).catch(function(i){r._log.warn('Calling setLocalDescription with an RTCSessionDescription of type "'+e.type+'" failed with the error "'+i.message+'".',i);var a=new vr,o={message:'Calling setLocalDescription with an RTCSessionDescription of type "'+e.type+'" failed',code:a.code,error:i};throw e.sdp&&(r._log.warn("The SDP was "+e.sdp),o.sdp=e.sdp),r._publishMediaWarning(o),a}).then(function(){e.type!=="rollback"&&(r._localDescription=r._addOrRewriteLocalTrackIds(e),r._shouldApplyDtx&&(r._localDescription=new r._RTCSessionDescription({sdp:ht(r._localDescription.sdp,[]),type:r._localDescription.type})),r._localCandidates=[],e.type==="offer"?r._descriptionRevision++:e.type==="answer"&&(r._lastStableDescriptionRevision=r._descriptionRevision,Fi(r)),r._localUfrag=$i(e),r.emit("description",r.getState()))})},t.prototype._setRemoteDescription=function(e){var r=this;return e.sdp&&(e.sdp=this._setCodecPreferences(e.sdp,this._preferredAudioCodecs,this._preferredVideoCodecs),this._shouldApplyDtx?e.sdp=ht(e.sdp):e.sdp=ht(e.sdp,[]),ye&&(e.sdp=Ib(e.sdp)),this._peerConnection.remoteDescription||(this._isIceLite=/a=ice-lite/.test(e.sdp))),e=new this._RTCSessionDescription(e),Promise.resolve().then(function(){if(e.type==="answer"&&r._localDescriptionWithoutSimulcast){var i=r._preferredVideoCodecs.find(function(s){return"adaptiveSimulcast"in s}),a=!!i&&i.adaptiveSimulcast===!1,o=r._revertSimulcast(r._localDescription.sdp,r._localDescriptionWithoutSimulcast.sdp,e.sdp,a);if(r._localDescriptionWithoutSimulcast=null,o!==r._localDescription.sdp)return r._rollbackAndApplyOffer({type:r._localDescription.type,sdp:o})}}).then(function(){return r._peerConnection.setRemoteDescription(e)}).then(function(){e.type==="answer"&&(r._isRestartingIce&&(r._log.debug("An ICE restart was in-progress and is now completed"),r._isRestartingIce=!1),Fi(r))},function(i){throw r._log.warn('Calling setRemoteDescription with an RTCSessionDescription of type "'+e.type+'" failed with the error "'+i.message+'".',i),e.sdp&&r._log.warn("The SDP was "+e.sdp),i})},t.prototype._updateDescription=function(e){var r=this;switch(e.type){case"answer":case"pranswer":if(e.revision!==this._descriptionRevision||this._peerConnection.signalingState!=="have-local-offer")return Promise.resolve();this._descriptionRevision=e.revision;break;case"close":return this._close();case"create-offer":return e.revision<=this._lastStableDescriptionRevision?Promise.resolve():this._needsAnswer?(this._queuedDescription=e,Promise.resolve()):(this._descriptionRevision=e.revision,this._offer());case"offer":return e.revision<=this._lastStableDescriptionRevision||this._peerConnection.signalingState==="closed"?Promise.resolve():this._peerConnection.signalingState==="have-local-offer"?this._needsAnswer&&this._lastStableDescriptionRevision===0?(this._queuedDescription=e,Promise.resolve()):(this._descriptionRevision=e.revision,this._handleGlare(e)):(this._descriptionRevision=e.revision,this._answer(e).then(function(){}))}var i=e.revision;return Promise.resolve().then(function(){return r._setRemoteDescription(e)}).catch(function(a){var o=new hr;throw r._publishMediaWarning({message:'Calling setRemoteDescription with an RTCSessionDescription of type "'+e.type+'" failed',code:o.code,error:a,sdp:e.sdp}),o}).then(function(){return r._lastStableDescriptionRevision=i,r._needsAnswer=!1,r._checkIceBox(e)}).then(function(){return r._queuedDescription&&r._updateDescription(r._queuedDescription)}).then(function(){return r._queuedDescription=null,r._maybeReoffer(r._peerConnection.localDescription).then(function(){})})},t.prototype._updateIce=function(e){var r=this._remoteCandidates.update(e);return this._addIceCandidates(r)},t.prototype.addDataTrackSender=function(e){if(!this._dataChannels.has(e))try{var r={ordered:e.ordered};e.maxPacketLifeTime!==null&&(r.maxPacketLifeTime=e.maxPacketLifeTime),e.maxRetransmits!==null&&(r.maxRetransmits=e.maxRetransmits);var i=this._peerConnection.createDataChannel(e.id,r);e.addDataChannel(i),this._dataChannels.set(e,i)}catch(a){this._log.warn('Error creating an RTCDataChannel for DataTrack "'+e.id+'": '+a.message)}},t.prototype._handleQueuedPublisherHints=function(){var e=this;this._peerConnection.signalingState==="stable"&&this._mediaTrackSenderToPublisherHints.forEach(function(r,i){var a=r.deferred,o=r.encodings;e._mediaTrackSenderToPublisherHints.delete(i),e._setPublisherHint(i,o).then(function(s){return a.resolve(s)}).catch(function(s){return a.reject(s)})})},t.prototype._setPublisherHint=function(e,r){var i=this;if(ye)return Promise.resolve("COULD_NOT_APPLY_HINT");if(this._mediaTrackSenderToPublisherHints.has(e)){var a=this._mediaTrackSenderToPublisherHints.get(e);a.deferred.resolve("REQUEST_SKIPPED"),this._mediaTrackSenderToPublisherHints.delete(e)}var o=this._rtpSenders.get(e);if(!o)return this._log.warn("Could not apply publisher hint because RTCRtpSender was not found"),Promise.resolve("UNKNOWN_TRACK");if(this._peerConnection.signalingState==="closed")return this._log.warn('Could not apply publisher hint because signalingState was "closed"'),Promise.resolve("COULD_NOT_APPLY_HINT");if(this._peerConnection.signalingState!=="stable"){this._log.debug("Queuing up publisher hint because signalingState:",this._peerConnection.signalingState);var s=_b();return this._mediaTrackSenderToPublisherHints.set(e,{deferred:s,encodings:r}),s.promise}var u=o.getParameters();return r!==null&&r.forEach(function(c){var l=c.enabled,f=c.layer_index;u.encodings.length>f?(i._log.debug("layer:"+f+", active:"+u.encodings[f].active+" => "+l),u.encodings[f].active=l):i._log.warn("invalid layer:"+f+", active:"+l)}),this._maybeUpdateEncodings(o.track,u.encodings,r===null),o.setParameters(u).then(function(){return"OK"}).catch(function(c){return i._log.error("Failed to apply publisher hints:",c),"COULD_NOT_APPLY_HINT"})},t.prototype.addMediaTrackSender=function(e){var r=this;if(!(this._peerConnection.signalingState==="closed"||this._rtpSenders.has(e))){var i=this._addOrUpdateTransceiver(e.track),a=i.sender;e.addSender(a,function(o){return r._setPublisherHint(e,o)}),this._rtpNewSenders.add(a),this._rtpSenders.set(e,a)}},t.prototype.close=function(){this._close()&&(this._descriptionRevision++,this._localDescription={type:"close"},this.emit("description",this.getState()))},t.prototype.getTrackReceivers=function(){return Array.from(this._dataTrackReceivers).concat(Array.from(this._mediaTrackReceivers))},t.prototype.getState=function(){if(!this._localDescription)return null;var e=this._localDescription.type==="answer"?this._lastStableDescriptionRevision:this._descriptionRevision,r={type:this._localDescription.type,revision:e};return this._localDescription.sdp&&(r.sdp=this._localDescription.sdp),{description:r,id:this.id}},t.prototype.offer=function(){var e=this;return this._needsAnswer||this._isRestartingIce?(this._shouldOffer=!0,Promise.resolve()):this.bracket("offering",function(r){e.transition("updating",r);var i=e._needsAnswer||e._isRestartingIce?Promise.resolve():e._offer();return i.then(function(){e.tryTransition("open",r)},function(a){throw e.tryTransition("open",r),a})})},t.prototype.removeDataTrackSender=function(e){var r=this._dataChannels.get(e);r&&(e.removeDataChannel(r),this._dataChannels.delete(e),r.close())},t.prototype.removeMediaTrackSender=function(e){var r=this._rtpSenders.get(e);if(r){if(this._peerConnection.signalingState!=="closed"&&this._peerConnection.removeTrack(r),e.removeSender(r),this._mediaTrackSenderToPublisherHints.has(e)){var i=this._mediaTrackSenderToPublisherHints.get(e);i.deferred.resolve("UNKNOWN_TRACK"),this._mediaTrackSenderToPublisherHints.delete(e)}this._rtpNewSenders.delete(r),this._rtpSenders.delete(e)}},t.prototype.setConfiguration=function(e){typeof this._peerConnection.setConfiguration=="function"&&this._peerConnection.setConfiguration(Ni(e))},t.prototype.setIceReconnectTimeout=function(e){return this._iceReconnectTimeout.setDelay(e),this._log.debug("Updated ICE reconnection timeout period:",this._iceReconnectTimeout.delay),this},t.prototype.update=function(e){var r=this;return this.bracket("updating",function(i){if(r.state==="closed")return Promise.resolve();r.transition("updating",i);var a=[];return e.ice&&a.push(r._updateIce(e.ice)),e.description&&a.push(r._updateDescription(e.description)),Promise.all(a).then(function(){r.tryTransition("open",i)},function(o){throw r.tryTransition("open",i),o})})},t.prototype.getStats=function(){var e=this;return eb(this._peerConnection).then(function(r){return xb(e,r)})},t}(Sb);function ji(n,t){var e=n._getMediaTrackSenderId(t.trackId);return Object.assign(t,{trackId:e})}function Di(n,t){var e=zg([],Kg(n._mediaTrackReceivers)).find(function(i){return i.track.id===t.trackId}),r=e?e.id:null;return Object.assign(t,{trackId:r})}function xb(n,t){return Object.assign(t,{remoteAudioTrackStats:t.remoteAudioTrackStats.map(function(e){return Di(n,e)}),remoteVideoTrackStats:t.remoteVideoTrackStats.map(function(e){return Di(n,e)}),localAudioTrackStats:t.localAudioTrackStats.map(function(e){return ji(n,e)}),localVideoTrackStats:t.localVideoTrackStats.map(function(e){return ji(n,e)})})}function $i(n){if(n.sdp){var t=n.sdp.match(/^a=ice-ufrag:([a-zA-Z0-9+/]+)/m);if(t)return t[1]}return null}function Ni(n){return Object.assign({bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"},n)}function Ab(n,t){var e=t.track;return e&&e.kind===n&&e.readyState!=="ended"}function Ib(n){return n.replace(/a=msid:[^ ]+ /g,"a=msid:- ")}function Mb(n,t){return!n.stopped&&!t._replaceTrackPromises.has(n)&&["inactive","recvonly"].includes(n.direction)}function jb(n,t){var e={audio:n._preferredAudioCodecs.map(function(o){var s=o.codec;return s.toLowerCase()}),video:n._preferredVideoCodecs.map(function(o){var s=o.codec;return s.toLowerCase()})}[t],r=n._recycledTransceivers[t],i=e.find(function(o){return n._localCodecs.has(o)});if(!i)return r.shift();var a=r.find(function(o){var s=n._remoteCodecMaps.get(o.mid);return s&&s.has(i)});return a&&r.splice(r.indexOf(a),1),a}function Db(n){var t=n._peerConnection.localDescription;!t||!t.sdp||Ke(t.sdp).forEach(function(e){var r=ac(e);r.forEach(function(i,a){return n._localCodecs.add(a)})})}function $b(n){var t=n._peerConnection.remoteDescription;!t||!t.sdp||Ke(t.sdp).forEach(function(e){var r=e.match(/^a=mid:(.+)$/m);if(!(!r||!r[1])){var i=r[1],a=ac(e);n._remoteCodecMaps.set(i,a)}})}function Nb(n){n._recycledTransceivers.audio=[],n._recycledTransceivers.video=[],n._peerConnection.getTransceivers().forEach(function(t){if(Mb(t,n)){var e=t.receiver.track;n._recycledTransceivers[e.kind].push(t)}})}function Fi(n){Nb(n),Db(n),$b(n),sc(n).then(function(){n._handleQueuedPublisherHints()})}function sc(n){var t=n._encodingParameters,e=t.maxAudioBitrate,r=t.maxVideoBitrate,i=new Map([["audio",e],["video",r]]),a=[];return n._peerConnection.getSenders().filter(function(o){return o.track}).forEach(function(o){var s=i.get(o.track.kind),u=o.getParameters();s===null||s===0?Fb(u):n._isChromeScreenShareTrack(o.track)?n._log.warn("Not setting maxBitrate for "+o.track.kind+" Track "+o.track.id+" because it appears to be screen share track: "+o.track.label):Vb(u,s),!ye&&u.encodings.length>0&&(o.track.kind==="audio"?u.encodings[0].priority="high":n._isChromeScreenShareTrack(o.track)&&(u.encodings[0].priority="medium"),n._enableDscp&&(u.encodings[0].networkPriority="high"));var c=n._rtpNewSenders.has(o);n._maybeUpdateEncodings(o.track,u.encodings,c),n._rtpNewSenders.delete(o);var l=o.setParameters(u).catch(function(f){n._log.warn("Error while setting encodings parameters for "+o.track.kind+" Track "+o.track.id+": "+(f.message||f.name))});a.push(l)}),Promise.all(a)}function Fb(n){Array.isArray(n.encodings)&&n.encodings.forEach(function(t){return delete t.maxBitrate})}function Vb(n,t){ye?n.encodings=[{maxBitrate:t}]:n.encodings.forEach(function(e){e.maxBitrate=t})}var Bb=Lb,Ub=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),cc=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},uc=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Wb=V.guessBrowser,Hb=Bb,qb=uo,Qb=oo,pe=R,Gb=L.MediaConnectionError,Kb=Wb()==="firefox",zb=function(n){Ub(t,n);function t(e,r,i){var a=n.call(this)||this;i=Object.assign({audioContextFactory:Kb?jt():null,PeerConnectionV2:Hb},i);var o=i.audioContextFactory?i.audioContextFactory.getOrCreate(a):null,s=o?{offerToReceiveVideo:!0}:{offerToReceiveAudio:!0,offerToReceiveVideo:!0};return Object.defineProperties(a,{_audioContextFactory:{value:i.audioContextFactory},_closedPeerConnectionIds:{value:new Set},_configuration:{writable:!0,value:null},_configurationDeferred:{writable:!0,value:pe.defer()},_connectionState:{value:"new",writable:!0},_dummyAudioTrackSender:{value:o?new qb(Yb(o)):null},_encodingParameters:{value:e},_iceConnectionState:{writable:!0,value:"new"},_dataTrackSenders:{writable:!0,value:new Set},_lastConnectionState:{value:"new",writable:!0},_lastIceConnectionState:{writable:!0,value:"new"},_mediaTrackSenders:{writable:!0,value:new Set},_offerOptions:{value:s},_peerConnections:{value:new Map},_preferredCodecs:{value:r},_sessionTimeout:{value:null,writable:!0},_PeerConnectionV2:{value:i.PeerConnectionV2}}),a}return t.prototype.setEffectiveAdaptiveSimulcast=function(e){this._peerConnections.forEach(function(r){return r.setEffectiveAdaptiveSimulcast(e)}),this._preferredCodecs.video.forEach(function(r){"adaptiveSimulcast"in r&&(r.adaptiveSimulcast=e)})},Object.defineProperty(t.prototype,"connectionState",{get:function(){return this._connectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._iceConnectionState},enumerable:!1,configurable:!0}),t.prototype._closeAbsentPeerConnections=function(e){var r=new Set(e.map(function(i){return i.id}));return this._peerConnections.forEach(function(i){r.has(i.id)||i._close()}),this},t.prototype._getConfiguration=function(){return this._configurationDeferred.promise},t.prototype._getOrCreate=function(e,r){var i=this,a=this,o=this._peerConnections.get(e);if(!o){var s=this._PeerConnectionV2,u=Object.assign({dummyAudioMediaStreamTrack:this._dummyAudioTrackSender?this._dummyAudioTrackSender.track:null,offerOptions:this._offerOptions},this._sessionTimeout?{sessionTimeout:this._sessionTimeout}:{},r);try{o=new s(e,this._encodingParameters,this._preferredCodecs,u)}catch{throw new Gb}this._peerConnections.set(o.id,o),o.on("candidates",this.queue.bind(this,"candidates")),o.on("description",this.queue.bind(this,"description")),o.on("trackAdded",this.queue.bind(this,"trackAdded")),o.on("stateChanged",function c(l){l==="closed"&&(o.removeListener("stateChanged",c),a._dataTrackSenders.forEach(function(f){return o.removeDataTrackSender(f)}),a._mediaTrackSenders.forEach(function(f){return o.removeMediaTrackSender(f)}),a._peerConnections.delete(o.id),a._closedPeerConnectionIds.add(o.id),Vi(a),_t(a))}),o.on("connectionStateChanged",function(){return Vi(i)}),o.on("iceConnectionStateChanged",function(){return _t(i)}),this._dataTrackSenders.forEach(o.addDataTrackSender,o),this._mediaTrackSenders.forEach(o.addMediaTrackSender,o),_t(this)}return o},t.prototype.close=function(){return this._peerConnections.forEach(function(e){e.close()}),this._dummyAudioTrackSender&&this._dummyAudioTrackSender.stop(),this._audioContextFactory&&this._audioContextFactory.release(this),_t(this),this},t.prototype.createAndOffer=function(){var e=this;return this._getConfiguration().then(function(r){var i;do i=pe.makeUUID();while(e._peerConnections.has(i));return e._getOrCreate(i,r)}).then(function(r){return r.offer()}).then(function(){return e})},t.prototype.getTrackReceivers=function(){return pe.flatMap(this._peerConnections,function(e){return e.getTrackReceivers()})},t.prototype.getStates=function(){var e=[];return this._peerConnections.forEach(function(r){var i=r.getState();i&&e.push(i)}),e},t.prototype.setConfiguration=function(e){return this._configuration&&(this._configurationDeferred=pe.defer(),this._peerConnections.forEach(function(r){r.setConfiguration(e)})),this._configuration=e,this._configurationDeferred.resolve(e),this},t.prototype.setIceReconnectTimeout=function(e){return this._sessionTimeout===null&&(this._peerConnections.forEach(function(r){r.setIceReconnectTimeout(e)}),this._sessionTimeout=e),this},t.prototype.setTrackSenders=function(e){var r=new Set(e.filter(function(o){return o.kind==="data"})),i=new Set(e.filter(function(o){return o&&(o.kind==="audio"||o.kind==="video")})),a=Zb(this,r,i);return this._dataTrackSenders=r,this._mediaTrackSenders=i,Jb(this,a),this},t.prototype.update=function(e,r){var i=this;return r===void 0&&(r=!1),r&&this._closeAbsentPeerConnections(e),this._getConfiguration().then(function(a){return Promise.all(e.map(function(o){if(i._closedPeerConnectionIds.has(o.id))return null;var s=i._getOrCreate(o.id,a);return s.update(o)}))}).then(function(){return i})},t.prototype.getStats=function(){var e=Array.from(this._peerConnections.values());return Promise.all(e.map(function(r){return r.getStats().then(function(i){return[r.id,i]})})).then(function(r){return new Map(r)})},t}(Qb);function Yb(n){var t=n.createMediaStreamDestination();return t.stream.getAudioTracks()[0]}function Jb(n,t){(t.data.add.size||t.data.remove.size||t.media.add.size||t.media.remove.size)&&n._peerConnections.forEach(function(e){t.data.remove.forEach(e.removeDataTrackSender,e),t.media.remove.forEach(e.removeMediaTrackSender,e),t.data.add.forEach(e.addDataTrackSender,e),t.media.add.forEach(e.addMediaTrackSender,e),(t.media.add.size||t.media.remove.size||t.data.add.size&&!e.isApplicationSectionNegotiated)&&e.offer()})}function Xb(n,t){var e=pe.difference(t,n._dataTrackSenders),r=pe.difference(n._dataTrackSenders,t);return{add:e,remove:r}}function Zb(n,t,e){return{data:Xb(n,t),media:eS(n,e)}}function eS(n,t){var e=pe.difference(t,n._mediaTrackSenders),r=pe.difference(n._mediaTrackSenders,t);return{add:e,remove:r}}var Et={new:0,checking:1,connecting:2,connected:3,completed:4,disconnected:-1,failed:-2,closed:-3},_r;function tS(){return Object.keys(Et).reduce(function(n,t){var e;return Object.assign(n,(e={},e[Et[t]]=t,e))},{})}function lc(n){return n.length?(_r=_r||tS(),n.reduce(function(t,e){return _r[Math.max(Et[t],Et[e])]})):"new"}function _t(n){n._lastIceConnectionState=n.iceConnectionState,n._iceConnectionState=lc(uc([],cc(n._peerConnections.values())).map(function(t){return t.iceConnectionState})),n.iceConnectionState!==n._lastIceConnectionState&&n.emit("iceConnectionStateChanged")}function Vi(n){n._lastConnectionState=n.connectionState,n._connectionState=lc(uc([],cc(n._peerConnections.values())).map(function(t){return t.connectionState})),n.connectionState!==n._lastConnectionState&&n.emit("connectionStateChanged")}var rS=zb,nS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),iS=K,aS=0,oS=function(n){nS(t,n);function t(e,r,i){var a=n.call(this)||this;return Object.defineProperties(a,{_instanceId:{value:aS++},channel:{value:r},_log:{value:i.log.createLog("default",a)},_getReceiver:{value:e},_receiverPromise:{value:null,writable:!0},_transport:{value:null,writable:!0}}),a}return Object.defineProperty(t.prototype,"isSetup",{get:function(){return!!this._receiverPromise},enumerable:!1,configurable:!0}),t.prototype.toString=function(){return"[MediaSignaling #"+this._instanceId+":"+this.channel+"]"},t.prototype.setup=function(e){var r=this;this._teardown(),this._log.info("setting up msp transport for id:",e);var i=this._getReceiver(e).then(function(a){if(a.kind!=="data"&&r._log.error("Expected a DataTrackReceiver"),r._receiverPromise===i){try{r._transport=a.toDataTransport(),r.emit("ready",r._transport)}catch(o){r._log.error("Failed to toDataTransport: "+o.message)}a.once("close",function(){return r._teardown()})}});this._receiverPromise=i},t.prototype._teardown=function(){this._transport&&(this._log.info("Tearing down"),this._transport=null,this._receiverPromise=null,this.emit("teardown"))},t}(iS),Ve=oS,sS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),cS=Ve,uS=function(n){sS(t,n);function t(e,r){var i=n.call(this,e,"active_speaker",r)||this;return Object.defineProperties(i,{_loudestParticipantSid:{value:null,writable:!0}}),i.on("ready",function(a){a.on("message",function(o){switch(o.type){case"active_speaker":i._setLoudestParticipantSid(o.participant);break}})}),i}return Object.defineProperty(t.prototype,"loudestParticipantSid",{get:function(){return this._loudestParticipantSid},enumerable:!1,configurable:!0}),t.prototype._setLoudestParticipantSid=function(e){this.loudestParticipantSid!==e&&(this._loudestParticipantSid=e,this.emit("updated"))},t}(cS),lS=uS,dS=function(){function n(t,e,r,i){Object.defineProperties(this,{availableSend:{enumerable:!0,value:r},recv:{enumerable:!0,value:e},rtt:{enumerable:!0,value:i},send:{enumerable:!0,value:t}})}return n.of=function(t,e){var r=(e.timestamp-t.timestamp)/1e3,i=e.bytesSent-t.bytesSent,a=e.bytesReceived-t.bytesReceived,o=r>0?i/r*8:0,s=r>0?a/r*8:0,u=e.availableOutgoingBitrate,c=e.currentRoundTripTime;return new n(o,s,u,c)},n}(),fS=dS,mr=fS,pS=function(){function n(){Object.defineProperties(this,{lastReport:{enumerable:!0,value:new mr(0,0),writable:!0},lastStats:{enumerable:!0,value:null,writable:!0}})}return n.prototype.next=function(t){var e=this.lastStats;if(this.lastStats=t,e){var r=e.id===t.id?mr.of(e,t):new mr(0,0);this.lastReport=r}return this.lastReport},n}(),vS=pS;function hS(n){return n=n.filter(function(t){return typeof t=="number"}),n.length<1?void 0:n.reduce(function(t,e){return e+t})/n.length}var dc=hS,_S=function(){function n(t,e,r){Object.defineProperties(this,{id:{enumerable:!0,value:t},trackId:{enumerable:!0,value:e},bitrate:{enumerable:!0,value:r}})}return n}(),fc=_S;function mS(n){return n.reduce(function(t,e){return typeof e=="number"?e+t:t},0)}var pc=mS,yS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Bi=dc,gS=fc,bS=pc,SS=function(n){yS(t,n);function t(e,r,i,a,o,s,u){var c=n.call(this,e,r,i)||this,l=o>0?a/o:0;return Object.defineProperties(c,{deltaPacketsLost:{enumerable:!0,value:a},deltaPacketsReceived:{enumerable:!0,value:o},fractionLost:{enumerable:!0,value:s},jitter:{enumerable:!0,value:u},phonyFractionLost:{enumerable:!0,value:l}}),c}return t.of=function(e,r,i){if(r.id!==i.id)throw new Error("RTCStats IDs must match");var a=(i.timestamp-r.timestamp)/1e3,o=i.bytesReceived-r.bytesReceived,s=a>0?o/a*8:0,u=Math.max(i.packetsLost-r.packetsLost,0),c=i.packetsReceived-r.packetsReceived,l=i.fractionLost,f=i.jitter;return new t(r.id,e,s,u,c,l,f)},t.summarize=function(e){var r=e.map(function(s){return s.summarize()}),i=bS(r.map(function(s){return s.bitrate})),a=Bi(r.map(function(s){return s.fractionLost})),o=Bi(r.map(function(s){return s.jitter}));return{bitrate:i,fractionLost:a,jitter:o}},t.prototype.summarize=function(){return{bitrate:this.bitrate,fractionLost:typeof this.fractionLost=="number"?this.fractionLost:this.phonyFractionLost,jitter:this.jitter}},t}(gS),vc=SS,wS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),kS=dc,TS=fc,PS=pc,ES=function(n){wS(t,n);function t(e,r,i,a){var o=n.call(this,e,r,i)||this;return Object.defineProperties(o,{rtt:{enumerable:!0,value:a}}),o}return t.of=function(e,r,i,a){if(r.id!==i.id)throw new Error("RTCStats IDs must match");var o=(i.timestamp-r.timestamp)/1e3,s=i.bytesSent-r.bytesSent,u=o>0?s/o*8:0,c=a&&typeof a.roundTripTime=="number"?a.roundTripTime/1e3:void 0;return new t(r.id,e,u,c)},t.summarize=function(e){var r=PS(e.map(function(a){return a.bitrate})),i=kS(e.map(function(a){return a.rtt}));return{bitrate:r,rtt:i}},t}(TS),hc=ES,yr=vc,gr=hc,OS=function(){function n(t,e,r){Object.defineProperties(this,{ice:{enumerable:!0,value:t},audio:{enumerable:!0,value:e},video:{enumerable:!0,value:r}})}return n.prototype.summarize=function(){var t=this.audio.send.concat(this.video.send),e=gr.summarize(t),r=this.audio.recv.concat(this.video.recv),i=yr.summarize(r);return{ice:this.ice,send:e,recv:i,audio:{send:gr.summarize(this.audio.send),recv:yr.summarize(this.audio.recv)},video:{send:gr.summarize(this.video.send),recv:yr.summarize(this.video.recv)}}},n}(),CS=OS,RS=function(){function n(t,e,r){Object.defineProperties(this,{id:{enumerable:!0,value:t,writable:!0},trackId:{enumerable:!0,value:e,writable:!0},lastStats:{enumerable:!0,value:r,writable:!0}})}return n}(),_c=RS,LS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),xS=vc,AS=_c,IS=function(n){LS(t,n);function t(e,r){var i=n.call(this,r.id,e,r)||this;return Object.defineProperties(i,{lastReport:{enumerable:!0,value:null,writable:!0}}),i}return t.prototype.next=function(e,r){var i=this.lastStats;this.lastStats=r,this.trackId=e;var a=xS.of(e,i,r);return this.lastReport=a,a},t}(AS),MS=IS,jS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),DS=_c,$S=hc,NS=function(n){jS(t,n);function t(e,r){var i=n.call(this,r.id,e,r)||this;return Object.defineProperties(i,{lastReport:{enumerable:!0,value:null,writable:!0}}),i}return t.prototype.next=function(e,r,i){var a=this.lastStats;this.lastStats=r,this.trackId=e;var o=$S.of(e,a,r,i);return this.lastReport=o,o},t}(DS),FS=NS,Ge=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},mt=d&&d.__spreadArray||function(n,t){for(var e=0,r=t.length,i=n.length;e<r;e++,i++)n[i]=t[e];return n},Xe=d&&d.__values||function(n){var t=typeof Symbol=="function"&&Symbol.iterator,e=t&&n[t],r=0;if(e)return e.call(n);if(n&&typeof n.length=="number")return{next:function(){return n&&r>=n.length&&(n=void 0),{value:n&&n[r++],done:!n}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},mc=V.guessBrowser,VS=vS,BS=CS,US=MS,WS=FS,HS=function(){function n(t){Object.defineProperties(this,{pc:{enumerable:!0,value:t},ice:{enumerable:!0,value:new VS},audio:{enumerable:!0,value:{send:new Map,recv:new Map}},video:{enumerable:!0,value:{send:new Map,recv:new Map}},lastReport:{enumerable:!0,value:null,writable:!0}})}return n.prototype.next=function(){var t=this,e=mc()==="firefox"?GS(this):KS(this);return e.then(function(){var r=mt([],Ge(t.audio.send.values())),i=mt([],Ge(t.video.send.values())),a=mt([],Ge(t.audio.recv.values())),o=mt([],Ge(t.video.recv.values())),s=new BS(t.ice.lastReport,{send:r.map(function(u){return u.lastReport}).filter(function(u){return u}),recv:a.map(function(u){return u.lastReport}).filter(function(u){return u})},{send:i.map(function(u){return u.lastReport}).filter(function(u){return u}),recv:o.map(function(u){return u.lastReport}).filter(function(u){return u})});return t.lastReport=s,s})},n}();function Ui(n){return Promise.all(n.map(function(t){var e=t.track.id;return t.getStats().then(function(r){var i,a;try{for(var o=Xe(r.values()),s=o.next();!s.done;s=o.next()){var u=s.value;u.type==="inbound-rtp"&&(u.id=e+"-"+u.id)}}catch(c){i={error:c}}finally{try{s&&!s.done&&(a=o.return)&&a.call(o)}finally{if(i)throw i.error}}return[e,r]})})).then(function(t){return new Map(t)})}function yc(n,t,e,r,i){var a=t[r.mediaType];if(!i){var o=e.get(r.trackId);o&&(i=o.trackIdentifier)}if(a&&i){if(a.has(r.id))return a.get(r.id);var s=new n(i,r);a.set(r.id,s)}return null}function yn(n){return{audio:n.audio.send,video:n.video.send}}function gn(n){return{audio:n.audio.recv,video:n.video.recv}}function qS(n,t,e,r){return yc(WS,yn(n),t,e,r)}function QS(n,t,e,r){return yc(US,gn(n),t,e,r)}function gc(n){return{audio:new Set(n.audio.send.keys()),video:new Set(n.video.send.keys())}}function bc(n){return{audio:new Set(n.audio.recv.keys()),video:new Set(n.video.recv.keys())}}function Sc(n,t,e,r){var i,a;try{for(var o=Xe(t.values()),s=o.next();!s.done;s=o.next()){var u=s.value;if(u.type==="outbound-rtp"&&!u.isRemote){if(mc()!=="firefox"&&!u.trackId)continue;var c=e[u.mediaType];c&&c.delete(u.id);var l=qS(n,t,u,r);if(l){var f=t.get(u.remoteId);l.next(r||l.trackId,u,f)}}}}catch(p){i={error:p}}finally{try{s&&!s.done&&(a=o.return)&&a.call(o)}finally{if(i)throw i.error}}}function wc(n,t,e,r){var i,a;try{for(var o=Xe(t.values()),s=o.next();!s.done;s=o.next()){var u=s.value;if(u.type==="inbound-rtp"&&!u.isRemote){var c=e[u.mediaType];c&&c.delete(u.id);var l=QS(n,t,u,r);l&&l.next(r||l.trackId,u)}}}catch(f){i={error:f}}finally{try{s&&!s.done&&(a=o.return)&&a.call(o)}finally{if(i)throw i.error}}}function Ot(n,t){var e=function(i){var a=n[i],o=t[i];o.forEach(function(s){return a.delete(s)})};for(var r in t)e(r)}function kc(n,t){var e,r,i,a,o;try{for(var s=Xe(t.values()),u=s.next();!u.done;u=s.next()){var c=u.value;c.type==="transport"&&(o=t.get(c.selectedCandidatePairId))}}catch(p){e={error:p}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}if(o){n.next(o);return}try{for(var l=Xe(t.values()),f=l.next();!f.done;f=l.next()){var c=f.value;c.type==="candidate-pair"&&c.nominated&&(!("selected"in c)||c.selected)&&n.next(c)}}catch(p){i={error:p}}finally{try{f&&!f.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}}function GS(n){var t=n.pc.getTransceivers().filter(function(r){return r.currentDirection&&r.currentDirection.match(/send/)&&r.sender.track}).map(function(r){return r.sender}),e=n.pc.getTransceivers().filter(function(r){return r.currentDirection&&r.currentDirection.match(/recv/)}).map(function(r){return r.receiver});return Promise.all([Ui(t),Ui(e),n.pc.getStats()]).then(function(r){var i=Ge(r,3),a=i[0],o=i[1],s=i[2],u=yn(n),c=gc(n);a.forEach(function(p,v){return Sc(n,p,c,v)}),Ot(u,c);var l=gn(n),f=bc(n);o.forEach(function(p,v){return wc(n,p,f,v)}),Ot(l,f),kc(n.ice,s)})}function KS(n){return n.pc.getStats().then(function(t){var e=yn(n),r=gc(n);Sc(n,t,r),Ot(e,r);var i=gn(n),a=bc(n);wc(n,t,a),Ot(i,a),kc(n.ice,t)})}var zS=HS,YS=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),JS=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},XS=K,ZS=zS,ew=function(n){YS(t,n);function t(e,r){var i=n.call(this)||this;return Object.defineProperties(i,{_factories:{value:new WeakMap},_manager:{value:e},_signaling:{value:r}}),r.on("updated",function(){return i.emit("updated")}),i}return Object.defineProperty(t.prototype,"level",{get:function(){return this._signaling.level},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"levels",{get:function(){return this._signaling.levels},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"remoteLevels",{get:function(){return this._signaling.remoteLevels},enumerable:!1,configurable:!0}),t.prototype.start=function(){var e=this;this.stop();var r=setTimeout(function(){e._timeout===r&&tw(e).then(function(i){if(e._timeout===r){if(i.length){var a=JS(i,1),o=a[0];e._signaling.put(o)}e.start()}})},200);this._timeout=r},t.prototype.stop=function(){clearTimeout(this._timeout),this._timeout=null},t}(XS);function tw(n){var t=n._manager._peerConnections?Array.from(n._manager._peerConnections.values()):[],e=t.map(function(a){return a._peerConnection}).filter(function(a){return a.signalingState!=="closed"}),r=e.map(function(a){if(n._factories.has(a))return n._factories.get(a);var o=new ZS(a);return n._factories.set(a,o),o}),i=r.map(function(a){return a.next().catch(function(){return null})});return Promise.all(i).then(function(a){return a.filter(function(o){return o}).map(function(o){return o.summarize()})})}var rw=ew,nw=R.defer,iw=function(){function n(){Object.defineProperties(this,{_deferreds:{value:[]},_hasValue:{value:!1,writable:!0},_value:{value:null,writable:!0}})}return n.prototype.put=function(t){this._hasValue=!0,this._value=t;var e=this._deferreds.shift();return e&&e.resolve(t),this},n.prototype.take=function(){var t=this;if(this._hasValue&&!this._deferreds.length)return this._hasValue=!1,Promise.resolve(this._value);var e=nw();return this._deferreds.push(e),e.promise.then(function(r){return t._hasValue=!1,r})},n}(),aw=iw,ow=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),sw=Ve,cw=aw,uw=we,Wi=5e3,lw=function(n){ow(t,n);function t(e,r,i){var a=n.call(this,e,"network_quality",i)||this;return Object.defineProperties(a,{_level:{value:null,writable:!0},_levels:{value:null,writable:!0},_remoteLevels:{value:new Map,writable:!0},_networkQualityInputs:{value:new cw},_resendTimer:{value:new uw(function(){a._resendTimer.setDelay(a._resendTimer.delay*1.5),a._sendNetworkQualityInputs()},Wi,!1)},_networkQualityReportLevels:{get:function(){return{reportLevel:r.local,remoteReportLevel:r.remote}}}}),a.on("ready",function(o){o.on("message",function(s){switch(a._log.debug("Incoming: ",s),s.type){case"network_quality":a._handleNetworkQualityMessage(s);break}})}),a._sendNetworkQualityInputs(),a}return Object.defineProperty(t.prototype,"level",{get:function(){return this._level},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"levels",{get:function(){return this._levels},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"remoteLevels",{get:function(){return this._remoteLevels},enumerable:!1,configurable:!0}),t.prototype._handleNetworkQualityMessage=function(e){var r=this,i=!1,a=null,o=e?e.local:null;typeof o=="number"?(a=o,this._levels=null):typeof o=="object"&&o&&(this._levels=o,a=typeof o.level=="number"?o.level:Math.min(o.audio.send,o.audio.recv,o.video.send,o.video.recv)),a!==null&&this.level!==a&&(this._level=a,i=!0),this._remoteLevels=e&&e.remotes?e.remotes.reduce(function(s,u){var c=r._remoteLevels.get(u.sid)||{};return c.level!==u.level&&(i=!0),s.set(u.sid,u)},new Map):this._remoteLevels,i&&this.emit("updated"),this._resendTimer.setDelay(Wi),this._resendTimer.isSet&&setTimeout(function(){return r._sendNetworkQualityInputs()},1e3)},t.prototype._sendNetworkQualityInputs=function(){var e=this;return this._resendTimer.clear(),this._networkQualityInputs.take().then(function(r){e._transport&&e._transport.publish(dw(r,e._networkQualityReportLevels))}).finally(function(){e._resendTimer.start()})},t.prototype.put=function(e){this._networkQualityInputs.put(e)},t}(sw);function dw(n,t){return Object.assign({type:"network_quality"},n,t)}var fw=lw,pw=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),vw=K.EventEmitter,hw=function(n){pw(t,n);function t(){var e=n.call(this)||this;return Object.defineProperties(e,{_isEnabled:{value:null,writable:!0},isEnabled:{enumerable:!0,get:function(){return this._isEnabled}}}),e}return t.prototype.disable=function(){return this.enable(!1)},t.prototype.enable=function(e){return e=typeof e=="boolean"?e:!0,this.isEnabled!==e&&(this._isEnabled=e,this.emit("updated")),this},t}(vw),Tc=hw,_w=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),mw=Tc,yw=function(n){_w(t,n);function t(){var e=n.call(this)||this;return Object.defineProperties(e,{_revision:{value:1,writable:!0}}),e}return t.prototype.update=function(e){return e.revision<this._revision?this:(this._revision=e.revision,this.enable(e.is_recording))},t}(mw),gw=yw,bw=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Sw=Tc,ww=$e,kw=we,Tw=R.buildLogLevels,Pw=U.DEFAULT_LOG_LEVEL,Ew=ce,bn=L,Ow=bn.MediaConnectionError,Cw=bn.MediaDTLSTransportFailedError,Rw=bn.SignalingConnectionDisconnectedError,Lw=0,xw={connected:["reconnecting","disconnected"],reconnecting:["connected","disconnected"],disconnected:[]},Aw=function(n){bw(t,n);function t(e,r,i,a){var o=this;a=Object.assign({logLevel:Pw,RecordingSignaling:Sw,Timeout:kw},a);var s=Tw(a.logLevel);o=n.call(this,"connected",xw)||this;var u=a.RecordingSignaling,c=new a.Timeout(function(){o._disconnect(o._reconnectingError)},a.sessionTimeout,!1);return Object.defineProperties(o,{_instanceId:{value:Lw++},_log:{value:a.log?a.log.createLog("default",o):new Ew("default",o,s,a.loggerName)},_mediaConnectionIsReconnecting:{writable:!0,value:!1},_options:{value:a},_reconnectingError:{value:null,writable:!0},_sessionTimeout:{value:c},dominantSpeakerSid:{enumerable:!0,value:null,writable:!0},localParticipant:{enumerable:!0,value:e},name:{enumerable:!0,value:i},participants:{enumerable:!0,value:new Map},recording:{enumerable:!0,value:new u},sid:{enumerable:!0,value:r}}),o.on("connectionStateChanged",function(){o.connectionState==="failed"&&!["disconnected","failed"].includes(o.iceConnectionState)&&o._disconnect(new Cw)}),o.on("iceConnectionStateChanged",function(){return br(o)}),o.on("signalingConnectionStateChanged",function(){return br(o)}),setTimeout(function(){return br(o)}),o}return t.prototype._disconnect=function(e){return this.state!=="disconnected"?(this.preempt("disconnected",null,[e]),!0):!1},t.prototype.toString=function(){return"[RoomSignaling #"+this._instanceId+": "+(this.localParticipant?this.localParticipant.sid:"null")+"]"},t.prototype.connectParticipant=function(e){var r=this;return e.state==="disconnected"||this.participants.has(e.sid)?!1:(this.participants.set(e.sid,e),e.on("stateChanged",function i(a){a==="disconnected"&&(e.removeListener("stateChanged",i),r.participants.delete(e.sid),r.emit("participantDisconnected",e))}),this.emit("participantConnected",e),!0)},t.prototype.disconnect=function(){return this._disconnect()},t.prototype.setDominantSpeaker=function(e){this.dominantSpeakerSid=e,this.emit("dominantSpeakerChanged")},t}(ww);function br(n){if(n.state==="disconnected"||n.signalingConnectionState==="disconnected"){n._sessionTimeout.clear();return}var t;n.signalingConnectionState==="reconnecting"?t=n.signalingConnectionState:n.iceConnectionState==="failed"?(n._mediaConnectionIsReconnecting=!0,t="reconnecting"):n.iceConnectionState==="new"||n.iceConnectionState==="checking"?t=n._mediaConnectionIsReconnecting?"reconnecting":"connected":(n._mediaConnectionIsReconnecting=!1,n._reconnectingError=null,n._sessionTimeout.clear(),t="connected"),t!==n.state&&(t==="reconnecting"?(n._reconnectingError=n.signalingConnectionState==="reconnecting"?new Rw:new Ow,n._sessionTimeout.start(),n.preempt(t,null,[n._reconnectingError])):n.preempt(t))}var Pc=Aw,Iw=function(){function n(t){var e=t.actual,r=e===void 0?null:e,i=t.available,a=i===void 0?null:i,o=t.level,s=o===void 0?null:o;Object.defineProperties(this,{actual:{value:r,enumerable:!0},available:{value:a,enumerable:!0},level:{value:s,enumerable:!0}})}return n}(),Mw=Iw,jw=function(){function n(t){var e=t.fractionLost,r=e===void 0?null:e,i=t.level,a=i===void 0?null:i;Object.defineProperties(this,{fractionLost:{value:r,enumerable:!0},level:{value:a,enumerable:!0}})}return n}(),Dw=jw,$w=function(){function n(t){var e=t.jitter,r=e===void 0?null:e,i=t.rtt,a=i===void 0?null:i,o=t.level,s=o===void 0?null:o;Object.defineProperties(this,{jitter:{value:r,enumerable:!0},rtt:{value:a,enumerable:!0},level:{value:s,enumerable:!0}})}return n}(),Nw=$w,Fw=Mw,Vw=Dw,Bw=Nw,Uw=function(){function n(t){var e=t.bandwidth,r=e===void 0?null:e,i=t.fractionLost,a=i===void 0?null:i,o=t.latency,s=o===void 0?null:o;Object.defineProperties(this,{bandwidth:{value:r?new Fw(r):null,enumerable:!0},fractionLost:{value:a?new Vw(a):null,enumerable:!0},latency:{value:s?new Bw(s):null,enumerable:!0}})}return n}(),Ec=Uw,Ww=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Hw=Ec,qw=function(n){Ww(t,n);function t(e){return n.call(this,e)||this}return t}(Hw),Qw=qw,Gw=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Kw=Ec,zw=function(n){Gw(t,n);function t(e){return n.call(this,e)||this}return t}(Kw),Yw=zw,Jw=Qw,Xw=Yw,Zw=function(){function n(t){var e=t.send,r=t.recv,i=t.sendStats,a=i===void 0?null:i,o=t.recvStats,s=o===void 0?null:o;Object.defineProperties(this,{send:{value:e,enumerable:!0},recv:{value:r,enumerable:!0},sendStats:{value:a?new Jw(a):null,enumerable:!0},recvStats:{value:s?new Xw(s):null,enumerable:!0}})}return n}(),Oc=Zw,ek=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),tk=Oc,rk=function(n){ek(t,n);function t(e){return n.call(this,e)||this}return t}(tk),nk=rk,ik=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),ak=Oc,ok=function(n){ik(t,n);function t(e){return n.call(this,e)||this}return t}(ak),sk=ok,ck=nk,uk=sk,lk=function(){function n(t){var e=t.level,r=t.audio,i=t.video;Object.defineProperties(this,{level:{value:e,enumerable:!0},audio:{value:r?new ck(r):null,enumerable:!0},video:{value:i?new uk(i):null,enumerable:!0}})}return n}(),dk=lk,fk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),pk=$e,vk=dk,hk={connecting:["connected"],connected:["disconnected","reconnecting"],reconnecting:["connected","disconnected"],disconnected:[]},_k=function(n){fk(t,n);function t(){var e=n.call(this,"connecting",hk)||this;return Object.defineProperties(e,{_identity:{writable:!0,value:null},_networkQualityLevel:{value:null,writable:!0},_networkQualityStats:{value:null,writable:!0},_sid:{writable:!0,value:null},identity:{enumerable:!0,get:function(){return this._identity}},sid:{enumerable:!0,get:function(){return this._sid}},tracks:{enumerable:!0,value:new Map}}),e}return Object.defineProperty(t.prototype,"networkQualityLevel",{get:function(){return this._networkQualityLevel},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"networkQualityStats",{get:function(){return this._networkQualityStats},enumerable:!1,configurable:!0}),t.prototype.addTrack=function(e){return this.tracks.set(e.id||e.sid,e),this.emit("trackAdded",e),this},t.prototype.disconnect=function(){return this.state!=="disconnected"?(this.preempt("disconnected"),!0):!1},t.prototype.removeTrack=function(e){var r=this.tracks.get(e.id||e.sid);return this.tracks.delete(e.id||e.sid),r&&this.emit("trackRemoved",e),r||null},t.prototype.setNetworkQualityLevel=function(e,r){this._networkQualityLevel!==e&&(this._networkQualityLevel=e,this._networkQualityStats=r&&(r.audio||r.video)?new vk(r):null,this.emit("networkQualityLevelChanged"))},t.prototype.connect=function(e,r){return this.state==="connecting"||this.state==="reconnecting"?(this._sid||(this._sid=e),this._identity||(this._identity=r),this.preempt("connected"),!0):!1},t.prototype.reconnecting=function(){return this.state==="connecting"||this.state==="connected"?(this.preempt("reconnecting"),!0):!1},t}(pk),Sn=_k,mk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),yk=Sn,gk=function(n){mk(t,n);function t(e,r){var i=n.call(this)||this;return i.connect(e,r),i}return t}(yk),bk=gk,Sk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),wk=K.EventEmitter,kk=function(n){Sk(t,n);function t(e,r,i,a){var o=n.call(this)||this,s=null;return Object.defineProperties(o,{_error:{value:null,writable:!0},_isEnabled:{value:i,writable:!0},_priority:{value:a,writable:!0},_trackTransceiver:{value:null,writable:!0},_sid:{get:function(){return s},set:function(u){s===null&&(s=u)}},kind:{enumerable:!0,value:r},name:{enumerable:!0,value:e}}),o}return Object.defineProperty(t.prototype,"error",{get:function(){return this._error},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEnabled",{get:function(){return this._isEnabled},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"priority",{get:function(){return this._priority},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sid",{get:function(){return this._sid},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"trackTransceiver",{get:function(){return this._trackTransceiver},enumerable:!1,configurable:!0}),t.prototype.disable=function(){return this.enable(!1)},t.prototype.enable=function(e){return e=typeof e=="boolean"?e:!0,this.isEnabled!==e&&(this._isEnabled=e,this.emit("updated")),this},t.prototype.setTrackTransceiver=function(e){return e=e||null,this.trackTransceiver!==e&&(this._trackTransceiver=e,this.emit("updated")),this},t.prototype.setSid=function(e){return this.sid===null&&(this._sid=e,this.emit("updated")),this},t}(wk),Cc=kk,Tk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Pk=Cc,Ek=function(n){Tk(t,n);function t(e,r,i,a,o,s){var u=n.call(this,r,i,a,o)||this;return Object.defineProperties(u,{_isSwitchedOff:{value:s,writable:!0}}),u.setSid(e),u}return Object.defineProperty(t.prototype,"isSubscribed",{get:function(){return!!this.trackTransceiver},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isSwitchedOff",{get:function(){return this._isSwitchedOff},enumerable:!1,configurable:!0}),t.prototype.subscribeFailed=function(e){return this.error||(this._error=e,this.emit("updated")),this},t.prototype.setPriority=function(e){return this._priority!==e&&(this._priority=e,this.emit("updated")),this},t.prototype.setSwitchedOff=function(e){return this._isSwitchedOff!==e&&(this._isSwitchedOff=e,this.emit("updated")),this},t}(Pk),Ok=Ek,Ck=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Rk=Ok,Lk=function(n){Ck(t,n);function t(e,r){return n.call(this,e.sid,e.name,e.kind,e.enabled,e.priority,r)||this}return t.prototype.update=function(e){return this.enable(e.enabled),this.setPriority(e.priority),this},t}(Rk),xk=Lk,Ak=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Ik=bk,Mk=xk,jk=function(n){Ak(t,n);function t(e,r,i,a,o,s){var u=n.call(this,e.sid,e.identity)||this;return s=Object.assign({RemoteTrackPublicationV2:Mk},s),Object.defineProperties(u,{_revision:{writable:!0,value:null},_RemoteTrackPublicationV2:{value:s.RemoteTrackPublicationV2},_getInitialTrackSwitchOffState:{value:r},updateSubscriberTrackPriority:{value:function(c,l){return i(c,l)}},updateTrackRenderHint:{value:function(c,l){return a(c,l)}},clearTrackHint:{value:function(c){return o(c)}},revision:{enumerable:!0,get:function(){return this._revision}}}),u.update(e)}return t.prototype._getOrCreateTrack=function(e){var r=this._RemoteTrackPublicationV2,i=this.tracks.get(e.sid);if(!i){var a=this._getInitialTrackSwitchOffState(e.sid);i=new r(e,a),this.addTrack(i)}return i},t.prototype.update=function(e){var r=this;if(this.revision!==null&&e.revision<=this.revision)return this;this._revision=e.revision;var i=new Set;switch(e.tracks.forEach(function(a){var o=r._getOrCreateTrack(a);o.update(a),i.add(o)}),this.tracks.forEach(function(a){i.has(a)||r.removeTrack(a)}),e.state){case"disconnected":this.disconnect();break;case"reconnecting":this.reconnecting();break;case"connected":this.connect(this.sid,this.identity);break}return this},t}(Ik),Dk=jk,$k=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Nk=Ve,Fk=function(n){$k(t,n);function t(e,r){var i=n.call(this,e,"track_priority",r)||this;return Object.defineProperties(i,{_enqueuedPriorityUpdates:{value:new Map}}),i.on("ready",function(a){Array.from(i._enqueuedPriorityUpdates.keys()).forEach(function(o){a.publish({type:"track_priority",track:o,subscribe:i._enqueuedPriorityUpdates.get(o)})})}),i}return t.prototype.sendTrackPriorityUpdate=function(e,r,i){if(r!=="subscribe")throw new Error("only subscribe priorities are supported, found: "+r);this._enqueuedPriorityUpdates.set(e,i),this._transport&&this._transport.publish({type:"track_priority",track:e,subscribe:i})},t}(Nk),Vk=Fk,Bk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Uk=Ve,Wk=function(n){Bk(t,n);function t(e,r){var i=n.call(this,e,"track_switch_off",r)||this;return i.on("ready",function(a){a.on("message",function(o){switch(o.type){case"track_switch_off":i._setTrackSwitchOffUpdates(o.off||[],o.on||[]);break}})}),i}return t.prototype._setTrackSwitchOffUpdates=function(e,r){this.emit("updated",e,r)},t}(Uk),Hk=Wk,qk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Qk=Ve,Gk=we,Kk=R.isDeepEqual,Hi=2e3,zk=1,Yk=function(n){qk(t,n);function t(e,r){var i=n.call(this,e,"render_hints",r)||this;return Object.defineProperties(i,{_trackSidsToRenderHints:{value:new Map},_responseTimer:{value:new Gk(function(){i._sendAllHints(),i._responseTimer.setDelay(i._responseTimer.delay*2)},Hi,!1)}}),i.on("ready",function(a){a.on("message",function(o){switch(i._log.debug("Incoming: ",o),o.type){case"render_hints":i._processHintResults(o&&o.subscriber&&o.subscriber.hints||[]);break;default:i._log.warn("Unknown message type: ",o.type);break}}),i._sendAllHints()}),i}return t.prototype._sendAllHints=function(){var e=this;Array.from(this._trackSidsToRenderHints.keys()).forEach(function(r){var i=e._trackSidsToRenderHints.get(r);i.renderDimensions&&(i.isDimensionDirty=!0),"enabled"in i&&(i.isEnabledDirty=!0)}),this._sendHints()},t.prototype._processHintResults=function(e){var r=this;this._responseTimer.clear(),this._responseTimer.setDelay(Hi),e.forEach(function(i){i.result!=="OK"&&r._log.debug("Server error processing hint:",i)}),this._sendHints()},t.prototype._sendHints=function(){var e=this;if(!(!this._transport||this._responseTimer.isSet)){var r=[];if(Array.from(this._trackSidsToRenderHints.keys()).forEach(function(a){var o=e._trackSidsToRenderHints.get(a);if(o.isEnabledDirty||o.isDimensionDirty){var s={track:a};o.isEnabledDirty&&(s.enabled=o.enabled,o.isEnabledDirty=!1),o.isDimensionDirty&&(s.render_dimensions=o.renderDimensions,o.isDimensionDirty=!1),r.push(s)}}),r.length>0){var i={type:"render_hints",subscriber:{id:zk++,hints:r}};this._log.debug("Outgoing: ",i),this._transport.publish(i),this._responseTimer.start()}}},t.prototype.setTrackHint=function(e,r){var i=this._trackSidsToRenderHints.get(e)||{isEnabledDirty:!1,isDimensionDirty:!1};"enabled"in r&&i.enabled!==r.enabled&&(i.enabled=!!r.enabled,i.isEnabledDirty=!0),r.renderDimensions&&!Kk(r.renderDimensions,i.renderDimensions)&&(i.renderDimensions=r.renderDimensions,i.isDimensionDirty=!0),this._trackSidsToRenderHints.set(e,i),this._sendHints()},t.prototype.clearTrackHint=function(e){this._trackSidsToRenderHints.delete(e)},t}(Qk),Jk=Yk,Xk=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Zk=Ve,eT=1,tT=function(n){Xk(t,n);function t(e,r){var i=n.call(this,e,"publisher_hints",r)||this;return i.on("ready",function(a){i._log.debug("publisher_hints transport ready:",a),a.on("message",function(o){switch(i._log.debug("Incoming: ",o),o.type){case"publisher_hints":o.publisher&&o.publisher.hints&&o.publisher.id&&i._processPublisherHints(o.publisher.hints,o.publisher.id);break;default:i._log.warn("Unknown message type: ",o.type);break}})}),i}return t.prototype.sendTrackReplaced=function(e){var r=e.trackSid;if(this._transport){var i={type:"client_reset",track:r,id:eT++};this._log.debug("Outgoing: ",i),this._transport.publish(i)}},t.prototype.sendHintResponse=function(e){var r=e.id,i=e.hints;if(this._transport){var a={type:"publisher_hints",id:r,hints:i};this._log.debug("Outgoing: ",a),this._transport.publish(a)}},t.prototype._processPublisherHints=function(e,r){try{this.emit("updated",e,r)}catch(i){this._log.error("error processing hints:",i)}},t}(Zk),rT=tT,nT=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Rc=d&&d.__read||function(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a},iT=lS,aT=rw,oT=fw,sT=gw,cT=Pc,uT=Dk,lT=Ys,dT=Vk,fT=Hk,pT=Jk,vT=rT,ke=R,hT=ke.constants.DEFAULT_SESSION_TIMEOUT_SEC,_T=ke.createBandwidthProfilePayload,mT=ke.defer,yT=ke.difference,Sr=ke.filterObject,wn=ke.flatMap,qi=ke.oncePerTick,Ct=Rs,gT=L.createTwilioError,bT=1e4,ST=function(n){nT(t,n);function t(e,r,i,a,o){var s=this;r.options=Object.assign({session_timeout:hT},r.options),o=Object.assign({DominantSpeakerSignaling:iT,NetworkQualityMonitor:aT,NetworkQualitySignaling:oT,RecordingSignaling:sT,RemoteParticipantV2:uT,TrackPrioritySignaling:dT,TrackSwitchOffSignaling:fT,bandwidthProfile:null,sessionTimeout:r.options.session_timeout*1e3,statsPublishIntervalMs:bT},o),e.setBandwidthProfile(o.bandwidthProfile);var u=r.options,c=u.signaling_region,l=u.audio_processors,f=l===void 0?[]:l;e.setSignalingRegion(c),f.includes("krisp")&&f.push("rnnoise"),e.setAudioProcessors(f),a.setIceReconnectTimeout(o.sessionTimeout),s=n.call(this,e,r.sid,r.name,o)||this;var p=function(_){return s._getTrackReceiver(_)},v=s._log;return Object.defineProperties(s,{_disconnectedParticipantRevisions:{value:new Map},_NetworkQualityMonitor:{value:o.NetworkQualityMonitor},_lastBandwidthProfileRevision:{value:e.bandwidthProfileRevision,writable:!0},_mediaStatesWarningsRevision:{value:0,writable:!0},_networkQualityMonitor:{value:null,writable:!0},_networkQualityConfiguration:{value:e.networkQualityConfiguration},_peerConnectionManager:{value:a},_published:{value:new Map},_publishedRevision:{value:0,writable:!0},_RemoteParticipantV2:{value:o.RemoteParticipantV2},_subscribed:{value:new Map},_subscribedRevision:{value:0,writable:!0},_subscriptionFailures:{value:new Map},_dominantSpeakerSignaling:{value:new o.DominantSpeakerSignaling(p,{log:v})},_networkQualitySignaling:{value:new o.NetworkQualitySignaling(p,e.networkQualityConfiguration,{log:v})},_renderHintsSignaling:{value:new pT(p,{log:v})},_publisherHintsSignaling:{value:new vT(p,{log:v})},_trackPrioritySignaling:{value:new o.TrackPrioritySignaling(p,{log:v})},_trackSwitchOffSignaling:{value:new o.TrackSwitchOffSignaling(p,{log:v})},_pendingSwitchOffStates:{value:new Map},_transport:{value:i},_trackReceiverDeferreds:{value:new Map},mediaRegion:{enumerable:!0,value:r.options.media_region||null}}),s._initTrackSwitchOffSignaling(),s._initDominantSpeakerSignaling(),s._initNetworkQualityMonitorSignaling(),s._initPublisherHintSignaling(),wT(s,e),kT(s,a),TT(s,i),PT(s,i,o.statsPublishIntervalMs),s._update(r),s._peerConnectionManager.setEffectiveAdaptiveSimulcast(s._publisherHintsSignaling.isSetup),s}return Object.defineProperty(t.prototype,"connectionState",{get:function(){return this._peerConnectionManager.connectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"signalingConnectionState",{get:function(){return this._transport.state==="syncing"?"reconnecting":this._transport.state},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._peerConnectionManager.iceConnectionState},enumerable:!1,configurable:!0}),t.prototype._deleteTrackReceiverDeferred=function(e){return this._trackReceiverDeferreds.delete(e)},t.prototype._getOrCreateTrackReceiverDeferred=function(e){var r=this._trackReceiverDeferreds.get(e)||mT(),i=this._peerConnectionManager.getTrackReceivers(),a=i.find(function(o){return o.id===e&&o.readyState!=="ended"});return a?r.resolve(a):this._trackReceiverDeferreds.set(e,r),r},t.prototype._addTrackReceiver=function(e){var r=this._getOrCreateTrackReceiverDeferred(e.id);return r.resolve(e),this},t.prototype._disconnect=function(e){var r=n.prototype._disconnect.call(this,e);return r&&(this._teardownNetworkQualityMonitor(),this._transport.disconnect(),this._peerConnectionManager.close()),this.localParticipant.tracks.forEach(function(i){i.publishFailed(e||new Error("LocalParticipant disconnected"))}),r},t.prototype._getTrackReceiver=function(e){var r=this;return this._getOrCreateTrackReceiverDeferred(e).promise.then(function(i){return r._deleteTrackReceiverDeferred(e),i})},t.prototype._getInitialTrackSwitchOffState=function(e){var r=this._pendingSwitchOffStates.get(e)||!1;return this._pendingSwitchOffStates.delete(e),r&&this._log.warn("["+e+"] was initially switched off! "),r},t.prototype._getTrackSidsToTrackSignalings=function(){var e=wn(this.participants,function(r){return Array.from(r.tracks)});return new Map(e)},t.prototype._getOrCreateRemoteParticipant=function(e){var r=this,i=this._RemoteParticipantV2,a=this.participants.get(e.sid),o=this;return a||(a=new i(e,function(s){return r._getInitialTrackSwitchOffState(s)},function(s,u){return r._trackPrioritySignaling.sendTrackPriorityUpdate(s,"subscribe",u)},function(s,u){return r._renderHintsSignaling.setTrackHint(s,u)},function(s){return r._renderHintsSignaling.clearTrackHint(s)}),a.on("stateChanged",function s(u){u==="disconnected"&&(a.removeListener("stateChanged",s),o.participants.delete(a.sid),o._disconnectedParticipantRevisions.set(a.sid,a.revision))}),this.connectParticipant(a)),a},t.prototype._getState=function(){return{participant:this.localParticipant.getState()}},t.prototype._maybeAddBandwidthProfile=function(e){var r=this.localParticipant,i=r.bandwidthProfile,a=r.bandwidthProfileRevision;return i&&this._lastBandwidthProfileRevision<a?(this._lastBandwidthProfileRevision=a,Object.assign({bandwidth_profile:_T(i)},e)):e},t.prototype._publishNewLocalParticipantState=function(){this._transport.publish(this._maybeAddBandwidthProfile(this._getState()))},t.prototype._publishPeerConnectionState=function(e){this._transport.publish(Object.assign({peer_connections:[e]},this._getState()))},t.prototype._update=function(e){var r=this;if(e.subscribed&&e.subscribed.revision>this._subscribedRevision){this._subscribedRevision=e.subscribed.revision,e.subscribed.tracks.forEach(function(o){o.id?(r._subscriptionFailures.delete(o.sid),r._subscribed.set(o.sid,o.id)):o.error&&!r._subscriptionFailures.has(o.sid)&&r._subscriptionFailures.set(o.sid,o.error)});var i=new Set(e.subscribed.tracks.filter(function(o){return!!o.id}).map(function(o){return o.sid}));this._subscribed.forEach(function(o,s){i.has(s)||r._subscribed.delete(s)})}var a=new Set;return(e.participants||[]).forEach(function(o){if(o.sid!==r.localParticipant.sid){var s=r._disconnectedParticipantRevisions.get(o.sid);if(!(s&&o.revision<=s)){s&&r._disconnectedParticipantRevisions.delete(o.sid);var u=r._getOrCreateRemoteParticipant(o);u.update(o),a.add(u)}}}),e.type==="synced"&&this.participants.forEach(function(o){a.has(o)||o.disconnect()}),ET(this),e.peer_connections&&this._peerConnectionManager.update(e.peer_connections,e.type==="synced"),e.recording&&this.recording.update(e.recording),e.published&&e.published.revision>this._publishedRevision&&(this._publishedRevision=e.published.revision,e.published.tracks.forEach(function(o){o.sid&&r._published.set(o.id,o.sid)}),this.localParticipant.update(e.published)),e.participant&&this.localParticipant.connect(e.participant.sid,e.participant.identity),[this._dominantSpeakerSignaling,this._networkQualitySignaling,this._trackPrioritySignaling,this._trackSwitchOffSignaling,this._renderHintsSignaling,this._publisherHintsSignaling].forEach(function(o){var s=o.channel;!o.isSetup&&e.media_signaling&&e.media_signaling[s]&&e.media_signaling[s].transport&&e.media_signaling[s].transport.type==="data-channel"&&o.setup(e.media_signaling[s].transport.label)}),e.type==="warning"&&e.states&&e.states.revision>this._mediaStatesWarningsRevision&&(this._mediaStatesWarningsRevision=e.states.revision,this.localParticipant.updateMediaStates(e.states)),this},t.prototype._initPublisherHintSignaling=function(){var e=this;this._publisherHintsSignaling.on("updated",function(i,a){Promise.all(i.map(function(o){return e.localParticipant.setPublisherHint(o.track,o.encodings).then(function(s){return{track:o.track,result:s}})})).then(function(o){e._publisherHintsSignaling.sendHintResponse({id:a,hints:o})})});var r=function(i){i.kind==="video"&&i.trackTransceiver.on("replaced",function(){e._publisherHintsSignaling.sendTrackReplaced({trackSid:i.sid})})};Array.from(this.localParticipant.tracks.values()).forEach(function(i){return r(i)}),this.localParticipant.on("trackAdded",function(i){return r(i)})},t.prototype._initTrackSwitchOffSignaling=function(){var e=this;this._trackSwitchOffSignaling.on("updated",function(r,i){try{e._log.debug("received trackSwitch: ",{tracksOn:i,tracksOff:r});var a=new Map;i.forEach(function(o){return a.set(o,!0)}),r.forEach(function(o){a.get(o)&&e._log.warn(o+" is DUPLICATED in both tracksOff and tracksOn list"),a.set(o,!1)}),e.participants.forEach(function(o){o.tracks.forEach(function(s){var u=a.get(s.sid);typeof u<"u"&&(s.setSwitchedOff(!u),a.delete(s.sid))})}),a.forEach(function(o,s){return e._pendingSwitchOffStates.set(s,!o)})}catch(o){e._log.error("error processing track switch off:",o)}})},t.prototype._initDominantSpeakerSignaling=function(){var e=this;this._dominantSpeakerSignaling.on("updated",function(){return e.setDominantSpeaker(e._dominantSpeakerSignaling.loudestParticipantSid)})},t.prototype._initNetworkQualityMonitorSignaling=function(){var e=this;this._networkQualitySignaling.on("ready",function(){var r=new e._NetworkQualityMonitor(e._peerConnectionManager,e._networkQualitySignaling);e._networkQualityMonitor=r,r.on("updated",function(){e.iceConnectionState!=="failed"&&(e.localParticipant.setNetworkQualityLevel(r.level,r.levels),e.participants.forEach(function(i){var a=r.remoteLevels.get(i.sid);a&&i.setNetworkQualityLevel(a.level,a)}))}),r.start()}),this._networkQualitySignaling.on("teardown",function(){return e._teardownNetworkQualityMonitor()})},t.prototype._teardownNetworkQualityMonitor=function(){this._networkQualityMonitor&&(this._networkQualityMonitor.stop(),this._networkQualityMonitor=null)},t.prototype.getStats=function(){var e=this;return this._peerConnectionManager.getStats().then(function(r){return new Map(Array.from(r).map(function(i){var a=Rc(i,2),o=a[0],s=a[1];return[o,Object.assign({},s,{localAudioTrackStats:Qi(e,s.localAudioTrackStats),localVideoTrackStats:Qi(e,s.localVideoTrackStats),remoteAudioTrackStats:Gi(e,s.remoteAudioTrackStats),remoteVideoTrackStats:Gi(e,s.remoteVideoTrackStats)})]}))})},t}(cT);function Lc(n,t){return t.reduce(function(e,r){var i=n.get(r.trackId);return i?[Object.assign({},r,{trackSid:i})].concat(e):e},[])}function Qi(n,t){return Lc(n._published,t)}function Gi(n,t){var e=new Map(Array.from(n._subscribed.entries()).map(function(r){var i=Rc(r,2),a=i[0],o=i[1];return[o,a]}));return Lc(e,t)}function wT(n,t){var e=qi(function(){n._publishNewLocalParticipantState()}),r=qi(function(){var i=wn(t.tracks,function(a){return a.trackTransceiver});n._peerConnectionManager.setTrackSenders(i)});t.on("trackAdded",r),t.on("trackRemoved",r),t.on("updated",e),n.on("stateChanged",function i(a){a==="disconnected"&&(t.removeListener("trackAdded",r),t.removeListener("trackRemoved",r),t.removeListener("updated",e),n.removeListener("stateChanged",i),t.disconnect())}),n.on("signalingConnectionStateChanged",function(){var i=n.localParticipant,a=n.signalingConnectionState,o=i.identity,s=i.sid;switch(a){case"connected":i.connect(s,o);break;case"reconnecting":i.reconnecting();break}})}function kT(n,t){t.on("description",function(r){n._publishPeerConnectionState(r)}),t.dequeue("description"),t.on("candidates",function(r){n._publishPeerConnectionState(r)}),t.dequeue("candidates"),t.on("trackAdded",n._addTrackReceiver.bind(n)),t.dequeue("trackAdded"),t.getTrackReceivers().forEach(n._addTrackReceiver,n),t.on("connectionStateChanged",function(){n.emit("connectionStateChanged")}),t.on("iceConnectionStateChanged",function(){n.emit("iceConnectionStateChanged"),n.iceConnectionState==="failed"&&(n.localParticipant.networkQualityLevel!==null&&n.localParticipant.setNetworkQualityLevel(0),n.participants.forEach(function(e){e.networkQualityLevel!==null&&e.setNetworkQualityLevel(0)}))})}function TT(n,t){t.on("message",n._update.bind(n)),t.on("stateChanged",function e(r,i){r==="disconnected"&&(n.state!=="disconnected"&&n._disconnect(i),t.removeListener("stateChanged",e)),n.emit("signalingConnectionStateChanged")})}function PT(n,t,e){var r=new Map,i=!1,a=setInterval(function(){n.getStats().then(function(o){i=!i,o.forEach(function(s,u){var c=new lT(u,s,!0);t.publishEvent("quality","stats-report","info",{audioTrackStats:c.remoteAudioTrackStats.map(function(v,_){return zi(v,s.remoteAudioTrackStats[_],r)}),localAudioTrackStats:c.localAudioTrackStats.map(function(v,_){return Ki(v,s.localAudioTrackStats[_],r)}),localVideoTrackStats:c.localVideoTrackStats.map(function(v,_){return Ki(v,s.localVideoTrackStats[_],r)}),peerConnectionId:c.peerConnectionId,videoTrackStats:c.remoteVideoTrackStats.map(function(v,_){return zi(v,s.remoteVideoTrackStats[_],r)})});var l=wn(["localAudioTrackStats","localVideoTrackStats","remoteAudioTrackStats","remoteVideoTrackStats"],function(v){return c[v].map(function(_){var m=_.ssrc,y=_.trackSid;return y+"+"+m})}),f=yT(Array.from(r.keys()),l);if(f.forEach(function(v){return r.delete(v)}),i){var p=OT(s.activeIceCandidatePair,c.peerConnectionId);t.publishEvent("quality","active-ice-candidate-pair","info",p)}})},function(){})},e);n.on("stateChanged",function o(s){s==="disconnected"&&(clearInterval(a),n.removeListener("stateChanged",o))})}function ET(n){var t=n._getTrackSidsToTrackSignalings();n._subscriptionFailures.forEach(function(e,r){var i=t.get(r);i&&(n._subscriptionFailures.delete(r),i.subscribeFailed(gT(e.code,e.message)))}),t.forEach(function(e){var r=n._subscribed.get(e.sid);(!r||e.isSubscribed&&e.trackTransceiver.id!==r)&&e.setTrackTransceiver(null),r&&n._getTrackReceiver(r).then(function(i){return e.setTrackTransceiver(i)})})}function Ki(n,t,e){var r=t.framesEncoded,i=t.packetsSent,a=t.totalEncodeTime,o=t.totalPacketSendDelay,s=Object.assign({},n),u=n.trackSid+"+"+n.ssrc,c=e.get(u)||new Map;if(typeof a=="number"&&typeof r=="number"){var l=c.get("avgEncodeDelay")||new Ct;l.putSample(a*1e3,r),s.avgEncodeDelay=Math.round(l.get()),c.set("avgEncodeDelay",l)}if(typeof o=="number"&&typeof i=="number"){var f=c.get("avgPacketSendDelay")||new Ct;f.putSample(o*1e3,i),s.avgPacketSendDelay=Math.round(f.get()),c.set("avgPacketSendDelay",f)}return e.set(u,c),s}function zi(n,t,e){var r=t.estimatedPlayoutTimestamp,i=t.framesDecoded,a=t.jitterBufferDelay,o=t.jitterBufferEmittedCount,s=t.totalDecodeTime,u=Object.assign({},n),c=n.trackSid+"+"+n.ssrc,l=e.get(c)||new Map;if(typeof r=="number"&&(u.estimatedPlayoutTimestamp=r),typeof i=="number"&&typeof s=="number"){var f=l.get("avgDecodeDelay")||new Ct;f.putSample(s*1e3,i),u.avgDecodeDelay=Math.round(f.get()),l.set("avgDecodeDelay",f)}if(typeof a=="number"&&typeof o=="number"){var p=l.get("avgJitterBufferDelay")||new Ct;p.putSample(a*1e3,o),u.avgJitterBufferDelay=Math.round(p.get()),l.set("avgJitterBufferDelay",p)}return e.set(c,l),u}function OT(n,t){return n=Object.assign({availableIncomingBitrate:0,availableOutgoingBitrate:0,bytesReceived:0,bytesSent:0,consentRequestsSent:0,currentRoundTripTime:0,lastPacketReceivedTimestamp:0,lastPacketSentTimestamp:0,nominated:!1,peerConnectionId:t,priority:0,readable:!1,requestsReceived:0,requestsSent:0,responsesReceived:0,responsesSent:0,retransmissionsReceived:0,retransmissionsSent:0,state:"failed",totalRoundTripTime:0,transportId:"",writable:!1},Sr(n||{},null)),n.localCandidate=Object.assign({candidateType:"host",deleted:!1,ip:"",port:0,priority:0,protocol:"udp",url:""},Sr(n.localCandidate||{},null)),n.remoteCandidate=Object.assign({candidateType:"host",ip:"",port:0,priority:0,protocol:"udp",url:""},Sr(n.remoteCandidate||{},null)),n}var CT=ST,RT=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),LT=$e,xc=Eo,xT=Xs,AT=U.reconnectBackoffConfig,IT=we,kn=U,MT=kn.SDK_NAME,jT=kn.SDK_VERSION,DT=kn.SDP_FORMAT,Be=R,$T=Be.createBandwidthProfilePayload,NT=Be.createMediaSignalingPayload,FT=Be.createMediaWarningsPayload,VT=Be.createSubscribePayload,BT=Be.getUserAgent,UT=Be.isNonArrayObject,Qt=L,WT=Qt.createTwilioError,wr=Qt.RoomCompletedError,HT=Qt.SignalingConnectionError,qT=Qt.SignalingServerBusyError,QT=1,kr=2,GT={connecting:["connected","disconnected"],connected:["disconnected","syncing"],syncing:["connected","disconnected"],disconnected:[]},KT=function(n){RT(t,n);function t(e,r,i,a,o,s){var u=this;return s=Object.assign({Backoff:xT,TwilioConnection:xc,iceServers:null,trackPriority:!0,trackSwitchOff:!0,renderHints:!0,userAgent:BT()},s),u=n.call(this,"connecting",GT)||this,Object.defineProperties(u,{_accessToken:{value:r},_automaticSubscription:{value:s.automaticSubscription},_bandwidthProfile:{value:s.bandwidthProfile},_dominantSpeaker:{value:s.dominantSpeaker},_adaptiveSimulcast:{value:s.adaptiveSimulcast},_eventObserver:{value:s.eventObserver,writable:!1},_renderHints:{value:s.renderHints},_iceServersStatus:{value:Array.isArray(s.iceServers)?"overrode":"acquire"},_localParticipant:{value:i},_name:{value:e},_networkQuality:{value:UT(s.networkQuality)||s.networkQuality},_notifyWarnings:{value:s.notifyWarnings},_options:{value:s},_peerConnectionManager:{value:a},_sessionTimer:{value:null,writable:!0},_sessionTimeoutMS:{value:0,writable:!0},_reconnectBackoff:{value:new s.Backoff(AT)},_session:{value:null,writable:!0},_trackPriority:{value:s.trackPriority},_trackSwitchOff:{value:s.trackSwitchOff},_twilioConnection:{value:null,writable:!0},_updatesReceived:{value:[]},_updatesToSend:{value:[]},_userAgent:{value:s.userAgent},_wsServer:{value:o}}),YT(u),u}return t.prototype._createConnectOrSyncOrDisconnectMessage=function(){if(this.state==="connected")return null;if(this.state==="disconnected")return{session:this._session,type:"disconnect",version:kr};var e={connecting:"connect",syncing:"sync"}[this.state],r={name:this._name,participant:this._localParticipant.getState(),peer_connections:this._peerConnectionManager.getStates(),type:e,version:kr};return r.type==="connect"?(r.ice_servers=this._iceServersStatus,r.publisher={name:MT,sdk_version:jT,user_agent:this._userAgent},this._bandwidthProfile&&(r.bandwidth_profile=$T(this._bandwidthProfile)),this._notifyWarnings&&(r.participant.media_warnings=FT(this._notifyWarnings)),r.media_signaling=NT(this._dominantSpeaker,this._networkQuality,this._trackPriority,this._trackSwitchOff,this._adaptiveSimulcast,this._renderHints),r.subscribe=VT(this._automaticSubscription),r.format=DT,r.token=this._accessToken):r.type==="sync"?(r.session=this._session,r.token=this._accessToken):r.type==="update"&&(r.session=this._session),r},t.prototype._createIceMessage=function(){return{edge:"roaming",token:this._accessToken,type:"ice",version:QT}},t.prototype._sendConnectOrSyncOrDisconnectMessage=function(){var e=this._createConnectOrSyncOrDisconnectMessage();e&&this._twilioConnection.sendMessage(e)},t.prototype.disconnect=function(e){return this.state!=="disconnected"?(this.preempt("disconnected",null,[e]),this._sendConnectOrSyncOrDisconnectMessage(),this._twilioConnection.close(),!0):!1},t.prototype.publish=function(e){switch(this.state){case"connected":return this._twilioConnection.sendMessage(Object.assign({session:this._session,type:"update",version:kr},e)),!0;case"connecting":case"syncing":return this._updatesToSend.push(e),!0;case"disconnected":default:return!1}},t.prototype.publishEvent=function(e,r,i,a){this._eventObserver.emit("event",{group:e,name:r,level:i,payload:a})},t.prototype.sync=function(){return this.state==="connected"?(this.preempt("syncing"),this._sendConnectOrSyncOrDisconnectMessage(),!0):!1},t.prototype._setSession=function(e,r){this._session=e,this._sessionTimeoutMS=r*1e3},t.prototype._getReconnectTimer=function(){var e=this;return this._sessionTimeoutMS===0?null:(this._sessionTimer||(this._sessionTimer=new IT(function(){e._sessionTimer&&(e._sessionTimeoutMS=0)},this._sessionTimeoutMS)),new Promise(function(r){e._reconnectBackoff.backoff(r)}))},t.prototype._clearReconnectTimer=function(){this._reconnectBackoff.reset(),this._sessionTimer&&(this._sessionTimer.clear(),this._sessionTimer=null)},t}(LT);function Yi(n){return Array.from(n.reduce(function(t,e){var r=t.get(e.id)||e;return(!r.description&&e.description||r.description&&e.description&&e.description.revision>r.description.revision)&&(r.description=e.description),(!r.ice&&e.ice||r.ice&&e.ice&&e.ice.revision>r.ice.revision)&&(r.ice=e.ice),t.set(r.id,r),t},new Map).values())}function zT(n){return n.reduce(function(t,e){return(!t.participant&&e.participant||t.participant&&e.participant&&e.participant.revision>t.participant.revision)&&(t.participant=e.participant),!t.peer_connections&&e.peer_connections?t.peer_connections=Yi(e.peer_connections):t.peer_connections&&e.peer_connections&&(t.peer_connections=Yi(t.peer_connections.concat(e.peer_connections))),t},{})}function YT(n){function t(){if(n.state!=="disconnected"){n._twilioConnection&&n._twilioConnection.removeListener("message",r);var u=n._iceServersStatus,c=n._options,l=n._wsServer,f=n.state,p=c.TwilioConnection,v=new p(l,Object.assign({helloBody:f==="connecting"&&u==="acquire"?n._createIceMessage():n._createConnectOrSyncOrDisconnectMessage()},c));v.once("close",function(_){_===p.CloseReason.LOCAL?e():e(new Error(_))}),v.on("message",r),n._twilioConnection=v}}function e(u){if(n.state!=="disconnected"){if(!u){n.disconnect();return}var c=n._getReconnectTimer();if(!c){var l=u.message===xc.CloseReason.BUSY?new qT:new HT;n.disconnect(l);return}n.state==="connected"&&n.preempt("syncing"),c.then(t)}}function r(u){if(n.state!=="disconnected"){if(u.type==="error"){n.disconnect(WT(u.code,u.message));return}switch(n.state){case"connected":switch(u.type){case"connected":case"synced":case"update":case"warning":n.emit("message",u);return;case"disconnected":n.disconnect(u.status==="completed"?new wr:null);return;default:return}case"connecting":switch(u.type){case"iced":n._options.onIced(u.ice_servers).then(function(){n._sendConnectOrSyncOrDisconnectMessage()});return;case"connected":n._setSession(u.session,u.options.session_timeout),n.emit("connected",u),n.preempt("connected");return;case"synced":case"update":n._updatesReceived.push(u);return;case"disconnected":n.disconnect(u.status==="completed"?new wr:null);return;default:return}case"syncing":switch(u.type){case"connected":case"update":n._updatesReceived.push(u);return;case"synced":n._clearReconnectTimer(),n.emit("message",u),n.preempt("connected");return;case"disconnected":n.disconnect(u.status==="completed"?new wr:null);return;default:return}default:return}}}n.on("stateChanged",function u(c){switch(c){case"connected":{var l=n._updatesToSend.splice(0);l.length&&n.publish(zT(l)),n._updatesReceived.splice(0).forEach(function(f){return n.emit("message",f)});return}case"disconnected":n._twilioConnection.removeListener("message",r),n.removeListener("stateChanged",u);return;case"syncing":return;default:return}});var i=n._options,a=n._iceServersStatus,o=i.iceServers,s=i.onIced;a==="overrode"?s(o).then(t):t()}var JT=KT,XT=un,ZT=rS,eP=CT,tP=JT,Ac=L,rP=Ac.SignalingConnectionDisconnectedError,nP=Ac.SignalingIncomingMessageInvalidError,Ic=R,iP=Ic.flatMap,aP=Ic.createRoomConnectEventPayload;function oP(n,t,e,r,i,a){a=Object.assign({PeerConnectionManager:ZT,RoomV2:eP,Transport:tP},a);var o=i.video[0]&&i.video[0].adaptiveSimulcast===!0,s=a.PeerConnectionManager,u=a.RoomV2,c=a.Transport,l=a.iceServers,f=a.log,p=new s(r,i,a),v=iP(e.tracks,function(P){return[P.trackTransceiver]});p.setTrackSenders(v);var _=new Error("Canceled"),m,y=new XT(function(P,h,b){var g=function(Q){return b()?(h(_),Promise.reject(_)):(f.debug("Got ICE servers:",Q),a.iceServers=Q,p.setConfiguration(a),p.createAndOffer().then(function(){if(b())throw h(_),_;f.debug("createAndOffer() succeeded."),p.dequeue("description")}).catch(function(ee){throw f.error("createAndOffer() failed:",ee),h(ee),ee}))},T=a.automaticSubscription,S=a.bandwidthProfile,w=a.dominantSpeaker,O=a.environment,D=a.eventObserver,F=a.loggerName,q=a.logLevel,E=a.name,I=a.networkMonitor,k=a.networkQuality,x=a.notifyWarnings,C=a.realm,$=a.sdpSemantics,N=!!S,W=!!S,G=!!S&&(a.clientTrackSwitchOffControl!=="disabled"||a.contentPreferencesMode!=="disabled"),H=Object.assign({adaptiveSimulcast:o,automaticSubscription:T,dominantSpeaker:w,environment:O,eventObserver:D,loggerName:F,logLevel:q,networkMonitor:I,networkQuality:k,notifyWarnings:x,iceServers:l,onIced:g,realm:C,renderHints:G,sdpSemantics:$,trackPriority:N,trackSwitchOff:W},S?{bandwidthProfile:S}:{});m=new c(E,n,e,p,t,H);var ae=aP(a);D.emit("event",ae),m.once("connected",function(Q){if(f.debug("Transport connected:",Q),b()){h(_);return}var ee=Q.participant;if(!ee){h(new nP);return}P(new u(e,Q,m,p,a))}),m.once("stateChanged",function(Q,ee){Q==="disconnected"?(m=null,h(ee||new rP)):f.debug("Transport state changed:",Q)})},function(){m&&(m.disconnect(),m=null)});return y.catch(function(){m&&(m.disconnect(),m=null),p.close()}),y}var sP=oP,cP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),uP=Sn,lP=function(n){cP(t,n);function t(){var e=n.call(this)||this;return Object.defineProperties(e,{_publicationsToTrackSenders:{value:new Map},_trackSendersToPublications:{value:new Map}}),e}return t.prototype.addTrack=function(e,r,i,a){a===void 0&&(a=null);var o=this._createLocalTrackPublicationSignaling(e,r,i,a);return this._trackSendersToPublications.set(e,o),this._publicationsToTrackSenders.set(o,e),n.prototype.addTrack.call(this,o),this},t.prototype.getPublication=function(e){return this._trackSendersToPublications.get(e)||null},t.prototype.getSender=function(e){return this._publicationsToTrackSenders.get(e)||null},t.prototype.removeTrack=function(e){var r=this._trackSendersToPublications.get(e);if(!r)return null;this._trackSendersToPublications.delete(e),this._publicationsToTrackSenders.delete(r);var i=n.prototype.removeTrack.call(this,r);return i&&r.stop(),r},t}(uP),dP=lP,fP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),pP=Cc,vP=function(n){fP(t,n);function t(e,r,i){var a=this;e=e.clone();var o=e.kind==="data"?!0:e.track.enabled;return a=n.call(this,r,e.kind,o,i)||this,a.setTrackTransceiver(e),Object.defineProperties(a,{_updatedPriority:{value:i,writable:!0},id:{enumerable:!0,value:e.id}}),a}return Object.defineProperty(t.prototype,"updatedPriority",{get:function(){return this._updatedPriority},enumerable:!1,configurable:!0}),t.prototype.enable=function(e){return e=typeof e=="boolean"?e:!0,this.trackTransceiver.track.enabled=e,n.prototype.enable.call(this,e)},t.prototype.publishFailed=function(e){return hP(this,e)&&this.emit("updated"),this},t.prototype.setPriority=function(e){return this._updatedPriority!==e&&(this._updatedPriority=e,this.emit("updated")),this},t.prototype.setSid=function(e){return this._error?this:n.prototype.setSid.call(this,e)},t.prototype.stop=function(){this.trackTransceiver.stop()},t}(pP);function hP(n,t){return n._sid!==null||n._error?!1:(n._error=t,!0)}var _P=vP,mP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),yP=_P,gP=ma,bP=L.createTwilioError,SP=function(n){mP(t,n);function t(e,r,i,a,o){var s=n.call(this,e,r,i)||this;return Object.defineProperties(s,{_log:{value:o.log.createLog("default",s)},_mediaStates:{value:{recordings:null},writable:!0},_noiseCancellationVendor:{value:a}}),s}return t.prototype.getState=function(){var e={enabled:this.isEnabled,id:this.id,kind:this.kind,name:this.name,priority:this.updatedPriority};return this._noiseCancellationVendor&&(e.audio_processor=this._noiseCancellationVendor),e},t.prototype.toString=function(){return"[LocalTrackPublicationV2: "+this.sid+"]"},t.prototype.update=function(e){switch(e.state){case"ready":this.setSid(e.sid);break;case"failed":{var r=e.error;this.publishFailed(bP(r.code,r.message));break}}return this},t.prototype.updateMediaStates=function(e){if(!e||!e.recordings||this._mediaStates.recordings===e.recordings)return this;switch(this._mediaStates.recordings=e.recordings,this._mediaStates.recordings){case"OK":this._log.info("Warnings have cleared."),this.emit("warningsCleared");break;case"NO_MEDIA":this._log.warn("Recording media lost."),this.emit("warning",gP.recordingMediaLost);break;default:this._log.warn("Unknown media state detected: "+this._mediaStates.recordings);break}return this},t}(yP),wP=SP,kP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),TP=dP,PP=wP,EP=U.DEFAULT_LOG_LEVEL,OP=ce,Mc=R,CP=Mc.buildLogLevels,RP=Mc.isDeepEqual,LP=function(n){kP(t,n);function t(e,r,i){var a=this;i=Object.assign({logLevel:EP,LocalTrackPublicationV2:PP},i),a=n.call(this)||this;var o=CP(i.logLevel);return Object.defineProperties(a,{_bandwidthProfile:{value:null,writable:!0},_bandwidthProfileRevision:{value:0,writable:!0},_encodingParameters:{value:e},_removeListeners:{value:new Map},_LocalTrackPublicationV2:{value:i.LocalTrackPublicationV2},_log:{value:i.log?i.log.createLog("default",a):new OP("default",a,o,i.loggerName)},_publishedRevision:{writable:!0,value:0},_revision:{writable:!0,value:1},_signalingRegion:{value:null,writable:!0},audioProcessors:{value:[],writable:!0},bandwidthProfile:{enumerable:!0,get:function(){return this._bandwidthProfile}},bandwidthProfileRevision:{enumerable:!0,get:function(){return this._bandwidthProfileRevision}},networkQualityConfiguration:{enumerable:!0,value:r},revision:{enumerable:!0,get:function(){return this._revision}},signalingRegion:{enumerable:!0,get:function(){return this._signalingRegion}}}),a}return t.prototype.toString=function(){return"[LocalParticipantSignaling: "+this.sid+"]"},t.prototype.setSignalingRegion=function(e){this._signalingRegion||(this._signalingRegion=e)},t.prototype.setBandwidthProfile=function(e){RP(this._bandwidthProfile,e)||(this._bandwidthProfile=JSON.parse(JSON.stringify(e)),this._bandwidthProfileRevision++,this.didUpdate())},t.prototype.setAudioProcessors=function(e){this.audioProcessors=e},t.prototype.getParameters=function(){return this._encodingParameters},t.prototype.setParameters=function(e){return this._encodingParameters.update(e),this},t.prototype.update=function(e){return this._publishedRevision>=e.revision?this:(this._publishedRevision=e.revision,e.tracks.forEach(function(r){var i=this.tracks.get(r.id);i&&i.update(r)},this),this)},t.prototype.updateMediaStates=function(e){return!e||!e.tracks?this:(Array.from(this.tracks.values()).forEach(function(r){var i=e.tracks[r.sid];i&&r.updateMediaStates(i)}),this)},t.prototype._createLocalTrackPublicationSignaling=function(e,r,i,a){return new this._LocalTrackPublicationV2(e,r,i,a,{log:this._log})},t.prototype.addTrack=function(e,r,i,a){var o=this;n.prototype.addTrack.call(this,e,r,i,a);var s=this.getPublication(e),u=s.isEnabled,c=s.updatedPriority,l=function(){(u!==s.isEnabled||c!==s.updatedPriority)&&(o.didUpdate(),u=s.isEnabled,c=s.updatedPriority)};return s.on("updated",l),this._removeListener(s),this._removeListeners.set(s,function(){return s.removeListener("updated",l)}),this.didUpdate(),this},t.prototype._removeListener=function(e){var r=this._removeListeners.get(e);r&&r()},t.prototype.getState=function(){return{revision:this.revision,tracks:Array.from(this.tracks.values()).map(function(e){return e.getState()})}},t.prototype.didUpdate=function(){this._revision++,this.emit("updated")},t.prototype.removeTrack=function(e){var r=n.prototype.removeTrack.call(this,e);return r&&(e.removeClone(r.trackTransceiver),this._removeListener(r),this.didUpdate()),r},t.prototype.setNetworkQualityConfiguration=function(e){this.networkQualityConfiguration.update(e)},t.prototype.setPublisherHint=function(e,r){var i=Array.from(this.tracks.values()).find(function(a){return a.sid===e});return i?i.trackTransceiver.setPublisherHint(r):(this._log.warn("track:"+e+" not found"),Promise.resolve("UNKNOWN_TRACK"))},t}(TP),xP=LP,AP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),IP=Sn,MP=Pc,jP=$e,DP={closed:["opening"],opening:["closed","open"],open:["closed","closing"],closing:["closed","open"]},$P=function(n){AP(t,n);function t(){return n.call(this,"closed",DP)||this}return t.prototype._close=function(e){return this.transition("closing",e),this.transition("closed",e),Promise.resolve(this)},t.prototype._connect=function(e,r,i,a,o){e.connect("PA00000000000000000000000000000000","test");var s="RM00000000000000000000000000000000",u=Promise.resolve(new MP(e,s,o));return u.cancel=function(){},u},t.prototype._open=function(e){return this.transition("opening",e),this.transition("open",e),Promise.resolve(this)},t.prototype.close=function(){var e=this;return this.bracket("close",function(r){switch(e.state){case"closed":return e;case"open":return e._close(r);default:throw new Error('Unexpected Signaling state "'+e.state+'"')}})},t.prototype.connect=function(e,r,i,a,o){var s=this;return this.bracket("connect",function u(c){switch(s.state){case"closed":return s._open(c).then(u.bind(null,c));case"open":return s.releaseLockCompletely(c),s._connect(e,r,i,a,o);default:throw new Error('Unexpected Signaling state "'+s.state+'"')}})},t.prototype.createLocalParticipantSignaling=function(){return new IP},t.prototype.open=function(){var e=this;return this.bracket("open",function(r){switch(e.state){case"closed":return e._open(r);case"open":return e;default:throw new Error('Unexpected Signaling state "'+e.state+'"')}})},t}(jP),NP=$P,FP=d&&d.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},n(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),VP=sP,BP=xP,UP=NP,WP=function(n){FP(t,n);function t(e,r){var i=this;return r=Object.assign({createCancelableRoomSignalingPromise:VP},r),i=n.call(this)||this,Object.defineProperties(i,{_createCancelableRoomSignalingPromise:{value:r.createCancelableRoomSignalingPromise},_options:{value:r},_wsServer:{value:e}}),i}return t.prototype._connect=function(e,r,i,a,o){return o=Object.assign({},this._options,o),this._createCancelableRoomSignalingPromise.bind(null,r,this._wsServer,e,i,a,o)},t.prototype.createLocalParticipantSignaling=function(e,r){return new BP(e,r)},t}(UP),HP=WP,qP=be.MediaStreamTrack,Tn=V,QP=Tn.guessBrowser,GP=Tn.guessBrowserVersion,KP=Tn.isCodecSupported,zP=Ah,YP=Dh,JP=Vm,XP=As,ZP=Um,Pn=Ae,eE=Pn.LocalAudioTrack,tE=Pn.LocalDataTrack,rE=Pn.LocalVideoTrack,nE=Gm,iE=Fy,aE=HP,Gt=R,oE=Gt.asLocalTrack,sE=Gt.buildLogLevels,cE=Gt.filterObject,Ji=Gt.isNonArrayObject,ue=U,uE=ue.DEFAULT_ENVIRONMENT,lE=ue.DEFAULT_LOG_LEVEL,dE=ue.DEFAULT_LOGGER_NAME,fE=ue.DEFAULT_REALM,pE=ue.DEFAULT_REGION,vE=ue.WS_SERVER,hE=ue.SDK_NAME,_E=ue.SDK_VERSION,yt=ue.typeErrors,me=un,mE=Ls,yE=ce,gE=tt.validateBandwidthProfile,Tr=QP()==="safari"&&GP(),bE=0,Xi=!1,jc=!1;if(Tr){var Zi=Tr.major,SE=Tr.minor;jc=Zi<12||Zi===12&&SE<1}var wE=new Set([{didWarn:!1,shouldDelete:!0,name:"abortOnIceServersTimeout"},{didWarn:!1,shouldDelete:!0,name:"dscpTagging",newName:"enableDscp"},{didWarn:!1,shouldDelete:!0,name:"iceServersTimeout"},{didWarn:!1,shouldDelete:!1,name:"eventListener",newName:"Video.Logger"},{didWarn:!1,shouldDelete:!1,name:"logLevel",newName:"Video.Logger"}]),kE=new Set([{didWarn:!1,shouldDelete:!1,name:"maxTracks",newName:"bandwidthProfile.video.clientTrackSwitchOffControl"},{didWarn:!1,shouldDelete:!1,name:"renderDimensions",newName:"bandwidthProfile.video.contentPreferencesMode"}]);function TE(n,t){if(typeof t>"u"&&(t={}),!Ji(t))return me.reject(yt.INVALID_TYPE("options","object"));var e=t.Log||yE,r=t.loggerName||dE,i=t.logLevel||lE,a=sE(i),o="[connect #"+ ++bE+"]",s;try{s=new e("default",o,a,r)}catch(S){return me.reject(S)}ea(t,s,wE);var u=t.preferredVideoCodecs==="auto";if(u&&(t.preferredVideoCodecs=[{codec:"VP8",simulcast:!0,adaptiveSimulcast:!0}]),t.maxVideoBitrate&&u)return s.error('ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"'),me.reject(yt.ILLEGAL_INVOKE("connect",'ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"'));t=Object.assign({automaticSubscription:!0,dominantSpeaker:!1,enableDscp:!1,environment:uE,eventListener:null,insights:!0,LocalAudioTrack:eE,LocalDataTrack:tE,LocalParticipant:JP,LocalVideoTrack:rE,Log:e,MediaStreamTrack:qP,loggerName:r,logLevel:i,maxAudioBitrate:null,maxVideoBitrate:null,name:null,networkMonitor:!0,networkQuality:!1,preferredAudioCodecs:[],preferredVideoCodecs:[],realm:fE,region:pE,signaling:aE},cE(t));var c={};typeof t.wsServerInsights=="string"&&(c.gateway=t.wsServerInsights);var l=t.insights?XP:ZP,f=new l(n,hE,_E,t.environment,t.realm,c),p=vE(t.environment,t.region),v=new mE(f,Date.now(),s,t.eventListener);if(t=Object.assign({eventObserver:v,wsServer:p},t),t.log=s,jc&&!Xi&&s.logLevel!=="error"&&s.logLevel!=="off"&&(Xi=!0,s.warn(["Support for Safari 12.0 and below is limited because it does not support VP8.","This means you may experience codec issues in Group Rooms. You may also","experience codec issues in Peer-to-Peer (P2P) Rooms containing Android- or","iOS-based Participants who do not support H.264. However, P2P Rooms","with browser-based Participants should work. For more information, please","refer to this guide: https://www.twilio.com/docs/video/javascript-v2-developing-safari-11"].join(" "))),typeof n!="string")return me.reject(yt.INVALID_TYPE("token","string"));var _=Object.assign({},t);if(delete _.name,"tracks"in t){if(!Array.isArray(t.tracks))return me.reject(yt.INVALID_TYPE("options.tracks","Array of LocalAudioTrack, LocalVideoTrack or MediaStreamTrack"));try{t.tracks=t.tracks.map(function(S){return oE(S,_)})}catch(S){return me.reject(S)}}var m=gE(t.bandwidthProfile);if(m)return me.reject(m);t.clientTrackSwitchOffControl="disabled",t.contentPreferencesMode="disabled",t.bandwidthProfile&&(t.clientTrackSwitchOffControl="auto",t.contentPreferencesMode="auto",t.bandwidthProfile.video&&(ea(t.bandwidthProfile.video,s,kE),"maxTracks"in t.bandwidthProfile.video?t.clientTrackSwitchOffControl="disabled":t.bandwidthProfile.video.clientTrackSwitchOffControl==="manual"?t.clientTrackSwitchOffControl="manual":t.clientTrackSwitchOffControl="auto","renderDimensions"in t.bandwidthProfile.video?t.contentPreferencesMode="disabled":t.bandwidthProfile.video.contentPreferencesMode==="manual"?t.contentPreferencesMode="manual":t.contentPreferencesMode="auto"));var y=t.signaling,P=new y(t.wsServer,t);s.info("Connecting to a Room"),s.debug("Options:",t);var h=new YP({maxAudioBitrate:t.maxAudioBitrate,maxVideoBitrate:t.maxVideoBitrate},u),b={audio:t.preferredAudioCodecs.map(ta),video:t.preferredVideoCodecs.map(ta)},g=new nE(Ji(t.networkQuality)?t.networkQuality:{});["audio","video"].forEach(function(S){return b[S].forEach(function(w){var O=w.codec;return KP(O,S).then(function(D){return!D&&s.warn("The preferred "+S+' codec "'+O+'" will be ignored as it is not supported by the browser.')})})});var T=zP(CE.bind(null,t),PE.bind(null,P,s,h,g,t),OE.bind(null,n,t,P,h,b),EE.bind(null,t));return T.then(function(S){return f.connect(S.sid,S.localParticipant.sid),s.info("Connected to Room:",S.toString()),s.info("Room name:",S.name),s.debug("Room:",S),S.once("disconnected",function(){return f.disconnect()}),S},function(S){f.disconnect(),T._isCanceled?s.info("Attempt to connect to a Room was canceled"):s.info("Error while connecting to a Room:",S)}),T}function ea(n,t,e){e.forEach(function(r){var i=r.didWarn,a=r.name,o=r.newName,s=r.shouldDelete;a in n&&typeof n[a]<"u"&&(o&&s&&(n[o]=n[a]),s&&delete n[a],!i&&!["error","off"].includes(t.level)&&(t.warn('The ConnectOptions "'+a+'" is '+(o?'deprecated and scheduled for removal. Please use "'+o+'" instead.':"no longer applicable and will be ignored.")),r.didWarn=!0))})}function PE(n,t,e,r,i,a){var o=n.createLocalParticipantSignaling(e,r);return t.debug("Creating a new LocalParticipant:",o),new i.LocalParticipant(o,a,i)}function EE(n,t,e){var r=new iE(t,e,n),i=n.log;return i.debug("Creating a new Room:",r),e.on("stateChanged",function a(o){o==="disconnected"&&(i.info("Disconnected from Room:",r.toString()),e.removeListener("stateChanged",a))}),r}function OE(n,t,e,r,i,a){return t.log.debug("Creating a new RoomSignaling"),e.connect(a._signaling,n,r,i,t)}function CE(n,t){var e=n.log;return n.shouldStopLocalTracks=!n.tracks,n.shouldStopLocalTracks?e.info("LocalTracks were not provided, so they will be acquired automatically before connecting to the Room. LocalTracks will be released if connecting to the Room fails or if the Room is disconnected"):(e.info("Getting LocalTracks"),e.debug("Options:",n)),n.createLocalTracks(n).then(function(i){var a=t(i);return a.catch(function(){n.shouldStopLocalTracks&&(e.info("The automatically acquired LocalTracks will now be stopped"),i.forEach(function(s){s.stop()}))}),a})}function ta(n){var t=typeof n=="string"?{codec:n}:n;switch(t.codec.toLowerCase()){case"opus":return Object.assign({dtx:!0},t);case"vp8":return Object.assign({simulcast:!1},t);default:return t}}var RE=TE,Dc=U,LE=Dc.DEFAULT_LOG_LEVEL,xE=Dc.DEFAULT_LOGGER_NAME;function $c(n,t){t=Object.assign({loggerName:xE,logLevel:LE},t);var e={};e.loggerName=t.loggerName,e.logLevel=t.logLevel,delete t.loggerName,delete t.logLevel;var r=t.createLocalTracks;return delete t.createLocalTracks,e[n]=Object.keys(t).length>0?t:!0,r(e).then(function(i){return i[0]})}function AE(n){return $c("audio",n)}function IE(n){return $c("video",n)}var ra={audio:AE,video:IE},Nc=V,ME=Nc.guessBrowser,jE=Nc.support,DE=J.getSdpFormat,at=Se,$E=at.isAndroid,NE=at.isMobile,FE=at.isNonChromiumEdge,VE=at.rebrandedChromeBrowser,BE=at.mobileWebKitBrowser,UE=["crios","edg","edge","electron","headlesschrome"],WE=["chrome","firefox"],HE=["chrome","safari"],qE=[];function QE(){var n=ME();if(!n)return!1;var t=VE(n),e=BE(n),r=$E()?WE:HE;return!!n&&jE()&&DE()==="unified"&&(!t||UE.includes(t))&&!FE(n)&&(!e||qE.includes(e))&&(!NE()||r.includes(n))}var GE=QE,KE=Ur.exports,Ze=d&&d.__assign||function(){return Ze=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},Ze.apply(this,arguments)};Object.defineProperty(KE,"__esModule",{value:!0});var Kt=Rt,zE=Me,le={connect:RE,createLocalAudioTrack:ra.audio,createLocalVideoTrack:ra.video,isSupported:GE(),version:sa.version,Logger:oa,LocalAudioTrack:Ae.LocalAudioTrack,LocalDataTrack:Ae.LocalDataTrack,LocalVideoTrack:Ae.LocalVideoTrack};function YE(n,t){var e=Ze({createLocalTracks:Kt.createLocalTracks},t);return le.connect(n,e)}function JE(n){var t=Ze({createLocalTracks:Kt.createLocalTracks},n);return le.createLocalAudioTrack(t)}function XE(n){var t=Ze({createLocalTracks:Kt.createLocalTracks},n);return le.createLocalVideoTrack(t)}var ZE=le.isSupported,eO=le.version,tO=le.Logger,rO=le.LocalAudioTrack,nO=le.LocalVideoTrack,iO=le.LocalDataTrack;Ur.exports={connect:YE,createLocalAudioTrack:JE,createLocalVideoTrack:XE,createLocalTracks:Kt.createLocalTracks,runPreflight:zE.runPreflight,isSupported:ZE,version:eO,Logger:tO,LocalAudioTrack:rO,LocalVideoTrack:nO,LocalDataTrack:iO};var aO=Ur.exports;const sO=Fc(aO);export{sO as T};
