import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as l,f as Ne,r as Se}from"./vendor-2ae44a2e.js";import{u as ve}from"./react-hook-form-47c010f8.js";import{o as Ce}from"./yup-5abd4662.js";import{c as Te}from"./yup-c2e87575.js";import{M as Pe,T as De,A as Ee,G as ke,B as Ae,a as Re,b as Fe,l as Be,R as Oe,c as qe,x as Le,y as $e,t as A,g as ze}from"./index-d0a8f5da.js";import{P as Me}from"./index-132fbad2.js";import{A as Ie}from"./index-e429b426.js";import{E as Ge}from"./ExportButton-6ac23de7.js";import{C as R,q as Ue}from"./@headlessui/react-7bce1936.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let x=new Pe;const bt=({columns:i=[],actions:o={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPosition:F="onTable",actionId:_="id",tableRole:H="admin",table:f="user",tableTitle:w="",tableSchema:_e=[],hasFilter:J=!0,schemaFields:K=[],showPagination:Q=!0,defaultFilter:j=[],refreshRef:B=null})=>{var M,I,G,U;const W=new De,{dispatch:v}=l.useContext(Ee);l.useContext(ke),Ne(),l.useState("");const[X,C]=l.useState([]),[u,O]=l.useState(10),[q,Y]=l.useState(0),[He,Z]=l.useState(0),[m,V]=l.useState(0),[ee,te]=l.useState(!1),[se,re]=l.useState(!1),[ae,T]=l.useState(!1),[oe,P]=l.useState(!1);l.useState(!1);const[L,D]=l.useState(!1),[n,b]=l.useState([]),[N,E]=l.useState([]),[g,le]=l.useState([]),[ne,ie]=l.useState(""),[$,ce]=l.useState("eq");l.useState(!1);const[de,S]=l.useState(!0),ue=Te({}),{register:Je,handleSubmit:pe,setError:Ke,reset:Qe,formState:{errors:We}}=ve({resolver:Ce(ue)});function he(e){console.log(i[e]),i[e].isSorted?i[e].isSortedDesc=!i[e].isSortedDesc:(i.map(s=>s.isSorted=!1),i.map(s=>s.isSortedDesc=!1),i[e].isSorted=!0),async function(){await p(0,u)}()}function xe(e){(async function(){O(e),await p(0,e)})()}function fe(){(async function(){await p(m-1>0?m-1:0,u)})()}function me(){(async function(){await p(m+1<=q?m+1:0,u)})()}const z=(e,s,a)=>{const r=s==="eq"&&isNaN(a)?`${a}`:a,c=`${e},${s},${r}`.toLowerCase();E(d=>[...d.filter(h=>!h.includes(e)),c]),ie(a)};async function p(e,s,a){console.log("filterConditions >>",N);try{S(!0);const r=await W.getPaginate(f,{size:s,page:e,...N.length?{filter:[...j.length&&j,...N]}:j.length?{filter:[...j]}:null});r&&S(!1);const{list:c,total:d,limit:k,num_pages:h,page:y}=r;C(c),console.log("v2 component fetch result"),console.log(r),console.log("list"),console.log(c),O(k),Y(h),V(y),Z(d),te(y>1),re(y+1<=h),S(!1)}catch(r){S(!1),console.log("ERROR",r),A(v,r.message)}}const ge=async e=>{async function s(a){try{P(!0),x.setTable(f);const r=await x.callRestAPI({id:a},"DELETE");r!=null&&r.error||(C(c=>c.filter(d=>Number(d.id)!==Number(a))),P(!1),T(!1))}catch(r){throw P(!1),T(!1),A(v,r==null?void 0:r.message),new Error(r)}}typeof e=="object"?e.forEach(async a=>{await s(a)}):typeof e=="number"&&await s(e)},ye=async e=>{try{x.setTable(f);const s=await x.exportCSV()}catch(s){throw new Error(s)}},we=e=>{for(const s of K){const[a]=s.split(":");ze(e[a])}p(1,u)};async function je(e,s,a){try{x.setTable(f);const r=await x.callRestAPI({id:e,[s]:a},"PUT");console.log("update user data"),console.log(r)}catch(r){console.log("ERROR",r),A(v,r.message)}}async function be(e,s,a,r){console.log(s),console.log(a),console.log(r);let c;s=isNaN(Number.parseInt(s))?s:Number.parseInt(s);try{clearTimeout(c),c=setTimeout(async()=>{await je(e,r,s)},200),C(d=>d.map((h,y)=>y===a?{...h,[r]:s}:h))}catch(d){console.error(d)}}return l.useEffect(()=>{var e;(e=o==null?void 0:o.select)!=null&&e.action&&o.select.action()},[g.length]),l.useEffect(()=>{const s=setTimeout(async()=>{await p(1,u)},700);return()=>{clearTimeout(s)}},[ne,N,$]),t.jsxs("div",{className:"px-8",children:[B&&t.jsx("button",{ref:B,onClick:()=>p(1,u),className:"hidden"}),t.jsxs("div",{className:`flex gap-3 ${w?"flex-col":"h-fit items-center"}`,children:[J?t.jsx("div",{className:"flex w-auto items-center justify-between",children:t.jsx("form",{className:"relative rounded bg-white",onSubmit:pe(we),children:t.jsxs(R,{children:[t.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[t.jsxs(R.Button,{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",children:[t.jsx(Ae,{}),t.jsx("span",{children:"Filters"}),n.length>0&&t.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length>0?n.length:null})]}),t.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[t.jsx(Re,{className:"text-xl text-gray-200"}),t.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:e=>{var s;return z("name","cs",(s=e.target)==null?void 0:s.value)}}),t.jsx(Fe,{className:"text-lg text-gray-200"})]})]}),t.jsx(Ue,{as:Se.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:t.jsx(R.Panel,{children:t.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-6 shadow-lg",children:[n==null?void 0:n.map((e,s)=>t.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[t.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:e}),t.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:a=>{ce(a.target.value)},children:[t.jsx("option",{value:"eq",selected:!0,children:"equals"}),t.jsx("option",{value:"cs",children:"contains"}),t.jsx("option",{value:"sw",children:"start with"}),t.jsx("option",{value:"ew",children:"ends with"}),t.jsx("option",{value:"lt",children:"lower than"}),t.jsx("option",{value:"le",children:"lower or equal"}),t.jsx("option",{value:"ge",children:"greater or equal"}),t.jsx("option",{value:"gt",children:"greater than"}),t.jsx("option",{value:"bt",children:"between"}),t.jsx("option",{value:"in",children:"in"}),t.jsx("option",{value:"is",children:"is null"})]}),t.jsx(Be,{type:"text",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" !mb-3 w-1/3 !rounded-md !border !border-gray-700 !px-3 !py-2 !leading-tight !text-gray-700 !outline-none",onReady:a=>z(e,$,a)}),t.jsx(Oe,{className:"cursor-pointer text-2xl",onClick:()=>{b(a=>a.filter(r=>r!==e)),E(a=>a.filter(r=>!r.includes(e)))}})]},s)),t.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[t.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{D(!L)},children:[t.jsx(qe,{}),"Add filter"]}),L&&t.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600",children:t.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:i.map(e=>{if(e.hasOwnProperty("isFilter")&&e.isFilter)return t.jsx("li",{className:`${n.includes(e.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{n.includes(e.header)||b(s=>[...s,e.accessor]),D(!1)},children:e.header},e.header);if(!e.hasOwnProperty("isFilter"))return t.jsx("li",{className:`${n.includes(e.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{n.includes(e.header)||b(s=>[...s,e.accessor]),D(!1)},children:e.header},e.header)}).filter(Boolean)})}),n.length>0&&t.jsx("div",{onClick:()=>{b([]),E([])},className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})})]})})}):null,t.jsxs("div",{className:"flex h-fit w-full justify-between text-center",children:[t.jsx("h4",{className:"text-2xl font-medium capitalize",children:w||""}),t.jsxs("div",{className:"flex h-full gap-2",children:[g!=null&&g.length&&F==="aboveTable"?t.jsx(Le,{actions:o,selectedItems:g}):null,((M=o==null?void 0:o.export)==null?void 0:M.show)&&t.jsx(Ge,{showText:!1,onClick:ye,className:"mx-1"}),((I=o==null?void 0:o.add)==null?void 0:I.show)&&t.jsx(Ie,{onClick:()=>{var e,s;(e=o==null?void 0:o.add)!=null&&e.action&&((s=o==null?void 0:o.add)==null||s.action())},showChildren:(G=o==null?void 0:o.add)==null?void 0:G.showChildren,children:(U=o==null?void 0:o.add)==null?void 0:U.children})]})]})]}),t.jsx("div",{className:"overflow-x-auto rounded bg-[#1d2937] p-5 px-0",children:t.jsx($e,{onSort:he,columns:i,tableRole:H,actionId:_,table:f,tableTitle:w,deleteItem:ge,loading:de,deleteLoading:oe,showDeleteModal:ae,currentTableData:X,setShowDeleteModal:T,actions:o,actionPosition:F,setSelectedItems:le,handleTableCellChange:be})}),Q&&t.jsx(Me,{currentPage:m,pageCount:q,pageSize:u,canPreviousPage:ee,canNextPage:se,updatePageSize:xe,previousPage:fe,nextPage:me})]})};export{bt as default};
