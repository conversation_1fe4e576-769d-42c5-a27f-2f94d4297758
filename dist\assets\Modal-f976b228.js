import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as d}from"./vendor-2ae44a2e.js";import{k as i}from"./index.esm-42944128.js";import"./react-icons-f29df01f.js";const m=({children:t,title:r,modalCloseClick:l,modalHeader:s,classes:o,page:a=""})=>e.jsx("div",{style:{zIndex:100000002,transform:"translate(-50%, -50%)"},className:"modal-holder top-[50 %] fixed left-[50%] flex h-full w-[100vw] w-full items-center justify-center overflow-auto bg-[#00000099]",children:e.jsxs("div",{className:`${a==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} rounded-lg bg-white py-5 shadow ${o==null?void 0:o.modalDialog} `,children:[s&&e.jsxs("div",{className:"flex justify-between border-b px-5 pb-2",children:[e.jsx("h5",{className:"text-center text-lg font-bold uppercase",children:r}),e.jsx("div",{className:"modal-close cursor-pointer",onClick:l,children:e.jsx(i,{className:"text-xl"})})]}),e.jsx("div",{className:"mt-4 px-5",children:t})]})}),p=d.memo(m);export{p as Modal};
