import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as h,f as $,h as B,r as f}from"./vendor-2ae44a2e.js";import{u as F}from"./react-hook-form-47c010f8.js";import{o as G}from"./yup-5abd4662.js";import{c as q,a as b}from"./yup-c2e87575.js";import{M as D,A as H,G as I,t as S,s as g}from"./index-d0a8f5da.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let r=new D;const ue=({activeId:i,setSidebar:u})=>{var N,E,A,C;const P=q({email:b().email().required(),password:b(),role:b()}).required(),{dispatch:w}=h.useContext(H),{dispatch:m}=h.useContext(I),R=$();B();const[T,U]=f.useState(""),[K,L]=f.useState(0),[y,j]=f.useState(!1),{register:d,handleSubmit:v,setError:c,setValue:x,formState:{errors:p}}=F({resolver:G(P)}),M=["admin","employee"],O=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],k=async s=>{j(!0);try{if(T!==s.email){const t=await r.updateEmailByAdmin(s.email,i);if(!t.error)g(m,"Email Updated",1e3);else if(t.validation){const l=Object.keys(t.validation);for(let a=0;a<l.length;a++){const n=l[a];c(n,{type:"manual",message:t.validation[n]})}}}if(s.password.length>0){const t=await r.updatePasswordByAdmin(s.password,i);if(!t.error)g(m,"Password Updated",2e3);else if(t.validation){const l=Object.keys(t.validation);for(let a=0;a<l.length;a++){const n=l[a];c(n,{type:"manual",message:t.validation[n]})}}}r.setTable("user");const o=await r.callRestAPI({activeId:i,email:s.email,role:s.role,status:s.status},"PUT");if(!o.error)g(m,"Added",4e3),R("/admin/users");else if(o.validation){const t=Object.keys(o.validation);for(let l=0;l<t.length;l++){const a=t[l];c(a,{type:"manual",message:o.validation[a]})}}}catch(o){console.log("Error",o),c("email",{type:"manual",message:o.message}),S(w,o.message)}j(!1)};return h.useEffect(()=>{m({type:"SETPATH",payload:{path:"users"}}),async function(){try{r.setTable("user");const s=await r.callRestAPI({id:i},"GET");s.error||(x("email",s.model.email),x("role",s.model.role),x("status",s.model.status),U(s.model.email),L(s.model.id))}catch(s){console.log("Error",s),S(w,s.message)}}()},[i]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>u(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>u(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await v(k)(),u(!1)},disabled:y,children:y?"Saving":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:v(k),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",...d("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=p.email)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=p.email)==null?void 0:E.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("role"),children:M.map(s=>e.jsx("option",{name:"role",value:s,children:s},s))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("status"),children:O.map(s=>e.jsx("option",{name:"status",value:s.key,children:s.value},s.key))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...d("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(A=p.password)!=null&&A.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=p.password)==null?void 0:C.message})]})]})]})};export{ue as default};
