import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as j,r as i}from"./vendor-2ae44a2e.js";import{A as S,G as T,h as E,j as N,k as F,M as A,s as f,t as C}from"./index-d0a8f5da.js";import"./index-9aa09a5c.js";import{q as l,_ as c}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const k={id:{operations:["eq","lt","le","gt","gte"],type:"number",label:"ID"},notes:{operations:["eq","cs","sw","ew"],type:"string",label:"notes"},assistant_name:{operations:["eq","cs","sw","ew"],type:"string",label:"Assistant Name"}},D=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},editable:!1},{header:"Assistant Name",accessor:"assistant_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},editable:!1,isSearchable:!0},{header:"Notes",accessor:"notes",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},editable:!1},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"inactive",1:"active"},editable:!1}],Y=()=>{const{state:h,dispatch:x}=a.useContext(S),{dispatch:d}=a.useContext(T);j();const[v,m]=a.useState(!1),[w,o]=a.useState(!1),[p,u]=a.useState(),n=i.useRef(null),[I,g]=a.useState([]);a.useEffect(()=>{d({type:"SETPATH",payload:{path:"assistants"}})},[]);const b=(s,r,t=[])=>{switch(s){case"add":m(r);break;case"edit":o(r),g(t),u(t[0]);break}};async function y(s){var r;try{const t=new A;t.setTable("assistants"),await t.callRestAPI({id:s},"DELETE"),(r=n.current)==null||r.click(),f(d,"Delete successful")}catch(t){C(x,t.message),f(d,t.message,5e3,"error")}}return e.jsxs("div",{className:"",children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:e.jsx(E,{columns:D,tableRole:"user",table:"assistants",defaultFilter:[`user_id,eq,${h.user}`],actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:s=>{o(!0),u(s[0])}},delete:{show:!0,action:y,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>b("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:n,filterConfig:k})})}),e.jsx(l,{appear:!0,show:v,as:i.Fragment,children:e.jsxs(c,{as:"div",className:"relative z-[100]",onClose:()=>m(!1),children:[e.jsx(l.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-transparent/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(l.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(c.Panel,{className:" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:e.jsx(N,{closeSidebar:()=>{var s;m(!1),(s=n.current)==null||s.click()}})})})})})]})}),e.jsx(l,{appear:!1,show:w&&!!p,as:i.Fragment,children:e.jsxs(c,{as:"div",className:"relative z-[100]",onClose:()=>o(!1),children:[e.jsx(l.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-transparent/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center",children:e.jsx(l.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-0",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(c.Panel,{className:"h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all",children:e.jsx(F,{activeId:p,closeSidebar:()=>{var s;o(!1),(s=n.current)==null||s.click()}})})})})})]})})]})};export{Y as default};
