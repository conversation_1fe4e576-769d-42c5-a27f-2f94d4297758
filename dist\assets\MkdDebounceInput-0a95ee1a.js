import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as c}from"./vendor-2ae44a2e.js";import{S as v}from"./index-d0a8f5da.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let s=null;const W=({type:o="text",label:d,className:i,placeholder:a="Search",options:x=[],disabled:l=!1,setValue:h,value:p,onReady:g,timer:f=1e3,showIcon:b=!0,labelClassName:j=""})=>{const n=c.useId(),[m,w]=c.useState("");function u(t){const r=t.target.value;h(r),w(r),s&&clearTimeout(s),s=setTimeout(()=>{r.length&&g(r)},f)}return e.jsx(e.Fragment,{children:e.jsxs("form",{children:[e.jsx("label",{className:`block mb-2 text-sm font-bold text-gray-700 cursor-pointer ${j}`,htmlFor:n,children:v(d,{casetype:"capitalize",separator:"space"})}),o==="dropdown"||o==="select"?e.jsxs("select",{type:o,id:n,disabled:l,placeholder:a,onChange:t=>u(t),value:p||m,className:`px-3 py-2 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none ${i}`,children:[e.jsx("option",{}),x.map((t,r)=>e.jsx("option",{value:t,children:t},r+1))]}):e.jsxs("div",{className:"relative",children:[b&&e.jsx("div",{className:"flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none",children:e.jsx("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:e.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),e.jsx("input",{type:o,id:n,disabled:l,placeholder:a,onChange:t=>u(t),value:p||m,className:`block p-4 pl-10 w-full text-sm text-gray-600 bg-gray-100 rounded-lg border border-blue-600 placeholder:font-medium placeholder:text-gray-600 focus:border-blue-500 focus:ring-blue-500 ${i}`})]})]})})};export{W as default};
