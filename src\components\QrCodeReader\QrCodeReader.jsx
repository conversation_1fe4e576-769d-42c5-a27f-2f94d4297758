import React, { useRef } from "react";
import QrScanner from "qr-scanner";

const QrCodeReader = ({ setResult }) => {
  const [scannedQrFile, setScannedQrFile] = React.useState("");
  const fileRef = useRef(null);

  function handleScanFileBtn() {
    fileRef.current.click();
  }

  async function handleChangeScanFileBtn(e) {
    const file = e.target.files[0];
    try {
      const result = await QrScanner.scanImage(file, {
        returnDetailedScanResult: true,
      });
      await setScannedQrFile(result.data);
      console.log(result);
      if (setResult) {
        setResult(result.data);
      }
    } catch (err) {
      setScannedQrFile(err);
      console.log(err);
    }
  }

  return (
    <div>
      <div className="filter-form-holder flex-column mt-10 flex-wrap justify-center">
        <div className="search-buttons pl-2">
          <button
            onClick={() => {
              handleScanFileBtn();
            }}
            className="mr-2 inline-block rounded bg-[#2cc9d5] px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-[#2cc9d5]/70 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg"
          >
            <span>Scan QR Code</span>
            <input
              type="file"
              ref={fileRef}
              onChange={handleChangeScanFileBtn}
              accept=".png, .jpg, .jpeg"
              className="hidden opacity-0"
            />
          </button>
        </div>
        <h4>Scanned Code Result: {scannedQrFile && scannedQrFile}</h4>
      </div>
    </div>
  );
};

export default QrCodeReader;
