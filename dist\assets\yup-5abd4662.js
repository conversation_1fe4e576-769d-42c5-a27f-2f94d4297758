import{n as h,u as v}from"./@hookform/resolvers-d6373084.js";import{a as m}from"./react-hook-form-47c010f8.js";function g(d,s,i){return s===void 0&&(s={}),i===void 0&&(i={}),function(u,l,n){try{return Promise.resolve(function(o,a){try{var r=(s.context,Promise.resolve(d[i.mode==="sync"?"validateSync":"validate"](u,Object.assign({abortEarly:!1},s,{context:l}))).then(function(t){return n.shouldUseNativeValidation&&h({},n),{values:i.raw?u:t,errors:{}}}))}catch(t){return a(t)}return r&&r.then?r.then(void 0,a):r}(0,function(o){if(!o.inner)throw o;return{values:{},errors:v((a=o,r=!n.shouldUseNativeValidation&&n.criteriaMode==="all",(a.inner||[]).reduce(function(t,e){if(t[e.path]||(t[e.path]={message:e.message,type:e.type}),r){var c=t[e.path].types,p=c&&c[e.type];t[e.path]=m(e.path,r,t,e.type,p?[].concat(p,e.message):e.message)}return t},{})),n)};var a,r}))}catch(o){return Promise.reject(o)}}}export{g as o};
