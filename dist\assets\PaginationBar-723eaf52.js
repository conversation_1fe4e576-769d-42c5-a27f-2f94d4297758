import{j as e}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";const m=({currentPage:t,pageCount:n,pageSize:l,canPreviousPage:a,canNextPage:r,updatePageSize:i,previousPage:c,nextPage:d})=>e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex justify-between items-center mt-3 text-black",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsxs("span",{children:["Page"," ",e.jsxs("strong",{children:[+t||1," of ",n]})," "]}),e.jsx("select",{className:"rounded-md py-0.5 !pr-6 text-sm text-black",value:l,onChange:s=>{i(Number(s.target.value))},children:[5,10,20,30,40,50].map(s=>e.jsxs("option",{value:s,children:["Show ",s]},s))})]}),e.jsxs("div",{children:[e.jsx("button",{onClick:c,disabled:!a,className:"w-10 h-10 font-bold",children:"←"})," ",e.jsx("button",{onClick:d,disabled:!r,className:"w-10 h-10 font-bold",children:"→"})," "]})]})});export{m as default};
