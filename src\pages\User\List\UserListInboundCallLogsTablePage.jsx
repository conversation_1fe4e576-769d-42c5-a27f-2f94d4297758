import React, { useRef } from "react";
import MkdSD<PERSON> from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";
import {
  UserAddKnowledgeBankTablePage,
  UserEditKnowledgeBankTablePage,
} from "Routes/LazyLoad";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  // {
  //   header: 'Update At',
  //   accessor: 'update_at',
  //   isSorted: false,
  //   isSortedDesc: false,
  //   mappingExist: false,
  //   mappings: {},
  // },

  {
    header: "campaign",
    accessor: "campaign_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Call Id",
    accessor: "call_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "Outbound", 1: "Inbound" },
  },
  {
    header: "Type",
    accessor: "type",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "Outbound", 1: "Inbound" },
  },

  {
    header: "Recording Link",
    accessor: "recording_link",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Duration (seconds)",
    accessor: "duration",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Number",
    accessor: "number",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "inactive", 1: "active" },
  },
  {
    header: "Action",
    accessor: "",
  },
];

const UserListInboundCallLogsTablePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const refreshRef = useRef(null);

  const [selectedItems, setSelectedItems] = React.useState([]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "inbound_call_logs",
      },
    });
  }, []);

  const onToggleModal = (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        // setSelectedItems(ids);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setSelectedItems(ids);
        setActiveEditId(ids[0]);
        break;
    }
  };
  return (
    <>
      <>
        <div className="overflow-x-auto  rounded bg-white pt-5 shadow md:p-5">
          <MkdListTableV2
            columns={columns}
            tableRole={"user"}
            table={"call_logs"}
            actionId={"id"}
            defaultFilter={["type,eq,2", `user_id,eq,${state.user}`]}
            actions={{
              view: { show: false, action: null, multiple: false },
              edit: {
                show: true,
                multiple: false,
                action: (ids) => onToggleModal("edit", true, ids),
              },
              delete: { show: true, action: null, multiple: false },
              select: { show: false, action: null, multiple: false },
              add: {
                show: false,
                action: () => onToggleModal("add", true),
                multiple: false,
                children: "Add New",
                showChildren: true,
              },
              export: { show: false, action: null, multiple: true },
            }}
            actionPosition={`onTable`}
            refreshRef={refreshRef}
          />
        </div>
      </>

      <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <UserAddKnowledgeBankTablePage setSidebar={setShowAddSidebar} />
      </ModalSidebar>

      {/* <ModalSidebar
        isModalActive={showEditSidebar}
        closeModalFn={() => setShowEditSidebar(false)}
      >
        <UserEditKnowledgeBankTablePage
          activeId={activeEditId}
          setSidebar={setShowEditSidebar}
        />
      </ModalSidebar> */}
    </>
  );
};

export default UserListInboundCallLogsTablePage;
