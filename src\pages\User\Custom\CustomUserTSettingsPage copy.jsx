import { useState, useEffect, useCallback } from "react";
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Calendar, Split, Database } from "lucide-react";
import { Input } from "../ui/input";
import { Switch } from "../ui/switch";
import { Slider } from "../ui/slider";
import { HelpCircle } from "lucide-react";
import { ghl } from "Assets/images";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { X } from "lucide-react";

import MkdSDK from "Utils/MkdSDK";
let sdk = new MkdSDK();
const timezones = [
  { value: "Pacific/Honolulu", label: "Hawaii-Aleutian Standard Time (HST)" },
  { value: "America/Anchorage", label: "Alaska Standard Time (AKST)" },
  { value: "America/Los_Angeles", label: "Pacific Standard Time (PST)" },
  { value: "America/Denver", label: "Mountain Standard Time (MST)" },
  { value: "America/Phoenix", label: "Mountain Standard Time (no DST)" },
  { value: "America/Chicago", label: "Central Standard Time (CST)" },
  { value: "America/Chihuahua", label: "America/Chihuahua" },
  { value: "America/New_York", label: "Eastern Standard Time (EST)" },
  { value: "America/Toronto", label: "Eastern Standard Time (Canada)" },
  { value: "America/Caracas", label: "Venezuela Standard Time (VET)" },
  { value: "America/Bogota", label: "Colombia Time (COT)" },
  { value: "America/Argentina/Buenos_Aires", label: "Argentina Time (ART)" },
  { value: "Atlantic/Bermuda", label: "Atlantic Standard Time (AST)" },
  { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
  { value: "Europe/Paris", label: "Central European Time (CET)" },
  { value: "Europe/Athens", label: "Eastern European Time (EET)" },
  { value: "Africa/Johannesburg", label: "South Africa Standard Time (SAST)" },
  { value: "Asia/Dubai", label: "Gulf Standard Time (GST)" },
  { value: "Asia/Kolkata", label: "Indian Standard Time (IST)" },
  { value: "Asia/Shanghai", label: "China Standard Time (CST)" },
  { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
  {
    value: "Australia/Sydney",
    label: "Australian Eastern Standard Time (AEST)",
  },
  { value: "Pacific/Auckland", label: "New Zealand Standard Time (NZST)" },
];

function CalendarModal({ open, onOpenChange }) {
  const [calendarLoading, setCalendarLoading] = useState(false);
  const connectCalendar = async () => {
    setCalendarLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/calendar/auth",
        {},
        "GET"
      );
      console.log(result);
      if (!result.error) {
        setCalendarLoading(false);
        window.open(result.link);
      }
    } catch (e) {
      console.log(e);
    }
    setCalendarLoading(false);
  };
  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      className="bg-[#1d2937] text-black"
    >
      <DialogContent className="max-w-md rounded-lg bg-[#1d2937] bg-opacity-100 shadow-lg">
        <DialogHeader className="space-y-4">
          <div className="flex justify-between items-center">
            <DialogTitle className="text-xl font-semibold text-white">
              Calendar Bookings
            </DialogTitle>
            {/* <button onClick={() => onOpenChange(false)}>
              <X className="w-5 h-5 text-white" />
            </button> */}
          </div>

          <div className="flex gap-4 mb-6">
            <button className="flex-1 px-4 py-2 rounded-md">
              <img src={ghl} alt="GHL Calendar Logo" className="w-15 h-15" />
            </button>
            <button className="flex-1 px-4 py-2 rounded-md">
              <p className="font-semibold text-blue-500">Cal.com</p>
            </button>
            <button
              className="flex-1 px-4 py-2 rounded-md"
              onClick={connectCalendar}
            >
              <span className="font-normal text-white">Google</span>
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <Input
                placeholder="Username"
                className="text-white bg-transparent border-red-400 placeholder:text-gray-300"
              />
              <p className="mt-1 text-sm text-red-400">calendar id required</p>
            </div>

            <div>
              <Input
                placeholder="API Key"
                className="text-white bg-transparent border-red-400 placeholder:text-gray-300"
              />
              <p className="mt-1 text-sm text-red-400">api key required</p>
            </div>

            <label
              htmlFor="timezone"
              className="block mt-4 text-sm font-medium text-white"
            >
              Timezone
            </label>
            <select
              name="timezone"
              className="block mt-1 w-full text-white bg-transparent rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="" disabled>
                Select your timezone
              </option>
              {timezones.map((tz) => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </select>
            <button
              type="button"
              className="inline-flex justify-center rounded-md border border-transparent bg-[#19b2f6]/80   px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-transparent focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              Connect GHL
            </button>
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export function SettingRow({
  title,
  leftLabel,
  rightLabel,
  value,
  onChange,
  tooltipContent,
}) {
  return (
    <div className="mb-6">
      <div className="flex gap-2 items-center mb-2">
        <span className="text-lg text-white">{title}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-white">{leftLabel}</span>
        <Switch
          checked={value}
          onCheckedChange={onChange}
          className="bg-gray-200 data-[state=checked]:bg-blue-500"
        />
        <span className="text-white">{rightLabel}</span>
      </div>
    </div>
  );
}

export function SliderSetting({
  title,
  min,
  max,
  step,
  value,
  onChange,
  tooltipContent,
  leftLabel,
  rightLabel,
}) {
  return (
    <div className="mb-6">
      <div className="flex gap-2 items-center mb-2">
        <span className="text-lg text-white">{title}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-blue-500">{leftLabel}</span>
        <Slider
          value={[value]}
          max={max}
          min={min}
          step={step}
          className="mx-4 w-[60%]"
          onValueChange={([val]) => onChange(val)}
        />
        <span className="text-blue-500">{rightLabel}</span>
      </div>
    </div>
  );
}

export function TransferSetting({ value, onChange }) {
  return (
    <div className="mb-6">
      <div className="flex gap-2 items-center mb-2">
        <span className="text-lg text-white">
          {" "}
          Enter your Elevenlabs Key here
        </span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Elevenlabs Key"
        className="w-full text-white bg-transparent"
      />
      <div className="flex gap-2 items-center mt-2 mb-2">
        <span className="text-lg text-white">
          {" "}
          Enter your Deepgram Key here
        </span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Deepgram Key"
        className="w-full text-white bg-transparent"
      />
      <div className="flex gap-2 items-center mt-2 mb-2">
        <span className="text-lg text-white">
          {" "}
          Enter your Anthropic Key here
        </span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Anthropic Key"
        className="w-full text-white bg-transparent"
      />
      <div className="flex gap-2 items-center mt-2 mb-2">
        <span className="text-lg text-white"> Enter your Telnyx Key here</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Telnyx Key"
        className="w-full text-white bg-transparent"
      />
      <div className="flex gap-2 items-center mt-2 mb-2">
        <span className="text-lg text-white">Transfer Number</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Enter phone number to transfer call to"
        className="w-full text-white bg-transparent"
      />
    </div>
  );
}
export function SettingSection({ icon, title, isActive, onClick }) {
  return (
    <div
      onClick={onClick}
      className={`flex cursor-pointer items-center justify-center space-x-3 px-4 py-2 ${
        isActive
          ? "font-medium text-center text-white"
          : "text-center text-white hover:text-white"
      }`}
    >
      {/* Icon */}
      <div
        className={`flex-shrink-0 ${isActive ? "text-white" : "text-white"}`}
      >
        {React.cloneElement(icon, { className: "w-6 h-6" })}{" "}
        {/* Adjusted size */}
      </div>
      {/* Title */}
      <span className="text-sm text-center">{title}</span>
    </div>
  );
}

import debounce from "lodash.debounce";
import { GlobalContext, showToast } from "Context/Global";
import { SkeletonLoader } from "Components/Skeleton";

export function SettingsModal({ open, onOpenChange }) {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [activeSection, setActiveSection] = useState("advanced");
  const [calendarModalOpen, setCalendarModalOpen] = useState(false);
  const [fetchSettings, setFetchSettings] = useState(false);
  const [settings, setSettings] = useState({
    interruptSensitivity: false,
    responseSpeed: false,
    doubleCall: false,
    vmDetection: false,
    initialDelay: 0,
    aiCreativity: 0.5,
    transferNumber: "",
    callerId: "",
  });

  React.useEffect(() => {
    async function fetchSettings() {
      try {
        setFetchSettings(true);
        const result = await sdk.callRawAPI(
          "/v3/api/custom/voiceoutreach/user/settings",
          {},
          "GET"
        );
        console.log(result);
        const storedSettings = JSON.parse(result.model.settings);
        console.log(storedSettings);
        if (storedSettings) {
          setSettings({
            ...storedSettings,
          });
        }
      } catch (error) {
        console.log("Error", error);
      }
      setFetchSettings(false);
    }

    fetchSettings();
  }, []);

  // Debounced function to update settings
  const updateSettingsAPI = useCallback(
    debounce(async (newSettings) => {
      const storedSettings = JSON.stringify(newSettings);
      await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/settings",
        { settings: storedSettings },
        "POST"
      );
    }, 500), // Wait for 500ms of inactivity
    []
  );

  // Update settings and debounce API call
  const handleSettingChange = (newSettings) => {
    setSettings(newSettings);
    updateSettingsAPI(newSettings); // Trigger debounced API call
  };

  const handleSaveSettings = async () => {
    const storedSettings = JSON.stringify(settings);
    await sdk.callRawAPI(
      "/v3/api/custom/voiceoutreach/user/settings",
      { settings: storedSettings },
      "POST"
    );
    // alert("Settings saved successfully!");
    showToast(globalDispatch, "Settings saved successfully!", 4000);
  };

  const handleSectionClick = (section) => {
    setActiveSection(section);
    if (section === "calendar") {
      setCalendarModalOpen(true);
    }
  };

  const tooltips = {
    interruptSensitivity:
      "Controls how easily the AI can be interrupted during conversations",
    responseSpeed: "Adjusts how quickly the AI responds to user input",
    initialDelay: "Sets the delay before the first AI response",
    doubleCall: "Enables double-call verification feature",
    vmDetection: "Activates voicemail detection capabilities",
    aiCreativity: "Controls the creativity level of AI responses",
    callerId: "Set the caller ID name for outbound calls",
    transferNumber: "Phone number to transfer calls to",
  };

  return (
    <>
      {fetchSettings ? (
        <SkeletonLoader />
      ) : (
        <>
          <div open={true} className="bg-[#1d2937] text-white">
            <div className="rounded-lg bg-[#1d2937] bg-opacity-100 p-12 shadow-lg">
              <div className="grid grid-cols-3 gap-8 mb-8">
                <SettingSection
                  icon={<Calendar className="w-8 h-8 text-blue-500" />}
                  title="Calendar Booking"
                  isActive={activeSection === "calendar"}
                  onClick={() => handleSectionClick("calendar")}
                />
                <SettingSection
                  icon={<Split className="w-8 h-8 text-blue-500" />}
                  title="Services"
                  isActive={activeSection === "transfer"}
                  onClick={() => handleSectionClick("transfer")}
                />
                <SettingSection
                  icon={<Database className="w-8 h-8 text-blue-500" />}
                  title="Advanced Options"
                  isActive={activeSection === "advanced"}
                  onClick={() => handleSectionClick("advanced")}
                />
              </div>

              {activeSection === "advanced" && (
                <div className="grid grid-cols-3 gap-12">
                  {/* Settings Rows */}
                  <div className="space-y-8">
                    <SettingRow
                      title="Interrupt Sensitivity"
                      leftLabel="Low"
                      rightLabel="High"
                      value={settings.interruptSensitivity}
                      onChange={(value) =>
                        handleSettingChange({
                          ...settings,
                          interruptSensitivity: value,
                        })
                      }
                      tooltipContent={tooltips.interruptSensitivity}
                    />
                    <SettingRow
                      title="Double Call"
                      leftLabel="False"
                      rightLabel="True"
                      value={settings.doubleCall}
                      onChange={(value) =>
                        handleSettingChange({ ...settings, doubleCall: value })
                      }
                      tooltipContent={tooltips.doubleCall}
                    />
                  </div>
                  <div className="space-y-8">
                    <SettingRow
                      title="Response Speed"
                      leftLabel="Auto"
                      rightLabel="Sensitive"
                      value={settings.responseSpeed}
                      onChange={(value) =>
                        handleSettingChange({
                          ...settings,
                          responseSpeed: value,
                        })
                      }
                      tooltipContent={tooltips.responseSpeed}
                    />
                    <SettingRow
                      title="VM Detection (Beta)"
                      leftLabel="Off"
                      rightLabel="On"
                      value={settings.vmDetection}
                      onChange={(value) =>
                        handleSettingChange({ ...settings, vmDetection: value })
                      }
                      tooltipContent={tooltips.vmDetection}
                    />
                  </div>
                  <div className="space-y-8">
                    <SliderSetting
                      title="Initial Message Delay"
                      min={0}
                      max={5}
                      step={0.1}
                      value={settings.initialDelay}
                      onChange={(value) =>
                        handleSettingChange({
                          ...settings,
                          initialDelay: value,
                        })
                      }
                      tooltipContent={tooltips.initialDelay}
                      leftLabel="0 (sec)"
                      rightLabel="5 (sec)"
                    />
                    <SliderSetting
                      title="AI Creativity"
                      min={0}
                      max={1}
                      step={0.01}
                      value={settings.aiCreativity}
                      onChange={(value) =>
                        handleSettingChange({
                          ...settings,
                          aiCreativity: value,
                        })
                      }
                      tooltipContent={tooltips.aiCreativity}
                      leftLabel="0"
                      rightLabel="1"
                    />
                    <div className="space-y-2">
                      <span className="text-sm text-white">Caller ID Name</span>
                      <Input
                        placeholder="Caller ID Name"
                        value={settings.callerId}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            callerId: e.target.value,
                          })
                        }
                        className="w-full text-white bg-transparent"
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end mt-8">
                <button
                  className="rounded-lg bg-[#19b2f6]/80  px-6 py-2 text-white hover:bg-[#2cc9d5]/70"
                  onClick={handleSaveSettings}
                >
                  Save Settings
                </button>
              </div>
            </div>
          </div>

          <CalendarModal
            open={calendarModalOpen}
            onOpenChange={setCalendarModalOpen}
          />
        </>
      )}
    </>
  );
}

export default SettingsModal;
