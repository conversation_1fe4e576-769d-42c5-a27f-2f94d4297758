import React, { Fragment } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Popover, Transition, Dialog } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { RiDeleteBin5Line } from "react-icons/ri";
import TreeSDK from "Utils/TreeSDK";
import { MkdListTableHead } from "Components/MkdListTable";
import { useForm } from "react-hook-form";
import PaginationBarV2 from "Components/PaginationBar/PaginationBarV2";
import TableSkeleton from "Components/MkdListTable/TableSkeleton";
import { useCSVDownloader } from "react-papaparse";
import { ClipboardCheckIcon, ClipboardIcon, XIcon } from "lucide-react";
import { XMarkIcon } from "@heroicons/react/24/outline";

const STATUS_MAPPING = {
  0: "Inactive",
  1: "Active",
  2: "Paused",
};

const REVERSE_STATUS_MAPPING = {
  inactive: 0,
  active: 1,
  paused: 2,
  INACTIVE: 0,
  ACTIVE: 1,
  PAUSED: 2,
  Inactive: 0,
  Active: 1,
  Paused: 2,
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Campaign ID",
    accessor: "campaign_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Call ID",
    accessor: "call_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Recording Link",
    accessor: "recording_link",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Duration (sec)",
    accessor: "duration",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: STATUS_MAPPING,
  },
  {
    header: "Credit Used",
    accessor: "cost",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
];

const filterableColumns = columns.filter((col) => {
  const excludedColumns = ["", "recording_link", "duration"];
  return !excludedColumns.includes(col.accessor);
});

const CustomUserListInboundCallLogsPage = () => {
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [selectedOptions, setSelectedOptions] = React.useState(
    searchParams.get("campaign_id") ? ["campaign_id"] : []
  );
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const { CSVDownloader } = useCSVDownloader();

  const [paginationData, setPaginationData] = React.useState({
    currentPage: 0,
    pageSize: 0,
    totalNumber: 0,
    totalPages: 0,
  });

  const { handleSubmit } = useForm({ defaultValues: {} });

  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");

  const [copiedLinkId, setCopiedLinkId] = React.useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState(null);

  const tdk = new TreeSDK();

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "inbound_call_logs",
      },
    });

    getData();
  }, [filterConditions, searchValue]);

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    let value = inputValue;
    let operator = selectedValue || "eq";

    if (option === "status") {
      const statusKey = inputValue.toLowerCase();
      if (REVERSE_STATUS_MAPPING.hasOwnProperty(statusKey)) {
        value = REVERSE_STATUS_MAPPING[statusKey];
      }
    }

    const condition = `${option},${operator},${value}`;

    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  async function getData() {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const tablePrefix = `${tdk.getProjectId()}_call_logs`;

      let filterArray = [
        "type,eq,2",
        searchParams.get("campaign_id")
          ? `${tablePrefix}.campaign_id,eq,${searchParams.get("campaign_id")}`
          : undefined,
      ].filter(Boolean);

      if (searchValue) {
        filterArray.push(`${tablePrefix}.call_id,cs,${searchValue}`);
      }

      if (filterConditions.length > 0) {
        const updatedFilters = filterConditions.map((condition) => {
          const [field, operator, value] = condition.split(",");
          return `${tablePrefix}.${field},${operator},${value}`;
        });
        filterArray = [...filterArray, ...updatedFilters];
      }

      console.log("filterArray", filterArray);

      const result = await tdk.getPaginate("call_logs", {
        size: searchParams.get("limit") ?? 50,
        page: searchParams.get("page") ?? 1,
        filter: filterArray.filter(Boolean),
      });

      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPaginationData({
        currentPage: page,
        pageSize: limit,
        totalNumber: total,
        totalPages: num_pages,
      });
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    for (const key of searchParams.keys()) {
      if (key !== "campaign_id") {
        searchParams.delete(key);
      }
    }
    setSearchParams(searchParams);
    getData();
  };

  const handleCopyLink = (link, id) => {
    navigator.clipboard.writeText(link);
    setCopiedLinkId(id);
    setTimeout(() => setCopiedLinkId(null), 2000);
  };

  const ViewModal = ({ isOpen, onClose, row }) => {
    if (!row) return null;

    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-[102]" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="overflow-y-auto fixed inset-0">
            <div className="flex justify-center items-center p-4 min-h-full">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex justify-between items-center mb-4">
                    <Dialog.Title className="text-xl font-medium text-white">
                      Call Details
                    </Dialog.Title>
                    <button onClick={onClose}>
                      <XMarkIcon className="w-6 h-6 text-white/70 hover:text-white" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-white/70">Call ID</p>
                        <p className="text-white">{row.call_id ?? "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Campaign ID</p>
                        <p className="text-white">{row.campaign_id ?? "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Duration</p>
                        <p className="text-white">{row.duration ?? "N/A"}</p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Status</p>
                        <p className="text-white">
                          {STATUS_MAPPING[row.status] ?? "N/A"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-white/70">Cost</p>
                        <p className="text-white">
                          {row.cost ? `$${row.cost?.toFixed(2)}` : "N/A"}
                        </p>
                      </div>
                    </div>

                    {row.recording_link ? (
                      <div className="mt-6">
                        <p className="mb-2 text-sm text-white/70">
                          Recording Link
                        </p>
                        <div className="flex items-center gap-2 rounded bg-[#2d3947] p-3">
                          <a
                            className="flex-1 text-white truncate hover:underline"
                            href={row.recording_link}
                            target="_blank"
                          >
                            {row.recording_link ?? "N/A"}
                          </a>
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              handleCopyLink(row.recording_link, row.id);
                            }}
                            className="flex items-center rounded bg-[#1d2937] p-1.5 transition-colors hover:bg-[#19b2f6]/20"
                            title="Copy link"
                          >
                            {copiedLinkId === row.id ? (
                              <ClipboardCheckIcon className="w-4 h-4 text-green-400" />
                            ) : (
                              <ClipboardIcon className="w-4 h-4 text-white/70 hover:text-white" />
                            )}
                          </button>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  };

  return (
    <>
      <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
        <h3 className="px-8 my-4 text-2xl font-bold text-white">
          Inbound Call Logs
        </h3>
        <div className="bg-[#1d2937] px-8 py-4">
          <div className="flex gap-3 justify-between items-center h-fit">
            <div className="flex w-[200px] min-w-[200px] items-center justify-between">
              <div className="relative z-10 rounded bg-[#1d2937]">
                <Popover>
                  <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                    <Popover.Button className="flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white ">
                      <BiFilterAlt />
                      <span>Filters</span>
                      {selectedOptions.length > 0 && (
                        <span className="flex justify-center items-center w-6 h-6 text-white bg-gray-800 rounded-full text-start">
                          {selectedOptions.length}
                        </span>
                      )}
                    </Popover.Button>
                    <div className="flex gap-3 justify-between items-center px-2 py-1 text-white bg-transparent rounded-md border cursor-pointer border-white/50 focus-within:border-gray-400">
                      <BiSearch className="text-xl text-white" />
                      <input
                        type="text"
                        placeholder="search by call id"
                        className="p-0 text-white bg-transparent border-none placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                        style={{ boxShadow: "0 0 transparent" }}
                        value={searchValue}
                        onChange={(e) => {
                          setSearchValue(e.target.value);
                          addFilterCondition("call_id", "cs", e.target.value);
                        }}
                      />
                      {searchValue && (
                        <AiOutlineClose
                          className="text-lg text-white cursor-pointer"
                          onClick={() => {
                            setSearchValue("");
                            addFilterCondition("call_id", "cs", "");
                          }}
                        />
                      )}
                    </div>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <Popover.Panel>
                      <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                        <span className="absolute top-2 left-5 font-medium text-white">
                          Filters
                        </span>
                        <Popover.Button
                          onClick={() => {
                            console.log("clicked");
                            setSelectedOptions([]);
                            setFilterConditions([]);
                            setFilterValues({});
                          }}
                        >
                          <XIcon className="absolute top-2 right-2 text-white cursor-pointer" />
                        </Popover.Button>
                        {selectedOptions?.map((option, index) => (
                          <div
                            key={index}
                            className="flex gap-3 justify-between items-center mb-2 w-full text-gray-600"
                          >
                            <button
                              type="button"
                              className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                              title={option}
                              style={{ WebkitTouchCallout: "none" }}
                            >
                              {filterableColumns.find(
                                (col) => col.accessor === option
                              )?.header || option}
                            </button>

                            <select
                              className="w-1/3 text-gray-600 capitalize bg-gray-100 rounded-md border-none appearance-none outline-0"
                              value={optionValue}
                              onChange={(e) => {
                                setOptionValue(e.target.value);
                                addFilterCondition(
                                  option,
                                  e.target.value,
                                  filterValues[option]
                                );
                              }}
                            >
                              <option value="eq">equals</option>
                              <option value="cs">contains</option>
                              <option value="sw">start with</option>
                              <option value="ew">ends with</option>
                              <option value="lt">lower than</option>
                              <option value="le">lower or equal</option>
                              <option value="ge">greater or equal</option>
                              <option value="gt">greater than</option>
                              <option value="bt">between</option>
                              <option value="in">in</option>
                              <option value="is">is null</option>
                            </select>

                            {option === "status" ? (
                              <select
                                value={filterValues[option] || ""}
                                onChange={(e) => {
                                  setFilterValues((prev) => ({
                                    ...prev,
                                    [option]: e.target.value,
                                  }));
                                  addFilterCondition(
                                    option,
                                    optionValue,
                                    e.target.value
                                  );
                                }}
                                className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                              >
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                              </select>
                            ) : (
                              <MkdDebounceInput
                                type="text"
                                labelClassName="!mb-0"
                                placeholder="Enter value"
                                setValue={() => {}}
                                showIcon={false}
                                className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                onReady={(value) =>
                                  addFilterCondition(option, optionValue, value)
                                }
                              />
                            )}

                            <RiDeleteBin5Line
                              className="text-2xl text-red-600 cursor-pointer"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) =>
                                  prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  )
                                );
                                setFilterValues((prev) => {
                                  const newValues = { ...prev };
                                  delete newValues[option];
                                  return newValues;
                                });
                              }}
                            />
                          </div>
                        ))}

                        <div className="flex relative justify-between items-center font-semibold search-buttons">
                          <div
                            className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                            onClick={() =>
                              setShowFilterOptions(!showFilterOptions)
                            }
                          >
                            <AiOutlinePlus />
                            Add filter
                          </div>

                          {showFilterOptions && (
                            <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                              <ul className="flex flex-col gap-2 text-gray-500">
                                {filterableColumns.map((column) => (
                                  <li
                                    key={column.accessor}
                                    className={`${
                                      selectedOptions.includes(column.accessor)
                                        ? "cursor-not-allowed text-gray-100"
                                        : "cursor-pointer text-gray-400 hover:text-white"
                                    }`}
                                    onClick={() => {
                                      if (
                                        !selectedOptions.includes(
                                          column.accessor
                                        )
                                      ) {
                                        setSelectedOptions((prev) => [
                                          ...prev,
                                          column.accessor,
                                        ]);
                                        setFilterValues((prev) => ({
                                          ...prev,
                                          [column.accessor]: "",
                                        }));
                                      }
                                      setShowFilterOptions(false);
                                    }}
                                  >
                                    {column.header}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {selectedOptions.length > 0 && (
                            <div
                              onClick={handleClearAllFilters}
                              className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                            >
                              Clear all filter
                            </div>
                          )}
                        </div>
                      </div>
                    </Popover.Panel>
                  </Transition>
                </Popover>
              </div>
            </div>

            <CSVDownloader
              filename={"inbound_call_logs"}
              className="relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]"
              data={() => currentTableData}
            >
              Download CSV
            </CSVDownloader>
          </div>

          <div className="mt-4 overflow-x-auto  bg-[#1d2937]">
            <div
              className={
                loading ? "":"overflow-x-auto border-b border-gray-200 shadow"
              }
            >
              {loading && currentTableData.length === 0 ? (
                <TableSkeleton columns={columns} />
              ) : (
                <table className="min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]">
                  <thead className="bg-[#1d2937]">
                    <MkdListTableHead
                      actionPosition={"onTable"}
                      onSort={() => {}}
                      columns={columns}
                      actions={{ view: { show: true } }}
                    />
                  </thead>
                  <tbody className="divide-y divide-gray-400 bg-[#1d2937]">
                    {currentTableData.map((row) => (
                      <tr key={row.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex gap-3 items-center text-sm">
                            <button
                              onClick={() => {
                                setSelectedRow(row);
                                setIsViewModalOpen(true);
                              }}
                              className="rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30"
                            >
                              View
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {row.id}
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {row.campaign_id ?? "N/A"}
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {row.call_id ?? "N/A"}
                        </td>
                        <td className="px-6 py-4 text-white">
                          {row.recording_link ? (
                            <div className="flex gap-2 items-center">
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleCopyLink(row.recording_link, row.id);
                                }}
                                className="flex items-center rounded bg-[#2d3947] p-1.5 transition-colors hover:bg-[#19b2f6]/20"
                                title="Copy link"
                              >
                                {copiedLinkId === row.id ? (
                                  <ClipboardCheckIcon className="w-4 h-4 text-green-400" />
                                ) : (
                                  <ClipboardIcon className="w-4 h-4 text-white/70 hover:text-white" />
                                )}
                              </button>
                              <p className="flex gap-2 items-center">
                                <a
                                  className="text-white hover:underline"
                                  href={row.recording_link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  {row.recording_link
                                    ? row.recording_link.length > 50
                                      ? `${row.recording_link.substring(
                                          0,
                                          50
                                        )}...`
                                      : row.recording_link
                                    : "N/A"}
                                </a>
                              </p>
                            </div>
                          ) : (
                            "N/A"
                          )}
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {row.duration ?? "N/A"}
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {STATUS_MAPPING[row.status] ?? "N/A"}
                        </td>
                        <td className="px-6 py-4 text-white whitespace-nowrap">
                          {row.cost?.toFixed(2) ?? "N/A"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <PaginationBarV2 paginationData={paginationData} />
        </div>
      </div>

      <ViewModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        row={selectedRow}
      />
    </>
  );
};

export default CustomUserListInboundCallLogsPage;
