import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as l,f as C}from"./vendor-2ae44a2e.js";import{u as A}from"./react-hook-form-47c010f8.js";import{o as T}from"./yup-5abd4662.js";import{c as q,a as o}from"./yup-c2e87575.js";import{G as v,M as F,s as $,t as L}from"./index-d0a8f5da.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const se=()=>{var u,h,b,g,f,j;const[n,d]=l.useState(!1),k=q({slug:o().required(),subject:o().required(),html:o().required(),tag:o().required()}).required(),{dispatch:E}=l.useContext(v),{dispatch:m}=l.useContext(v),S=C(),{register:a,handleSubmit:c,setError:p,formState:{errors:s}}=A({resolver:T(k)}),x=async r=>{let y=new F;d(!0);try{y.setTable("email");const t=await y.callRestAPI({slug:r.slug,subject:r.subject,html:r.html,tag:r.tag},"POST");if(!t.error)S("/admin/email"),$(m,"Added");else if(t.validation){const w=Object.keys(t.validation);for(let i=0;i<w.length;i++){const N=w[i];p(N,{type:"manual",message:t.validation[N]})}}}catch(t){console.log("Error",t),p("subject",{type:"manual",message:t.message}),L(E,t.message)}d(!1)};return l.useEffect(()=>{m({type:"SETPATH",payload:{path:"email"}})},[]),e.jsxs("div",{className:"mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>setSidebar(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Email"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>setSidebar(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await c(x)(),setSidebar(!1)},disabled:n,children:n?"Saving":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:c(x),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",...a("slug"),className:`focus:shadow-outline } w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow
focus:outline-none`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"subject",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...a("subject"),className:`focus: shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(u=s.subject)!=null&&u.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=s.subject)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...a("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=s.tag)!=null&&b.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=s.tag)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(f=s.html)!=null&&f.message?"border-red-500":""}`,...a("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=s.html)==null?void 0:j.message})]})]})]})};export{se as default};
