import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as m,r as f}from"./vendor-2ae44a2e.js";import{M as u,A as h,G as S,d as x,U as g}from"./index-d0a8f5da.js";import{M as w}from"./index-9aa09a5c.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";new u;const b=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Transcript",accessor:"transcript",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"text",1:"audio",2:"video"}},{header:"Filename",accessor:"filename",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"inactive",1:"active"}},{header:"Action",accessor:""}],q=()=>{s.useContext(h),s.useContext(S),m();const[l,t]=s.useState(!1),[E,d]=s.useState(!1),[v,n]=s.useState(),c=f.useRef(null),[A,p]=s.useState([]),i=(a,o,r=[])=>{switch(a){case"add":t(o);break;case"edit":d(o),p(r),n(r[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white pt-5 shadow md:p-5",children:e.jsx(x,{columns:b,tableRole:"user",table:"knowledge_bank",actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:a=>i("edit",!0,a)},delete:{show:!0,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>i("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:c})})}),e.jsx(w,{isModalActive:l,closeModalFn:()=>t(!1),children:e.jsx(g,{setSidebar:t})})]})};export{q as default};
