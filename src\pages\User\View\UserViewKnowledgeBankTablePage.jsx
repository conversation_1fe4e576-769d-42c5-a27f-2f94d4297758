import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import MkdSDK from 'Utils/MkdSDK';
import { useNavigate, useParams } from 'react-router-dom';
import { tokenExpireError } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';
import { isImage, empty, isVideo } from 'Utils/utils';
import { MkdInput } from 'Components/MkdInput';
import { SkeletonLoader } from 'Components/Skeleton';

let sdk = new MkdSDK();

const ViewKnowledgeBankPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const { dispatch } = React.useContext(GlobalContext);
  const [viewModel, setViewModel] = React.useState({});
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'knowledge_bank',
      },
    });
  }, []);

  const params = useParams();

  React.useEffect(function () {
    (async function () {
      try {
        setLoading(true);
        sdk.setTable('knowledge_bank');
        const result = await sdk.callRestAPI(
          { id: Number(params?.id), join: '' },
          'GET',
        );
        if (!result.error) {
          setViewModel(result.model);
          setLoading(false);
        }
      } catch (error) {
        setLoading(false);

        console.log('error', error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);
  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      {loading ? (
        <SkeletonLoader />
      ) : (
        <>
          <h4 className="text-2xl font-medium">View Knowledge Bank</h4>

          <div className="mb-4 mt-4">
            <div className="mb-4 flex">
              <div className="flex-1">Transcript</div>
              <div className="flex-1">{viewModel?.transcript}</div>
            </div>
          </div>

          <div className="mb-4 mt-4">
            <div className="mb-4 flex">
              <div className="flex-1">Type</div>
              <div className="flex-1">{viewModel?.type}</div>
            </div>
          </div>

          <div className="mb-4 mt-4">
            <div className="mb-4 flex">
              <div className="flex-1">Filename</div>
              <div className="flex-1">{viewModel?.filename}</div>
            </div>
          </div>

          <div className="mb-4 mt-4">
            <div className="mb-4 flex">
              <div className="flex-1">Status</div>
              <div className="flex-1">{viewModel?.status}</div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ViewKnowledgeBankPage;
