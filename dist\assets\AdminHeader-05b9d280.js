import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{f,R as o,r as l,L as c,l as g}from"./vendor-2ae44a2e.js";import{c as m,d as h,e as j,f as N,g as x,h as b,i as v,j as A,k as d,l as y}from"./index.esm-4b383179.js";import{M as w,G as P,A as C,t as E}from"./index-d0a8f5da.js";import"./react-icons-f29df01f.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let D=new w;const k=[{to:"/admin/customers",text:"Customers",icon:e.jsx(x,{className:"text-xl text-[#A8A8A8]"}),value:"customers"},{to:"/admin/admins",text:"Admins",icon:e.jsx(x,{className:"text-xl text-[#A8A8A8]"}),value:"admins"},{to:"/admin/forwarding-numbers",text:"Forwarding Numbers",icon:e.jsx(b,{className:"text-xl text-[#A8A8A8]"}),value:"forwarding"},{to:"/admin/reports",text:"Bug Reports",icon:e.jsx(v,{className:"text-xl text-[#A8A8A8]"}),value:"bugs"},{to:"/admin/invoices",text:"Invoices",icon:e.jsx(A,{className:"text-xl text-[#A8A8A8]"}),value:"invoices"},{to:"/admin/stripe_product",text:"Stripe Products",icon:e.jsx(d,{className:"text-xl text-[#A8A8A8]"}),value:"products"},{to:"/admin/stripe_price",text:"Stripe Price",icon:e.jsx(d,{className:"text-xl text-[#A8A8A8]"}),value:"prices"}],L=[{to:"/admin/profile",text:"Profile",icon:e.jsx(m,{className:"text-xl"})},{to:"/admin/settings",text:"Settings",icon:e.jsx(y,{className:"text-xl"})}],X=()=>{const p=f();o.useContext(P);const{state:S,dispatch:r}=o.useContext(C),[i,a]=o.useState(!1),n=l.useRef(null),u=async()=>{r({type:"LOGOUT"}),p("/admin/login")};return l.useEffect(()=>{const t=s=>{n.current&&!n.current.contains(s.target)&&a(!1)};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[]),o.useEffect(()=>{async function t(){try{const s=await D.getProfile();r({type:"UPDATE_PROFILE",payload:s})}catch(s){console.log("Error",s),E(r,s.response.data.message?s.response.data.message:s.message)}}t()},[]),e.jsx("nav",{className:"fixed top-0 z-50  w-full border-b border-[#E0E0E0] bg-white px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx(c,{to:"/",className:"text-lg font-bold text-[#393939]",children:"voiceoutreach"})}),e.jsx("div",{className:"flex-1 px-8",children:e.jsx("ul",{className:"flex items-center justify-center space-x-3",children:k.map(t=>e.jsx("li",{children:e.jsx(g,{to:t.to,className:({isActive:s})=>`flex items-center space-x-2 border-b-2   px-3 py-2 transition-colors duration-150 ease-out ${s?" border-b-[#2CC9D5] text-black":"hover: border-b-transparent text-[#353535] hover:border-b-[#2CC9D5]"}`,children:e.jsx("span",{className:"text-sm",children:t.text})})},t.value))})}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("div",{className:"relative",ref:n,children:[e.jsxs("button",{onClick:()=>a(!i),className:"flex items-center space-x-2 rounded-lg px-3 py-2 text-gray-600 hover:bg-gray-100",children:[e.jsx(m,{className:"text-xl"}),e.jsx("span",{children:"Automateintel Admin"}),i?e.jsx(h,{className:"ml-1 text-lg"}):e.jsx(j,{className:"ml-1 text-lg"})]}),i&&e.jsxs("div",{className:"absolute right-0 mt-2 w-48 rounded-lg border border-gray-200 bg-white py-2 shadow-lg",children:[L.map(t=>e.jsxs(c,{to:t.to,className:"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100",onClick:()=>a(!1),children:[t.icon,e.jsx("span",{children:t.text})]},t.text)),e.jsx("div",{className:"my-2 border-t border-gray-200"}),e.jsxs("button",{onClick:()=>{a(!1),u()},className:"flex w-full items-center space-x-2 px-4 py-2 text-red-600 hover:bg-gray-100",children:[e.jsx(N,{className:"text-xl"}),e.jsx("span",{children:"Logout"})]})]})]})})]})})};export{X as AdminHeader,X as default};
