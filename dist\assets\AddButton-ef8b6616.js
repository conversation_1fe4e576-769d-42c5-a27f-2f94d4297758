import{j as d}from"./@react-google-maps/api-c55ecefa.js";import{r as l}from"./vendor-2ae44a2e.js";const m="_button_1xtn3_3",u={button:m},b=({onClick:t,children:n="Add New",showPlus:o=!0,className:s,showChildren:r=!0})=>{const[a,e]=l.useState(!1),i=()=>{t&&t(),e(!0)};return d.jsxs("button",{onAnimationEnd:()=>e(!1),onClick:i,className:`${a&&"animate-wiggle"} ${u.button} black relative flex h-[2.125rem] w-fit  min-w-fit items-center justify-center overflow-hidden rounded-md border bg-[#19b2f6]/80  px-[.9125rem] py-[.8625rem]  text-base font-medium leading-none text-white shadow-md shadow-black/20 hover:bg-[#19b2f6]/60  ${s}`,children:[o?"+":null," ",r?n:null]})};export{b as default};
