import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import { AddButton } from "Components/AddButton";
import { SkeletonLoader } from "Components/Skeleton";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { ModalSidebar } from "Components/ModalSidebar";
import { AddAdminUserPage, EditAdminUserPage } from "Src/routes/LazyLoad";
import {
  AddAdminStripePricePage,
  EditAdminStripePricePage,
} from "Src/routes/LazyLoad";
import { XIcon } from "lucide-react";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Customer",
    accessor: "userEmail",
  },
  {
    header: "Plan",
    accessor: "planName",
  },
  {
    header: "Starts",
    accessor: "currentPeriodStart",
    type: "timestamp",
  },
  {
    header: "Ends",
    accessor: "currentPeriodEnd",
    type: "timestamp",
  },
  {
    header: "type",
    accessor: "planType",
    mapping: {
      recurring: "Recurring",
      life_time: "Lifetime",
    },
  },
  {
    header: "Usage Type",
    accessor: "isMetered",
    mapping: {
      0: "Upfront",
      1: "Metered",
    },
  },
  {
    header: "Price",
    accessor: "planAmount",
    type: "currency",
  },
  {
    header: "Has Trial",
    accessor: "trialDays",
  },
  {
    header: "Status",
    accessor: "status",
  },
  {
    header: "Action",
    accessor: "",
  },
];
const AdminStripeSubscriptionsListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [query, setQuery] = React.useState("");
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [optionValue, setOptionValue] = React.useState("eq");
  const [isLoading, setIsLoading] = React.useState(false);
  const navigate = useNavigate();

  const schema = yup.object({
    customer_email: yup.string(),
    plan_name: yup.string(),
    sub_status: yup.string(),
    plan_type: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectStatus = [
    { key: "", value: "All" },
    { key: "active", value: "Active" },
    { key: "trialing", value: "Trialing" },
    { key: "canceled", value: "Canceled" },
  ];

  const typeStatus = [
    { key: "", value: "All" },
    { key: "recurring", value: "Recurring" },
    { key: "lifetime", value: "Lifetime" },
  ];

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
        ? `"${inputValue}"`
        : inputValue;
    const condition = `${option},${selectedValue},${input}`;
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
    setSearchValue(inputValue);
  };

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(1, limit);
    })();
  }
  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 1 ? currentPage - 1 : 1, pageSize);
    })();
  }

  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 1,
        pageSize
      );
    })();
  }

  async function getData(pageNum, limitNum, filterParams) {
    setIsLoading(true);

    try {
      const result = await sdk.getStripeSubscriptions(
        { page: pageNum, limit: limitNum },
        `filter=${filterParams.toString()}`
      );
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(+limit);
      setPageCount(+num_pages);
      setPage(+page);
      setDataTotal(+total);
      setCanPreviousPage(+page > 1);
      setCanNextPage(+page + 1 <= +num_pages);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
    setIsLoading(false);
  }

  const onSubmit = (data) => {
    const customer_email = getNonNullValue(data.customer_email);
    const plan_name = getNonNullValue(data.plan_name);
    const sub_status = getNonNullValue(data.sub_status);
    const plan_type = getNonNullValue(data.plan_type);
    getData(1, pageSize, {
      customer_email,
      plan_name,
      sub_status,
      plan_type,
    });
  };

  const cancelCustomerSubscription = async (id) => {
    console.log(id);
    try {
      const result = await sdk.adminCancelStripeSubscription(id, {});
      showToast(globalDispatch, result.message, 3000);
      getData(1, pageSize);
    } catch (error) {
      console.log("ERROR", error);
      showToast(globalDispatch, error.message);
      tokenExpireError(dispatch, error.message);
    }
  };

  const createCharge = async (subId, quantity) => {
    console.log(subId);
    try {
      const result = await sdk.adminCreateUsageCharge(subId, quantity);
      showToast(globalDispatch, result.message, 3000);
      getData(1, pageSize);
    } catch (error) {
      console.log("ERROR", error);
      showToast(globalDispatch, error.message);
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "subscriptions",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize, filterConditions);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [searchValue, filterConditions, optionValue]);

  console.log("data in subscriptions", data);
  return (
    <>
      {/* <form
        className="p-5 mb-10 bg-white rounded shadow"
        onSubmit={handleSubmit(onSubmit)}
      >
        <h4 className="text-2xl font-medium">Search</h4>
        <div className="flex flex-wrap mt-10 filter-form-holder">
          <div className="pr-2 pl-2 mb-4 w-full md:w-1/2">
            <label className="block mb-2 text-sm font-bold text-gray-700">Customer</label>
            <input
              type="text"
              placeholder="Email"
              {...register("customer_email")}
              className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:outline-none focus:shadow-outline"
            />
            <p className="text-xs italic text-red-500">{errors.customer_email?.message}</p>
          </div>

          <div className="pr-2 pl-2 mb-4 w-full md:w-1/2">
            <label className="block mb-2 text-sm font-bold text-gray-700">Plan</label>
            <input
              type="text"
              placeholder="Plan Name"
              {...register("plan_name")}
              className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:outline-none focus:shadow-outline"
            />
            <p className="text-xs italic text-red-500">{errors.plan_name?.message}</p>
          </div>

          <div className="pr-2 pl-2 mb-4 w-full md:w-1/2">
            <label className="block mb-2 text-sm font-bold text-gray-700">Type</label>
            <select
              className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:outline-none focus:shadow-outline"
              {...register("plan_type")}
            >
              {typeStatus.map((option) => (
                <option
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-xs italic text-red-500"></p>
          </div>

          <div className="pr-2 pl-2 mb-4 w-full md:w-1/2">
            <label className="block mb-2 text-sm font-bold text-gray-700">Status</label>
            <select
              className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:outline-none focus:shadow-outline"
              {...register("sub_status")}
            >
              {selectStatus.map((option) => (
                <option
                  value={option.key}
                  key={option.key}
                  defaultValue=""
                >
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-xs italic text-red-500"></p>
          </div>
        </div>

        <div className="pl-2 search-buttons">
          <button
            type="submit"
            className="mr-2 inline-block px-6 py-2.5 bg-blue-600 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-[#2cc9d5]/70 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg transition duration-150 ease-in-out"
          >
            Search
          </button>

          <button
            type="reset"
            onClick={() => getData(1, pageSize)}
            className="inline-block px-6 py-2.5 bg-gray-800 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg transition duration-150 ease-in-out"
          >
            Reset
          </button>
        </div>
      </form> */}
      <div className="flex items-center justify-between py-3 text-black">
        <form
          className="relative rounded bg-white"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="flex items-center gap-4 text-gray-700">
            <div
              className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1"
              onClick={() => setOpenFilter(!openFilter)}
            >
              <BiFilterAlt />
              <span>Filters</span>
              {selectedOptions.length > 0 && (
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                  {selectedOptions.length > 0 ? selectedOptions.length : null}
                </span>
              )}
            </div>
            <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400">
              <BiSearch className="text-xl text-gray-200" />
              <input
                type="text"
                placeholder="search"
                className="border-none p-0 placeholder:text-left focus:outline-none"
                style={{ boxShadow: "0 0 transparent" }}
                onInput={(e) =>
                  addFilterCondition("name", "cs", e.target?.value)
                }
              />
              <AiOutlineClose className="text-lg text-gray-200" />
            </div>
          </div>
          {openFilter && (
            <div className="absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-lg font-semibold text-gray-700">
                  Filters
                </span>
                <XIcon
                  onClick={() => {
                    setSelectedOptions([]);
                    setFilterConditions([]);
                    setFilterValues({});
                  }}
                  className="cursor-pointer text-lg text-gray-400 hover:text-gray-600"
                />
              </div>
              {selectedOptions?.map((option, index) => (
                <div
                  key={index}
                  className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                >
                  <div className="w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
                    {option}
                  </div>
                  <select
                    className="h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0"
                    onChange={(e) => {
                      setOptionValue(e.target.value);
                    }}
                  >
                    <option value="eq" selected>
                      equals
                    </option>
                    <option value="cs">contains</option>
                    <option value="sw">start with</option>
                    <option value="ew">ends with</option>
                    <option value="lt">lower than</option>
                    <option value="le">lower or equal</option>
                    <option value="ge">greater or equal</option>
                    <option value="gt">greater than</option>
                    <option value="bt">between</option>
                    <option value="in">in</option>
                    <option value="is">is null</option>
                  </select>

                  <input
                    type="text"
                    placeholder="Enter value"
                    className=" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none"
                    onChange={(e) =>
                      addFilterCondition(option, optionValue, e.target.value)
                    }
                  />
                  {/* <p className="text-xs italic text-red-500">
                       {errors.id?.message}
                     </p> */}

                  <RiDeleteBin5Line
                    className="cursor-pointer text-2xl text-red-600"
                    onClick={() => {
                      setSelectedOptions((prevOptions) =>
                        prevOptions.filter((op) => op !== option)
                      );
                      setFilterConditions((prevConditions) => {
                        return prevConditions.filter(
                          (condition) => !condition.includes(option)
                        );
                      });
                    }}
                  />
                </div>
              ))}

              <div className="search-buttons relative flex items-center justify-between font-semibold">
                <div
                  // type="submit"
                  className="mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                  onClick={() => {
                    setShowFilterOptions(!showFilterOptions);
                  }}
                >
                  <AiOutlinePlus />
                  Add filter
                </div>

                {showFilterOptions && (
                  <div className="absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md">
                    <ul className="flex flex-col gap-2 text-gray-500">
                      {columns.slice(0, -1).map((column) => (
                        <li
                          key={column.header}
                          className={`${
                            selectedOptions.includes(column.header)
                              ? "cursor-not-allowed text-gray-400"
                              : "cursor-pointer"
                          }`}
                          onClick={() => {
                            if (!selectedOptions.includes(column.header)) {
                              setSelectedOptions((prev) => [
                                ...prev,
                                column.header,
                              ]);
                            }
                            setShowFilterOptions(false);
                          }}
                        >
                          {column.header}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {selectedOptions.length > 0 && (
                  <div
                    // type="reset"
                    onClick={() => {
                      setSelectedOptions([]);
                      setFilterConditions([]);
                    }}
                    className="inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out"
                  >
                    Clear all filter
                  </div>
                )}
              </div>
            </div>
          )}
        </form>
      </div>

      {isLoading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto border-b border-gray-200 shadow">
          <table className="min-w-full divide-y divide-gray-200 text-black">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {data.map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {row.status !== "canceled" ? (
                              <button
                                onClick={() =>
                                  cancelCustomerSubscription(row.subId)
                                }
                                type="button"
                                className="mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg"
                              >
                                Cancel
                              </button>
                            ) : (
                              ""
                            )}
                            {row.isMetered === 1 ? (
                              <button
                                onClick={() => createCharge(row.subId, 1)}
                                type="button"
                                className="mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg"
                              >
                                Create Charge
                              </button>
                            ) : (
                              ""
                            )}
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      if (
                        row.planType === "recurring" &&
                        cell.type === "timestamp"
                      ) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {new Date(
                              row[cell.accessor] * 1000
                            ).toLocaleDateString("en-US", {
                              dateStyle: "medium",
                            })}
                          </td>
                        );
                      } else if (
                        row.planType === "lifetime" &&
                        cell.type === "timestamp"
                      ) {
                        if (cell.accessor === "currentPeriodStart") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              {new Date(
                                row.createdAt * 1000
                              ).toLocaleDateString("en-US", {
                                dateStyle: "medium",
                              })}
                            </td>
                          );
                        } else if (cell.accessor === "currentPeriodEnd") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              Infinity
                            </td>
                          );
                        }
                      } else if (cell.type == "currency") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            ${+(row[cell.accessor] ?? 0)}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="whitespace-nowrap px-6 py-4">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={updatePageSize}
        previousPage={previousPage}
        nextPage={nextPage}
      />
    </>
  );
};

export default AdminStripeSubscriptionsListPage;
