import React, { useState, useContext } from 'react';
//  import { LazyLoad } from "Components/LazyLoad";
import { tokenExpireError, AuthContext } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';

import { Chat } from 'Components/Chat';
import { ChatBot } from 'Components/ChatBot';

const ChatPage = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'chat',
      },
    });
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <div className={` `}>
        {/* <Chat roles={['user']} /> */}
        <ChatBot />
      </div>
    </div>
  );
};

export default ChatPage;
