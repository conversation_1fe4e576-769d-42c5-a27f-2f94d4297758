import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f as ae}from"./vendor-2ae44a2e.js";import{M as re,G as ie,A as k,B as oe,a as ne,b as le,R as de,c as ce,u as xe,v as ue,t as me}from"./index-d0a8f5da.js";import{o as pe}from"./yup-5abd4662.js";import{u as he}from"./react-hook-form-47c010f8.js";import{c as ge,a as fe}from"./yup-c2e87575.js";import{P as ye}from"./index-132fbad2.js";import"./index-e429b426.js";import{S as be}from"./index-a74110af.js";import{M as R}from"./index-9aa09a5c.js";import{X as je}from"./lucide-react-f66dbccf.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let T=new re;const M=[{header:"Number",accessor:"number"}],We=()=>{const{dispatch:D}=s.useContext(ie),{dispatch:I}=s.useContext(k);s.useState("");const[y,O]=s.useState([]),[n,b]=s.useState(10),[j,$]=s.useState(0),[ve,q]=s.useState(0),[d,z]=s.useState(0),[B,L]=s.useState(!1),[G,U]=s.useState(!1),[v,_]=s.useState(!1),[w,S]=s.useState(!1),[o,c]=s.useState([]),[N,x]=s.useState([]),[H,K]=s.useState(""),[C,V]=s.useState("eq"),[P,p]=s.useState(!0),[X,A]=s.useState(!1),[J,u]=s.useState(!1),[Q,we]=s.useState();ae(),s.useContext(k);const W=ge({number:fe()}),{register:Se,handleSubmit:Y,formState:{errors:Ne}}=he({resolver:pe(W)});function Z(t){(async function(){b(t),await l(0,t)})()}function ee(){(async function(){await l(d-1>0?d-1:0,n)})()}function te(){(async function(){await l(d+1<=j?d+1:0,n)})()}const F=(t,a,r)=>{const i=a==="eq"&&isNaN(r)?`"${r}"`:r,h=`${t},${a},${i}`;x(g=>[...g.filter(m=>!m.includes(t)),h]),K(r)};async function l(t,a,r){p(!0);try{T.setTable("forwarding_numbers");const i=await T.callRestAPI({payload:{},page:t,limit:a,filter:[...N]},"PAGINATE");i&&p(!1);const{list:h,total:g,limit:E,num_pages:m,page:f}=i;O(h),b(E),$(m),z(f),q(g),L(f>1),U(f+1<=m)}catch(i){p(!1),console.log("ERROR",i),me(I,i.message)}}const se=t=>{l(0,n)};return s.useEffect(()=>{D({type:"SETPATH",payload:{path:"forwarding"}});const a=setTimeout(async()=>{await l(1,n)},700);return()=>{clearTimeout(a)}},[H,N,C]),e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsx("div",{className:"flex items-center justify-between px-8 py-6",children:e.jsxs("form",{className:"relative",onSubmit:Y(se),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 hover:border-gray-300",onClick:()=>_(!v),children:[e.jsx(oe,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-white",children:o.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 focus-within:border-gray-300",children:[e.jsx(ne,{className:"text-xl text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search",className:"border-none p-0 placeholder:text-gray-400 focus:outline-none",style:{boxShadow:"none"},onInput:t=>{var a;return F("number","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(le,{className:"text-lg text-gray-400 hover:text-gray-600"})]})]}),v&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-700",children:"Filters"}),e.jsx(je,{onClick:()=>{c([]),x([]),setFilterValues({})},className:"cursor-pointer text-lg text-gray-400 hover:text-gray-600"})]}),o==null?void 0:o.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border-gray-300 text-gray-600 outline-0",onChange:r=>{V(r.target.value)},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:r=>F(t,C,r.target.value)}),e.jsx(de,{className:"cursor-pointer text-2xl text-red-500 hover:text-red-600",onClick:()=>{c(r=>r.filter(i=>i!==t)),x(r=>r.filter(i=>!i.includes(t)))}})]},a)),e.jsxs("div",{className:"relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"flex cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 text-gray-600 hover:bg-gray-200",onClick:()=>{S(!w)},children:[e.jsx(ce,{}),"Add filter"]}),w&&e.jsx("div",{className:"absolute top-11 z-10 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg",children:e.jsx("ul",{className:"flex flex-col",children:M.map(t=>e.jsx("li",{className:`px-4 py-2 ${o.includes(t.header)?"cursor-not-allowed text-gray-700":"cursor-pointer text-gray-400"}`,onClick:()=>{o.includes(t.header)||c(a=>[...a,t.header]),S(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("button",{type:"button",onClick:()=>{c([]),x([])},className:"py-2 pl-4 text-gray-600 hover:text-gray-800",children:"Clear all filter"})]})]})]})}),P?e.jsx(be,{}):e.jsx("div",{className:"px-8",children:e.jsxs("div",{className:"overflow-x-auto rounded-lg border border-gray-200 bg-white shadow",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:M.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:y.map((t,a)=>e.jsx("tr",{className:"hover:bg-gray-50",children:e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t.number})},a))})]}),!P&&y.length===0&&e.jsx("div",{className:"px-6 py-4 text-center text-sm text-gray-500",children:"No forwarding numbers found"})]})}),e.jsx("div",{className:"px-8 py-4",children:e.jsx(ye,{currentPage:d,pageCount:j,pageSize:n,canPreviousPage:B,canNextPage:G,updatePageSize:Z,previousPage:ee,nextPage:te})}),e.jsx(R,{isModalActive:J,closeModalFn:()=>u(!1),children:e.jsx(xe,{setSidebar:u,closeSidebar:()=>{u(!1),l(1,n)}})}),e.jsx(R,{isModalActive:X,closeModalFn:()=>A(!1),children:e.jsx(ue,{activeId:Q,setSidebar:A,closeSidebar:()=>{u(!1),l(1,n)}})})]})};export{We as default};
