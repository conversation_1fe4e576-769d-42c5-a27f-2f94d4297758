import React from "react";

const AddTags = ({ tags, tag, setTagData }) => {
  // console.log('addtag--->',{tags, tag, setTagData})
  return (
    <li className="inline-flex items-center gap-2 rounded bg-[#2cc9d5] px-2 py-1 text-sm text-white">
      <span>{tag}</span>{" "}
      <button
        onClick={() => setTagData(tags.filter((tags) => tags.name !== tag))}
        className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 duration-300 hover:bg-blue-200"
      >
        <span className="leading-0 -mt-1 text-black">&times;</span>
      </button>
    </li>
  );
};

export default AddTags;
