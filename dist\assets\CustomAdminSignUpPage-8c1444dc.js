import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as j,r as E,u as A,f as $,L as F}from"./vendor-2ae44a2e.js";import{u as G}from"./react-hook-form-47c010f8.js";import{o as I}from"./yup-5abd4662.js";import{c as _,a as y}from"./yup-c2e87575.js";import{M as q,A as D,G as z,s as N}from"./index-d0a8f5da.js";import{I as B}from"./InteractiveButton-bff38983.js";import{L as M}from"./login-new-bg-eb709951.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";let O=new q;const ge=()=>{var m,c,d,p;const v=_({email:y().email().required(),password:y().required()}).required(),{dispatch:k}=j.useContext(D),{dispatch:r}=j.useContext(z),[i,a]=E.useState(!1),S=A(),L=new URLSearchParams(S.search).get("redirect_uri"),P=$(),{register:l,handleSubmit:R,setError:n,formState:{errors:t}}=G({resolver:I(v)}),C=async x=>{var u,g,h,f;try{a(!0);const s=await O.register(x.email,x.password,"admin");if(!s.error)k({type:"LOGIN",payload:s}),N(r,"Succesfully Registered",4e3,"success"),P(L??"/admin/customers");else if(a(!1),s.validation){const b=Object.keys(s.validation);for(let o=0;o<b.length;o++){const w=b[o];n(w,{type:"manual",message:s.validation[w]})}}}catch(s){a(!1),console.log("Error",s),N(r,s==null?void 0:s.message,4e3,"error"),n("email",{type:"manual",message:(g=(u=s==null?void 0:s.response)==null?void 0:u.data)!=null&&g.message?(f=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:f.message:s==null?void 0:s.message})}};return e.jsx("div",{className:"m-auto h-screen min-h-screen max-h-screen",children:e.jsxs("div",{className:"flex justify-center w-full h-full min-h-full max-h-full",children:[e.jsxs("section",{className:"flex flex-col justify-center items-center w-full bg-white md:w-1/2",children:[e.jsxs("form",{onSubmit:R(C),className:"mt-[9.375rem] flex w-full max-w-md flex-col px-6",children:[e.jsx("h1",{className:"mb-8 text-3xl font-semibold text-center md:text-5xl md:font-bold",children:"Register"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",autoComplete:"off",placeholder:"Email",...l("email"),className:`focus:shadow-outline mb-3 w-full resize-none appearance-none rounded border-2 bg-transparent p-2 px-4 py-2 leading-tight  text-gray-700 shadow focus:outline-none active:outline-none ${(m=t.email)!=null&&m.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(c=t.email)==null?void 0:c.message})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{autoComplete:"off",type:"password",name:"password",placeholder:"Password",...l("password"),className:`focus:shadow-outline mb-3 w-full flex-grow appearance-none rounded border-2 p-2 px-4 py-2 leading-tight text-gray-700 shadow focus:outline-none active:outline-none ${(d=t.password)!=null&&d.message?"border-red-500":""}`}),e.jsx("button",{type:"button",className:"absolute right-1 top-[20%]",children:e.jsx("img",{src:"/invisible.png",alt:"",className:"mr-2 w-6"})}),e.jsx("p",{className:"text-xs italic text-red-500",children:(p=t.password)==null?void 0:p.message})]}),e.jsx(B,{type:"submit",className:"flex items-center justify-center rounded bg-gradient-to-l from-[#33d4b7_9.11%] to-[#0d9895_69.45%] py-2 tracking-wide text-white outline-none  focus:outline-none",loading:i,disabled:i,children:e.jsx("span",{children:"Register"})})]}),e.jsx("div",{className:"oauth flex w-full max-w-md grow flex-col gap-4 px-6 text-[#344054]",children:e.jsx("div",{children:e.jsxs("h3",{className:"mt-5 text-sm text-center text-gray-800 normal-case",children:["Already have an account?"," ",e.jsxs(F,{className:"self-end mb-8 text-sm font-semibold my-text-gradient",to:"/admin/login",children:["Login"," "]})," "]})})}),e.jsxs("p",{className:"my-5 h-10 text-xs text-center text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]}),e.jsx("section",{className:"hidden w-1/2 md:block",style:{backgroundImage:`url(${M})`,backgroundSize:"cover",backgroundPosition:"center center"}})]})})};export{ge as default};
