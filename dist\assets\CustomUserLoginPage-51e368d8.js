import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as S,r as k,u as A,f as H}from"./vendor-2ae44a2e.js";import{u as W}from"./react-hook-form-47c010f8.js";import{o as $}from"./yup-5abd4662.js";import{c as q,a as P}from"./yup-c2e87575.js";import{M as D,A as _,G as z,s as L}from"./index-d0a8f5da.js";import{I as F}from"./InteractiveButton-bff38983.js";import{a as M}from"./automate-icon-06435bcb.js";import{b as O}from"./wbg-a13cc141.js";import{P as U,a as K}from"./index.esm-4b383179.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";let N=new D;const ve=()=>{const B=q({email:P().email().required(),password:P().required()}).required(),{dispatch:R}=S.useContext(_),{dispatch:l}=S.useContext(z),[n,a]=k.useState(!1),[i,C]=k.useState(!1),E=A(),d=new URLSearchParams(E.search).get("redirect_uri"),m=H(),{register:p,handleSubmit:I,setError:c,formState:{errors:s}}=W({resolver:$(B)}),G=async x=>{var g,h,u,f,b,y,w,j;try{a(!0);const e=await N.login(x.email,x.password,"user");if(e.error){if(a(!1),e.validation){const o=Object.keys(e.validation);for(let r=0;r<o.length;r++){const v=o[r];c(v,{type:"manual",message:e.validation[v]})}}}else{await R({type:"LOGIN",payload:e});const o=await N.callRawAPiWithToken(`/v4/api/records/profile/${e.user_id}`,"","GET",e.token);L(l,"Succesfully Logged In",4e3,"success"),o.model.onboarded?m(d??"/user/chat"):m(d??"/user/onboarding")}}catch(e){a(!1),L(l,(h=(g=e==null?void 0:e.response)==null?void 0:g.data)!=null&&h.message?(f=(u=e==null?void 0:e.response)==null?void 0:u.data)==null?void 0:f.message:e==null?void 0:e.message,4e3,"error"),c("email",{type:"manual",message:(y=(b=e==null?void 0:e.response)==null?void 0:b.data)!=null&&y.message?(j=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:j.message:e==null?void 0:e.message})}},T=()=>t.jsx("div",{style:{display:"flex",height:"100vh",backgroundImage:`url(${O})`,backgroundRepeat:"no-repeat",backgroundSize:"cover"},className:"",children:t.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[rgb(255,255,255,0.01)] backdrop-blur-md",children:t.jsx("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"center"},children:t.jsxs("div",{style:{width:"600px",backgroundColor:"#1d2937",padding:"40px",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)"},children:[t.jsx("img",{src:M,alt:"Logo",style:{width:"50px",height:"50px",marginBottom:"20px",display:"block",marginLeft:"auto",marginRight:"auto"}}),t.jsx("h2",{style:{marginBottom:"20px",color:"#fff",textAlign:"center",fontSize:"1.5rem",fontWeight:"bold"},children:"Login"}),t.jsxs("form",{onSubmit:I(G),children:[t.jsxs("div",{style:{marginBottom:"24px",minHeight:"90px"},children:[t.jsx("label",{style:{display:"block",color:"white",marginBottom:"5px",fontWeight:"bold"},children:"Email"}),t.jsx("input",{type:"email",className:"h-[42px] bg-transparent",...p("email"),style:{width:"100%",color:"white",padding:"10px",borderRadius:"5px",border:"1px solid #ccc",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"}}),s.email&&t.jsx("p",{style:{color:"red",marginTop:"5px",minHeight:"20px"},children:s.email.message})]}),t.jsxs("div",{style:{marginBottom:"24px",minHeight:"90px"},children:[t.jsx("label",{style:{display:"block",color:"white",marginBottom:"5px",fontWeight:"bold"},children:"Password"}),t.jsxs("div",{className:"relative",children:[t.jsx("input",{type:i?"text":"password",...p("password"),className:"h-[42px] w-full bg-transparent",style:{color:"white",padding:"10px",borderRadius:"5px",border:"1px solid #ccc",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"}}),t.jsx("button",{type:"button",onClick:()=>C(!i),className:"absolute right-3 top-1/2 -translate-y-1/2 text-white hover:text-gray-300",children:i?t.jsx(U,{className:"text-xl"}):t.jsx(K,{className:"text-xl"})})]}),s.password&&t.jsx("p",{style:{color:"red",marginTop:"5px",minHeight:"20px"},children:s.password.message})]}),t.jsx("div",{style:{display:"flex",color:"white",justifyContent:"flex-end",marginBottom:"20px"},children:t.jsx("a",{href:"/user/forgot",style:{textDecoration:"none",color:"white"},children:"Forgot password?"})}),t.jsx(F,{type:"submit",className:"hover: flex h-[42px] w-full items-center justify-center rounded-md bg-[#19b2f6]/80 py-2 tracking-wide text-white outline-none hover:bg-[#19b2f6]/60 focus:outline-none",loading:n,disabled:n,children:t.jsx("span",{children:"Sign in"})})]})]})})})});return t.jsx(T,{})};export{ve as default};
