import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{i as $e,R as o,r as v}from"./vendor-2ae44a2e.js";import{M as ke,T as Fe,A as Pe,G as Ae,t as M,s as De,B as Re,a as Ie,b as Be,R as Le,c as Oe,x as qe,y as Me,P as ze}from"./index-d0a8f5da.js";import{A as Ve}from"./index-e429b426.js";import{E as We}from"./ExportButton-6ac23de7.js";import{X as Ge}from"./lucide-react-f66dbccf.js";import{C as T,q as _e}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let b=new ke;const mt=({columns:h=[],actions:i={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPosition:z="onTable",actionId:se="id",tableRole:ae="admin",table:g="user",tableTitle:N="",tableSchema:Ke=[],hasFilter:le=!0,schemaFields:Ue=[],showPagination:ie=!0,defaultFilter:ne=[],refreshRef:V=null,filterConfig:oe={}})=>{var X,H,J,Q,Y;const W=new Fe,[c,Xe]=$e(),{dispatch:$}=o.useContext(Pe),{dispatch:de}=o.useContext(Ae),[ce,k]=o.useState([]),[ue,F]=o.useState(!1),[pe,P]=o.useState(!1),[G,_]=o.useState(!1),[w,he]=o.useState([]),[xe,K]=o.useState(!0),[me,ge]=o.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0}),[y,C]=o.useState([]),[S,j]=o.useState([]),[A,f]=o.useState({}),[D,fe]=o.useState("eq"),[u,R]=o.useState(""),[be,I]=o.useState(!1);o.useRef(null),o.useRef("");const B=v.useMemo(()=>S.length+(u?1:0),[S,u]),U=v.useMemo(()=>h.filter(e=>(!e.hasOwnProperty("isFilter")||e.isFilter)&&e.accessor!=="actions"&&!e.isAction&&e.accessor!=="").map(e=>({...e,type:e.mappingExist?"select":e.type,options:e.mappingExist?Object.entries(e.mappings).map(([r,s])=>({value:r,label:s})):e.options})),[h]);function we(e){c.set("sortId",e),c.set("direction",c.get("direction")=="asc"?"desc":"asc"),p()}v.useEffect(()=>{p()},[c]);const L=(e,r,s)=>{if(!s){j(d=>d.filter(x=>!x.startsWith(e+",")));return}const l=h.find(d=>d.accessor===e),a=r||"eq";let n=s;if(l!=null&&l.mappingExist&&l.mappings){const d=Object.entries(l.mappings).find(([x,m])=>m.toLowerCase()===s.toLowerCase());d&&(n=d[0])}const E=`${e},${a},${n}`;j(d=>[...d.filter(m=>!m.startsWith(e+",")),E])};async function p(){var e;K(!0);try{const r=`${W.getProjectId()}_${g}`;let s=[...ne];if(u.trim()){const m=((e=h.find(O=>O.isSearchable))==null?void 0:e.accessor)||"id";s.push(`${r}.${m},cs,${u.trim()}`)}if(S.length>0){const m=S.map(O=>{var te,re;const[q,Ee,Z]=O.split(",");let ee=Z;return(((te=oe[q])==null?void 0:te.type)==="string"||((re=h.find(Te=>Te.accessor===q))==null?void 0:re.type)==="string")&&(ee=`'${Z}'`),`${r}.${q},${Ee},${ee}`});s=[...s,...m]}console.log("Filter Array:",s);const l=await W.getPaginate(g,{size:c.get("limit")??50,page:c.get("page")??1,filter:s.filter(Boolean),sort:c.get("sortId")?`${c.get("sortId")},${c.get("direction")||"asc"}`:void 0}),{list:a,total:n,limit:E,num_pages:d,page:x}=l;ge({currentPage:x,pageSize:E,totalNumber:n,totalPages:d}),k(a)}catch(r){console.log("ERROR",r),M($,r.message),De(de,r.message)}K(!1)}const ye=async e=>{async function r(s){try{P(!0),b.setTable(g);const l=await b.callRestAPI({id:s},"DELETE");l!=null&&l.error||(k(a=>a.filter(n=>Number(n.id)!==Number(s))),P(!1),F(!1))}catch(l){throw P(!1),F(!1),M($,l==null?void 0:l.message),new Error(l)}}typeof e=="object"?e.forEach(async s=>{await r(s)}):typeof e=="number"&&await r(e)},je=async e=>{try{b.setTable(g);const r=await b.exportCSV()}catch(r){throw new Error(r)}};async function ve(e,r,s){try{b.setTable(g);const l=await b.callRestAPI({id:e,[r]:s},"PUT")}catch(l){console.log("ERROR",l),M($,l.message)}}async function Ne(e,r,s,l){let a;r=isNaN(Number.parseInt(r))?r:Number.parseInt(r);try{clearTimeout(a),a=setTimeout(async()=>{await ve(e,l,r)},200),k(n=>n.map((d,x)=>x===s?{...d,[l]:r}:d))}catch(n){console.error(n)}}o.useEffect(()=>{var e;(e=i==null?void 0:i.select)!=null&&e.action&&i.select.action()},[w.length]),v.useEffect(()=>{p()},[]);const Ce=()=>{C([]),j([]),f({}),R(""),p()};o.useEffect(()=>{if(u===""){p();return}if(/^\d+$/.test(u))p();else{const e=setTimeout(()=>{p()},300);return()=>clearTimeout(e)}},[u]);const Se=e=>{I(!0),R(e),e||I(!1)};return t.jsxs("div",{className:"px-8",children:[V&&t.jsx("button",{ref:V,onClick:()=>p(),className:"hidden"}),t.jsxs("div",{className:`flex gap-3 ${N?"flex-col items-center":"items-center h-fit"}`,children:[le?t.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:t.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:t.jsxs(T,{children:[t.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[t.jsxs(T.Button,{className:"flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white ",children:[t.jsx(Re,{}),t.jsx("span",{children:"Filters"}),B>0&&t.jsx("span",{className:"flex justify-center items-center w-6 h-6 text-white bg-gray-800 rounded-full text-start",children:B})]}),t.jsxs("div",{className:"flex gap-3 justify-between items-center px-2 py-1 text-white bg-transparent rounded-md border cursor-pointer focus-within:border-gray-40 border-white/50",children:[t.jsx(Ie,{className:`text-xl ${be?"text-gray-400":"text-white"}`}),t.jsx("input",{type:"text",placeholder:`Search ${((X=h.find(e=>e.isSearchable))==null?void 0:X.header)||" by Id"}`,className:"p-0 text-white bg-transparent border-none placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:u,onChange:e=>{const r=e.target.value;Se(r)}}),u&&t.jsx(Be,{className:"text-lg text-white cursor-pointer",onClick:()=>{R(""),I(!1)}})]})]}),t.jsx(_e,{as:v.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",afterLeave:()=>p(),children:t.jsx(T.Panel,{children:t.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[t.jsx("span",{className:"absolute top-2 left-5 font-medium text-white",children:"Filters"}),t.jsx(T.Button,{onClick:()=>{console.log("clicked"),C([]),j([]),f({})},children:t.jsx(Ge,{className:"absolute top-2 right-2 text-white cursor-pointer"})}),y==null?void 0:y.map((e,r)=>{var l;const s=U.find(a=>a.accessor===e);return t.jsxs("div",{className:"flex gap-3 justify-between items-center mb-2 w-full text-gray-600",children:[t.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:(s==null?void 0:s.header)||e,children:(s==null?void 0:s.header)||e}),t.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:D,onChange:a=>{fe(a.target.value),L(e,a.target.value,A[e])},children:[t.jsx("option",{value:"eq",children:"equals"}),t.jsx("option",{value:"cs",children:"contains"}),t.jsx("option",{value:"sw",children:"start with"}),t.jsx("option",{value:"ew",children:"ends with"}),t.jsx("option",{value:"lt",children:"lower than"}),t.jsx("option",{value:"le",children:"lower or equal"}),t.jsx("option",{value:"ge",children:"greater or equal"}),t.jsx("option",{value:"gt",children:"greater than"}),t.jsx("option",{value:"bt",children:"between"}),t.jsx("option",{value:"in",children:"in"}),t.jsx("option",{value:"is",children:"is null"})]}),(s==null?void 0:s.type)==="select"?t.jsxs("select",{value:A[e]||"",onChange:a=>{f(n=>({...n,[e]:a.target.value})),L(e,D,a.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[t.jsx("option",{value:"",children:"Select..."}),(l=s.options)==null?void 0:l.map(a=>t.jsx("option",{value:a.value,children:a.label},a.value))]}):t.jsx("input",{type:(s==null?void 0:s.type)||"text",placeholder:"Enter value",className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",value:A[e]||"",onChange:a=>{f(n=>({...n,[e]:a.target.value})),L(e,D,a.target.value)}}),t.jsx(Le,{className:"text-2xl text-red-600 cursor-pointer",onClick:()=>{C(a=>a.filter(n=>n!==e)),j(a=>a.filter(n=>!n.startsWith(e+","))),f(a=>{const n={...a};return delete n[e],n})}})]},r)}),t.jsxs("div",{className:"flex relative justify-between items-center font-semibold search-buttons",children:[t.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{_(!G)},children:[t.jsx(Oe,{}),"Add filter"]}),G&&t.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3 text-gray-600",children:t.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:U.map(e=>t.jsx("li",{className:`${y.includes(e.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400"}`,onClick:()=>{y.includes(e.accessor)||(C(r=>[...r,e.accessor]),f(r=>({...r,[e.accessor]:""})),_(!1))},children:e.header},e.accessor))})}),B>0&&t.jsx("div",{onClick:Ce,className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-normal leading-tight text-white  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})})]})})}):null,t.jsx("div",{className:"flex justify-end w-full text-center h-fit",children:t.jsxs("div",{className:"flex justify-between w-full max-w-7xl",children:[t.jsx("h4",{className:"text-2xl font-medium capitalize",children:N||""}),t.jsxs("div",{className:"flex gap-2 h-full",children:[w!=null&&w.length&&z==="aboveTable"?t.jsx(qe,{actions:i,selectedItems:w}):null,((H=i==null?void 0:i.export)==null?void 0:H.show)&&t.jsx(We,{showText:!1,onClick:je,className:"mx-1"}),((J=i==null?void 0:i.add)==null?void 0:J.show)&&t.jsx(Ve,{onClick:()=>{var e,r;(e=i==null?void 0:i.add)!=null&&e.action&&((r=i==null?void 0:i.add)==null||r.action())},showChildren:(Q=i==null?void 0:i.add)==null?void 0:Q.showChildren,children:(Y=i==null?void 0:i.add)==null?void 0:Y.children})]})]})})]}),t.jsx("div",{className:"overflow-x-auto rounded bg-[#1d2937] p-5 px-0",children:t.jsx(Me,{onSort:we,columns:h,tableRole:ae,actionId:se,table:g,tableTitle:N,deleteItem:ye,loading:xe,deleteLoading:pe,showDeleteModal:ue,currentTableData:ce,setShowDeleteModal:F,actions:i,actionPosition:z,setSelectedItems:he,handleTableCellChange:Ne})}),ie&&t.jsx(ze,{paginationData:me})]})};export{mt as default};
