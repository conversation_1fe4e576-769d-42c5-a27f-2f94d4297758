import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as F,r as n,f as V}from"./vendor-2ae44a2e.js";import{u as ee}from"./react-hook-form-47c010f8.js";import{o as se}from"./yup-5abd4662.js";import{c as te,a as ae}from"./yup-c2e87575.js";import{M as ie,G as K,t as U,s as I}from"./index-d0a8f5da.js";import{I as z}from"./InteractiveButton-bff38983.js";import le from"./ModalPrompt-fb38cd0a.js";import{b as ne,S as oe}from"./lucide-react-f66dbccf.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";import"./index-f2c2b086.js";let y=new ie;function J({title:u,isActive:r,onClick:d,icon:g=null}){return e.jsxs("button",{onClick:d,className:`flex items-center gap-2 border-b-2 px-6 py-3 text-sm font-medium transition-colors ${r?"border-blue-500 text-white":"border-transparent text-gray-400 hover:border-gray-400 hover:text-gray-300"}`,children:[g,u]})}const Ie=()=>{var q;const u=te({email:ae().email().required()}).required(),{dispatch:r}=F.useContext(K),[d,g]=n.useState(""),[E,j]=n.useState(!1),[k,v]=n.useState(!1),[w,i]=n.useState(!1),[f,t]=n.useState(!1),[H,h]=n.useState(!1),[b,c]=n.useState(!1);n.useState(!1);const[N,_]=n.useState("Profile"),[a,C]=n.useState({});n.useState(!1);const[A,O]=n.useState(!1),{dispatch:Q}=F.useContext(K);V();const{register:P,handleSubmit:T,setError:S,setValue:$,formState:{errors:M}}=ee({resolver:se(u)});async function R(){try{O(!0);const s=await y.getProfile();C(s),$("email",s==null?void 0:s.email),$("first_name",s==null?void 0:s.first_name),$("last_name",s==null?void 0:s.last_name),g(s==null?void 0:s.email)}catch(s){console.log("Error",s),U(r,s.response.data.message?s.response.data.message:s.message)}O(!1)}const D=async s=>{C(s);try{c(!0);const o=await y.updateProfile({first_name:s.first_name||(a==null?void 0:a.first_name),last_name:s.last_name||(a==null?void 0:a.last_name)});if(!o.error)I(r,"Profile Updated",4e3),p();else{if(o.validation){const l=Object.keys(o.validation);for(let m=0;m<l.length;m++){const x=l[m];S(x,{type:"manual",message:o.validation[x]})}}p()}if(d!==s.email){const l=await y.updateEmail(s.email);if(!l.error)I(r,"Email Updated",1e3);else if(l.validation){const m=Object.keys(l.validation);for(let x=0;x<m.length;x++){const L=m[x];S(L,{type:"manual",message:l.validation[L]})}}p()}if(s.password.length>0){const l=await y.updatePassword(s.password);if(!l.error)I(r,"Password Updated",2e3);else if(l.validation){const m=Object.keys(l.validation);for(let x=0;x<m.length;x++){const L=m[x];S(L,{type:"manual",message:l.validation[L]})}}}await R(),c(!1)}catch(o){c(!1),console.log("Error",o),S("email",{type:"manual",message:o.response.data.message?o.response.data.message:o.message}),U(r,o.response.data.message?o.response.data.message:o.message)}};F.useEffect(()=>{Q({type:"SETPATH",payload:{path:"profile"}}),R()},[]);const G=()=>{v(!0)},W=()=>{i(!0)},Z=()=>{t(!0)},X=()=>{h(!0)},p=()=>{j(!1),v(!1),i(!1),t(!1),h(!1)},Y=async()=>{try{c(!0);const s=await y.updateProfile({first_name:a==null?void 0:a.first_name,last_name:a==null?void 0:a.last_name,photo:""});if(!s.error)I(r,"Profile Picture Deleted",1e3);else if(s.validation){const o=Object.keys(s.validation);for(let l=0;l<o.length;l++){const m=o[l];S(m,{type:"manual",message:s.validation[m]})}}await R(),c(!1),p()}catch(s){c(!1),console.log("Error",s)}};return e.jsxs("div",{className:"mt-6 rounded-lg bg-[#1d2937]",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex justify-center space-x-8 pt-5",children:[e.jsx(J,{icon:e.jsx(ne,{className:"h-4 w-4"}),title:"Profile",isActive:N==="Profile",onClick:()=>_("Profile")}),e.jsx(J,{icon:e.jsx(oe,{className:"h-4 w-4"}),title:"Security",isActive:N==="Security",onClick:()=>_("Security")})]})}),e.jsxs("main",{className:"p-8 pt-0",children:[N==="Profile"&&e.jsxs("div",{className:"mx-auto mb-4 max-w-2xl",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold text-white",children:"Personal Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-sm font-medium text-gray-400",children:"First Name"}),e.jsx("p",{className:"text-base font-medium text-white",children:a==null?void 0:a.first_name})]}),e.jsx("button",{onClick:G,className:"text-sm font-medium text-blue-400 transition-colors hover:text-blue-300",children:"Edit"})]})}),e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-sm font-medium text-gray-400",children:"Last Name"}),e.jsx("p",{className:"text-base font-medium text-white",children:a==null?void 0:a.last_name})]}),e.jsx("button",{onClick:W,className:"text-sm font-medium text-blue-400 transition-colors hover:text-blue-300",children:"Edit"})]})}),e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-sm font-medium text-gray-400",children:"Email"}),e.jsx("p",{className:"text-base font-medium text-white",children:d})]}),e.jsx("button",{onClick:Z,className:"text-sm font-medium text-blue-400 transition-colors hover:text-blue-300",children:"Edit"})]})})]})]}),N==="Security"&&e.jsxs("div",{className:"mx-auto max-w-2xl",children:[e.jsx("h2",{className:" mb-0 text-xl font-semibold text-white",children:"Security Settings"}),e.jsxs("form",{onSubmit:T(D),className:"space-y-2",children:[e.jsxs("div",{className:"py-2",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-400",children:"Password"}),e.jsx("input",{...P("password"),type:"password",className:"w-[300px] border border-gray-600 bg-[#1d2937] px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter new password"}),((q=M.password)==null?void 0:q.message)&&e.jsx("p",{className:"mt-2 text-sm text-red-400",children:M.password.message})]}),e.jsx("div",{className:"flex justify-start",children:e.jsx(z,{type:"submit",loading:b,disabled:b,className:"rounded-md bg-[#19b2f6]/80  px-6 py-2 text-white transition-colors hover:bg-blue-600 disabled:opacity-50",children:"Update Password"})})]})]}),E&&e.jsx(le,{actionHandler:Y,closeModalFunction:p,title:"Are you sure ? ",message:"Are you sure you want to delete profile picture ? ",acceptText:"DELETE",rejectText:"CANCEL"}),k&&e.jsx(B,{title:"Edit information",label:"First Name",buttonName:"Save and close",isOpen:G,onClose:p,handleSubmit:T,onSubmit:D,register:P,id:"first_name",submitLoading:b,errors:M}),w&&e.jsx(B,{title:"Edit information",label:"Last Name",buttonName:"Save and close",isOpen:W,onClose:p,handleSubmit:T,onSubmit:D,register:P,id:"last_name",submitLoading:b,errors:M}),f&&e.jsx(B,{title:"Change Email",label:"Email",buttonName:"Submit",isOpen:Z,onClose:p,handleSubmit:T,onSubmit:D,register:P,id:"email",submitLoading:b,errors:M,defaultValues:a}),H&&e.jsx(re,{title:"View Events",label:"Events",buttonName:"okay",isOpen:X,onClose:p})]})]})},B=u=>{var a,C;const{title:r,label:d,buttonName:g,isOpen:E,onClose:j,handleSubmit:k,onSubmit:v,register:w,id:i,submitLoading:f,errors:t,defaultValues:H}=u,[h,b]=n.useState(!1),[c,N]=n.useState({email:""}),_=A=>O=>{A==="email"&&N({...c,[A]:O.target.value})};return e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${E?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg  bg-[#1d2937] px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-white",children:r}),e.jsx("button",{className:"text-gray-500 hover:text-white focus:outline-none",onClick:j,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:k(v),className:"max-w-lg",children:[h===!0&&e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"transparent"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-sm font-medium text-white",children:"We've send an email to:"}),e.jsx("p",{className:"mb-2 text-sm font-semibold text-white",children:c==null?void 0:c.email}),e.jsx("p",{className:"mb-2 text-sm font-medium text-white",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2 text-sm font-medium text-white",children:"(the link expires in 24 hours)"})]})]}),h===!1&&(i==="first_name"||i==="last_name")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-white",children:d}),e.jsx("input",{className:"focus:shadow-outline placeholder-text-white w-full appearance-none rounded bg-transparent px-3 py-2 leading-tight text-white shadow focus:outline-none",id:i,type:"text",placeholder:`Enter ${d} `,name:i,...w(i)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(a=t==null?void 0:t.id)==null?void 0:a.message})]}),h===!1&&i==="email"&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-white",children:d}),e.jsx("input",{className:"focus:shadow-outline placeholder-text-white w-full appearance-none rounded bg-transparent px-3 py-2 leading-tight text-white shadow focus:outline-none",id:i,type:"text",placeholder:`Enter ${d}`,name:i,...w(i),onChange:_("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=t==null?void 0:t.id)==null?void 0:C.message})]}),h===!1&&i==="events"&&e.jsx("div",{className:"mt-3",children:e.jsx("label",{htmlFor:"events",className:"mb-1 block text-sm font-medium text-white",children:d})}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx("button",{className:"-gray-300 mr-2 w-full rounded-md bg-red-600 px-4 py-2 text-white",onClick:j,children:"Cancel"}),(i==="first_name"||i==="last_name"||h===!0)&&e.jsx(z,{className:"focus:shadow-outline hover:transparent w-full rounded-md bg-[#19b2f6]/80  px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:f,disabled:f,children:g}),i==="email"&&!h&&e.jsx(z,{className:"focus:shadow-outline hover:transparent w-full rounded-md bg-[#19b2f6]/80  px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:f,disabled:f,onClick:()=>b(!0),children:"Submit"})]})]})]})]})})})},re=u=>{const{title:r,label:d,buttonName:g,isOpen:E,onClose:j}=u,[k,v]=n.useState([]),[w,i]=n.useState([]);async function f(){i(!0);try{const t=await y.callRawAPI("/v3/api/custom/voiceoutreach/user/calendar/events",{},"GET");v(t==null?void 0:t.list)}catch(t){console.log("Error",t),U(dispatch,t.response.data.message?t.response.data.message:t.message)}i(!1)}return F.useEffect(()=>{f()},[]),e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${E?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg  bg-[#1d2937] px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-white",children:r}),e.jsx("button",{className:"text-gray-500 hover:text-white focus:outline-none",onClick:j,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"events",className:"mb-1 block text-sm font-medium text-white",children:d}),w?"Fetching Events":"",e.jsx("ul",{children:k.map(t=>e.jsxs("div",{children:[e.jsxs("li",{children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Event:"})," ",t.summary]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Start:"})," ",new Date(t.start.dateTime||t.start.date).toLocaleString()]}),e.jsxs("div",{children:[e.jsx("strong",{children:"End:"})," ",new Date(t.end.dateTime||t.end.date).toLocaleString()]})]}),e.jsx("br",{})]},t.id))})]})]})]})})})};export{B as EditInfoModal,re as EventModal,Ie as default};
