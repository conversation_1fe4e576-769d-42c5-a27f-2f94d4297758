import{R as u}from"../vendor-2ae44a2e.js";import{P as i}from"../@fortawesome/react-fontawesome-27c5bed3.js";function M(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable})),t.push.apply(t,n)}return t}function _(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?M(Object(t),!0).forEach(function(n){K(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):M(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function w(r){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?w=function(e){return typeof e}:w=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(r)}function K(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Y(r,e){return oe(r)||ae(r,e)||ue(r,e)||ie()}function oe(r){if(Array.isArray(r))return r}function ae(r,e){var t=r&&(typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"]);if(t!=null){var n=[],s=!0,a=!1,f,m;try{for(t=t.call(r);!(s=(f=t.next()).done)&&(n.push(f.value),!(e&&n.length===e));s=!0);}catch(o){a=!0,m=o}finally{try{!s&&t.return!=null&&t.return()}finally{if(a)throw m}}return n}}function ue(r,e){if(r){if(typeof r=="string")return W(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return W(r,e)}}function W(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function ie(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var v=function(e,t,n){var s=!!n,a=u.useRef(n);u.useEffect(function(){a.current=n},[n]),u.useEffect(function(){if(!s||!e)return function(){};var f=function(){a.current&&a.current.apply(a,arguments)};return e.on(t,f),function(){e.off(t,f)}},[s,t,e,a])},I=function(e){var t=u.useRef(e);return u.useEffect(function(){t.current=e},[e]),t.current},O=function(e){return e!==null&&w(e)==="object"},se=function(e){return O(e)&&typeof e.then=="function"},ce=function(e){return O(e)&&typeof e.elements=="function"&&typeof e.createToken=="function"&&typeof e.createPaymentMethod=="function"&&typeof e.confirmCardPayment=="function"},q="[object Object]",le=function r(e,t){if(!O(e)||!O(t))return e===t;var n=Array.isArray(e),s=Array.isArray(t);if(n!==s)return!1;var a=Object.prototype.toString.call(e)===q,f=Object.prototype.toString.call(t)===q;if(a!==f)return!1;if(!a&&!n)return e===t;var m=Object.keys(e),o=Object.keys(t);if(m.length!==o.length)return!1;for(var C={},y=0;y<m.length;y+=1)C[m[y]]=!0;for(var g=0;g<o.length;g+=1)C[o[g]]=!0;var p=Object.keys(C);if(p.length!==m.length)return!1;var k=e,E=t,S=function(P){return r(k[P],E[P])};return p.every(S)},F=function(e,t,n){return O(e)?Object.keys(e).reduce(function(s,a){var f=!O(t)||!le(e[a],t[a]);return n.includes(a)?(f&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),s):f?_(_({},s||{}),{},K({},a,e[a])):s},null):null},J="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",$=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:J;if(e===null||ce(e))return e;throw new Error(t)},fe=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:J;if(se(e))return{tag:"async",stripePromise:Promise.resolve(e).then(function(s){return $(s,t)})};var n=$(e,t);return n===null?{tag:"empty"}:{tag:"sync",stripe:n}},pe=function(e){!e||!e._registerWrapper||!e.registerAppInfo||(e._registerWrapper({name:"react-stripe-js",version:"2.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"2.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},L=u.createContext(null);L.displayName="ElementsContext";var de=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},me=function(e){var t=e.stripe,n=e.options,s=e.children,a=u.useMemo(function(){return fe(t)},[t]),f=u.useState(function(){return{stripe:a.tag==="sync"?a.stripe:null,elements:a.tag==="sync"?a.stripe.elements(n):null}}),m=Y(f,2),o=m[0],C=m[1];u.useEffect(function(){var p=!0,k=function(S){C(function(x){return x.stripe?x:{stripe:S,elements:S.elements(n)}})};return a.tag==="async"&&!o.stripe?a.stripePromise.then(function(E){E&&p&&k(E)}):a.tag==="sync"&&!o.stripe&&k(a.stripe),function(){p=!1}},[a,o,n]);var y=I(t);u.useEffect(function(){y!==null&&y!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[y,t]);var g=I(n);return u.useEffect(function(){if(o.elements){var p=F(n,g,["clientSecret","fonts"]);p&&o.elements.update(p)}},[n,g,o.elements]),u.useEffect(function(){pe(o.stripe)},[o.stripe]),u.createElement(L.Provider,{value:o},s)};me.propTypes={stripe:i.any,options:i.object};i.func.isRequired;var z=u.createContext(null);z.displayName="CustomCheckoutSdkContext";var ve=function(e,t){if(!e)throw new Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},ye=u.createContext(null);ye.displayName="CustomCheckoutContext";i.any,i.shape({clientSecret:i.string.isRequired,elementsOptions:i.object}).isRequired;var D=function(e){var t=u.useContext(z),n=u.useContext(L);if(t&&n)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return t?ve(t,e):de(n,e)},he=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},c=function(e,t){var n="".concat(he(e),"Element"),s=function(o){var C=o.id,y=o.className,g=o.options,p=g===void 0?{}:g,k=o.onBlur,E=o.onFocus,S=o.onReady,x=o.onChange,P=o.onEscape,H=o.onClick,V=o.onLoadError,G=o.onLoaderStart,Q=o.onNetworksChange,X=o.onConfirm,Z=o.onCancel,ee=o.onShippingAddressChange,te=o.onShippingRateChange,A=D("mounts <".concat(n,">")),j="elements"in A?A.elements:null,R="customCheckoutSdk"in A?A.customCheckoutSdk:null,re=u.useState(null),U=Y(re,2),d=U[0],ne=U[1],b=u.useRef(null),N=u.useRef(null);v(d,"blur",k),v(d,"focus",E),v(d,"escape",P),v(d,"click",H),v(d,"loaderror",V),v(d,"loaderstart",G),v(d,"networkschange",Q),v(d,"confirm",X),v(d,"cancel",Z),v(d,"shippingaddresschange",ee),v(d,"shippingratechange",te),v(d,"change",x);var T;S&&(e==="expressCheckout"?T=S:T=function(){S(d)}),v(d,"ready",T),u.useLayoutEffect(function(){if(b.current===null&&N.current!==null&&(j||R)){var h=null;R?h=R.createElement(e,p):j&&(h=j.create(e,p)),b.current=h,ne(h),h&&h.mount(N.current)}},[j,R,p]);var B=I(p);return u.useEffect(function(){if(b.current){var h=F(p,B,["paymentRequest"]);h&&b.current.update(h)}},[p,B]),u.useLayoutEffect(function(){return function(){if(b.current&&typeof b.current.destroy=="function")try{b.current.destroy(),b.current=null}catch{}}},[]),u.createElement("div",{id:C,className:y,ref:N})},a=function(o){D("mounts <".concat(n,">"));var C=o.id,y=o.className;return u.createElement("div",{id:C,className:y})},f=t?a:s;return f.propTypes={id:i.string,className:i.string,onChange:i.func,onBlur:i.func,onFocus:i.func,onReady:i.func,onEscape:i.func,onClick:i.func,onLoadError:i.func,onLoaderStart:i.func,onNetworksChange:i.func,onConfirm:i.func,onCancel:i.func,onShippingAddressChange:i.func,onShippingRateChange:i.func,options:i.object},f.displayName=n,f.__elementType=e,f},l=typeof window>"u",Ce=u.createContext(null);Ce.displayName="EmbeddedCheckoutProviderContext";c("auBankAccount",l);c("card",l);c("cardNumber",l);c("cardExpiry",l);c("cardCvc",l);c("fpxBank",l);c("iban",l);c("idealBank",l);c("p24Bank",l);c("epsBank",l);c("payment",l);c("expressCheckout",l);c("paymentRequestButton",l);c("linkAuthentication",l);c("address",l);c("shippingAddress",l);c("paymentMethodMessaging",l);c("affirmMessage",l);c("afterpayClearpayMessage",l);export{me as E};
