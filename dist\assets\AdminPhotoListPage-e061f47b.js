import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as ee}from"./vendor-2ae44a2e.js";import{M as te,A as se,G as E,B as ae,a as re,b as ie,R as oe,c as ne,t as le,g as y,s as ce}from"./index-d0a8f5da.js";import{o as de}from"./yup-5abd4662.js";import{c as pe,a as b}from"./yup-c2e87575.js";import{u as ue}from"./react-hook-form-47c010f8.js";import{P as me}from"./index-132fbad2.js";import{S as xe}from"./react-loading-skeleton-f53ed7d1.js";import{M as he}from"./index-9aa09a5c.js";import ge from"./AddAdminPhotoPage-3b123d51.js";import{A as fe}from"./index-e429b426.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";import"./@uppy/xhr-upload-502ff6d3.js";import"./@uppy/aws-s3-a38c5234.js";import"./@craftjs/core-9da1c17f.js";import"./@uppy/aws-s3-multipart-4b7e97ea.js";import"./@uppy/react-66cc10ac.js";import"./@uppy/core-a7fbc19c.js";import"./@uppy/compressor-d7e7d557.js";import"./@fullcalendar/core-a789a586.js";import"./@uppy/dashboard-af41baed.js";import"./@uppy/drag-drop-aa7c4730.js";import"./@uppy/progress-bar-3613651c.js";import"./@uppy/file-input-ca192629.js";let p=new te;const F=[{header:"Photos",accessor:"url"},{header:"Create at",accessor:"create_at"},{header:"Action",accessor:""}],je=[{header:"Date",accessor:"create_at"},{header:"ID",accessor:"id"},{header:"User ID",accessor:"user_id"},{header:"Action",accessor:""}],ct=()=>{const{dispatch:R}=a.useContext(se),{dispatch:w}=a.useContext(E);a.useState("");const[u,T]=a.useState([]),[m,v]=a.useState(3),[S,_]=a.useState(0),[ye,I]=a.useState(0),[l,O]=a.useState(0),[$,q]=a.useState(!1),[B,M]=a.useState(!1),[z,N]=a.useState(!1),[L,x]=a.useState(!1),[C,G]=a.useState(!1),[P,A]=a.useState(!1),[o,h]=a.useState([]),[be,g]=a.useState([]),[we,H]=a.useState(""),[K,U]=a.useState("eq");ee(),a.useContext(E);const V=pe({date:b(),id:b(),user_id:b()}),{register:ve,handleSubmit:J,setError:Se,formState:{errors:Ne}}=ue({resolver:de(V)});function Q(t){(async function(){v(t),await n(0,t)})()}function W(){(async function(){await n(l-1>0?l-1:0,m)})()}function X(){(async function(){await n(l+1<=S?l+1:0,m)})()}async function n(t,r,s){try{p.setTable("photo");const i=await p.callRestAPI({payload:{...s},page:t,limit:r},"PAGINATE"),{list:c,total:f,limit:D,num_pages:d,page:j}=i;T(c),v(D),_(d),O(j),I(f),q(j>1),M(j+1<=d)}catch(i){console.log("ERROR",i),le(R,i.message)}}const k=(t,r,s)=>{const i=r==="eq"&&isNaN(s)?`"${s}"`:s,c=`${t},${r},${i}`;g(f=>[...f.filter(d=>!d.includes(t)),c]),H(s)},Y=t=>{let r=y(t.date),s=y(t.id),i=y(t.user_id);n(0,50,{create_at:r,id:s,user_id:i})};a.useEffect(()=>{w({type:"SETPATH",payload:{path:"photos"}}),async function(){N(!0),await n(0,50),N(!1)}()},[]);async function Z(t){p.setTable("photo"),await p.callRestAPI({id:t},"DELETE"),ce(w,"Deleted"),await n(0,50)}return console.log("data",u),e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3 text-black",children:[e.jsxs("form",{className:"relative rounded bg-white",onSubmit:J(Y),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>G(!C),children:[e.jsx(ae,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white",children:o.length>0?o.length:null})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(re,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var r;return k("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(ie,{className:"text-lg text-gray-200"})]})]}),C&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-6 shadow-lg",children:[o==null?void 0:o.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:s=>{U(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>k(t,K,s.target.value)}),e.jsx(oe,{className:" cursor-pointer text-2xl",onClick:()=>{h(s=>s.filter(i=>i!==t)),g(s=>s.filter(i=>!i.includes(t)))}})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{A(!P)},children:[e.jsx(ne,{}),"Add filter"]}),P&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:je.slice(0,-1).map(t=>e.jsx("li",{className:`${o.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{o.includes(t.header)||h(r=>[...r,t.header]),A(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("div",{onClick:()=>{h([]),g([])},className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]}),e.jsx(fe,{onClick:()=>x(!0)})]}),e.jsx("div",{children:e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 text-black",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:F.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsxs("tbody",{className:"divide-y divide-gray-200 bg-white",children:[u.length==0?e.jsx("tr",{children:e.jsx("td",{colSpan:3,children:z&&e.jsx(xe,{count:4})})}):null,u.map((t,r)=>e.jsx("tr",{children:F.map((s,i)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-xs text-red-400",onClick:()=>{Z(t.id)},children:[" ","Delete"]})},i):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.accessor=="url"?e.jsx("img",{width:200,height:200,src:t==null?void 0:t.url}):t[s.accessor]},i))},r))]})]})})}),e.jsx(me,{currentPage:l,pageCount:S,pageSize:m,canPreviousPage:$,canNextPage:B,updatePageSize:Q,previousPage:W,nextPage:X}),e.jsx(he,{isModalActive:L,closeModalFn:()=>x(!1),children:e.jsx(ge,{setSidebar:x})})]})};export{ct as default};
