import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import { AddButton } from "Components/AddButton";
import { SkeletonLoader } from "Components/Skeleton";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { ModalSidebar } from "Components/ModalSidebar";
import { AddAdminUserPage, EditAdminUserPage } from "Src/routes/LazyLoad";
// import { AddAdminStripePricePage, EditAdminStripePricePage } from "Src/routes/LazyLoad";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Stripe Id",
    accessor: "stripe_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Price Id",
    accessor: "price_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "User Id",
    accessor: "user_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Object",
    accessor: "object",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Is Lifetime",
    accessor: "is_lifetime",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Update At",
    accessor: "update_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Action",
    accessor: "",
  },
];

const StripeSubscriptionListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [query, setQuery] = React.useState("");
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();

  const navigate = useNavigate();

  const schema = yup.object({
    stripe_id: yup.string(),
    name: yup.string(),
    status: yup.string(),
    product_name: yup.string(),
    amount: yup.string(),
    type: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectStatus = [
    { key: "", value: "All" },
    { key: 0, value: "Archived" },
    { key: 1, value: "Active" },
  ];
  const typeStatus = [
    { key: "", value: "All" },
    { key: "one_time", value: "One time" },
    { key: "recurring", value: "Recurring" },
  ];

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(1, limit);
    })();
  }
  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 1 ? currentPage - 1 : 1, pageSize);
    })();
  }

  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 1,
        pageSize
      );
    })();
  }

  async function getData(pageNum, limitNum, data) {
    try {
      setLoading(true);
      const result = await sdk.getStripeSubscriptions(
        { page: pageNum, limit: limitNum },
        data
      );
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(+limit);
      setPageCount(+num_pages);
      setPage(+page);
      setDataTotal(+total);
      setCanPreviousPage(+page > 1);
      setCanNextPage(+page + 1 <= +num_pages);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
    setLoading(false);
  }

  const onSubmit = (data) => {
    const stripe_id = getNonNullValue(data.stripe_id);
    const product_name = getNonNullValue(data.product_name);
    const name = getNonNullValue(data.name);
    const amount = getNonNullValue(data.amount);
    const type = getNonNullValue(data.type);
    const status = getNonNullValue(data.status);
    getData(1, pageSize, {
      stripe_id,
      product_name,
      name,
      amount,
      type,
      status,
    });
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "prices",
      },
    });

    (async function () {
      await getData(1, pageSize);
    })();
  }, []);

  return (
    <div className="px-8">
      <div className="flex items-center justify-between py-3 text-black">
        <form
          className="mb-10 rounded bg-white p-5 shadow"
          onSubmit={handleSubmit(onSubmit)}
        >
          <h4 className="text-2xl font-medium">Search</h4>
          <div className="filter-form-holder mt-10 flex flex-wrap">
            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Stripe Id
              </label>
              <input
                type="text"
                placeholder="Stripe Id"
                {...register("stripe_id")}
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              />
              <p className="text-xs italic text-red-500">
                {errors.stripe_id?.message}
              </p>
            </div>

            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Product
              </label>
              <input
                type="text"
                placeholder="Product Name"
                {...register("product_name")}
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              />
              <p className="text-xs italic text-red-500">
                {errors.product_name?.message}
              </p>
            </div>

            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Name
              </label>
              <input
                type="text"
                placeholder="Name"
                {...register("name")}
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              />
              <p className="text-xs italic text-red-500">
                {errors.name?.message}
              </p>
            </div>

            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Amount
              </label>
              <input
                type="number"
                placeholder="Amount"
                {...register("amount")}
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              />
              <p className="text-xs italic text-red-500">
                {errors.amount?.message}
              </p>
            </div>

            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Type
              </label>
              <select
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                {...register("type")}
              >
                {typeStatus.map((option) => (
                  <option value={option.key} key={option.key} defaultValue="">
                    {option.value}
                  </option>
                ))}
              </select>
              <p className="text-xs italic text-red-500"></p>
            </div>

            <div className="mb-4 w-full pl-2 pr-2 md:w-1/2">
              <label className="mb-2 block text-sm font-bold text-gray-700">
                Status
              </label>
              <select
                className="focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                {...register("status")}
              >
                {selectStatus.map((option) => (
                  <option value={option.key} key={option.key} defaultValue="">
                    {option.value}
                  </option>
                ))}
              </select>
              <p className="text-xs italic text-red-500"></p>
            </div>
          </div>
          <div className="search-buttons pl-2">
            <button
              type="submit"
              className="mr-2 inline-block rounded bg-[#2cc9d5] px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-[#2cc9d5]/70 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg"
            >
              Search
            </button>

            <button
              type="reset"
              onClick={() => getData(1, pageSize)}
              className="inline-block rounded bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-500 hover:shadow-lg focus:bg-[red]/70 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-[red]/70 active:shadow-lg"
            >
              Reset
            </button>
          </div>
        </form>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto border-b border-gray-200 shadow">
          <table className="min-w-full divide-y divide-gray-200 text-black">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {data.map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            <button
                              className="text-[black]"
                              onClick={() => {
                                setActiveEditId(row.id);
                                setShowEditSidebar(true);
                                // navigate("/admin/edit-user/" + row.id, {
                                //   state: row,
                                // });
                              }}
                            >
                              {" "}
                              Edit
                            </button>
                          </td>
                        );
                      }
                      if (cell.mapping && cell.accessor === "status") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4 text-sm"
                          >
                            {row[cell.accessor] === 1 ? (
                              <span className="rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]">
                                {cell.mapping[row[cell.accessor]]}
                              </span>
                            ) : (
                              <span className="rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]">
                                {cell.mapping[row[cell.accessor]]}
                              </span>
                            )}
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="whitespace-nowrap px-6 py-4">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
          {loading && (
            <>
              <p className="px-10 py-3 text-xl capitalize">Loading...</p>
            </>
          )}
          {!loading && data.length === 0 && (
            <>
              <p className="px-10 py-3 text-xl capitalize">
                You Don't have any Subscriptions
              </p>
            </>
          )}
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={updatePageSize}
        previousPage={previousPage}
        nextPage={nextPage}
      />
    </div>
  );
};

export default StripeSubscriptionListPage;
