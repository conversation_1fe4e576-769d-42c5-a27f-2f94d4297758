var A,f,ie,S,w,G,le,j={},ue=[],we=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function k(e,_){for(var t in _)e[t]=_[t];return e}function ce(e){var _=e.parentNode;_&&_.removeChild(e)}function xe(e,_,t){var r,i,n,u={};for(n in _)n=="key"?r=_[n]:n=="ref"?i=_[n]:u[n]=_[n];if(arguments.length>2&&(u.children=arguments.length>3?A.call(arguments,2):t),typeof e=="function"&&e.defaultProps!=null)for(n in e.defaultProps)u[n]===void 0&&(u[n]=e.defaultProps[n]);return E(e,u,r,i,null)}function E(e,_,t,r,i){var n={type:e,props:_,key:t,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++ie};return i==null&&f.vnode!=null&&f.vnode(n),n}function Te(){return{current:null}}function V(e){return e.children}function Se(e,_,t,r,i){var n;for(n in t)n==="children"||n==="key"||n in _||L(e,n,null,t[n],r);for(n in _)i&&typeof _[n]!="function"||n==="children"||n==="key"||n==="value"||n==="checked"||t[n]===_[n]||L(e,n,_[n],t[n],r)}function J(e,_,t){_[0]==="-"?e.setProperty(_,t??""):e[_]=t==null?"":typeof t!="number"||we.test(_)?t:t+"px"}function L(e,_,t,r,i){var n;e:if(_==="style")if(typeof t=="string")e.style.cssText=t;else{if(typeof r=="string"&&(e.style.cssText=r=""),r)for(_ in r)t&&_ in t||J(e.style,_,"");if(t)for(_ in t)r&&t[_]===r[_]||J(e.style,_,t[_])}else if(_[0]==="o"&&_[1]==="n")n=_!==(_=_.replace(/Capture$/,"")),_=_.toLowerCase()in e?_.toLowerCase().slice(2):_.slice(2),e.l||(e.l={}),e.l[_+n]=t,t?r||e.addEventListener(_,n?Q:K,n):e.removeEventListener(_,n?Q:K,n);else if(_!=="dangerouslySetInnerHTML"){if(i)_=_.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(_!=="width"&&_!=="height"&&_!=="href"&&_!=="list"&&_!=="form"&&_!=="tabIndex"&&_!=="download"&&_ in e)try{e[_]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&_.indexOf("-")==-1?e.removeAttribute(_):e.setAttribute(_,t))}}function K(e){S=!0;try{return this.l[e.type+!1](f.event?f.event(e):e)}finally{S=!1}}function Q(e){S=!0;try{return this.l[e.type+!0](f.event?f.event(e):e)}finally{S=!1}}function F(e,_){this.props=e,this.context=_}function H(e,_){if(_==null)return e.__?H(e.__,e.__.__k.indexOf(e)+1):null;for(var t;_<e.__k.length;_++)if((t=e.__k[_])!=null&&t.__e!=null)return t.__e;return typeof e.type=="function"?H(e):null}function fe(e){var _,t;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,_=0;_<e.__k.length;_++)if((t=e.__k[_])!=null&&t.__e!=null){e.__e=e.__c.base=t.__e;break}return fe(e)}}function $e(e){S?setTimeout(e):le(e)}function X(e){(!e.__d&&(e.__d=!0)&&w.push(e)&&!M.__r++||G!==f.debounceRendering)&&((G=f.debounceRendering)||$e)(M)}function M(){var e,_,t,r,i,n,u,a;for(w.sort(function(s,l){return s.__v.__b-l.__v.__b});e=w.shift();)e.__d&&(_=w.length,r=void 0,i=void 0,u=(n=(t=e).__v).__e,(a=t.__P)&&(r=[],(i=k({},n)).__v=n.__v+1,B(a,n,i,t.__n,a.ownerSVGElement!==void 0,n.__h!=null?[u]:null,r,u??H(n),n.__h),de(r,n),n.__e!=u&&fe(n)),w.length>_&&w.sort(function(s,l){return s.__v.__b-l.__v.__b}));M.__r=0}function ae(e,_,t,r,i,n,u,a,s,l){var o,d,p,c,h,x,y,m=r&&r.__k||ue,b=m.length;for(t.__k=[],o=0;o<_.length;o++)if((c=t.__k[o]=(c=_[o])==null||typeof c=="boolean"?null:typeof c=="string"||typeof c=="number"||typeof c=="bigint"?E(null,c,null,null,c):Array.isArray(c)?E(V,{children:c},null,null,null):c.__b>0?E(c.type,c.props,c.key,c.ref?c.ref:null,c.__v):c)!=null){if(c.__=t,c.__b=t.__b+1,(p=m[o])===null||p&&c.key==p.key&&c.type===p.type)m[o]=void 0;else for(d=0;d<b;d++){if((p=m[d])&&c.key==p.key&&c.type===p.type){m[d]=void 0;break}p=null}B(e,c,p=p||j,i,n,u,a,s,l),h=c.__e,(d=c.ref)&&p.ref!=d&&(y||(y=[]),p.ref&&y.push(p.ref,null,c),y.push(d,c.__c||h,c)),h!=null?(x==null&&(x=h),typeof c.type=="function"&&c.__k===p.__k?c.__d=s=se(c,s,e):s=pe(e,c,p,m,h,s),typeof t.type=="function"&&(t.__d=s)):s&&p.__e==s&&s.parentNode!=e&&(s=H(p))}for(t.__e=x,o=b;o--;)m[o]!=null&&(typeof t.type=="function"&&m[o].__e!=null&&m[o].__e==t.__d&&(t.__d=he(r).nextSibling),ve(m[o],m[o]));if(y)for(o=0;o<y.length;o++)ye(y[o],y[++o],y[++o])}function se(e,_,t){for(var r,i=e.__k,n=0;i&&n<i.length;n++)(r=i[n])&&(r.__=e,_=typeof r.type=="function"?se(r,_,t):pe(t,r,r,i,r.__e,_));return _}function Ee(e,_){return _=_||[],e==null||typeof e=="boolean"||(Array.isArray(e)?e.some(function(t){Ee(t,_)}):_.push(e)),_}function pe(e,_,t,r,i,n){var u,a,s;if(_.__d!==void 0)u=_.__d,_.__d=void 0;else if(t==null||i!=n||i.parentNode==null)e:if(n==null||n.parentNode!==e)e.appendChild(i),u=null;else{for(a=n,s=0;(a=a.nextSibling)&&s<r.length;s+=1)if(a==i)break e;e.insertBefore(i,n),u=n}return u!==void 0?u:i.nextSibling}function he(e){var _,t,r;if(e.type==null||typeof e.type=="string")return e.__e;if(e.__k){for(_=e.__k.length-1;_>=0;_--)if((t=e.__k[_])&&(r=he(t)))return r}return null}function B(e,_,t,r,i,n,u,a,s){var l,o,d,p,c,h,x,y,m,b,P,$,z,C,T,g=_.type;if(_.constructor!==void 0)return null;t.__h!=null&&(s=t.__h,a=_.__e=t.__e,_.__h=null,n=[a]),(l=f.__b)&&l(_);try{e:if(typeof g=="function"){if(y=_.props,m=(l=g.contextType)&&r[l.__c],b=l?m?m.props.value:l.__:r,t.__c?x=(o=_.__c=t.__c).__=o.__E:("prototype"in g&&g.prototype.render?_.__c=o=new g(y,b):(_.__c=o=new F(y,b),o.constructor=g,o.render=Ne),m&&m.sub(o),o.props=y,o.state||(o.state={}),o.context=b,o.__n=r,d=o.__d=!0,o.__h=[],o._sb=[]),o.__s==null&&(o.__s=o.state),g.getDerivedStateFromProps!=null&&(o.__s==o.state&&(o.__s=k({},o.__s)),k(o.__s,g.getDerivedStateFromProps(y,o.__s))),p=o.props,c=o.state,o.__v=_,d)g.getDerivedStateFromProps==null&&o.componentWillMount!=null&&o.componentWillMount(),o.componentDidMount!=null&&o.__h.push(o.componentDidMount);else{if(g.getDerivedStateFromProps==null&&y!==p&&o.componentWillReceiveProps!=null&&o.componentWillReceiveProps(y,b),!o.__e&&o.shouldComponentUpdate!=null&&o.shouldComponentUpdate(y,o.__s,b)===!1||_.__v===t.__v){for(_.__v!==t.__v&&(o.props=y,o.state=o.__s,o.__d=!1),_.__e=t.__e,_.__k=t.__k,_.__k.forEach(function(O){O&&(O.__=_)}),P=0;P<o._sb.length;P++)o.__h.push(o._sb[P]);o._sb=[],o.__h.length&&u.push(o);break e}o.componentWillUpdate!=null&&o.componentWillUpdate(y,o.__s,b),o.componentDidUpdate!=null&&o.__h.push(function(){o.componentDidUpdate(p,c,h)})}if(o.context=b,o.props=y,o.__P=e,$=f.__r,z=0,"prototype"in g&&g.prototype.render){for(o.state=o.__s,o.__d=!1,$&&$(_),l=o.render(o.props,o.state,o.context),C=0;C<o._sb.length;C++)o.__h.push(o._sb[C]);o._sb=[]}else do o.__d=!1,$&&$(_),l=o.render(o.props,o.state,o.context),o.state=o.__s;while(o.__d&&++z<25);o.state=o.__s,o.getChildContext!=null&&(r=k(k({},r),o.getChildContext())),d||o.getSnapshotBeforeUpdate==null||(h=o.getSnapshotBeforeUpdate(p,c)),T=l!=null&&l.type===V&&l.key==null?l.props.children:l,ae(e,Array.isArray(T)?T:[T],_,t,r,i,n,u,a,s),o.base=_.__e,_.__h=null,o.__h.length&&u.push(o),x&&(o.__E=o.__=null),o.__e=!1}else n==null&&_.__v===t.__v?(_.__k=t.__k,_.__e=t.__e):_.__e=He(t.__e,_,t,r,i,n,u,s);(l=f.diffed)&&l(_)}catch(O){_.__v=null,(s||n!=null)&&(_.__e=a,_.__h=!!s,n[n.indexOf(a)]=null),f.__e(O,_,t)}}function de(e,_){f.__c&&f.__c(_,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(r){r.call(t)})}catch(r){f.__e(r,t.__v)}})}function He(e,_,t,r,i,n,u,a){var s,l,o,d=t.props,p=_.props,c=_.type,h=0;if(c==="svg"&&(i=!0),n!=null){for(;h<n.length;h++)if((s=n[h])&&"setAttribute"in s==!!c&&(c?s.localName===c:s.nodeType===3)){e=s,n[h]=null;break}}if(e==null){if(c===null)return document.createTextNode(p);e=i?document.createElementNS("http://www.w3.org/2000/svg",c):document.createElement(c,p.is&&p),n=null,a=!1}if(c===null)d===p||a&&e.data===p||(e.data=p);else{if(n=n&&A.call(e.childNodes),l=(d=t.props||j).dangerouslySetInnerHTML,o=p.dangerouslySetInnerHTML,!a){if(n!=null)for(d={},h=0;h<e.attributes.length;h++)d[e.attributes[h].name]=e.attributes[h].value;(o||l)&&(o&&(l&&o.__html==l.__html||o.__html===e.innerHTML)||(e.innerHTML=o&&o.__html||""))}if(Se(e,p,d,i,a),o)_.__k=[];else if(h=_.props.children,ae(e,Array.isArray(h)?h:[h],_,t,r,i&&c!=="foreignObject",n,u,n?n[0]:t.__k&&H(t,0),a),n!=null)for(h=n.length;h--;)n[h]!=null&&ce(n[h]);a||("value"in p&&(h=p.value)!==void 0&&(h!==e.value||c==="progress"&&!h||c==="option"&&h!==d.value)&&L(e,"value",h,d.value,!1),"checked"in p&&(h=p.checked)!==void 0&&h!==e.checked&&L(e,"checked",h,d.checked,!1))}return e}function ye(e,_,t){try{typeof e=="function"?e(_):e.current=_}catch(r){f.__e(r,t)}}function ve(e,_,t){var r,i;if(f.unmount&&f.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||ye(r,null,_)),(r=e.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(n){f.__e(n,_)}r.base=r.__P=null,e.__c=void 0}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&ve(r[i],_,t||typeof e.type!="function");t||e.__e==null||ce(e.__e),e.__=e.__e=e.__d=void 0}function Ne(e,_,t){return this.constructor(e,t)}function Oe(e,_,t){var r,i,n;f.__&&f.__(e,_),i=(r=typeof t=="function")?null:t&&t.__k||_.__k,n=[],B(_,e=(!r&&t||_).__k=xe(V,null,[e]),i||j,j,_.ownerSVGElement!==void 0,!r&&t?[t]:i?null:_.firstChild?A.call(_.childNodes):null,n,!r&&t?t:i?i.__e:_.firstChild,r),de(n,e)}function Fe(e,_,t){var r,i,n,u=k({},e.props);for(n in _)n=="key"?r=_[n]:n=="ref"?i=_[n]:u[n]=_[n];return arguments.length>2&&(u.children=arguments.length>3?A.call(arguments,2):t),E(e.type,u,r||e.key,i||e.ref,null)}A=ue.slice,f={__e:function(e,_,t,r){for(var i,n,u;_=_.__;)if((i=_.__c)&&!i.__)try{if((n=i.constructor)&&n.getDerivedStateFromError!=null&&(i.setState(n.getDerivedStateFromError(e)),u=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(e,r||{}),u=i.__d),u)return i.__E=i}catch(a){e=a}throw e}},ie=0,S=!1,F.prototype.setState=function(e,_){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=k({},this.state),typeof e=="function"&&(e=e(k({},t),this.props)),e&&k(t,e),e!=null&&this.__v&&(_&&this._sb.push(_),X(this))},F.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),X(this))},F.prototype.render=V,w=[],le=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,M.__r=0;var W,v,I,Y,N=0,me=[],D=[],Z=f.__b,ee=f.__r,_e=f.diffed,te=f.__c,ne=f.unmount;function R(e,_){f.__h&&f.__h(v,e,N||_),N=0;var t=v.__H||(v.__H={__:[],__h:[]});return e>=t.__.length&&t.__.push({__V:D}),t.__[e]}function De(e){return N=1,Ae(ke,e)}function Ae(e,_,t){var r=R(W++,2);if(r.t=e,!r.__c&&(r.__=[t?t(_):ke(void 0,_),function(n){var u=r.__N?r.__N[0]:r.__[0],a=r.t(u,n);u!==a&&(r.__N=[a,r.__[1]],r.__c.setState({}))}],r.__c=v,!v.u)){v.u=!0;var i=v.shouldComponentUpdate;v.shouldComponentUpdate=function(n,u,a){if(!r.__c.__H)return!0;var s=r.__c.__H.__.filter(function(o){return o.__c});if(s.every(function(o){return!o.__N}))return!i||i.call(this,n,u,a);var l=!1;return s.forEach(function(o){if(o.__N){var d=o.__[0];o.__=o.__N,o.__N=void 0,d!==o.__[0]&&(l=!0)}}),!(!l&&r.__c.props===n)&&(!i||i.call(this,n,u,a))}}return r.__N||r.__}function Ue(e,_){var t=R(W++,3);!f.__s&&be(t.__H,_)&&(t.__=e,t.i=_,v.__H.__h.push(t))}function je(e){return N=5,ge(function(){return{current:e}},[])}function ge(e,_){var t=R(W++,7);return be(t.__H,_)?(t.__V=e(),t.i=_,t.__h=e,t.__V):t.__}function Le(e,_){return N=8,ge(function(){return e},_)}function Pe(){for(var e;e=me.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(U),e.__H.__h.forEach(q),e.__H.__h=[]}catch(_){e.__H.__h=[],f.__e(_,e.__v)}}f.__b=function(e){v=null,Z&&Z(e)},f.__r=function(e){ee&&ee(e),W=0;var _=(v=e.__c).__H;_&&(I===v?(_.__h=[],v.__h=[],_.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=D,t.__N=t.i=void 0})):(_.__h.forEach(U),_.__h.forEach(q),_.__h=[])),I=v},f.diffed=function(e){_e&&_e(e);var _=e.__c;_&&_.__H&&(_.__H.__h.length&&(me.push(_)!==1&&Y===f.requestAnimationFrame||((Y=f.requestAnimationFrame)||Ce)(Pe)),_.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==D&&(t.__=t.__V),t.i=void 0,t.__V=D})),I=v=null},f.__c=function(e,_){_.some(function(t){try{t.__h.forEach(U),t.__h=t.__h.filter(function(r){return!r.__||q(r)})}catch(r){_.some(function(i){i.__h&&(i.__h=[])}),_=[],f.__e(r,t.__v)}}),te&&te(e,_)},f.unmount=function(e){ne&&ne(e);var _,t=e.__c;t&&t.__H&&(t.__H.__.forEach(function(r){try{U(r)}catch(i){_=i}}),t.__H=void 0,_&&f.__e(_,t.__v))};var re=typeof requestAnimationFrame=="function";function Ce(e){var _,t=function(){clearTimeout(r),re&&cancelAnimationFrame(_),setTimeout(e)},r=setTimeout(t,100);re&&(_=requestAnimationFrame(t))}function U(e){var _=v,t=e.__c;typeof t=="function"&&(e.__c=void 0,t()),v=_}function q(e){var _=v;e.__c=e.__(),v=_}function be(e,_){return!e||e.length!==_.length||_.some(function(t,r){return t!==e[r]})}function ke(e,_){return typeof _=="function"?_(e):_}var oe=function(){return oe=Object.assign||function(_){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(_[n]=t[n])}return _},oe.apply(this,arguments)};function Me(e,_){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&_.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)_.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(t[r[i]]=e[r[i]]);return t}function Ve(e,_,t,r){function i(n){return n instanceof t?n:new t(function(u){u(n)})}return new(t||(t=Promise))(function(n,u){function a(o){try{l(r.next(o))}catch(d){u(d)}}function s(o){try{l(r.throw(o))}catch(d){u(d)}}function l(o){o.done?n(o.value):i(o.value).then(a,s)}l((r=r.apply(e,_||[])).next())})}function We(e,_){var t={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},r,i,n,u;return u={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function a(l){return function(o){return s([l,o])}}function s(l){if(r)throw new TypeError("Generator is already executing.");for(;u&&(u=0,l[0]&&(t=0)),t;)try{if(r=1,i&&(n=l[0]&2?i.return:l[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,l[1])).done)return n;switch(i=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return t.label++,{value:l[1],done:!1};case 5:t.label++,i=l[1],l=[0];continue;case 7:l=t.ops.pop(),t.trys.pop();continue;default:if(n=t.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){t=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){t.label=l[1];break}if(l[0]===6&&t.label<n[1]){t.label=n[1],n=l;break}if(n&&t.label<n[2]){t.label=n[2],t.ops.push(l);break}n[2]&&t.ops.pop(),t.trys.pop();continue}l=_.call(e,t)}catch(o){l=[6,o],i=0}finally{r=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function Ie(e,_){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),i,n=[],u;try{for(;(_===void 0||_-- >0)&&!(i=r.next()).done;)n.push(i.value)}catch(a){u={error:a}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(u)throw u.error}}return n}function qe(e,_,t){if(t||arguments.length===2)for(var r=0,i=_.length,n;r<i;r++)(n||!(r in _))&&(n||(n=Array.prototype.slice.call(_,0,r)),n[r]=_[r]);return e.concat(n||Array.prototype.slice.call(_))}export{Oe as D,Fe as F,Le as T,oe as _,Me as a,qe as b,V as c,Te as d,ge as e,je as f,Ve as g,Ue as h,We as i,Ee as j,Ie as k,De as p,F as x,xe as y};
