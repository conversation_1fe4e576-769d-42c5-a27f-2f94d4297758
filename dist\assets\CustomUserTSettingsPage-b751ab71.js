import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as d,R as V}from"./vendor-2ae44a2e.js";import{O as ee,C as se,a as oe,T as te,D as ae,P as de,R as le,b as me,c as re,d as he,e as xe,f as pe,g as ie,h as ue,i as ge,j as fe}from"./radix-ui-c399dc8b.js";import{g as L}from"./@mantine/core-1006e8cf.js";import{t as G}from"./tailwind-merge-05141ada.js";import{X as be,c as j,b as je,D as ve,P as ye,E as Z,d as X}from"./lucide-react-f66dbccf.js";import{M as Ne,G as ne,A as we,s as K}from"./index-d0a8f5da.js";import{u as Se}from"./react-hook-form-47c010f8.js";import{c as _e,a as q}from"./yup-c2e87575.js";import{o as Te}from"./yup-5abd4662.js";import{d as Ce}from"./react-slick-6ad4ab99.js";import{S as Ae}from"./index-f2c2b086.js";import"./@fullcalendar/core-a789a586.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";import"./@uppy/dashboard-af41baed.js";import"./@uppy/core-a7fbc19c.js";import"./@uppy/aws-s3-a38c5234.js";import"./@craftjs/core-9da1c17f.js";import"./@uppy/compressor-d7e7d557.js";function H(...a){return G(L(a))}const ke=de,ce=d.forwardRef(({className:a,...l},r)=>e.jsx(ee,{ref:r,className:H("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0  fixed inset-0 z-50 bg-black/80",a),...l}));ce.displayName=ee.displayName;const Ee=d.forwardRef(({className:a,children:l,...r},c)=>e.jsxs(ke,{children:[e.jsx(ce,{}),e.jsxs(se,{ref:c,className:H("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-transparent p-6  duration-200 sm:rounded-lg",a),...r,children:[l,e.jsxs(oe,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent absolute right-4 top-4 rounded-sm text-white opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:text-white",children:[e.jsx(be,{className:"h-5 w-5 2xl:w-7 2xl:w-7"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Ee.displayName=se.displayName;const Ie=d.forwardRef(({className:a,...l},r)=>e.jsx(te,{ref:r,className:H("text-lg font-semibold leading-none tracking-tight",a),...l}));Ie.displayName=te.displayName;const Pe=d.forwardRef(({className:a,...l},r)=>e.jsx(ae,{ref:r,className:H("text-muted-foreground text-sm",a),...l}));Pe.displayName=ae.displayName;function De(...a){return G(L(a))}const I=d.forwardRef(({className:a,type:l,...r},c)=>e.jsx("input",{type:l,className:De("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:c,...r}));I.displayName="Input";function W(...a){return G(L(a))}const R=d.forwardRef(({className:a,...l},r)=>e.jsx(le,{className:W("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=unchecked]:bg-input peer inline-flex h-4 w-8 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary",a),...l,ref:r,children:e.jsx(me,{className:W("pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));R.displayName=le.displayName;function Ke(...a){return G(L(a))}const F=d.forwardRef(({className:a,...l},r)=>e.jsxs(re,{ref:r,className:Ke("relative flex w-full touch-none select-none items-center",a),...l,children:[e.jsx(he,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:e.jsx(xe,{className:"absolute h-full bg-primary"})}),e.jsx(pe,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-white ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));F.displayName=re.displayName;function Oe(...a){return G(L(a))}const v=ue,y=ge,N=fe,f=d.forwardRef(({className:a,sideOffset:l=4,...r},c)=>e.jsx(ie,{ref:c,sideOffset:l,className:Oe("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r}));f.displayName=ie.displayName;let E=new Ne;const Re=[{value:"Pacific/Honolulu",label:"Hawaii-Aleutian Standard Time (HST)"},{value:"America/Anchorage",label:"Alaska Standard Time (AKST)"},{value:"America/Los_Angeles",label:"Pacific Standard Time (PST)"},{value:"America/Denver",label:"Mountain Standard Time (MST)"},{value:"America/Phoenix",label:"Mountain Standard Time (no DST)"},{value:"America/Chicago",label:"Central Standard Time (CST)"},{value:"America/Chihuahua",label:"America/Chihuahua"},{value:"America/New_York",label:"Eastern Standard Time (EST)"},{value:"America/Toronto",label:"Eastern Standard Time (Canada)"},{value:"America/Caracas",label:"Venezuela Standard Time (VET)"},{value:"America/Bogota",label:"Colombia Time (COT)"},{value:"America/Argentina/Buenos_Aires",label:"Argentina Time (ART)"},{value:"Atlantic/Bermuda",label:"Atlantic Standard Time (AST)"},{value:"Europe/London",label:"Greenwich Mean Time (GMT)"},{value:"Europe/Paris",label:"Central European Time (CET)"},{value:"Europe/Athens",label:"Eastern European Time (EET)"},{value:"Africa/Johannesburg",label:"South Africa Standard Time (SAST)"},{value:"Asia/Dubai",label:"Gulf Standard Time (GST)"},{value:"Asia/Kolkata",label:"Indian Standard Time (IST)"},{value:"Asia/Shanghai",label:"China Standard Time (CST)"},{value:"Asia/Tokyo",label:"Japan Standard Time (JST)"},{value:"Australia/Sydney",label:"Australian Eastern Standard Time (AEST)"},{value:"Pacific/Auckland",label:"New Zealand Standard Time (NZST)"}],Fe=_e().shape({username:q().when("activeTab",{is:"ghl",then:a=>a.required("Calendar ID is required"),otherwise:a=>a}),apiKey:q().required("API Key is required"),timezone:q().when("activeTab",{is:"ghl",then:a=>a.required("Timezone is required"),otherwise:a=>a}),activeTab:q().required()});function Me(){const[a,l]=d.useState(!1),[r,c]=d.useState(!1),[h,S]=d.useState("ghl"),[m,t]=d.useState(null),{dispatch:p}=V.useContext(ne),{state:_}=V.useContext(we),{register:n,handleSubmit:g,formState:{errors:u,isValid:w},reset:O,watch:P,setValue:M}=Se({mode:"onChange",resolver:Te(Fe),defaultValues:{username:"",apiKey:"",timezone:"",activeTab:"ghl"}});d.useEffect(()=>{(async()=>{try{await T()}catch(o){console.error("Error in useEffect:",o),p&&K(p,"Failed to load settings",4e3,"error")}})()},[]),d.useEffect(()=>{try{M("activeTab",h),T()}catch(o){console.error("Error updating form:",o)}},[h]);const T=async()=>{var o;console.log(_);try{E.setTable("user_settings");const x=await E.callRestAPI({user_id:_.user,filter:[`user_id,eq,${_.user}`]},"GETALL");if((o=x==null?void 0:x.list)!=null&&o[0]){t(x.list[0]),console.log(x.list[0]);let C={};try{C=x.list[0].settings?JSON.parse(x.list[0].settings):{}}catch(b){console.error("Error parsing settings JSON:",b)}if(h==="ghl")try{const b=C.calendars||x.list[0].calendars,A=b?JSON.parse(b):[];A!=null&&A[0]&&O({username:A[0].calendarId,apiKey:A[0].GOHIGHLEVEL_TOKEN,timezone:A[0].timezone,activeTab:"ghl"})}catch(b){console.error("Error parsing calendars:",b),p&&K(p,"Failed to parse calendar settings",4e3,"error")}if(h==="cal"){const b=C.cal_api_key||x.list[0].cal_api_key;b&&O({apiKey:b,activeTab:"cal"})}}}catch(x){console.error("Error fetching settings:",x),p&&K(p,"Failed to fetch settings",4e3,"error")}},z=async o=>{try{if(l(!0),E.setTable("user_settings"),h==="ghl"){const x={calendarId:o.username,GOHIGHLEVEL_TOKEN:o.apiKey,timezone:o.timezone,campaign_id:"1"},b={...m!=null&&m.settings?JSON.parse(m.settings):{},calendars:JSON.stringify([x])};await E.callRestAPI({id:m==null?void 0:m.id,calendars:JSON.stringify([x]),settings:JSON.stringify(b)},"PUT")}else if(h==="cal"){const C={...m!=null&&m.settings?JSON.parse(m.settings):{},cal_api_key:o.apiKey};await E.callRestAPI({id:m==null?void 0:m.id,cal_api_key:o.apiKey,settings:JSON.stringify(C)},"PUT")}p&&K(p,"Settings updated successfully!",4e3),await T()}catch(x){console.error("Error saving settings:",x),p&&K(p,"Failed to update settings",4e3,"error")}finally{l(!1)}},$=async()=>{try{l(!0);const o=await E.callRawAPI("/v3/api/custom/voiceoutreach/user/calendar/auth",{},"GET");if(!o.error&&o.link)window.open(o.link);else throw new Error("Invalid response from calendar auth")}catch(o){console.error("Error connecting calendar:",o),p&&K(p,"Failed to connect to Google Calendar",4e3,"error")}finally{l(!1)}},D=P(),J=h==="ghl"?D.username&&D.apiKey&&D.timezone:D.apiKey;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${h==="ghl"?"bg-[#19b2f6]/20":"hover:bg-gray-100/10"}`,onClick:()=>S("ghl"),children:e.jsx("span",{className:"font-semibold text-white",children:"GHL"})}),e.jsx("button",{type:"button",className:`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${h==="cal"?"bg-[#19b2f6]/20":"hover:bg-gray-100/10"}`,onClick:()=>S("cal"),children:e.jsx("p",{className:"font-semibold text-white",children:"Cal.com"})}),e.jsx("button",{type:"button",className:"flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 hover:bg-gray-100/10",onClick:$,children:e.jsx("span",{className:"font-semibold text-white",children:"Google"})})]}),e.jsx("form",{onSubmit:g(z),className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[h==="ghl"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(I,{...n("username"),placeholder:"Calendar ID",className:`bg-transparent text-white ${u.username?"border-red-400":"border-gray-300"} placeholder:text-gray-300`}),u.username&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:u.username.message})]}),e.jsxs("div",{className:"relative mt-2",children:[e.jsx(I,{...n("apiKey"),type:r?"text":"password",placeholder:"API Key",className:`bg-transparent text-white ${u.apiKey?"border-red-400":"border-gray-300"} pr-10 placeholder:text-gray-300`}),e.jsx("button",{type:"button",onClick:()=>c(!r),className:"absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300",children:r?e.jsx(Z,{className:"h-5 w-5"}):e.jsx(X,{className:"h-5 w-5"})}),u.apiKey&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:u.apiKey.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"timezone",className:"mt-1 block text-sm font-medium text-white",children:"Timezone"}),e.jsxs("select",{...n("timezone"),className:`mt-1 block w-full rounded-md ${u.timezone?"border-red-400":"border-gray-300"} bg-[#1d2937] text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm`,children:[e.jsx("option",{value:"",children:"Select your timezone"}),Re.map(o=>e.jsx("option",{value:o.value,children:o.label},o.value))]}),u.timezone&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:u.timezone.message})]})]}),h==="cal"&&e.jsxs("div",{className:"relative mt-2",children:[e.jsx(I,{...n("apiKey"),type:r?"text":"password",placeholder:"Cal.com API Key",className:`bg-transparent text-white ${u.apiKey?"border-red-400":"border-gray-300"} pr-10 placeholder:text-gray-300`}),e.jsx("button",{type:"button",onClick:()=>c(!r),className:"absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300",children:r?e.jsx(Z,{className:"h-5 w-5"}):e.jsx(X,{className:"h-5 w-5"})}),u.apiKey&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:u.apiKey.message})]}),e.jsx("button",{disabled:!J||a,type:"submit",className:"mt-3 inline-flex h-[41px] justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-3 py-2 text-sm font-medium text-white shadow-sm hover:border-[#19b2f6] hover:bg-[#19b2f6]/60 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:a?"Connecting":h==="ghl"?"Connect GHL":"Save API Key"})]})})]})}function js({title:a,leftLabel:l,rightLabel:r,value:c,onChange:h,tooltipContent:S}){return e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:a}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(f,{children:e.jsx("p",{children:S})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-white",children:l}),e.jsx(R,{checked:c,onCheckedChange:h,className:"bg-gray-200 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-white",children:r})]})]})}function vs({title:a,min:l,max:r,step:c,value:h,onChange:S,tooltipContent:m,leftLabel:t,rightLabel:p}){return e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:a}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(f,{children:e.jsx("p",{children:m})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-blue-500",children:t}),e.jsx(F,{value:[h],max:r,min:l,step:c,className:"mx-4 w-[60%]",onValueChange:([_])=>S(_)}),e.jsx("span",{className:"text-blue-500",children:p})]})]})}function ys({value:a,onChange:l}){const[r,c]=d.useState(!1),[h,S]=d.useState(!1),[m,t]=d.useState(!1),[p,_]=d.useState(!1),n=({label:g,placeholder:u,showKey:w,setShowKey:O,value:P,onChange:M,tooltipText:T})=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-2 mt-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:g}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(f,{children:e.jsx("p",{children:T})})]})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(I,{type:w?"text":"password",value:P,onChange:M,placeholder:u,className:"w-full bg-transparent pr-10 text-white"}),e.jsx("button",{type:"button",onClick:()=>O(!w),className:"absolute right-2 top-1/2 -translate-y-1/2 text-white hover:text-gray-300",children:w?e.jsx(Z,{className:"h-5 w-5"}):e.jsx(X,{className:"h-5 w-5"})})]})]});return e.jsxs("div",{className:"mb-6",children:[e.jsx(n,{label:"Elevenlabs Key",placeholder:"Enter your Elevenlabs API key",showKey:r,setShowKey:c,value:a,onChange:g=>l(g.target.value),tooltipText:"API key for Elevenlabs text-to-speech service"}),e.jsx(n,{label:"Deepgram Key",placeholder:"Enter your Deepgram API key",showKey:h,setShowKey:S,value:a,onChange:g=>l(g.target.value),tooltipText:"API key for Deepgram speech recognition service"}),e.jsx(n,{label:"Anthropic Key",placeholder:"Enter your Anthropic API key",showKey:m,setShowKey:t,value:a,onChange:g=>l(g.target.value),tooltipText:"API key for Anthropic AI service"}),e.jsx(n,{label:"Telnyx Key",placeholder:"Enter your Telnyx API key",showKey:p,setShowKey:_,value:a,onChange:g=>l(g.target.value),tooltipText:"API key for Telnyx telephony service"}),e.jsxs("div",{className:"mb-2 mt-2 flex items-center gap-2",children:[e.jsx("span",{className:"text-lg text-white",children:"Transfer Number"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-white"})}),e.jsx(f,{children:e.jsx("p",{children:"Phone number to transfer calls to"})})]})})]}),e.jsx(I,{type:"tel",value:a,onChange:g=>l(g.target.value),placeholder:"Enter phone number to transfer call to",className:"w-full bg-transparent text-white"})]})}function ze(){return e.jsx("div",{className:"h-[600px] w-full",children:e.jsx("iframe",{src:"https://callagentds.manaknightdigital.com/voice-dialer",className:"h-full w-full border-0",title:"Voice Dialer"})})}function Ns({icon:a,title:l,isActive:r,onClick:c}){return e.jsxs("div",{onClick:c,className:`flex cursor-pointer items-center justify-center space-x-3 px-4 py-2 ${r?"text-center font-medium text-white":"text-center text-white hover:text-white"}`,children:[e.jsxs("div",{className:"flex-shrink-0 text-white",children:[V.cloneElement(a,{className:"w-6 h-6"})," "]}),e.jsx("span",{className:"text-center text-sm",children:l})]})}function U({title:a,isActive:l,onClick:r,icon:c=null}){return e.jsxs("button",{onClick:r,className:`flex items-center gap-2 rounded-t-md px-6 py-3 text-sm font-medium transition-colors ${l?"border-l border-r border-t border-gray-600 bg-[#2d3748] text-white":"bg-transparent text-gray-400 hover:bg-[#2d3748]/50 hover:text-gray-300"}`,children:[c||null,a]})}function ws({open:a,onOpenChange:l}){var $,D,J,o,x,C,b,A;const{dispatch:r}=V.useContext(ne),[c,h]=d.useState("calendar"),[S,m]=d.useState(!1),[t,p]=d.useState({interruptSensitivity:!1,responseSpeed:!1,doubleCall:!1,vmDetection:!1,initialDelay:0,aiCreativity:.5,transferNumber:"",callerId:"",llm_settings:{provider:"groq",model_name:"llama-3.3-70b-versatile",temperature:"0.01",max_tokens:"250",top_p:"0.9",system_prompt:""},tts_settings:{provider:"elevenlabs",model_name:"eleven_multilingual_v2"}});V.useEffect(()=>{async function s(){try{m(!0);const i=await E.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{},"GET");if(console.log(i),i!=null&&i.model){const k=i.model.settings?JSON.parse(i.model.settings):{};let Y={provider:"groq",model_name:"llama-3.3-70b-versatile",temperature:"0.01",max_tokens:"250",top_p:"0.9",system_prompt:""};try{i.model.llm_settings&&(Y=JSON.parse(i.model.llm_settings))}catch(B){console.error("Error parsing llm_settings:",B)}let Q={provider:"elevenlabs",model_name:"eleven_multilingual_v2"};try{i.model.tts_settings&&(Q=JSON.parse(i.model.tts_settings))}catch(B){console.error("Error parsing tts_settings:",B)}console.log(k),k&&p({...k,llm_settings:Y,tts_settings:Q})}}catch(i){console.log("Error",i)}m(!1)}s()},[]);const _=d.useCallback(Ce(async s=>{const i=JSON.stringify(s);await E.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{settings:i},"POST")},500),[]),n=s=>{p(s),_(s)},g=async()=>{const s=JSON.stringify(t);await E.callRawAPI("/v3/api/custom/voiceoutreach/user/settings",{settings:s},"POST"),K(r,"Settings saved successfully!",4e3)},u=s=>{h(s)},w={interruptSensitivity:"Controls how easily the AI can be interrupted during conversations",responseSpeed:"Adjusts how quickly the AI responds to user input",initialDelay:"Sets the delay before the first AI response",doubleCall:"Enables double-call verification feature",vmDetection:"Activates voicemail detection capabilities",aiCreativity:"Controls the creativity level of AI responses",callerId:"Set the caller ID name for outbound calls",transferNumber:"Phone number to transfer calls to"},O={groq:{"llama-3.3-70b-versatile":"Fast","meta-llama/Llama-4-Scout-17B-16E-Instruct":"Medium","meta-llama/llama-4-maverick-17b-128e-instruct":"Medium-Fast"},anthropic:{"claude-3-haiku-20240307":"Very Fast","claude-3-5-haiku-20241022":"Fast","claude-3-opus-20240229":"Slow","claude-3-5-sonnet-20240620":"Medium"},"direct-openai":{"gpt-3.5-turbo":"Very Fast","gpt-4o-mini":"Medium"},lambda_ai:{"llama-3.3-70b-instruct":"Very Fast"}},P={groq:{label:"Groq",models:["llama-3.3-70b-versatile","meta-llama/Llama-4-Scout-17B-16E-Instruct","meta-llama/llama-4-maverick-17b-128e-instruct"]},anthropic:{label:"Anthropic",models:["claude-3-haiku-20240307","claude-3-5-haiku-20241022","claude-3-opus-20240229","claude-3-5-sonnet-20240620"]},"direct-openai":{label:"OpenAI",models:["gpt-3.5-turbo","gpt-4o-mini"]},lambda_ai:{label:"Lambda AI",models:["llama-3.3-70B-instruct"]}},M={deepgram:{"nova-3":"Very Fast","nova-3-general":"Fast","nova-3-medical":"Fast","nova-2-phonecall":"Medium"},assemblyai:{"slam-1":"Very Fast","universal-1":"Fast","universal-2":"Medium"}},T={deepgram:{label:"Deepgram",models:["nova-3","nova-3-general","nova-3-medical","nova-2-phonecall"]},assemblyai:{label:"AssemblyAI",models:["slam-1","universal-1","universal-2"]}},z={elevenlabs:{label:"ElevenLabs",models:["eleven_multilingual_v2","eleven_flash_v2"]},orpheus_tts:{label:"Orpheus TTS",models:["canopylabs/orpheus-tts-0.1-finetune-prod"]},kokoro_tts:{label:"Kokoro TTS",models:["base"]}};return e.jsx(e.Fragment,{children:S?e.jsx("div",{className:"flex max-h-fit min-h-fit w-full min-w-full max-w-full items-center justify-center py-5",children:e.jsx(Ae,{size:100,color:"#0EA5E9"})}):e.jsx("div",{className:"mx-auto max-w-3xl bg-[#1d2937] text-white",children:e.jsxs("div",{className:"rounded-lg bg-[#1d2937] bg-opacity-100 p-12 shadow-lg",children:[e.jsx("div",{children:e.jsxs("div",{className:"flex justify-center",children:[e.jsx(U,{icon:e.jsx(je,{className:"h-4 w-4"}),title:"Calendar & Email Booking",isActive:c==="calendar",onClick:()=>u("calendar")}),e.jsx(U,{icon:e.jsx(ve,{className:"h-4 w-4"}),title:"Advanced Options",isActive:c==="advanced",onClick:()=>u("advanced")}),e.jsx(U,{icon:e.jsx(ye,{className:"h-4 w-4"}),title:"Voice Dialer",isActive:c==="voicedialer",onClick:()=>u("voicedialer")})]})}),e.jsxs("div",{className:"rounded-b-md rounded-tr-md border border-gray-600 bg-[#2d3748] p-6",children:[c==="calendar"&&e.jsx(Me,{}),c==="voicedialer"&&e.jsx(ze,{}),c==="advanced"&&e.jsxs("div",{className:"mx-auto max-w-2xl space-y-6",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Interrupt Sensitivity"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.interruptSensitivity})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Low"}),e.jsx(R,{checked:t.interruptSensitivity,onCheckedChange:s=>n({...t,interruptSensitivity:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"High"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Double Call"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.doubleCall})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"False"}),e.jsx(R,{checked:t.doubleCall,onCheckedChange:s=>n({...t,doubleCall:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"True"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Response Speed"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.responseSpeed})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Auto"}),e.jsx(R,{checked:t.responseSpeed,onCheckedChange:s=>n({...t,responseSpeed:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Sensitive"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"VM Detection (Beta)"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.vmDetection})})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Off"}),e.jsx(R,{checked:t.vmDetection,onCheckedChange:s=>n({...t,vmDetection:s}),className:"bg-gray-600 data-[state=checked]:bg-blue-500"}),e.jsx("span",{className:"text-sm text-gray-400",children:"On"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Initial Message Delay"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.initialDelay})})]})})]}),e.jsxs("span",{className:"text-sm text-white",children:[t.initialDelay,"s"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"whitespace-nowrap text-sm text-gray-400",children:"0 (sec)"}),e.jsx(F,{value:[t.initialDelay],max:5,min:0,step:.1,className:"w-full",onValueChange:([s])=>n({...t,initialDelay:s})}),e.jsx("span",{className:"whitespace-nowrap text-sm text-gray-400",children:"5 (sec)"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"AI Creativity"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.aiCreativity})})]})})]}),e.jsx("span",{className:"text-sm text-white",children:t.aiCreativity})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"pr-[28px] text-sm text-gray-400",children:["0"," "]}),e.jsx(F,{value:[t.aiCreativity],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...t,aiCreativity:s})}),e.jsx("span",{className:"pl-[28px] text-sm text-gray-400",children:"1"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-white",children:"Caller ID Name"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:w.callerId})})]})})]}),e.jsx(I,{placeholder:"Caller ID Name",value:t.callerId,onChange:s=>n({...t,callerId:s.target.value}),className:"w-full border-gray-200 bg-transparent text-white placeholder:text-gray-300"})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"LLM Settings"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:"Configure Language Model settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Provider"}),e.jsx("select",{value:t.llm_settings.provider,onChange:s=>{const i=s.target.value,k=P[i].models[0];n({...t,llm_settings:{...t.llm_settings,provider:i,model_name:k}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(P).map(([s,{label:i}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:i},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Model"}),e.jsx("select",{value:t.llm_settings.model_name,onChange:s=>n({...t,llm_settings:{...t.llm_settings,model_name:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:P[t.llm_settings.provider].models.map(s=>e.jsxs("option",{value:s,className:"bg-[#1d2937]",children:[s," (",O[t.llm_settings.provider][s],")"]},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Temperature"}),e.jsx("span",{className:"text-sm text-white",children:t.llm_settings.temperature})]}),e.jsx(F,{value:[parseFloat(t.llm_settings.temperature)],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...t,llm_settings:{...t.llm_settings,temperature:s.toString()}})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Max Tokens"}),e.jsx(I,{type:"number",value:t.llm_settings.max_tokens,onChange:s=>n({...t,llm_settings:{...t.llm_settings,max_tokens:s.target.value}}),className:"w-full border-gray-600 bg-transparent text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Top P"}),e.jsx("span",{className:"text-sm text-white",children:t.llm_settings.top_p})]}),e.jsx(F,{value:[parseFloat(t.llm_settings.top_p)],max:1,min:0,step:.01,className:"w-full",onValueChange:([s])=>n({...t,llm_settings:{...t.llm_settings,top_p:s.toString()}})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"System Prompt"}),e.jsx("textarea",{value:t.llm_settings.system_prompt,onChange:s=>n({...t,llm_settings:{...t.llm_settings,system_prompt:s.target.value}}),className:"h-24 w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",placeholder:"Enter system prompt..."})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"Speech Recognition Settings"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:"Configure Speech Recognition settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Speech Recognition Provider"}),e.jsx("select",{value:($=t.speech_recognition_settings)==null?void 0:$.provider,onChange:s=>{const i=s.target.value,k=T[i].models[0];n({...t,speech_recognition_settings:{...t.speech_recognition_settings,provider:i,model:k}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(T).map(([s,{label:i}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:i},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"Speech Recognition Model"}),e.jsx("select",{value:(D=t.speech_recognition_settings)==null?void 0:D.model,onChange:s=>n({...t,speech_recognition_settings:{...t.speech_recognition_settings,model:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:(o=T[(J=t.speech_recognition_settings)==null?void 0:J.provider])==null?void 0:o.models.map(s=>{var i;return e.jsxs("option",{value:s,className:"bg-[#1d2937]",children:[s," (",M[(i=t.speech_recognition_settings)==null?void 0:i.provider][s],")"]},s)})})]})]}),e.jsxs("div",{className:"mt-8 space-y-4 border-t border-gray-700 pt-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg font-medium text-white",children:"Text-to-Speech Settings"}),e.jsx(v,{children:e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(j,{className:"h-4 w-4 text-gray-400"})}),e.jsx(f,{children:e.jsx("p",{children:"Configure Text-to-Speech settings"})})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"TTS Provider"}),e.jsx("select",{value:(x=t.tts_settings)==null?void 0:x.provider,onChange:s=>{const i=s.target.value,k=z[i].models[0];n({...t,tts_settings:{...t.tts_settings,provider:i,model_name:k}})},className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:Object.entries(z).map(([s,{label:i}])=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:i},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm text-white",children:"TTS Model"}),e.jsx("select",{value:(C=t.tts_settings)==null?void 0:C.model_name,onChange:s=>n({...t,tts_settings:{...t.tts_settings,model_name:s.target.value}}),className:"w-full rounded-md border border-gray-600 bg-transparent p-2 text-white",children:(A=z[(b=t.tts_settings)==null?void 0:b.provider])==null?void 0:A.models.map(s=>e.jsx("option",{value:s,className:"bg-[#1d2937]",children:s},s))})]})]})]})]}),c=="advanced"&&e.jsx("div",{className:"mt-8 flex w-full justify-center",children:e.jsx("button",{className:"focus:shadow-outline w-full rounded bg-[#19b2f6]/80 px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none",onClick:g,children:"Save Settings"})})]})})})}export{js as SettingRow,Ns as SettingSection,ws as SettingsModal,vs as SliderSetting,ys as TransferSetting,ze as VoiceDialerSettings,ws as default};
