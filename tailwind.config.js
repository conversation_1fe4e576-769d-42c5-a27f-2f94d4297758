

  /** @type {import('tailwindcss').Config} */
  module.exports = {
    content: [
      "./src/**/*.{js,jsx,ts,tsx}",
      "./node_modules/tw-elements/dist/js/**/*.js",
    ],
    theme: {
      extend: {
        colors: {
          primaryBlue: "black",
          "primary-light": "black50",
        },
  
        keyframes: {
          wiggle: {
            "0%, 100%": { transform: "rotate(-3deg)" },
            "50%": { transform: "rotate(3deg)" },
          },
        },
        animation: {
          wiggle: "wiggle 200ms ease-in-out",
        },
      },
    },
    plugins: [require("tw-elements/dist/plugin"), require("@tailwindcss/forms")],
  };
