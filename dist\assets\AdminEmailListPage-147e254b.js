import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as r,f as L}from"./vendor-2ae44a2e.js";import{M as G,A as V,G as z,B as H,a as K,b as J,R as Q,c as U,t as W}from"./index-d0a8f5da.js";import{S as X}from"./react-loading-skeleton-f53ed7d1.js";import{M as y}from"./index-9aa09a5c.js";import{c as Y,a as p}from"./yup-c2e87575.js";import{u as Z}from"./react-hook-form-47c010f8.js";import{o as _}from"./yup-5abd4662.js";import ee from"./AddAdminEmailPage-23f87b66.js";import te from"./EditAdminEmailPage-e73a2ce7.js";import{A as se}from"./index-e429b426.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let w=new G;const u=[{header:"ID",accessor:"id"},{header:"Email Type",accessor:"slug"},{header:"Subject",accessor:"subject"},{header:"Tags",accessor:"tag"},{header:"Action",accessor:""}],Oe=()=>{const{dispatch:v}=r.useContext(V),[x,N]=r.useState([]),[S,m]=r.useState(!1),[A,n]=r.useState(!1),[C,l]=r.useState(!1),[E,k]=r.useState(),[h,F]=r.useState(!1),[g,f]=r.useState(!1),[i,d]=r.useState([]),[ae,c]=r.useState([]),[re,D]=r.useState(""),[R,M]=r.useState("eq");L();const{dispatch:O}=r.useContext(z),T=Y({page:p(),key:p(),type:p()}),{handleSubmit:$,formState:{errors:ie}}=Z({resolver:_(T)}),q=t=>{getNonNullValue(t.page),getNonNullValue(t.key),getNonNullValue(t.type),b()},j=(t,a,s)=>{const o=a==="eq"&&isNaN(s)?`"${s}"`:s,I=`${t},${a},${o}`;c(P=>[...P.filter(B=>!B.includes(t)),I]),D(s)};async function b(){try{w.setTable("email");const t=await w.callRestAPI({},"GETALL"),{list:a}=t;N(a)}catch(t){console.log("ERROR",t),W(v,t.message)}}return r.useEffect(()=>{O({type:"SETPATH",payload:{path:"email"}}),async function(){m(!0),await b(),m(!1)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded bg-white p-5 shadow",children:[e.jsxs("div",{className:"mb-3 flex w-full justify-between text-center",children:[e.jsxs("form",{className:"relative rounded bg-white",onSubmit:$(q),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>F(!h),children:[e.jsx(H,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length>0?i.length:null})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1  focus-within:border-gray-400",children:[e.jsx(K,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var a;return j("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(J,{className:"text-lg text-gray-200"})]})]}),h&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-6 shadow-lg",children:[i==null?void 0:i.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:s=>{M(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>j(t,R,s.target.value)}),e.jsx(Q,{className:"cursor-pointer text-2xl",onClick:()=>{d(s=>s.filter(o=>o!==t)),c(s=>s.filter(o=>!o.includes(t)))}})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{f(!g)},children:[e.jsx(U,{}),"Add filter"]}),g&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:u.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||d(a=>[...a,t.header]),f(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{d([]),c([])},className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]}),e.jsx(se,{onClick:()=>n(!0)})]}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 text-black",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:u.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsxs("tbody",{className:"divide-y divide-gray-200 bg-white",children:[x.length==0?e.jsx("tr",{children:e.jsx("td",{colSpan:5,children:S&&e.jsx(X,{count:4})})}):null,x.map((t,a)=>e.jsx("tr",{children:u.map((s,o)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"cursor-pointer text-xs text-[black]",onClick:()=>{k(t),l(!0)},children:[" ","Edit"]})},o):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},o):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},o))},a))]})]})})]}),e.jsx(y,{isModalActive:A,closeModalFn:()=>n(!1),children:e.jsx(ee,{setSidebar:n})}),e.jsx(y,{isModalActive:C,closeModalFn:()=>l(!1),children:e.jsx(te,{activeId:E,setSidebar:l})})]})};export{Oe as default};
