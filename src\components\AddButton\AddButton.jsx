import React, { useState } from "react";
import classes from "./AddButton.module.css";
const AddButton = ({
  onClick,
  children = "Add New",
  showPlus = true,
  className,
  showChildren = true,
}) => {
  const [animate, setAnimate] = useState(false);

  const onClickHandle = () => {
    if (onClick) {
      onClick();
    }
    setAnimate(true);
  };

  return (
    <button
      onAnimationEnd={() => setAnimate(false)}
      onClick={onClickHandle}
      className={`${animate && "animate-wiggle"} ${
        classes.button
      } black relative flex h-[2.125rem] w-fit  min-w-fit items-center justify-center overflow-hidden rounded-md border bg-[#19b2f6]/80  px-[.9125rem] py-[.8625rem]  text-base font-medium leading-none text-white shadow-md shadow-black/20 hover:bg-[#19b2f6]/60  ${className}`}
    >
      {showPlus ? "+" : null} {showChildren ? children : null}
    </button>
  );
};

export default AddButton;
