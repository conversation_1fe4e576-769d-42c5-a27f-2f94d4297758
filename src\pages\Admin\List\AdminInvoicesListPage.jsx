import React from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { PaginationBar } from "Components/PaginationBar";
import { SkeletonLoader } from "Components/Skeleton";
import { format } from "date-fns";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlinePlus, AiOutlineClose } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";

const sdk = new MkdSDK();

const columns = [
  { header: "Call ID", accessor: "call_id" },
  { header: "User Name", accessor: "first_name" },
  { header: "Campaign Name", accessor: "name" },
  { header: "Duration", accessor: "duration" },
  { header: "Total Cost", accessor: "calculated_cost" },
  { header: "Deepgram", accessor: "deepgram_cost" },
  { header: "ElevenLabs", accessor: "elevenlabs_cost" },
  { header: "Azure", accessor: "azure_cost" },
  { header: "Claude", accessor: "claude_cost" },
  { header: "Twilio", accessor: "twilio_cost" },
  { header: "Date", accessor: "update_at" },
];

const schema = yup.object({
  userId: yup.string().required("User is required"),
  campaignId: yup.string(),
  startDate: yup.date(),
  endDate: yup.date(),
});

const AdminLogsListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [users, setUsers] = React.useState([]);
  const [campaigns, setCampaigns] = React.useState([]);
  const [logs, setLogs] = React.useState([]);
  const [tableLoading, setTableLoading] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  React.useEffect(() => {
    globalDispatch({ type: "SETPATH", payload: { path: "logs" } });
    (async () => {
      await fetchUsers();
      await fetchCampaigns();
      // Auto-filter with first user if available
      if (users.length > 0) {
        const firstUser = users[0];
        setValue("userId", firstUser.id);
        fetchLogs({ user_id: firstUser.id });
      } else {
        fetchLogs();
      }
    })();
  }, []);

  const fetchUsers = async () => {
    try {
      sdk.setTable("user");
      const result = await sdk.callRestAPI(
        {
          payload: { role: "user" },
          page: 1,
          limit: 50,
        },
        "GETALL"
      );
      setUsers(result.list);
    } catch (error) {
      console.error("Error fetching users", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  const fetchCampaigns = async () => {
    try {
      sdk.setTable("campaign");
      const result = await sdk.callRestAPI({}, "GETALL");
      setCampaigns(result.list);
    } catch (error) {
      console.error("Error fetching campaigns", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  const fetchLogs = async (filters) => {
    setTableLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/calls/logs",
        {
          ...filters,
          page: currentPage,
          limit: pageSize,
        },
        "POST"
      );

      const { logs: lg, total, num_pages } = result;
      setLogs(lg);
      setPageCount(num_pages);
      setDataTotal(total);
      setCanPreviousPage(currentPage > 1);
      setCanNextPage(currentPage + 1 <= num_pages);
    } catch (error) {
      console.error("Error fetching logs", error);
      tokenExpireError(dispatch, error.message);
    }
    setTableLoading(false);
  };

  const downloadCSV = () => {
    let csvContent = [
      columns.map((col) => col.header).join(","),
      ...logs.map((log) =>
        columns
          .map((col) => {
            const value = log[col.accessor];
            return typeof value === "number" ? value.toFixed(2) : value;
          })
          .join(",")
      ),
    ].join("\n");

    const totalRow = columns
      .map((col) => {
        if (col.accessor === "calculated_cost") {
          return "Total";
        }
        return "";
      })
      .join(",");
    csvContent += `\n${totalRow},${calculateTotalCost()}`;

    const blob = new Blob([csvContent], { type: "text/csv" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "invoice.csv";
    link.click();
  };

  const calculateTotalCost = () => {
    return logs
      .reduce((acc, log) => acc + parseFloat(log.calculated_cost || 0), 0)
      .toFixed(2);
  };

  const onSubmit = (data) => {
    const filters = {
      user_id: data.userId,
      campaign_id: data.campaignId,
      start_date: format(new Date(data.startDate), "yyyy-MM-dd"),
      end_date: format(new Date(data.endDate), "yyyy-MM-dd"),
    };
    fetchLogs(filters);
  };

  function updatePageSize(limit) {
    setPageSize(limit);
    fetchLogs({});
  }

  function previousPage() {
    setCurrentPage((prev) => (prev > 1 ? prev - 1 : 1));
    fetchLogs({});
  }

  function nextPage() {
    setCurrentPage((prev) => (prev < pageCount ? prev + 1 : prev));
    fetchLogs({});
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="flex justify-between items-center px-8 py-6 admin-input">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex gap-4 items-end text-black"
        >
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">User</label>
            <select
              {...register("userId")}
              className="h-10 w-48 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"
            >
              <option value="">Select User</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.first_name} {user.last_name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Campaign
            </label>
            <select
              {...register("campaignId")}
              className="h-10 w-48 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"
            >
              <option value="">All Campaigns</option>
              {campaigns.map((campaign) => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              {...register("startDate")}
              className="h-10 w-40 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"
            />
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              End Date
            </label>
            <input
              type="date"
              style={{ filter: "invert" }}
              {...register("endDate")}
              className="h-10 w-40 rounded-md border border-gray-300 bg-white px-3 text-gray-900 focus:border-[#2cc9d5] focus:outline-none"
            />
          </div>

          <button
            type="submit"
            className="h-10 rounded-md bg-[#2cc9d5] px-4 text-white hover:bg-[#28b8c3]"
          >
            Filter
          </button>
        </form>

        <div className="flex gap-4 items-center">
          <div className="text-lg font-semibold">
            Total Cost:{" "}
            <span className="text-[#2cc9d5]">${calculateTotalCost()}</span>
          </div>
          <button
            onClick={downloadCSV}
            className="rounded bg-[#2cc9d5] px-4 py-2 text-white hover:bg-[#28b8c3]"
          >
            Download CSV
          </button>
        </div>
      </div>

      {tableLoading ? (
        <SkeletonLoader />
      ) : (
        <div className="px-8">
          <div className="overflow-x-auto bg-white rounded-lg border border-gray-200 shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {logs.map((row, i) => (
                  <tr key={i} className="hover:bg-gray-50">
                    {columns.map((cell, index) => (
                      <td
                        key={index}
                        className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap"
                      >
                        {cell.accessor === "update_at"
                          ? format(new Date(row[cell.accessor]), "yyyy-MM-dd")
                          : cell.accessor === "calculated_cost" ||
                            cell.accessor === "deepgram_cost" ||
                            cell.accessor === "elevenlabs_cost" ||
                            cell.accessor === "azure_cost" ||
                            cell.accessor === "claude_cost" ||
                            cell.accessor === "twilio_cost"
                          ? `$${row[cell.accessor].toFixed(2)}`
                          : row[cell.accessor]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {!tableLoading && logs.length === 0 && (
              <div className="px-6 py-4 text-sm text-center text-gray-500">
                No logs found
              </div>
            )}
          </div>
        </div>
      )}

      <div className="px-8 py-4">
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={updatePageSize}
          previousPage={previousPage}
          nextPage={nextPage}
        />
      </div>
    </div>
  );
};

export default AdminLogsListPage;
