import{j as o}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";import{S as C}from"./index-d0a8f5da.js";const A=({type:t="text",page:b,cols:m="30",rows:k="50",name:e,label:v,errors:n,register:i,className:d,placeholder:s,onChange:N=null,options:a=[],mapping:c=null,disabled:u=!1,containerClassName:F="",dateTime:I=!1,min:M,maxLength:P=null,time:S=!1,LabelclassName:z=""})=>{var x,h,p,w,f,g,j,$;return o.jsx(o.Fragment,{children:o.jsxs("div",{className:`mb-4 ${b==="list"?"w-full pl-2 pr-2 md:w-1/2":""} ${F}`,children:[o.jsx("label",{className:`mb-2 block cursor-pointer text-base font-semibold text-white ${z}`,htmlFor:e,children:C(v,{casetype:"capitalize",separator:"space"})}),t==="textarea"?o.jsx("textarea",{className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(x=n[e])!=null&&x.message?"border-red-500":""}`,disabled:u,id:e,cols:m,name:e,placeholder:s,rows:k,...i(e)}):t==="radio"||t==="checkbox"||t==="color"?o.jsx("input",{disabled:u,type:t,id:e,name:e,placeholder:s,...i(e),className:`focus:shadow-outline cursor-pointer appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(h=n[e])!=null&&h.message?"border-red-500":""} ${t==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""}`}):t==="dropdown"||t==="select"?o.jsxs("select",{type:t,id:e,disabled:u,placeholder:s,...i(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(p=n[e])!=null&&p.message?"border-red-500":""}`,children:[o.jsx("option",{children:s}),a.map((l,r)=>o.jsx("option",{value:l,children:l},r+1))]}):t==="mapping"?o.jsx(o.Fragment,{children:c?o.jsxs("select",{type:t,id:e,disabled:u,placeholder:s,...i(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(w=n[e])!=null&&w.message?"border-red-500":""}`,children:[o.jsx("option",{children:s}),a.map((l,r)=>o.jsx("option",{value:l,children:c[l]},r+1))]}):"Please Pass the mapping e.g {key:value}"}):I?o.jsx("input",{type:t,id:e,disabled:u,placeholder:s,min:M,maxLength:P,...i(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(f=n[e])!=null&&f.message?"border-red-500":""}`}):S?o.jsx("input",{type:t,id:e,disabled:u,placeholder:s,onInput:N,...i(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(g=n[e])!=null&&g.message?"border-red-500":""}`}):o.jsx("input",{type:t,id:e,disabled:u,placeholder:s,step:"any",...i(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-white shadow focus:outline-none ${d} ${(j=n[e])!=null&&j.message?"border-red-500":""}`}),o.jsx("p",{className:"text-field-error italic text-red-500",children:($=n[e])==null?void 0:$.message})]})})};export{A as M};
