import React, { Suspense, memo } from "react";
import { AdminHeader } from "Components/AdminHeader";
import { Spinner } from "Assets/svgs";

const AdminWrapper = ({ children }) => {
  return (
    <div className="flex flex-col w-full max-w-full min-h-screen bg-white">
      <AdminHeader />
      <div className="mt-[92px] flex min-h-screen w-full max-w-full">
        <div className="overflow-hidden mb-20 w-full">
          <Suspense
            fallback={
              <div className="flex justify-center items-center w-full h-screen">
                <Spinner size={100} color="#2CC9D5" />
              </div>
            }
          >
            <div className="overflow-y-auto overflow-x-hidden px-6 w-full">
              {children}
            </div>
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default memo(AdminWrapper);
