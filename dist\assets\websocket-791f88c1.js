var i,s;function l(){if(s)return i;s=1;var e=function(){if(typeof self=="object"&&self)return self;if(typeof window=="object"&&window)return window;throw new Error("Unable to resolve global `this`")};return i=function(){if(this)return this;if(typeof globalThis=="object"&&globalThis)return globalThis;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch{return e()}try{return __global__||e()}finally{delete Object.prototype.__global__}}(),i}const a="websocket",b="Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.",u=["websocket","websockets","socket","networking","comet","push","RFC-6455","realtime","server","client"],f="<PERSON> <<EMAIL>> (https://github.com/theturtle32)",h=["<PERSON><PERSON><PERSON> <<EMAIL>> (http://dev.sipdoc.net)"],p="1.0.35",d={type:"git",url:"https://github.com/theturtle32/WebSocket-Node.git"},g="https://github.com/theturtle32/WebSocket-Node",w={node:">=4.0.0"},v={bufferutil:"^4.0.1",debug:"^2.2.0","es5-ext":"^0.10.63","typedarray-to-buffer":"^3.1.5","utf-8-validate":"^5.0.2",yaeti:"^0.0.6"},y={"buffer-equal":"^1.0.0",gulp:"^4.0.2","gulp-jshint":"^2.0.4","jshint-stylish":"^2.2.1",jshint:"^2.0.0",tape:"^4.9.1"},k={verbose:!1},_={test:"tape test/unit/*.js",gulp:"gulp"},m="index",j={lib:"./lib"},C="lib/browser.js",S="Apache-2.0",W={name:a,description:b,keywords:u,author:f,contributors:h,version:p,repository:d,homepage:g,engines:w,dependencies:v,devDependencies:y,config:k,scripts:_,main:m,directories:j,browser:C,license:S};var N=W.version,t;if(typeof globalThis=="object")t=globalThis;else try{t=l()}catch{}finally{if(!t&&typeof window<"u"&&(t=window),!t)throw new Error("Could not determine global this")}var o=t.WebSocket||t.MozWebSocket,O=N;function c(e,r){var n;return r?n=new o(e,r):n=new o(e),n}o&&["CONNECTING","OPEN","CLOSING","CLOSED"].forEach(function(e){Object.defineProperty(c,e,{get:function(){return o[e]}})});var T={w3cwebsocket:o?c:null,version:O};export{T as b};
