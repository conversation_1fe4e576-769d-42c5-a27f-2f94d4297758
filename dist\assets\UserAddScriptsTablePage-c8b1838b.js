import{j as r}from"./@react-google-maps/api-c55ecefa.js";import{R as e,f as j}from"./vendor-2ae44a2e.js";import{u as k}from"./react-hook-form-47c010f8.js";import{o as I}from"./yup-5abd4662.js";import{c as L,a as N}from"./yup-c2e87575.js";import{M as x,G as g,A as P,s as R,t as _}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as C}from"./MkdInput-c12da351.js";import{I as G}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let i=new x;const dt=({closeSidebar:n})=>{const{dispatch:m}=e.useContext(g),{state:o}=e.useContext(P),S=L({script:N()}).required(),{dispatch:b}=e.useContext(g);e.useState({});const[M,h]=e.useState([]),[V,y]=e.useState([]),[p,a]=e.useState(!1),v=j(),{register:A,handleSubmit:w,setError:u,setValue:B,formState:{errors:T}}=k({resolver:I(S)});e.useState([]);const E=async c=>{let s=new x;a(!0);try{s.setTable("scripts");const t=await s.callRestAPI({script:c.script,user_id:o.user},"POST");if(!t.error)R(m,"Added"),v("/user/scripts");else if(t.validation){const d=Object.keys(t.validation);for(let l=0;l<d.length;l++){const f=d[l];u(f,{type:"manual",message:t.validation[f]})}}a(!1),n&&n()}catch(t){a(!1),console.log("Error",t),u("script",{type:"manual",message:t.message}),_(b,t.message)}};return e.useEffect(()=>{m({type:"SETPATH",payload:{path:"scripts"}}),async function(){i.setTable("scripts"),console.log("state",o.user);const s=await i.callRestAPI({user_id:o.user},"GETALL");s.error||(console.log("scripts",s),h(s.list?s.list.map(t=>t.script):[]))}(),async function(){i.setTable("voice_list"),console.log("state",o.user);const s=await i.callRestAPI({user_id:o.user},"GETALL");s.error||(console.log("voices",s),y(s.list?s.list.map(t=>({voice_id:t.voice_id,name:t.name})):[]))}()},[]),r.jsxs("div",{className:"mx-auto",children:[r.jsx("h4",{className:"text-3xl font-semibold",children:"Add Script"}),r.jsxs("form",{className:"w-full mt-4",onSubmit:w(E),children:[r.jsx(C,{type:"textarea",page:"add",name:"script",errors:T,label:"Script",placeholder:"Script",register:A,className:"",rows:40}),r.jsx(G,{type:"submit",loading:p,disabled:p,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{dt as default};
