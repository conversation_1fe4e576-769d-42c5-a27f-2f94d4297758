
    import React, { useEffect, useState } from "react";
    import { useForm } from "react-hook-form";
    import { yupResolver } from "@hookform/resolvers/yup";
    import * as yup from "yup";
    import MkdSDK from "Utils/MkdSDK";
    import { useNavigate, useParams } from "react-router-dom";
    import { AuthContext, tokenExpireError } from "Context/Auth";
    import { GlobalContext, showToast } from "Context/Global";
    import ReactQuill from 'react-quill';
    import 'react-quill/dist/quill.snow.css';
    import { isImage, empty, isVideo, isPdf } from "Utils/utils";
    import {MkdInput} from "Components/MkdInput";
import {InteractiveButton} from "Components/InteractiveButton"
import { SkeletonLoader } from "Components/Skeleton";


    let sdk = new MkdSDK();

    const EditStripeSubscriptionPage = () => {
    const { dispatch } = React.useContext(AuthContext);
    const schema = yup
        .object({
    
        	stripe_id: yup.string(),
        	price_id: yup.string(),
        	user_id: yup.string(),
        	object: yup.string(),
        	status: yup.string(),
        	is_lifetime: yup.string(),
        })
        .required();
    const { dispatch: globalDispatch } = React.useContext(GlobalContext);
    const [fileObj, setFileObj] = React.useState({});
    const [isLoading, setIsLoading] = React.useState(false)
    const [loading, setLoading] = React.useState(false)

    const navigate = useNavigate();
    
          	const [stripe_id, setStripeId] = useState('');
          	const [price_id, setPriceId] = useState('');
          	const [user_id, setUserId] = useState(0);
          	const [object, setObject] = useState('');
          	const [status, setStatus] = useState('');
          	const [is_lifetime, setIsLifetime] = useState('');
    // const [id, setId] = useState(0);
    const {
        register,
        handleSubmit,
        setError,
        setValue,
        formState: { errors },
    } = useForm({
        resolver: yupResolver(schema),
    });

    const params = useParams();

    useEffect(function () {
        (async function () {
        try {
            setLoading(true)

            sdk.setTable("stripe_subscription");
            const result = await sdk.callRestAPI({ id: Number(params?.id)}, "GET");
            if (!result.error) {
    
        	 setValue('stripe_id', result.model.stripe_id);
        	 setValue('price_id', result.model.price_id);
        	 setValue('user_id', result.model.user_id);
        	 setValue('object', result.model.object);
        	 setValue('status', result.model.status);
        	 setValue('is_lifetime', result.model.is_lifetime);

    
      	 setStripeId(result.model.stripe_id);
      	 setPriceId(result.model.price_id);
      	 setUserId(result.model.user_id);
      	 setObject(result.model.object);
      	 setStatus(result.model.status);
      	 setIsLifetime(result.model.is_lifetime);
                setId(result.model.id);
                setLoading(false)

            }
        } catch (error) {
            setLoading(false)

            console.log("error", error);
            tokenExpireError(dispatch, error.message);
        }
        })();
    }, []);

    const previewImage = (field, target) => {
        let tempFileObj = fileObj;
        tempFileObj[field] = {
        file: target.files[0],
        tempURL: URL.createObjectURL(target.files[0]),
        };
        setFileObj({ ...tempFileObj });
    };


    const onSubmit = async (_data) => {
        setIsLoading(true)
        try {
            sdk.setTable("stripe_subscription");
        for (let item in fileObj) {
            let formData = new FormData();
            formData.append('file', fileObj[item].file);
            let uploadResult = await sdk.uploadImage(formData);
            _data[item] = uploadResult.url;
            
        }
        const result = await sdk.callRestAPI(
            {
            id: id,
            
        		stripe_id: _data.stripe_id,
        		price_id: _data.price_id,
        		user_id: _data.user_id,
        		object: _data.object,
        		status: _data.status,
        		is_lifetime: _data.is_lifetime,

       
            },
            "PUT"
        );

        if (!result.error) {
            showToast(globalDispatch, "Updated");
            navigate("/admin/stripe_subscription");
        } else {
            if (result.validation) {
            const keys = Object.keys(result.validation);
            for (let i = 0; i < keys.length; i++) {
                const field = keys[i];
                setError(field, {
                type: "manual",
                message: result.validation[field],
                });
            }
            }
        }
        setIsLoading(false)
        } catch (error) {
            setIsLoading(false)
        console.log("Error", error);
        setError("stripe_id", {
            type: "manual",
            message: error.message,
        });
        }
    };
    React.useEffect(() => {
        globalDispatch({
        type: "SETPATH",
        payload: {
            path: "stripe_subscription",
        },
        });
    }, []);

    return (
        <div className=" shadow-md rounded   mx-auto p-5">
        <h4 className="text-2xl font-medium">Edit Stripe Subscription</h4>
       {loading? (<SkeletonLoader/>) :  (<form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
            
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"stripe_id"}
      errors={errors}
      label={"Stripe Id"}
      placeholder={"Stripe Id"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"price_id"}
      errors={errors}
      label={"Price Id"}
      placeholder={"Price Id"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"number"}
      page={"edit"}
      name={"user_id"}
      errors={errors}
      label={"User Id"}
      placeholder={"User Id"}
      register={register}
      className={""}
    />
          
        
            
        <div className="mb-4  ">
        <label
          className="block text-gray-700 text-sm font-bold mb-2"
          htmlFor="object"
        >
          Object
        </label>
        <textarea
          placeholder="Object"
          {...register("object")}
          className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
            errors.object?.message ? "border-red-500" : ""
          }`}
          row={50}
        ></textarea>
        <p className="text-red-500 text-xs italic">
          {errors.object?.message}
        </p>
      </div>
        
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"status"}
      errors={errors}
      label={"Status"}
      placeholder={"Status"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"number"}
      page={"edit"}
      name={"is_lifetime"}
      errors={errors}
      label={"Is Lifetime"}
      placeholder={"Is Lifetime"}
      register={register}
      className={""}
    />
          
        
            <InteractiveButton
            type="submit"
            className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            loading={isLoading}
            disable={isLoading}
            >
            Submit
            </InteractiveButton>
        </form>)}
        </div>
    );
    };

    export default EditStripeSubscriptionPage;

    