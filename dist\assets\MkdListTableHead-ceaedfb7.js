import{j as d}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";const y=({onSort:h,columns:b,actions:e,actionPosition:g,areAllRowsSelected:t,handleSelectAll:p})=>d.jsx(d.Fragment,{children:d.jsx("tr",{children:b.map((r,s)=>{var x,l,w,j,k,f,i;if((r==null?void 0:r.accessor)===""){if([(x=e==null?void 0:e.select)==null?void 0:x.show,(l=e==null?void 0:e.view)==null?void 0:l.show,(w=e==null?void 0:e.edit)==null?void 0:w.show,(j=e==null?void 0:e.delete)==null?void 0:j.show].includes(!0))return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>h(s):void 0,children:[r.header==="Action"&&((k=e==null?void 0:e.select)!=null&&k.show)?d.jsx("input",{type:"checkbox",disabled:!((f=e==null?void 0:e.select)!=null&&f.multiple),id:"select_all_rows",className:"mr-3",checked:t,onChange:p}):null,g==="onTable"&&r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s)}else return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>h(s):void 0,children:[r.header==="Action"&&((i=e==null?void 0:e.select)!=null&&i.show)?d.jsx("input",{type:"checkbox",id:"select_all_rows",className:"mr-3",checked:t,onChange:p}):null,r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s);return null})})});export{y as default};
