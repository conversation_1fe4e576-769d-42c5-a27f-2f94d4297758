import React, { memo, useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from 'Context/Auth';
import { NotFound } from './Routes';
import PublicRoute from './PublicRoutes';
import UserRoute from './UserRoutes';
import AdminRoute from './AdminRoutes';

const PrivateRoute = ({ path, element, access }) => {
  const Auth = useContext(AuthContext);

  if (Auth?.state?.isAuthenticated) {
    switch (true) {
      case Auth?.state?.role === 'user' && access === 'user':
        return <UserRoute path={path}>{element}</UserRoute>;
      case Auth?.state?.role === 'admin' && access === 'admin':
        return <AdminRoute path={path}>{element}</AdminRoute>;

      default:
        return <PublicRoute path={'*'} element={<NotFound />} />;
    }
  }
  if (!Auth?.state?.isAuthenticated) {
    return <PublicRoute path={'*'} element={<NotFound />} />;
  }
};

export default memo(PrivateRoute);
