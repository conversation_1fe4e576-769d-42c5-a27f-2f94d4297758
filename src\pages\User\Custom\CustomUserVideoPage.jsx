import React, { useState, useContext, Fragment } from "react";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";

import { AddButton } from "Components/AddButton";
import { Video } from "Components/video";
import { MkdFileUpload } from "Components/MkdFileUpload";
import MkdSDK from "Utils/MkdSDK";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { useNavigate } from "react-router";
import MoonLoader from "react-spinners/MoonLoader";

const VideoPage = () => {
  const { state, dispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState([]);
  const [fileObj2, setFileObj2] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const navigate = useNavigate();

  const addAudio = () => {
    const inputFile = document.createElement("input");
    inputFile.type = "file";
    inputFile.id = "imagefile";
    inputFile.setAttribute("multiple", true);
    inputFile.accept = ".txt, .docx, .pdf";
    inputFile.value = null;
    inputFile.click();

    inputFile.addEventListener("change", async (e) => {
      try {
        let files = e.target.files;
        const validExtensions = [".wav", ".mp3"];

        let validFiles = [];
        for (let file of files) {
          const fileExtension = `.${file.name.split(".").pop().toLowerCase()}`;
          if (validExtensions.includes(fileExtension)) {
            validFiles.push(file);
          }
        }

        if (validFiles.length !== files.length) {
          showToast(
            globalDispatch,
            "Some files have invalid extensions. Only  .wav, and .mp3 files are allowed.",
            2000,
            "error"
          );
        }

        setFileObj2(validFiles);
      } catch (error) {
        console.error("Error processing files:", error);
        showToast(
          globalDispatch,
          "An error occurred while processing the files. Please try again.",
          2000,
          "error"
        );
      }
    });
  };

  const sdk = new MkdSDK();

  const submitData = async () => {
    setLoading(true);

    if (!fileObj?.length) {
      showToast(
        globalDispatch,
        "No audio/video files uploaded!",
        2000,
        "error"
      );
      setLoading(false);
      return;
    }

    const newFiles = fileObj?.map((item) => item.file);
    newFiles.concat(fileObj2);

    try {
      await handleOnboarding(newFiles);
      setLoading(false);
      showToast(globalDispatch, "Success");
      navigate("/user/knowledge_bank");
    } catch (err) {
      setLoading(false);
      navigate("/user/video_generation");
      showToast(
        globalDispatch,
        err.message || "An error occurred",
        2000,
        "error"
      );
    }
  };

  const handleOnboarding = async (files) => {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append("files", file);
    });

    console.log(formData.getAll("files"), "FormData files");

    await sdk.sendFormData(
      "/v3/api/custom/voiceoutreach/video_upload",
      formData,
      "POST"
    );
  };

  const getAudioTranscript = async (formData) => {
    try {
      const res = await sdk.callRawAPI(
        `/v3/api/custom/voiceoutreach/transcribe`,
        { url: formData },
        "POST"
      );
      return res;
    } catch (err) {
      console.log(err);
      return err;
    }
  };

  const getVideoTranscript = async (formData) => {
    try {
      const res = await sdk.callRawAPI(
        `/v3/api/custom/voiceoutreach/transcribe_video`,
        { url: formData },
        "POST"
      );
      return res;
    } catch (err) {
      console.log(err);
      return err;
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "video",
      },
    });
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <Transition.Root show={open} as={Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-50 overflow-hidden"
          onClose={setOpen}
        >
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex justify-center">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="fixed inset-y-0 right-0 flex w-full justify-center ">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute right-8 top-8 flex ">
                    <button
                      type="button"
                      className="p-2.5 text-white"
                      onClick={() => {
                        setOpen(false);
                        navigate("/user/chat");
                      }}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
                  <div className="flex min-h-[300px] w-full min-w-[500px] items-center justify-center rounded-[3px] bg-white">
                    <h4>Uploaded successfully</h4>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      <div className={`min-h-screen `}>
        <MkdFileUpload
          multiple={true}
          name={"fileData"}
          fileType={"video/audio"}
          onAddSuccess={(files) => {
            console.log(files.fileData.file);
            setFileObj(files.fileData);
          }}
        />
        {fileObj.map((item) => (
          <span className="block text-[1rem] text-[red]">
            {item?.file?.name}
          </span>
        ))}

        <button
          onClick={() => addAudio()}
          className=" mt-10 block min-h-[70px] w-[100%] rounded-[10px] border-4 border-dashed border-blue-500 font-bold md:w-[41rem]"
        >
          Upload Voice Clip
        </button>
        <span className="block text-[1rem] text-[red]">{fileObj2?.name}</span>

        <button
          onClick={submitData}
          className="mt-7 flex items-center justify-center gap-2 rounded-[3px] bg-[black]  px-10 py-3 text-white"
        >
          {loading ? (
            <MoonLoader color={"white"} loading={true} size={20} />
          ) : null}
          Save
        </button>
      </div>
    </div>
  );
};

export default VideoPage;
