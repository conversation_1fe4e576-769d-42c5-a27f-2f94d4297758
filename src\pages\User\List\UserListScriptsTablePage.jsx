import React, { Fragment, useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";
import { UserAddScriptsTablePage } from "Routes/LazyLoad";
import { Dialog, Transition } from "@headlessui/react";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Script",
    accessor: "script",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Action",
    accessor: "",
  },
];

const UserListScriptsTablePage = () => {
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const refreshRef = useRef(null);

  const [selectedItems, setSelectedItems] = React.useState([]);
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "scripts",
      },
    });
  }, []);
  const onToggleModal = (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        // setSelectedItems(ids);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setSelectedItems(ids);
        setActiveEditId(ids[0]);
        break;
    }
  };
  return (
    <>
      <>
        <div className="overflow-x-auto rounded bg-white pt-5 shadow md:p-5">
          <MkdListTableV2
            columns={columns}
            tableRole={"user"}
            table={"scripts"}
            actionId={"id"}
            defaultFilter={[`user_id,eq,${state.user}`]}
            actions={{
              view: { show: false, action: null, multiple: false },
              edit: {
                show: true,
                multiple: false,
                action: (ids) => onToggleModal("edit", true, ids),
              },
              delete: { show: true, action: null, multiple: false },
              select: { show: false, action: null, multiple: false },
              add: {
                show: true,
                action: () => onToggleModal("add", true),
                multiple: false,
                children: "Add New",
                showChildren: true,
              },
              export: { show: false, action: null, multiple: true },
            }}
            actionPosition={`onTable`}
            refreshRef={refreshRef}
          />
        </div>
      </>

      <Transition appear show={showAddSidebar} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setShowAddSidebar(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-x-full"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className="h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all">
                  <UserAddScriptsTablePage
                    closeSidebar={() => setShowAddSidebar(false)}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default UserListScriptsTablePage;
