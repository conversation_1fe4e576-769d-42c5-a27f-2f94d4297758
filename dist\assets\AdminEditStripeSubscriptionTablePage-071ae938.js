import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as m,f as U,r as o,h as C}from"./vendor-2ae44a2e.js";import{u as D}from"./react-hook-form-47c010f8.js";import{o as F}from"./yup-5abd4662.js";import{c as M,a as d}from"./yup-c2e87575.js";import{M as G,A as $,G as B,t as q,s as H}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as p}from"./MkdInput-c12da351.js";import{I as K}from"./InteractiveButton-bff38983.js";import{S as V}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let n=new G;const Pe=()=>{var S,y;const{dispatch:I}=m.useContext($),w=M({stripe_id:d(),price_id:d(),user_id:d(),object:d(),status:d(),is_lifetime:d()}).required(),{dispatch:h}=m.useContext(B),[j,z]=m.useState({}),[g,f]=m.useState(!1),[N,b]=m.useState(!1),E=U(),[J,v]=o.useState(""),[Q,P]=o.useState(""),[W,k]=o.useState(0),[X,L]=o.useState(""),[Y,T]=o.useState(""),[Z,O]=o.useState(""),{register:r,handleSubmit:R,setError:_,setValue:a,formState:{errors:s}}=D({resolver:F(w)}),x=C();o.useEffect(function(){(async function(){try{b(!0),n.setTable("stripe_subscription");const e=await n.callRestAPI({id:Number(x==null?void 0:x.id)},"GET");e.error||(a("stripe_id",e.model.stripe_id),a("price_id",e.model.price_id),a("user_id",e.model.user_id),a("object",e.model.object),a("status",e.model.status),a("is_lifetime",e.model.is_lifetime),v(e.model.stripe_id),P(e.model.price_id),k(e.model.user_id),L(e.model.object),T(e.model.status),O(e.model.is_lifetime),setId(e.model.id),b(!1))}catch(e){b(!1),console.log("error",e),q(I,e.message)}})()},[]);const A=async e=>{f(!0);try{n.setTable("stripe_subscription");for(let c in j){let l=new FormData;l.append("file",j[c].file);let u=await n.uploadImage(l);e[c]=u.url}const i=await n.callRestAPI({id,stripe_id:e.stripe_id,price_id:e.price_id,user_id:e.user_id,object:e.object,status:e.status,is_lifetime:e.is_lifetime},"PUT");if(!i.error)H(h,"Updated"),E("/admin/stripe_subscription");else if(i.validation){const c=Object.keys(i.validation);for(let l=0;l<c.length;l++){const u=c[l];_(u,{type:"manual",message:i.validation[u]})}}f(!1)}catch(i){f(!1),console.log("Error",i),_("stripe_id",{type:"manual",message:i.message})}};return m.useEffect(()=>{h({type:"SETPATH",payload:{path:"stripe_subscription"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe Subscription"}),N?t.jsx(V,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:R(A),children:[t.jsx(p,{type:"text",page:"edit",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:r,className:""}),t.jsx(p,{type:"text",page:"edit",name:"price_id",errors:s,label:"Price Id",placeholder:"Price Id",register:r,className:""}),t.jsx(p,{type:"number",page:"edit",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:r,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),t.jsx("textarea",{placeholder:"Object",...r("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=s.object)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(y=s.object)==null?void 0:y.message})]}),t.jsx(p,{type:"text",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(p,{type:"number",page:"edit",name:"is_lifetime",errors:s,label:"Is Lifetime",placeholder:"Is Lifetime",register:r,className:""}),t.jsx(K,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{Pe as default};
