import{j as a}from"./@react-google-maps/api-c55ecefa.js";import{R as t,f as E}from"./vendor-2ae44a2e.js";import{u as T}from"./react-hook-form-47c010f8.js";import{o as k}from"./yup-5abd4662.js";import{c as I,a as f}from"./yup-c2e87575.js";import{M as y,G as g,A as P,s as R,t as L}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as x}from"./MkdInput-c12da351.js";import{I as M}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let h=new y;const pe=()=>{const{dispatch:n}=t.useContext(g),{state:r}=t.useContext(P),S=I({number:f(),campaign:f()}).required(),{dispatch:w}=t.useContext(g);t.useState({});const[N,j]=t.useState([]),[l,o]=t.useState(!1),C=E(),{register:p,handleSubmit:v,setError:u,setValue:G,formState:{errors:c}}=T({resolver:k(S)});t.useState([]);const A=async m=>{let s=new y;o(!0);try{s.setTable("numbers");const e=await s.callRestAPI({number:m.number,campaign:m.campaign,user_id:r.user,status:1},"POST");if(!e.error)R(n,"Added"),C("/user/numbers");else if(e.validation){const d=Object.keys(e.validation);for(let i=0;i<d.length;i++){const b=d[i];u(b,{type:"manual",message:e.validation[b]})}}o(!1)}catch(e){o(!1),console.log("Error",e),u("number",{type:"manual",message:e.message}),L(w,e.message)}};return t.useEffect(()=>{n({type:"SETPATH",payload:{path:"numbers"}}),async function(){h.setTable("campaign");const s=await h.callRestAPI({user_id:r.user,filter:[`user_id,eq,${r.user}`]},"GETALL");s.error||j(s.list?s.list.map(e=>e.name):[])}()},[]),a.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Add New Number"}),a.jsxs("form",{className:" w-full max-w-lg",onSubmit:v(A),children:[a.jsx(x,{type:"text",page:"add",name:"number",errors:c,label:"Number",placeholder:"Phone Number",register:p,className:""}),a.jsx(x,{type:"dropdown",page:"add",name:"campaign",errors:c,label:"Campaign",placeholder:"Campaign",options:N,register:p,className:""}),a.jsx(M,{type:"submit",loading:l,disabled:l,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{pe as default};
