import React from "react";
const PaginationBar = ({
  currentPage,
  pageCount,
  pageSize,
  canPreviousPage,
  canNextPage,
  updatePageSize,
  previousPage,
  nextPage,
}) => {
  return (
    <>
      <div className="flex justify-between items-center mt-3 text-black">
        <div className="flex gap-2 items-center">
          <span>
            Page{" "}
            <strong>
              {+currentPage || 1} of {pageCount}
            </strong>{" "}
          </span>
          <select
            className="rounded-md py-0.5 !pr-6 text-sm text-black"
            value={pageSize}
            onChange={(e) => {
              updatePageSize(Number(e.target.value));
            }}
          >
            {[5, 10, 20, 30, 40, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                Show {pageSize}
              </option>
            ))}
          </select>
        </div>
        {/*  */}
        <div>
          <button
            onClick={previousPage}
            disabled={!canPreviousPage}
            className={`w-10 h-10 font-bold`}
          >
            &#x02190;
          </button>{" "}
          <button
            onClick={nextPage}
            disabled={!canNextPage}
            className={`w-10 h-10 font-bold`}
          >
            &#x02192;
          </button>{" "}
        </div>
      </div>
    </>
  );
};

export default PaginationBar;
