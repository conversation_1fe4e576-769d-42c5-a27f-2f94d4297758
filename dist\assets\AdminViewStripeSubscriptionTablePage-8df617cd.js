import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as a,h as n}from"./vendor-2ae44a2e.js";import"./yup-c2e87575.js";import{M as j,G as c,t as h}from"./index-d0a8f5da.js";import{S as o}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let m=new j;const T=()=>{a.useContext(c);const{dispatch:t}=a.useContext(c),[e,d]=a.useState({}),[x,l]=a.useState(!0),r=n();return a.useEffect(function(){(async function(){try{l(!0),m.setTable("stripe_subscription");const i=await m.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");i.error||(d(i.model),l(!1))}catch(i){l(!1),console.log("error",i),h(t,i.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:x?s.jsx(o,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Stripe Subscription"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Stripe Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.stripe_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Price Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.price_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Object"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.object})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Is Lifetime"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.is_lifetime})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Create At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.create_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Update At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.update_at})]})})]})})};export{T as default};
