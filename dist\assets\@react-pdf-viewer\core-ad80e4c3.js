var Zi=Object.defineProperty;var ts=(At,d,rt)=>d in At?Zi(At,d,{enumerable:!0,configurable:!0,writable:!0,value:rt}):At[d]=rt;var ee=(At,d,rt)=>(ts(At,typeof d!="symbol"?d+"":d,rt),rt),Ne=(At,d,rt)=>{if(!d.has(At))throw TypeError("Cannot "+rt)};var t=(At,d,rt)=>(Ne(At,d,"read from private field"),rt?rt.call(At):d.get(At)),I=(At,d,rt)=>{if(d.has(At))throw TypeError("Cannot add the same private member more than once");d instanceof WeakSet?d.add(At):d.set(At,rt)},Z=(At,d,rt,h)=>(Ne(At,d,"write to private field"),h?h.call(At,rt):d.set(At,rt),rt);var ge=(At,d,rt,h)=>({set _(M){Z(At,d,M,rt)},get _(){return t(At,d,h)}}),W=(At,d,rt)=>(Ne(At,d,"access private method"),rt);import{d as getAugmentedNamespace}from"../vendor-2ae44a2e.js";function commonjsRequire(At){throw new Error('Could not dynamically require "'+At+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var pdf={exports:{}};const __viteBrowserExternal={},__viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"})),require$$5=getAugmentedNamespace(__viteBrowserExternal$1);(function(module,exports){(function(d,rt){module.exports=d.pdfjsLib=rt()})(globalThis,()=>(()=>{var __webpack_modules__=[,(At,d)=>{var qt;Object.defineProperty(d,"__esModule",{value:!0}),d.VerbosityLevel=d.Util=d.UnknownErrorException=d.UnexpectedResponseException=d.TextRenderingMode=d.RenderingIntentFlag=d.PromiseCapability=d.PermissionFlag=d.PasswordResponses=d.PasswordException=d.PageActionEventType=d.OPS=d.MissingPDFException=d.MAX_IMAGE_SIZE_TO_CACHE=d.LINE_FACTOR=d.LINE_DESCENT_FACTOR=d.InvalidPDFException=d.ImageKind=d.IDENTITY_MATRIX=d.FormatError=d.FeatureTest=d.FONT_IDENTITY_MATRIX=d.DocumentActionEventType=d.CMapCompressionType=d.BaseException=d.BASELINE_FACTOR=d.AnnotationType=d.AnnotationReplyType=d.AnnotationPrefix=d.AnnotationMode=d.AnnotationFlag=d.AnnotationFieldFlag=d.AnnotationEditorType=d.AnnotationEditorPrefix=d.AnnotationEditorParamsType=d.AnnotationBorderStyleType=d.AnnotationActionEventType=d.AbortException=void 0,d.assert=lt,d.bytesToString=N,d.createValidAbsoluteUrl=wt,d.getModificationDate=Ct,d.getUuid=Vt,d.getVerbosityLevel=V,d.info=it,d.isArrayBuffer=ht,d.isArrayEqual=Et,d.isNodeJS=void 0,d.normalizeUnicode=Xt,d.objectFromMap=ct,d.objectSize=st,d.setVerbosityLevel=E,d.shadow=Pt,d.string32=Q,d.stringToBytes=tt,d.stringToPDFString=ut,d.stringToUTF8String=K,d.unreachable=H,d.utf8StringToString=J,d.warn=nt;const rt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser");d.isNodeJS=rt;const h=[1,0,0,1,0,0];d.IDENTITY_MATRIX=h;const M=[.001,0,0,.001,0,0];d.FONT_IDENTITY_MATRIX=M;const at=1e7;d.MAX_IMAGE_SIZE_TO_CACHE=at;const X=1.35;d.LINE_FACTOR=X;const ft=.35;d.LINE_DESCENT_FACTOR=ft;const U=ft/X;d.BASELINE_FACTOR=U;const k={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};d.RenderingIntentFlag=k;const g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};d.AnnotationMode=g;const L="pdfjs_internal_editor_";d.AnnotationEditorPrefix=L;const O={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};d.AnnotationEditorType=O;const x={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};d.AnnotationEditorParamsType=x;const v={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};d.PermissionFlag=v;const y={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};d.TextRenderingMode=y;const u={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};d.ImageKind=u;const _={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};d.AnnotationType=_;const w={GROUP:"Group",REPLY:"R"};d.AnnotationReplyType=w;const C={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};d.AnnotationFlag=C;const A={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};d.AnnotationFieldFlag=A;const a={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};d.AnnotationBorderStyleType=a;const l={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};d.AnnotationActionEventType=l;const P={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};d.DocumentActionEventType=P;const p={O:"PageOpen",C:"PageClose"};d.PageActionEventType=p;const r={ERRORS:0,WARNINGS:1,INFOS:5};d.VerbosityLevel=r;const T={NONE:0,BINARY:1};d.CMapCompressionType=T;const m={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};d.OPS=m;const B={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};d.PasswordResponses=B;let z=r.WARNINGS;function E(ot){Number.isInteger(ot)&&(z=ot)}function V(){return z}function it(ot){z>=r.INFOS&&console.log(`Info: ${ot}`)}function nt(ot){z>=r.WARNINGS&&console.log(`Warning: ${ot}`)}function H(ot){throw new Error(ot)}function lt(ot,Y){ot||H(Y)}function pt(ot){switch(ot==null?void 0:ot.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function wt(ot,Y=null,G=null){if(!ot)return null;try{if(G&&typeof ot=="string"){if(G.addDefaultProtocol&&ot.startsWith("www.")){const _t=ot.match(/\./g);(_t==null?void 0:_t.length)>=2&&(ot=`http://${ot}`)}if(G.tryConvertEncoding)try{ot=K(ot)}catch{}}const mt=Y?new URL(ot,Y):new URL(ot);if(pt(mt))return mt}catch{}return null}function Pt(ot,Y,G,mt=!1){return Object.defineProperty(ot,Y,{value:G,enumerable:!mt,configurable:!0,writable:!1}),G}const S=function(){function Y(G,mt){this.constructor===Y&&H("Cannot initialize BaseException."),this.message=G,this.name=mt}return Y.prototype=new Error,Y.constructor=Y,Y}();d.BaseException=S;class i extends S{constructor(Y,G){super(Y,"PasswordException"),this.code=G}}d.PasswordException=i;class n extends S{constructor(Y,G){super(Y,"UnknownErrorException"),this.details=G}}d.UnknownErrorException=n;class s extends S{constructor(Y){super(Y,"InvalidPDFException")}}d.InvalidPDFException=s;class o extends S{constructor(Y){super(Y,"MissingPDFException")}}d.MissingPDFException=o;class c extends S{constructor(Y,G){super(Y,"UnexpectedResponseException"),this.status=G}}d.UnexpectedResponseException=c;class b extends S{constructor(Y){super(Y,"FormatError")}}d.FormatError=b;class F extends S{constructor(Y){super(Y,"AbortException")}}d.AbortException=F;function N(ot){(typeof ot!="object"||(ot==null?void 0:ot.length)===void 0)&&H("Invalid argument for bytesToString");const Y=ot.length,G=8192;if(Y<G)return String.fromCharCode.apply(null,ot);const mt=[];for(let _t=0;_t<Y;_t+=G){const te=Math.min(_t+G,Y),Zt=ot.subarray(_t,te);mt.push(String.fromCharCode.apply(null,Zt))}return mt.join("")}function tt(ot){typeof ot!="string"&&H("Invalid argument for stringToBytes");const Y=ot.length,G=new Uint8Array(Y);for(let mt=0;mt<Y;++mt)G[mt]=ot.charCodeAt(mt)&255;return G}function Q(ot){return String.fromCharCode(ot>>24&255,ot>>16&255,ot>>8&255,ot&255)}function st(ot){return Object.keys(ot).length}function ct(ot){const Y=Object.create(null);for(const[G,mt]of ot)Y[G]=mt;return Y}function yt(){const ot=new Uint8Array(4);return ot[0]=1,new Uint32Array(ot.buffer,0,1)[0]===1}function dt(){try{return new Function(""),!0}catch{return!1}}class Ft{static get isLittleEndian(){return Pt(this,"isLittleEndian",yt())}static get isEvalSupported(){return Pt(this,"isEvalSupported",dt())}static get isOffscreenCanvasSupported(){return Pt(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get platform(){return typeof navigator>"u"?Pt(this,"platform",{isWin:!1,isMac:!1}):Pt(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){var Y,G;return Pt(this,"isCSSRoundSupported",(G=(Y=globalThis.CSS)==null?void 0:Y.supports)==null?void 0:G.call(Y,"width: round(1.5px, 1px)"))}}d.FeatureTest=Ft;const Bt=[...Array(256).keys()].map(ot=>ot.toString(16).padStart(2,"0"));class St{static makeHexColor(Y,G,mt){return`#${Bt[Y]}${Bt[G]}${Bt[mt]}`}static scaleMinMax(Y,G){let mt;Y[0]?(Y[0]<0&&(mt=G[0],G[0]=G[1],G[1]=mt),G[0]*=Y[0],G[1]*=Y[0],Y[3]<0&&(mt=G[2],G[2]=G[3],G[3]=mt),G[2]*=Y[3],G[3]*=Y[3]):(mt=G[0],G[0]=G[2],G[2]=mt,mt=G[1],G[1]=G[3],G[3]=mt,Y[1]<0&&(mt=G[2],G[2]=G[3],G[3]=mt),G[2]*=Y[1],G[3]*=Y[1],Y[2]<0&&(mt=G[0],G[0]=G[1],G[1]=mt),G[0]*=Y[2],G[1]*=Y[2]),G[0]+=Y[4],G[1]+=Y[4],G[2]+=Y[5],G[3]+=Y[5]}static transform(Y,G){return[Y[0]*G[0]+Y[2]*G[1],Y[1]*G[0]+Y[3]*G[1],Y[0]*G[2]+Y[2]*G[3],Y[1]*G[2]+Y[3]*G[3],Y[0]*G[4]+Y[2]*G[5]+Y[4],Y[1]*G[4]+Y[3]*G[5]+Y[5]]}static applyTransform(Y,G){const mt=Y[0]*G[0]+Y[1]*G[2]+G[4],_t=Y[0]*G[1]+Y[1]*G[3]+G[5];return[mt,_t]}static applyInverseTransform(Y,G){const mt=G[0]*G[3]-G[1]*G[2],_t=(Y[0]*G[3]-Y[1]*G[2]+G[2]*G[5]-G[4]*G[3])/mt,te=(-Y[0]*G[1]+Y[1]*G[0]+G[4]*G[1]-G[5]*G[0])/mt;return[_t,te]}static getAxialAlignedBoundingBox(Y,G){const mt=this.applyTransform(Y,G),_t=this.applyTransform(Y.slice(2,4),G),te=this.applyTransform([Y[0],Y[3]],G),Zt=this.applyTransform([Y[2],Y[1]],G);return[Math.min(mt[0],_t[0],te[0],Zt[0]),Math.min(mt[1],_t[1],te[1],Zt[1]),Math.max(mt[0],_t[0],te[0],Zt[0]),Math.max(mt[1],_t[1],te[1],Zt[1])]}static inverseTransform(Y){const G=Y[0]*Y[3]-Y[1]*Y[2];return[Y[3]/G,-Y[1]/G,-Y[2]/G,Y[0]/G,(Y[2]*Y[5]-Y[4]*Y[3])/G,(Y[4]*Y[1]-Y[5]*Y[0])/G]}static singularValueDecompose2dScale(Y){const G=[Y[0],Y[2],Y[1],Y[3]],mt=Y[0]*G[0]+Y[1]*G[2],_t=Y[0]*G[1]+Y[1]*G[3],te=Y[2]*G[0]+Y[3]*G[2],Zt=Y[2]*G[1]+Y[3]*G[3],q=(mt+Zt)/2,vt=Math.sqrt((mt+Zt)**2-4*(mt*Zt-te*_t))/2,Lt=q+vt||1,Tt=q-vt||1;return[Math.sqrt(Lt),Math.sqrt(Tt)]}static normalizeRect(Y){const G=Y.slice(0);return Y[0]>Y[2]&&(G[0]=Y[2],G[2]=Y[0]),Y[1]>Y[3]&&(G[1]=Y[3],G[3]=Y[1]),G}static intersect(Y,G){const mt=Math.max(Math.min(Y[0],Y[2]),Math.min(G[0],G[2])),_t=Math.min(Math.max(Y[0],Y[2]),Math.max(G[0],G[2]));if(mt>_t)return null;const te=Math.max(Math.min(Y[1],Y[3]),Math.min(G[1],G[3])),Zt=Math.min(Math.max(Y[1],Y[3]),Math.max(G[1],G[3]));return te>Zt?null:[mt,te,_t,Zt]}static bezierBoundingBox(Y,G,mt,_t,te,Zt,q,vt){const Lt=[],Tt=[[],[]];let Ot,Nt,Jt,bt,Yt,It,R,e;for(let $=0;$<2;++$){if($===0?(Nt=6*Y-12*mt+6*te,Ot=-3*Y+9*mt-9*te+3*q,Jt=3*mt-3*Y):(Nt=6*G-12*_t+6*Zt,Ot=-3*G+9*_t-9*Zt+3*vt,Jt=3*_t-3*G),Math.abs(Ot)<1e-12){if(Math.abs(Nt)<1e-12)continue;bt=-Jt/Nt,0<bt&&bt<1&&Lt.push(bt);continue}R=Nt*Nt-4*Jt*Ot,e=Math.sqrt(R),!(R<0)&&(Yt=(-Nt+e)/(2*Ot),0<Yt&&Yt<1&&Lt.push(Yt),It=(-Nt-e)/(2*Ot),0<It&&It<1&&Lt.push(It))}let f=Lt.length,D;const j=f;for(;f--;)bt=Lt[f],D=1-bt,Tt[0][f]=D*D*D*Y+3*D*D*bt*mt+3*D*bt*bt*te+bt*bt*bt*q,Tt[1][f]=D*D*D*G+3*D*D*bt*_t+3*D*bt*bt*Zt+bt*bt*bt*vt;return Tt[0][j]=Y,Tt[1][j]=G,Tt[0][j+1]=q,Tt[1][j+1]=vt,Tt[0].length=Tt[1].length=j+2,[Math.min(...Tt[0]),Math.min(...Tt[1]),Math.max(...Tt[0]),Math.max(...Tt[1])]}}d.Util=St;const Dt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function ut(ot){if(ot[0]>="ï"){let G;if(ot[0]==="þ"&&ot[1]==="ÿ"?G="utf-16be":ot[0]==="ÿ"&&ot[1]==="þ"?G="utf-16le":ot[0]==="ï"&&ot[1]==="»"&&ot[2]==="¿"&&(G="utf-8"),G)try{const mt=new TextDecoder(G,{fatal:!0}),_t=tt(ot);return mt.decode(_t)}catch(mt){nt(`stringToPDFString: "${mt}".`)}}const Y=[];for(let G=0,mt=ot.length;G<mt;G++){const _t=Dt[ot.charCodeAt(G)];Y.push(_t?String.fromCharCode(_t):ot.charAt(G))}return Y.join("")}function K(ot){return decodeURIComponent(escape(ot))}function J(ot){return unescape(encodeURIComponent(ot))}function ht(ot){return typeof ot=="object"&&(ot==null?void 0:ot.byteLength)!==void 0}function Et(ot,Y){if(ot.length!==Y.length)return!1;for(let G=0,mt=ot.length;G<mt;G++)if(ot[G]!==Y[G])return!1;return!0}function Ct(ot=new Date){return[ot.getUTCFullYear().toString(),(ot.getUTCMonth()+1).toString().padStart(2,"0"),ot.getUTCDate().toString().padStart(2,"0"),ot.getUTCHours().toString().padStart(2,"0"),ot.getUTCMinutes().toString().padStart(2,"0"),ot.getUTCSeconds().toString().padStart(2,"0")].join("")}class jt{constructor(){I(this,qt,!1);this.promise=new Promise((Y,G)=>{this.resolve=mt=>{Z(this,qt,!0),Y(mt)},this.reject=mt=>{Z(this,qt,!0),G(mt)}})}get settled(){return t(this,qt)}}qt=new WeakMap,d.PromiseCapability=jt;let Gt=null,Ht=null;function Xt(ot){return Gt||(Gt=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Ht=new Map([["ﬅ","ſt"]])),ot.replaceAll(Gt,(Y,G,mt)=>G?G.normalize("NFKC"):Ht.get(mt))}function Vt(){if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.randomUUID)=="function")return crypto.randomUUID();const ot=new Uint8Array(32);if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)=="function")crypto.getRandomValues(ot);else for(let Y=0;Y<32;Y++)ot[Y]=Math.floor(Math.random()*255);return N(ot)}const Wt="pdfjs_internal_id_";d.AnnotationPrefix=Wt},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{var At,rt,h,M,he,X,Ee,U,k,g,L,O,x,v,y,u,we,w,C,Be,a,l;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(3),_display_utils=__w_pdfjs_require__(6),_font_loader=__w_pdfjs_require__(9),_displayNode_utils=__w_pdfjs_require__(10),_canvas=__w_pdfjs_require__(11),_worker_options=__w_pdfjs_require__(14),_message_handler=__w_pdfjs_require__(15),_metadata=__w_pdfjs_require__(16),_optional_content_config=__w_pdfjs_require__(17),_transport_stream=__w_pdfjs_require__(18),_displayFetch_stream=__w_pdfjs_require__(19),_displayNetwork=__w_pdfjs_require__(22),_displayNode_stream=__w_pdfjs_require__(23),_displaySvg=__w_pdfjs_require__(24),_xfa_text=__w_pdfjs_require__(25);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;function getDocument(p){if(typeof p=="string"||p instanceof URL?p={url:p}:(0,_util.isArrayBuffer)(p)&&(p={data:p}),typeof p!="object")throw new Error("Invalid parameter in getDocument, need parameter object.");if(!p.url&&!p.data&&!p.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const r=new PDFDocumentLoadingTask,{docId:T}=r,m=p.url?getUrlProp(p.url):null,B=p.data?getDataProp(p.data):null,z=p.httpHeaders||null,E=p.withCredentials===!0,V=p.password??null,it=p.range instanceof PDFDataRangeTransport?p.range:null,nt=Number.isInteger(p.rangeChunkSize)&&p.rangeChunkSize>0?p.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let H=p.worker instanceof PDFWorker?p.worker:null;const lt=p.verbosity,pt=typeof p.docBaseUrl=="string"&&!(0,_display_utils.isDataScheme)(p.docBaseUrl)?p.docBaseUrl:null,wt=typeof p.cMapUrl=="string"?p.cMapUrl:null,Pt=p.cMapPacked!==!1,S=p.CMapReaderFactory||DefaultCMapReaderFactory,i=typeof p.standardFontDataUrl=="string"?p.standardFontDataUrl:null,n=p.StandardFontDataFactory||DefaultStandardFontDataFactory,s=p.stopAtErrors!==!0,o=Number.isInteger(p.maxImageSize)&&p.maxImageSize>-1?p.maxImageSize:-1,c=p.isEvalSupported!==!1,b=typeof p.isOffscreenCanvasSupported=="boolean"?p.isOffscreenCanvasSupported:!_util.isNodeJS,F=Number.isInteger(p.canvasMaxAreaInBytes)?p.canvasMaxAreaInBytes:-1,N=typeof p.disableFontFace=="boolean"?p.disableFontFace:_util.isNodeJS,tt=p.fontExtraProperties===!0,Q=p.enableXfa===!0,st=p.ownerDocument||globalThis.document,ct=p.disableRange===!0,yt=p.disableStream===!0,dt=p.disableAutoFetch===!0,Ft=p.pdfBug===!0,Bt=it?it.length:p.length??NaN,St=typeof p.useSystemFonts=="boolean"?p.useSystemFonts:!_util.isNodeJS&&!N,Dt=typeof p.useWorkerFetch=="boolean"?p.useWorkerFetch:S===_display_utils.DOMCMapReaderFactory&&n===_display_utils.DOMStandardFontDataFactory&&wt&&i&&(0,_display_utils.isValidFetchUrl)(wt,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(i,document.baseURI),ut=p.canvasFactory||new DefaultCanvasFactory({ownerDocument:st}),K=p.filterFactory||new DefaultFilterFactory({docId:T,ownerDocument:st}),J=null;(0,_util.setVerbosityLevel)(lt);const ht={canvasFactory:ut,filterFactory:K};if(Dt||(ht.cMapReaderFactory=new S({baseUrl:wt,isCompressed:Pt}),ht.standardFontDataFactory=new n({baseUrl:i})),!H){const jt={verbosity:lt,port:_worker_options.GlobalWorkerOptions.workerPort};H=jt.port?PDFWorker.fromPort(jt):new PDFWorker(jt),r._worker=H}const Et={docId:T,apiVersion:"3.11.174",data:B,password:V,disableAutoFetch:dt,rangeChunkSize:nt,length:Bt,docBaseUrl:pt,enableXfa:Q,evaluatorOptions:{maxImageSize:o,disableFontFace:N,ignoreErrors:s,isEvalSupported:c,isOffscreenCanvasSupported:b,canvasMaxAreaInBytes:F,fontExtraProperties:tt,useSystemFonts:St,cMapUrl:Dt?wt:null,standardFontDataUrl:Dt?i:null}},Ct={ignoreErrors:s,isEvalSupported:c,disableFontFace:N,fontExtraProperties:tt,enableXfa:Q,ownerDocument:st,disableAutoFetch:dt,pdfBug:Ft,styleElement:J};return H.promise.then(function(){if(r.destroyed)throw new Error("Loading aborted");const jt=_fetchDocument(H,Et),Gt=new Promise(function(Ht){let Xt;it?Xt=new _transport_stream.PDFDataTransportStream({length:Bt,initialData:it.initialData,progressiveDone:it.progressiveDone,contentDispositionFilename:it.contentDispositionFilename,disableRange:ct,disableStream:yt},it):B||(Xt=(Wt=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(Wt):(0,_display_utils.isValidFetchUrl)(Wt.url)?new _displayFetch_stream.PDFFetchStream(Wt):new _displayNetwork.PDFNetworkStream(Wt))({url:m,length:Bt,httpHeaders:z,withCredentials:E,rangeChunkSize:nt,disableRange:ct,disableStream:yt})),Ht(Xt)});return Promise.all([jt,Gt]).then(function([Ht,Xt]){if(r.destroyed)throw new Error("Loading aborted");const Vt=new _message_handler.MessageHandler(T,Ht,H.port),Wt=new WorkerTransport(Vt,r,Xt,Ct,ht);r._transport=Wt,Vt.send("Ready",null)})}).catch(r._capability.reject),r}async function _fetchDocument(p,r){if(p.destroyed)throw new Error("Worker was destroyed");const T=await p.messageHandler.sendWithPromise("GetDocRequest",r,r.data?[r.data.buffer]:null);if(p.destroyed)throw new Error("Worker was destroyed");return T}function getUrlProp(p){if(p instanceof URL)return p.href;try{return new URL(p,window.location).href}catch{if(_util.isNodeJS&&typeof p=="string")return p}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(p){if(_util.isNodeJS&&typeof Buffer<"u"&&p instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(p instanceof Uint8Array&&p.byteLength===p.buffer.byteLength)return p;if(typeof p=="string")return(0,_util.stringToBytes)(p);if(typeof p=="object"&&!isNaN(p==null?void 0:p.length)||(0,_util.isArrayBuffer)(p))return new Uint8Array(p);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}const d=class d{constructor(){this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId=`d${ge(d,At)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){var r,T,m;this.destroyed=!0;try{(r=this._worker)!=null&&r.port&&(this._worker._pendingDestroy=!0),await((T=this._transport)==null?void 0:T.destroy())}catch(B){throw(m=this._worker)!=null&&m.port&&delete this._worker._pendingDestroy,B}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}};At=new WeakMap,I(d,At,0);let PDFDocumentLoadingTask=d;exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(r,T,m=!1,B=null){this.length=r,this.initialData=T,this.progressiveDone=m,this.contentDispositionFilename=B,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}addRangeListener(r){this._rangeListeners.push(r)}addProgressListener(r){this._progressListeners.push(r)}addProgressiveReadListener(r){this._progressiveReadListeners.push(r)}addProgressiveDoneListener(r){this._progressiveDoneListeners.push(r)}onDataRange(r,T){for(const m of this._rangeListeners)m(r,T)}onDataProgress(r,T){this._readyCapability.promise.then(()=>{for(const m of this._progressListeners)m(r,T)})}onDataProgressiveRead(r){this._readyCapability.promise.then(()=>{for(const T of this._progressiveReadListeners)T(r)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const r of this._progressiveDoneListeners)r()})}transportReady(){this._readyCapability.resolve()}requestDataRange(r,T){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(r,T){this._pdfInfo=r,this._transport=T,Object.defineProperty(this,"getJavaScript",{value:()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),this.getJSActions().then(m=>{if(!m)return m;const B=[];for(const z in m)B.push(...m[z]);return B}))})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(r){return this._transport.getPage(r)}getPageIndex(r){return this._transport.getPageIndex(r)}getDestinations(){return this._transport.getDestinations()}getDestination(r){return this._transport.getDestination(r)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(r=!1){return this._transport.startCleanup(r||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(r,T,m,B=!1){I(this,M);I(this,X);I(this,rt,null);I(this,h,!1);this._pageIndex=r,this._pageInfo=T,this._transport=m,this._stats=B?new _display_utils.StatTimer:null,this._pdfBug=B,this.commonObjs=m.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:r,rotation:T=this.rotate,offsetX:m=0,offsetY:B=0,dontFlip:z=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:r,rotation:T,offsetX:m,offsetY:B,dontFlip:z})}getAnnotations({intent:r="display"}={}){const T=this._transport.getRenderingIntent(r);return this._transport.getAnnotations(this._pageIndex,T.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var r;return((r=this._transport._htmlForXfa)==null?void 0:r.children[this._pageIndex])||null}render({canvasContext:r,viewport:T,intent:m="display",annotationMode:B=_util.AnnotationMode.ENABLE,transform:z=null,background:E=null,optionalContentConfigPromise:V=null,annotationCanvasMap:it=null,pageColors:nt=null,printAnnotationStorage:H=null}){var n,s;(n=this._stats)==null||n.time("Overall");const lt=this._transport.getRenderingIntent(m,B,H);Z(this,h,!1),W(this,X,Ee).call(this),V||(V=this._transport.getOptionalContentConfig());let pt=this._intentStates.get(lt.cacheKey);pt||(pt=Object.create(null),this._intentStates.set(lt.cacheKey,pt)),pt.streamReaderCancelTimeout&&(clearTimeout(pt.streamReaderCancelTimeout),pt.streamReaderCancelTimeout=null);const wt=!!(lt.renderingIntent&_util.RenderingIntentFlag.PRINT);pt.displayReadyCapability||(pt.displayReadyCapability=new _util.PromiseCapability,pt.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(s=this._stats)==null||s.time("Page Request"),this._pumpOperatorList(lt));const Pt=o=>{var c,b;pt.renderTasks.delete(S),(this._maybeCleanupAfterRender||wt)&&Z(this,h,!0),W(this,M,he).call(this,!wt),o?(S.capability.reject(o),this._abortOperatorList({intentState:pt,reason:o instanceof Error?o:new Error(o)})):S.capability.resolve(),(c=this._stats)==null||c.timeEnd("Rendering"),(b=this._stats)==null||b.timeEnd("Overall")},S=new InternalRenderTask({callback:Pt,params:{canvasContext:r,viewport:T,transform:z,background:E},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:it,operatorList:pt.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!wt,pdfBug:this._pdfBug,pageColors:nt});(pt.renderTasks||(pt.renderTasks=new Set)).add(S);const i=S.task;return Promise.all([pt.displayReadyCapability.promise,V]).then(([o,c])=>{var b;if(this.destroyed){Pt();return}(b=this._stats)==null||b.time("Rendering"),S.initializeGraphics({transparency:o,optionalContentConfig:c}),S.operatorListChanged()}).catch(Pt),i}getOperatorList({intent:r="display",annotationMode:T=_util.AnnotationMode.ENABLE,printAnnotationStorage:m=null}={}){var it;function B(){E.operatorList.lastChunk&&(E.opListReadCapability.resolve(E.operatorList),E.renderTasks.delete(V))}const z=this._transport.getRenderingIntent(r,T,m,!0);let E=this._intentStates.get(z.cacheKey);E||(E=Object.create(null),this._intentStates.set(z.cacheKey,E));let V;return E.opListReadCapability||(V=Object.create(null),V.operatorListChanged=B,E.opListReadCapability=new _util.PromiseCapability,(E.renderTasks||(E.renderTasks=new Set)).add(V),E.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(it=this._stats)==null||it.time("Page Request"),this._pumpOperatorList(z)),E.opListReadCapability.promise}streamTextContent({includeMarkedContent:r=!1,disableNormalization:T=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:r===!0,disableNormalization:T===!0},{highWaterMark:100,size(B){return B.items.length}})}getTextContent(r={}){if(this._transport._htmlForXfa)return this.getXfa().then(m=>_xfa_text.XfaText.textContent(m));const T=this.streamTextContent(r);return new Promise(function(m,B){function z(){E.read().then(function({value:it,done:nt}){if(nt){m(V);return}Object.assign(V.styles,it.styles),V.items.push(...it.items),z()},B)}const E=T.getReader(),V={items:[],styles:Object.create(null)};z()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const r=[];for(const T of this._intentStates.values())if(this._abortOperatorList({intentState:T,reason:new Error("Page was destroyed."),force:!0}),!T.opListReadCapability)for(const m of T.renderTasks)r.push(m.completed),m.cancel();return this.objs.clear(),Z(this,h,!1),W(this,X,Ee).call(this),Promise.all(r)}cleanup(r=!1){Z(this,h,!0);const T=W(this,M,he).call(this,!1);return r&&T&&this._stats&&(this._stats=new _display_utils.StatTimer),T}_startRenderPage(r,T){var B,z;const m=this._intentStates.get(T);m&&((B=this._stats)==null||B.timeEnd("Page Request"),(z=m.displayReadyCapability)==null||z.resolve(r))}_renderPageChunk(r,T){for(let m=0,B=r.length;m<B;m++)T.operatorList.fnArray.push(r.fnArray[m]),T.operatorList.argsArray.push(r.argsArray[m]);T.operatorList.lastChunk=r.lastChunk,T.operatorList.separateAnnots=r.separateAnnots;for(const m of T.renderTasks)m.operatorListChanged();r.lastChunk&&W(this,M,he).call(this,!0)}_pumpOperatorList({renderingIntent:r,cacheKey:T,annotationStorageSerializable:m}){const{map:B,transfers:z}=m,V=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:r,cacheKey:T,annotationStorage:B},z).getReader(),it=this._intentStates.get(T);it.streamReader=V;const nt=()=>{V.read().then(({value:H,done:lt})=>{if(lt){it.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(H,it),nt())},H=>{if(it.streamReader=null,!this._transport.destroyed){if(it.operatorList){it.operatorList.lastChunk=!0;for(const lt of it.renderTasks)lt.operatorListChanged();W(this,M,he).call(this,!0)}if(it.displayReadyCapability)it.displayReadyCapability.reject(H);else if(it.opListReadCapability)it.opListReadCapability.reject(H);else throw H}})};nt()}_abortOperatorList({intentState:r,reason:T,force:m=!1}){if(r.streamReader){if(r.streamReaderCancelTimeout&&(clearTimeout(r.streamReaderCancelTimeout),r.streamReaderCancelTimeout=null),!m){if(r.renderTasks.size>0)return;if(T instanceof _display_utils.RenderingCancelledException){let B=RENDERING_CANCELLED_TIMEOUT;T.extraDelay>0&&T.extraDelay<1e3&&(B+=T.extraDelay),r.streamReaderCancelTimeout=setTimeout(()=>{r.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:r,reason:T,force:!0})},B);return}}if(r.streamReader.cancel(new _util.AbortException(T.message)).catch(()=>{}),r.streamReader=null,!this._transport.destroyed){for(const[B,z]of this._intentStates)if(z===r){this._intentStates.delete(B);break}this.cleanup()}}}get stats(){return this._stats}}rt=new WeakMap,h=new WeakMap,M=new WeakSet,he=function(r=!1){if(W(this,X,Ee).call(this),!t(this,h)||this.destroyed)return!1;if(r)return Z(this,rt,setTimeout(()=>{Z(this,rt,null),W(this,M,he).call(this,!1)},DELAYED_CLEANUP_TIMEOUT)),!1;for(const{renderTasks:T,operatorList:m}of this._intentStates.values())if(T.size>0||!m.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),Z(this,h,!1),!0},X=new WeakSet,Ee=function(){t(this,rt)&&(clearTimeout(t(this,rt)),Z(this,rt,null))},exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){I(this,U,new Set);I(this,k,Promise.resolve())}postMessage(r,T){const m={data:structuredClone(r,T?{transfer:T}:null)};t(this,k).then(()=>{for(const B of t(this,U))B.call(this,m)})}addEventListener(r,T){t(this,U).add(T)}removeEventListener(r,T){t(this,U).delete(T)}terminate(){t(this,U).clear()}}U=new WeakMap,k=new WeakMap,exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;{if(_util.isNodeJS&&typeof commonjsRequire=="function")PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if(typeof document=="object"){const p=(g=document==null?void 0:document.currentScript)==null?void 0:g.src;p&&(PDFWorkerUtil.fallbackWorkerSrc=p.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(p,r){let T;try{if(T=new URL(p),!T.origin||T.origin==="null")return!1}catch{return!1}const m=new URL(r,T);return T.origin===m.origin},PDFWorkerUtil.createCDNWrapper=function(p){const r=`importScripts("${p}");`;return URL.createObjectURL(new Blob([r]))}}const _PDFWorker=class _PDFWorker{constructor({name:p=null,port:r=null,verbosity:T=(0,_util.getVerbosityLevel)()}={}){var m;if(this.name=p,this.destroyed=!1,this.verbosity=T,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,r){if((m=t(_PDFWorker,L))!=null&&m.has(r))throw new Error("Cannot use more than one PDFWorker per port.");(t(_PDFWorker,L)||Z(_PDFWorker,L,new WeakMap)).set(r,this),this._initializeFromPort(r);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(p){this._port=p,this._messageHandler=new _message_handler.MessageHandler("main","worker",p),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!_PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:p}=_PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,p)||(p=PDFWorkerUtil.createCDNWrapper(new URL(p,window.location).href));const r=new Worker(p),T=new _message_handler.MessageHandler("main","worker",r),m=()=>{r.removeEventListener("error",B),T.destroy(),r.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},B=()=>{this._webWorker||m()};r.addEventListener("error",B),T.on("test",E=>{if(r.removeEventListener("error",B),this.destroyed){m();return}E?(this._messageHandler=T,this._port=r,this._webWorker=r,this._readyCapability.resolve(),T.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),T.destroy(),r.terminate())}),T.on("ready",E=>{if(r.removeEventListener("error",B),this.destroyed){m();return}try{z()}catch{this._setupFakeWorker()}});const z=()=>{const E=new Uint8Array;T.send("test",E,[E.buffer])};z();return}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),_PDFWorker._setupFakeWorkerGlobal.then(p=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const r=new LoopbackPort;this._port=r;const T=`fake${PDFWorkerUtil.fakeWorkerId++}`,m=new _message_handler.MessageHandler(T+"_worker",T,r);p.setup(m,r);const B=new _message_handler.MessageHandler(T,T+"_worker",r);this._messageHandler=B,this._readyCapability.resolve(),B.send("configure",{verbosity:this.verbosity})}).catch(p=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${p.message}".`))})}destroy(){var p;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),(p=t(_PDFWorker,L))==null||p.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(p){var T;if(!(p!=null&&p.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const r=(T=t(this,L))==null?void 0:T.get(p.port);if(r){if(r._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return r}return new _PDFWorker(p)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(PDFWorkerUtil.fallbackWorkerSrc!==null)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){var p;try{return((p=globalThis.pdfjsWorker)==null?void 0:p.WorkerMessageHandler)||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&typeof commonjsRequire=="function"){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}};L=new WeakMap,I(_PDFWorker,L,void 0);let PDFWorker=_PDFWorker;exports.PDFWorker=PDFWorker;class WorkerTransport{constructor(r,T,m,B,z){I(this,u);I(this,O,new Map);I(this,x,new Map);I(this,v,new Map);I(this,y,null);this.messageHandler=r,this.loadingTask=T,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:B.ownerDocument,styleElement:B.styleElement}),this._params=B,this.canvasFactory=z.canvasFactory,this.filterFactory=z.filterFactory,this.cMapReaderFactory=z.cMapReaderFactory,this.standardFontDataFactory=z.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=m,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(r,T=_util.AnnotationMode.ENABLE,m=null,B=!1){let z=_util.RenderingIntentFlag.DISPLAY,E=_annotation_storage.SerializableEmpty;switch(r){case"any":z=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":z=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${r}`)}switch(T){case _util.AnnotationMode.DISABLE:z+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:z+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:z+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE,E=(z&_util.RenderingIntentFlag.PRINT&&m instanceof _annotation_storage.PrintAnnotationStorage?m:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${T}`)}return B&&(z+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:z,cacheKey:`${z}_${E.hash}`,annotationStorageSerializable:E}}destroy(){var m;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,(m=t(this,y))==null||m.reject(new Error("Worker was destroyed during onPassword callback"));const r=[];for(const B of t(this,x).values())r.push(B._destroy());t(this,x).clear(),t(this,v).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const T=this.messageHandler.sendWithPromise("Terminate",null);return r.push(T),Promise.all(r).then(()=>{var B;this.commonObjs.clear(),this.fontLoader.clear(),t(this,O).clear(),this.filterFactory.destroy(),(B=this._networkStream)==null||B.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:r,loadingTask:T}=this;r.on("GetReader",(m,B)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=z=>{this._lastProgress={loaded:z.loaded,total:z.total}},B.onPull=()=>{this._fullReader.read().then(function({value:z,done:E}){if(E){B.close();return}(0,_util.assert)(z instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),B.enqueue(new Uint8Array(z),1,[z])}).catch(z=>{B.error(z)})},B.onCancel=z=>{this._fullReader.cancel(z),B.ready.catch(E=>{if(!this.destroyed)throw E})}}),r.on("ReaderHeadersReady",m=>{const B=new _util.PromiseCapability,z=this._fullReader;return z.headersReady.then(()=>{var E;(!z.isStreamingSupported||!z.isRangeSupported)&&(this._lastProgress&&((E=T.onProgress)==null||E.call(T,this._lastProgress)),z.onProgress=V=>{var it;(it=T.onProgress)==null||it.call(T,{loaded:V.loaded,total:V.total})}),B.resolve({isStreamingSupported:z.isStreamingSupported,isRangeSupported:z.isRangeSupported,contentLength:z.contentLength})},B.reject),B.promise}),r.on("GetRangeReader",(m,B)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const z=this._networkStream.getRangeReader(m.begin,m.end);if(!z){B.close();return}B.onPull=()=>{z.read().then(function({value:E,done:V}){if(V){B.close();return}(0,_util.assert)(E instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),B.enqueue(new Uint8Array(E),1,[E])}).catch(E=>{B.error(E)})},B.onCancel=E=>{z.cancel(E),B.ready.catch(V=>{if(!this.destroyed)throw V})}}),r.on("GetDoc",({pdfInfo:m})=>{this._numPages=m.numPages,this._htmlForXfa=m.htmlForXfa,delete m.htmlForXfa,T._capability.resolve(new PDFDocumentProxy(m,this))}),r.on("DocException",function(m){let B;switch(m.name){case"PasswordException":B=new _util.PasswordException(m.message,m.code);break;case"InvalidPDFException":B=new _util.InvalidPDFException(m.message);break;case"MissingPDFException":B=new _util.MissingPDFException(m.message);break;case"UnexpectedResponseException":B=new _util.UnexpectedResponseException(m.message,m.status);break;case"UnknownErrorException":B=new _util.UnknownErrorException(m.message,m.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}T._capability.reject(B)}),r.on("PasswordRequest",m=>{if(Z(this,y,new _util.PromiseCapability),T.onPassword){const B=z=>{z instanceof Error?t(this,y).reject(z):t(this,y).resolve({password:z})};try{T.onPassword(B,m.code)}catch(z){t(this,y).reject(z)}}else t(this,y).reject(new _util.PasswordException(m.message,m.code));return t(this,y).promise}),r.on("DataLoaded",m=>{var B;(B=T.onProgress)==null||B.call(T,{loaded:m.length,total:m.length}),this.downloadInfoCapability.resolve(m)}),r.on("StartRenderPage",m=>{if(this.destroyed)return;t(this,x).get(m.pageIndex)._startRenderPage(m.transparency,m.cacheKey)}),r.on("commonobj",([m,B,z])=>{var E;if(!this.destroyed&&!this.commonObjs.has(m))switch(B){case"Font":const V=this._params;if("error"in z){const H=z.error;(0,_util.warn)(`Error during font loading: ${H}`),this.commonObjs.resolve(m,H);break}const it=V.pdfBug&&((E=globalThis.FontInspector)!=null&&E.enabled)?(H,lt)=>globalThis.FontInspector.fontAdded(H,lt):null,nt=new _font_loader.FontFaceObject(z,{isEvalSupported:V.isEvalSupported,disableFontFace:V.disableFontFace,ignoreErrors:V.ignoreErrors,inspectFont:it});this.fontLoader.bind(nt).catch(H=>r.sendWithPromise("FontFallback",{id:m})).finally(()=>{!V.fontExtraProperties&&nt.data&&(nt.data=null),this.commonObjs.resolve(m,nt)});break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(m,z);break;default:throw new Error(`Got unknown common object type ${B}`)}}),r.on("obj",([m,B,z,E])=>{var it;if(this.destroyed)return;const V=t(this,x).get(B);if(!V.objs.has(m))switch(z){case"Image":if(V.objs.resolve(m,E),E){let nt;if(E.bitmap){const{width:H,height:lt}=E;nt=H*lt*4}else nt=((it=E.data)==null?void 0:it.length)||0;nt>_util.MAX_IMAGE_SIZE_TO_CACHE&&(V._maybeCleanupAfterRender=!0)}break;case"Pattern":V.objs.resolve(m,E);break;default:throw new Error(`Got unknown object type ${z}`)}}),r.on("DocProgress",m=>{var B;this.destroyed||(B=T.onProgress)==null||B.call(T,{loaded:m.loaded,total:m.total})}),r.on("FetchBuiltInCMap",m=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(m):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),r.on("FetchStandardFontData",m=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(m):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var m;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:r,transfers:T}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:r,filename:((m=this._fullReader)==null?void 0:m.filename)??null},T).finally(()=>{this.annotationStorage.resetModified()})}getPage(r){if(!Number.isInteger(r)||r<=0||r>this._numPages)return Promise.reject(new Error("Invalid page request."));const T=r-1,m=t(this,v).get(T);if(m)return m;const B=this.messageHandler.sendWithPromise("GetPage",{pageIndex:T}).then(z=>{if(this.destroyed)throw new Error("Transport destroyed");const E=new PDFPageProxy(T,z,this,this._params.pdfBug);return t(this,x).set(T,E),E});return t(this,v).set(T,B),B}getPageIndex(r){return typeof r!="object"||r===null||!Number.isInteger(r.num)||r.num<0||!Number.isInteger(r.gen)||r.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:r.num,gen:r.gen})}getAnnotations(r,T){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:r,intent:T})}getFieldObjects(){return W(this,u,we).call(this,"GetFieldObjects")}hasJSActions(){return W(this,u,we).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(r){return typeof r!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:r})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return W(this,u,we).call(this,"GetDocJSActions")}getPageJSActions(r){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:r})}getStructTree(r){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:r})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then(r=>new _optional_content_config.OptionalContentConfig(r))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const r="GetMetadata",T=t(this,O).get(r);if(T)return T;const m=this.messageHandler.sendWithPromise(r,null).then(B=>{var z,E;return{info:B[0],metadata:B[1]?new _metadata.Metadata(B[1]):null,contentDispositionFilename:((z=this._fullReader)==null?void 0:z.filename)??null,contentLength:((E=this._fullReader)==null?void 0:E.contentLength)??null}});return t(this,O).set(r,m),m}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(r=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const T of t(this,x).values())if(!T.cleanup())throw new Error(`startCleanup: Page ${T.pageNumber} is currently rendering.`);this.commonObjs.clear(),r||this.fontLoader.clear(),t(this,O).clear(),this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:r,enableXfa:T}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:r,enableXfa:T})}}O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakSet,we=function(r,T=null){const m=t(this,O).get(r);if(m)return m;const B=this.messageHandler.sendWithPromise(r,T);return t(this,O).set(r,B),B};class PDFObjects{constructor(){I(this,C);I(this,w,Object.create(null))}get(r,T=null){if(T){const B=W(this,C,Be).call(this,r);return B.capability.promise.then(()=>T(B.data)),null}const m=t(this,w)[r];if(!(m!=null&&m.capability.settled))throw new Error(`Requesting object that isn't resolved yet ${r}.`);return m.data}has(r){const T=t(this,w)[r];return(T==null?void 0:T.capability.settled)||!1}resolve(r,T=null){const m=W(this,C,Be).call(this,r);m.data=T,m.capability.resolve()}clear(){var r;for(const T in t(this,w)){const{data:m}=t(this,w)[T];(r=m==null?void 0:m.bitmap)==null||r.close()}Z(this,w,Object.create(null))}}w=new WeakMap,C=new WeakSet,Be=function(r){var T;return(T=t(this,w))[r]||(T[r]={capability:new _util.PromiseCapability,data:null})};class RenderTask{constructor(r){I(this,a,null);Z(this,a,r),this.onContinue=null}get promise(){return t(this,a).capability.promise}cancel(r=0){t(this,a).cancel(null,r)}get separateAnnots(){const{separateAnnots:r}=t(this,a).operatorList;if(!r)return!1;const{annotationCanvasMap:T}=t(this,a);return r.form||r.canvas&&(T==null?void 0:T.size)>0}}a=new WeakMap,exports.RenderTask=RenderTask;const P=class P{constructor({callback:r,params:T,objs:m,commonObjs:B,annotationCanvasMap:z,operatorList:E,pageIndex:V,canvasFactory:it,filterFactory:nt,useRequestAnimationFrame:H=!1,pdfBug:lt=!1,pageColors:pt=null}){this.callback=r,this.params=T,this.objs=m,this.commonObjs=B,this.annotationCanvasMap=z,this.operatorListIdx=null,this.operatorList=E,this._pageIndex=V,this.canvasFactory=it,this.filterFactory=nt,this._pdfBug=lt,this.pageColors=pt,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=H===!0&&typeof window<"u",this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=T.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:r=!1,optionalContentConfig:T}){var V,it;if(this.cancelled)return;if(this._canvas){if(t(P,l).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");t(P,l).add(this._canvas)}this._pdfBug&&((V=globalThis.StepperManager)!=null&&V.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:m,viewport:B,transform:z,background:E}=this.params;this.gfx=new _canvas.CanvasGraphics(m,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:T},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:z,viewport:B,transparency:r,background:E}),this.operatorListIdx=0,this.graphicsReady=!0,(it=this.graphicsReadyCallback)==null||it.call(this)}cancel(r=null,T=0){var m;this.running=!1,this.cancelled=!0,(m=this.gfx)==null||m.endDrawing(),t(P,l).delete(this._canvas),this.callback(r||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,T))}operatorListChanged(){var r;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(r=this.stepper)==null||r.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),t(P,l).delete(this._canvas),this.callback())))}};l=new WeakMap,I(P,l,new WeakSet);let InternalRenderTask=P;const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(At,d,rt)=>{var k,g,L,Ai,x;Object.defineProperty(d,"__esModule",{value:!0}),d.SerializableEmpty=d.PrintAnnotationStorage=d.AnnotationStorage=void 0;var h=rt(1),M=rt(4),at=rt(8);const X=Object.freeze({map:null,hash:"",transfers:void 0});d.SerializableEmpty=X;class ft{constructor(){I(this,L);I(this,k,!1);I(this,g,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(y,u){const _=t(this,g).get(y);return _===void 0?u:Object.assign(u,_)}getRawValue(y){return t(this,g).get(y)}remove(y){if(t(this,g).delete(y),t(this,g).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const u of t(this,g).values())if(u instanceof M.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(y,u){const _=t(this,g).get(y);let w=!1;if(_!==void 0)for(const[C,A]of Object.entries(u))_[C]!==A&&(w=!0,_[C]=A);else w=!0,t(this,g).set(y,u);w&&W(this,L,Ai).call(this),u instanceof M.AnnotationEditor&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(u.constructor._type)}has(y){return t(this,g).has(y)}getAll(){return t(this,g).size>0?(0,h.objectFromMap)(t(this,g)):null}setAll(y){for(const[u,_]of Object.entries(y))this.setValue(u,_)}get size(){return t(this,g).size}resetModified(){t(this,k)&&(Z(this,k,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new U(this)}get serializable(){if(t(this,g).size===0)return X;const y=new Map,u=new at.MurmurHash3_64,_=[],w=Object.create(null);let C=!1;for(const[A,a]of t(this,g)){const l=a instanceof M.AnnotationEditor?a.serialize(!1,w):a;l&&(y.set(A,l),u.update(`${A}:${JSON.stringify(l)}`),C||(C=!!l.bitmap))}if(C)for(const A of y.values())A.bitmap&&_.push(A.bitmap);return y.size>0?{map:y,hash:u.hexdigest(),transfers:_}:X}}k=new WeakMap,g=new WeakMap,L=new WeakSet,Ai=function(){t(this,k)||(Z(this,k,!0),typeof this.onSetModified=="function"&&this.onSetModified())},d.AnnotationStorage=ft;class U extends ft{constructor(u){super();I(this,x,void 0);const{map:_,hash:w,transfers:C}=u.serializable,A=structuredClone(_,C?{transfer:C}:null);Z(this,x,{map:A,hash:w,transfers:C})}get print(){(0,h.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return t(this,x)}}x=new WeakMap,d.PrintAnnotationStorage=U},(At,d,rt)=>{var U,k,g,L,O,x,v,y,u,_,w,C,A,a,l,Ue,p,je,T,He,B,We,E,yi,it,vi,H,Si,pt,Ge,Pt,Ei;Object.defineProperty(d,"__esModule",{value:!0}),d.AnnotationEditor=void 0;var h=rt(5),M=rt(1),at=rt(6);const i=class i{constructor(s){I(this,l);I(this,p);I(this,B);I(this,E);I(this,it);I(this,H);I(this,pt);I(this,Pt);I(this,U,"");I(this,k,!1);I(this,g,null);I(this,L,null);I(this,O,null);I(this,x,!1);I(this,v,null);I(this,y,this.focusin.bind(this));I(this,u,this.focusout.bind(this));I(this,_,!1);I(this,w,!1);I(this,C,!1);ee(this,"_initialOptions",Object.create(null));ee(this,"_uiManager",null);ee(this,"_focusEventsAllowed",!0);ee(this,"_l10nPromise",null);I(this,A,!1);I(this,a,i._zIndex++);this.constructor===i&&(0,M.unreachable)("Cannot initialize AnnotationEditor."),this.parent=s.parent,this.id=s.id,this.width=this.height=null,this.pageIndex=s.parent.pageIndex,this.name=s.name,this.div=null,this._uiManager=s.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=s.isCentered,this._structTreeParentId=null;const{rotation:o,rawDims:{pageWidth:c,pageHeight:b,pageX:F,pageY:N}}=this.parent.viewport;this.rotation=o,this.pageRotation=(360+o-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[c,b],this.pageTranslation=[F,N];const[tt,Q]=this.parentDimensions;this.x=s.x/tt,this.y=s.y/Q,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,M.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(s){const o=new ft({id:s.parent.getNextId(),parent:s.parent,uiManager:s._uiManager});o.annotationElementId=s.annotationElementId,o.deleted=!0,o._uiManager.addToAnnotationStorage(o)}static initialize(s,o=null){if(i._l10nPromise||(i._l10nPromise=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map(b=>[b,s.get(b)]))),o!=null&&o.strings)for(const b of o.strings)i._l10nPromise.set(b,s.get(b));if(i._borderLineWidth!==-1)return;const c=getComputedStyle(document.documentElement);i._borderLineWidth=parseFloat(c.getPropertyValue("--outline-width"))||0}static updateDefaultParams(s,o){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(s){return!1}static paste(s,o){(0,M.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return t(this,A)}set _isDraggable(s){var o;Z(this,A,s),(o=this.div)==null||o.classList.toggle("draggable",s)}center(){const[s,o]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*o/(s*2),this.y+=this.width*s/(o*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*o/(s*2),this.y-=this.width*s/(o*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(s){this._uiManager.addCommands(s)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=t(this,a)}setParent(s){s!==null&&(this.pageIndex=s.pageIndex,this.pageDimensions=s.pageDimensions),this.parent=s}focusin(s){this._focusEventsAllowed&&(t(this,_)?Z(this,_,!1):this.parent.setSelected(this))}focusout(s){var c;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const o=s.relatedTarget;o!=null&&o.closest(`#${this.id}`)||(s.preventDefault(),(c=this.parent)!=null&&c.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(s,o,c,b){const[F,N]=this.parentDimensions;[c,b]=this.screenToPageTranslation(c,b),this.x=(s+c)/F,this.y=(o+b)/N,this.fixAndSetPosition()}translate(s,o){W(this,l,Ue).call(this,this.parentDimensions,s,o)}translateInPage(s,o){W(this,l,Ue).call(this,this.pageDimensions,s,o),this.div.scrollIntoView({block:"nearest"})}drag(s,o){const[c,b]=this.parentDimensions;if(this.x+=s/c,this.y+=o/b,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:st,y:ct}=this.div.getBoundingClientRect();this.parent.findNewParent(this,st,ct)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:F,y:N}=this;const[tt,Q]=W(this,p,je).call(this);F+=tt,N+=Q,this.div.style.left=`${(100*F).toFixed(2)}%`,this.div.style.top=`${(100*N).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}fixAndSetPosition(){const[s,o]=this.pageDimensions;let{x:c,y:b,width:F,height:N}=this;switch(F*=s,N*=o,c*=s,b*=o,this.rotation){case 0:c=Math.max(0,Math.min(s-F,c)),b=Math.max(0,Math.min(o-N,b));break;case 90:c=Math.max(0,Math.min(s-N,c)),b=Math.min(o,Math.max(F,b));break;case 180:c=Math.min(s,Math.max(F,c)),b=Math.min(o,Math.max(N,b));break;case 270:c=Math.min(s,Math.max(N,c)),b=Math.max(0,Math.min(o-F,b));break}this.x=c/=s,this.y=b/=o;const[tt,Q]=W(this,p,je).call(this);c+=tt,b+=Q;const{style:st}=this.div;st.left=`${(100*c).toFixed(2)}%`,st.top=`${(100*b).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(s,o){var c;return W(c=i,T,He).call(c,s,o,this.parentRotation)}pageTranslationToScreen(s,o){var c;return W(c=i,T,He).call(c,s,o,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:s,pageDimensions:[o,c]}=this,b=o*s,F=c*s;return M.FeatureTest.isCSSRoundSupported?[Math.round(b),Math.round(F)]:[b,F]}setDims(s,o){var F;const[c,b]=this.parentDimensions;this.div.style.width=`${(100*s/c).toFixed(2)}%`,t(this,x)||(this.div.style.height=`${(100*o/b).toFixed(2)}%`),(F=t(this,g))==null||F.classList.toggle("small",s<i.SMALL_EDITOR_SIZE||o<i.SMALL_EDITOR_SIZE)}fixDims(){const{style:s}=this.div,{height:o,width:c}=s,b=c.endsWith("%"),F=!t(this,x)&&o.endsWith("%");if(b&&F)return;const[N,tt]=this.parentDimensions;b||(s.width=`${(100*parseFloat(c)/N).toFixed(2)}%`),!t(this,x)&&!F&&(s.height=`${(100*parseFloat(o)/tt).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}async addAltTextButton(){if(t(this,g))return;const s=Z(this,g,document.createElement("button"));s.className="altText";const o=await i._l10nPromise.get("editor_alt_text_button_label");s.textContent=o,s.setAttribute("aria-label",o),s.tabIndex="0",s.addEventListener("contextmenu",at.noContextMenu),s.addEventListener("pointerdown",c=>c.stopPropagation()),s.addEventListener("click",c=>{c.preventDefault(),this._uiManager.editAltText(this)},{capture:!0}),s.addEventListener("keydown",c=>{c.target===s&&c.key==="Enter"&&(c.preventDefault(),this._uiManager.editAltText(this))}),W(this,pt,Ge).call(this),this.div.append(s),i.SMALL_EDITOR_SIZE||(i.SMALL_EDITOR_SIZE=Math.min(128,Math.round(s.getBoundingClientRect().width*1.4)))}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:t(this,U),decorative:t(this,k)}}set altTextData({altText:s,decorative:o}){t(this,U)===s&&t(this,k)===o||(Z(this,U,s),Z(this,k,o),W(this,pt,Ge).call(this))}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",t(this,y)),this.div.addEventListener("focusout",t(this,u));const[s,o]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*o/s).toFixed(2)}%`,this.div.style.maxHeight=`${(100*s/o).toFixed(2)}%`);const[c,b]=this.getInitialTranslation();return this.translate(c,b),(0,h.bindEvents)(this,this.div,["pointerdown"]),this.div}pointerdown(s){const{isMac:o}=M.FeatureTest.platform;if(s.button!==0||s.ctrlKey&&o){s.preventDefault();return}Z(this,_,!0),W(this,Pt,Ei).call(this,s)}moveInDOM(){var s;(s=this.parent)==null||s.moveEditorInDOM(this)}_setParentAndPosition(s,o,c){s.changeParent(this),this.x=o,this.y=c,this.fixAndSetPosition()}getRect(s,o){const c=this.parentScale,[b,F]=this.pageDimensions,[N,tt]=this.pageTranslation,Q=s/c,st=o/c,ct=this.x*b,yt=this.y*F,dt=this.width*b,Ft=this.height*F;switch(this.rotation){case 0:return[ct+Q+N,F-yt-st-Ft+tt,ct+Q+dt+N,F-yt-st+tt];case 90:return[ct+st+N,F-yt+Q+tt,ct+st+Ft+N,F-yt+Q+dt+tt];case 180:return[ct-Q-dt+N,F-yt+st+tt,ct-Q+N,F-yt+st+Ft+tt];case 270:return[ct-st-Ft+N,F-yt-Q-dt+tt,ct-st+N,F-yt-Q+tt];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(s,o){const[c,b,F,N]=s,tt=F-c,Q=N-b;switch(this.rotation){case 0:return[c,o-N,tt,Q];case 90:return[c,o-b,Q,tt];case 180:return[F,o-b,tt,Q];case 270:return[F,o-N,Q,tt];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){Z(this,C,!0)}disableEditMode(){Z(this,C,!1)}isInEditMode(){return t(this,C)}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var s,o;(s=this.div)==null||s.addEventListener("focusin",t(this,y)),(o=this.div)==null||o.addEventListener("focusout",t(this,u))}serialize(s=!1,o=null){(0,M.unreachable)("An editor must be serializable")}static deserialize(s,o,c){const b=new this.prototype.constructor({parent:o,id:o.getNextId(),uiManager:c});b.rotation=s.rotation;const[F,N]=b.pageDimensions,[tt,Q,st,ct]=b.getRectInCurrentCoords(s.rect,N);return b.x=tt/F,b.y=Q/N,b.width=st/F,b.height=ct/N,b}remove(){var s;this.div.removeEventListener("focusin",t(this,y)),this.div.removeEventListener("focusout",t(this,u)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),(s=t(this,g))==null||s.remove(),Z(this,g,null),Z(this,L,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(W(this,E,yi).call(this),t(this,v).classList.remove("hidden"))}select(){var s;this.makeResizable(),(s=this.div)==null||s.classList.add("selectedEditor")}unselect(){var s,o,c;(s=t(this,v))==null||s.classList.add("hidden"),(o=this.div)==null||o.classList.remove("selectedEditor"),(c=this.div)!=null&&c.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(s,o){}disableEditing(){t(this,g)&&(t(this,g).hidden=!0)}enableEditing(){t(this,g)&&(t(this,g).hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return t(this,w)}set isEditing(s){Z(this,w,s),this.parent&&(s?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(s,o){Z(this,x,!0);const c=s/o,{style:b}=this.div;b.aspectRatio=c,b.height="auto"}static get MIN_SIZE(){return 16}};U=new WeakMap,k=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakMap,w=new WeakMap,C=new WeakMap,A=new WeakMap,a=new WeakMap,l=new WeakSet,Ue=function([s,o],c,b){[c,b]=this.screenToPageTranslation(c,b),this.x+=c/s,this.y+=b/o,this.fixAndSetPosition()},p=new WeakSet,je=function(){const[s,o]=this.parentDimensions,{_borderLineWidth:c}=i,b=c/s,F=c/o;switch(this.rotation){case 90:return[-b,F];case 180:return[b,F];case 270:return[b,-F];default:return[-b,-F]}},T=new WeakSet,He=function(s,o,c){switch(c){case 90:return[o,-s];case 180:return[-s,-o];case 270:return[-o,s];default:return[s,o]}},B=new WeakSet,We=function(s){switch(s){case 90:{const[o,c]=this.pageDimensions;return[0,-o/c,c/o,0]}case 180:return[-1,0,0,-1];case 270:{const[o,c]=this.pageDimensions;return[0,o/c,-c/o,0]}default:return[1,0,0,1]}},E=new WeakSet,yi=function(){if(t(this,v))return;Z(this,v,document.createElement("div")),t(this,v).classList.add("resizers");const s=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||s.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const o of s){const c=document.createElement("div");t(this,v).append(c),c.classList.add("resizer",o),c.addEventListener("pointerdown",W(this,it,vi).bind(this,o)),c.addEventListener("contextmenu",at.noContextMenu)}this.div.prepend(t(this,v))},it=new WeakSet,vi=function(s,o){o.preventDefault();const{isMac:c}=M.FeatureTest.platform;if(o.button!==0||o.ctrlKey&&c)return;const b=W(this,H,Si).bind(this,s),F=this._isDraggable;this._isDraggable=!1;const N={passive:!0,capture:!0};window.addEventListener("pointermove",b,N);const tt=this.x,Q=this.y,st=this.width,ct=this.height,yt=this.parent.div.style.cursor,dt=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(o.target).cursor;const Ft=()=>{this._isDraggable=F,window.removeEventListener("pointerup",Ft),window.removeEventListener("blur",Ft),window.removeEventListener("pointermove",b,N),this.parent.div.style.cursor=yt,this.div.style.cursor=dt;const Bt=this.x,St=this.y,Dt=this.width,ut=this.height;Bt===tt&&St===Q&&Dt===st&&ut===ct||this.addCommands({cmd:()=>{this.width=Dt,this.height=ut,this.x=Bt,this.y=St;const[K,J]=this.parentDimensions;this.setDims(K*Dt,J*ut),this.fixAndSetPosition()},undo:()=>{this.width=st,this.height=ct,this.x=tt,this.y=Q;const[K,J]=this.parentDimensions;this.setDims(K*st,J*ct),this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",Ft),window.addEventListener("blur",Ft)},H=new WeakSet,Si=function(s,o){const[c,b]=this.parentDimensions,F=this.x,N=this.y,tt=this.width,Q=this.height,st=i.MIN_SIZE/c,ct=i.MIN_SIZE/b,yt=mt=>Math.round(mt*1e4)/1e4,dt=W(this,B,We).call(this,this.rotation),Ft=(mt,_t)=>[dt[0]*mt+dt[2]*_t,dt[1]*mt+dt[3]*_t],Bt=W(this,B,We).call(this,360-this.rotation),St=(mt,_t)=>[Bt[0]*mt+Bt[2]*_t,Bt[1]*mt+Bt[3]*_t];let Dt,ut,K=!1,J=!1;switch(s){case"topLeft":K=!0,Dt=(mt,_t)=>[0,0],ut=(mt,_t)=>[mt,_t];break;case"topMiddle":Dt=(mt,_t)=>[mt/2,0],ut=(mt,_t)=>[mt/2,_t];break;case"topRight":K=!0,Dt=(mt,_t)=>[mt,0],ut=(mt,_t)=>[0,_t];break;case"middleRight":J=!0,Dt=(mt,_t)=>[mt,_t/2],ut=(mt,_t)=>[0,_t/2];break;case"bottomRight":K=!0,Dt=(mt,_t)=>[mt,_t],ut=(mt,_t)=>[0,0];break;case"bottomMiddle":Dt=(mt,_t)=>[mt/2,_t],ut=(mt,_t)=>[mt/2,0];break;case"bottomLeft":K=!0,Dt=(mt,_t)=>[0,_t],ut=(mt,_t)=>[mt,0];break;case"middleLeft":J=!0,Dt=(mt,_t)=>[0,_t/2],ut=(mt,_t)=>[mt,_t/2];break}const ht=Dt(tt,Q),Et=ut(tt,Q);let Ct=Ft(...Et);const jt=yt(F+Ct[0]),Gt=yt(N+Ct[1]);let Ht=1,Xt=1,[Vt,Wt]=this.screenToPageTranslation(o.movementX,o.movementY);if([Vt,Wt]=St(Vt/c,Wt/b),K){const mt=Math.hypot(tt,Q);Ht=Xt=Math.max(Math.min(Math.hypot(Et[0]-ht[0]-Vt,Et[1]-ht[1]-Wt)/mt,1/tt,1/Q),st/tt,ct/Q)}else J?Ht=Math.max(st,Math.min(1,Math.abs(Et[0]-ht[0]-Vt)))/tt:Xt=Math.max(ct,Math.min(1,Math.abs(Et[1]-ht[1]-Wt)))/Q;const qt=yt(tt*Ht),ot=yt(Q*Xt);Ct=Ft(...ut(qt,ot));const Y=jt-Ct[0],G=Gt-Ct[1];this.width=qt,this.height=ot,this.x=Y,this.y=G,this.setDims(c*qt,b*ot),this.fixAndSetPosition()},pt=new WeakSet,Ge=async function(){var c;const s=t(this,g);if(!s)return;if(!t(this,U)&&!t(this,k)){s.classList.remove("done"),(c=t(this,L))==null||c.remove();return}i._l10nPromise.get("editor_alt_text_edit_button_label").then(b=>{s.setAttribute("aria-label",b)});let o=t(this,L);if(!o){Z(this,L,o=document.createElement("span")),o.className="tooltip",o.setAttribute("role","tooltip");const b=o.id=`alt-text-tooltip-${this.id}`;s.setAttribute("aria-describedby",b);const F=100;s.addEventListener("mouseenter",()=>{Z(this,O,setTimeout(()=>{Z(this,O,null),t(this,L).classList.add("show"),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})},F))}),s.addEventListener("mouseleave",()=>{var N;clearTimeout(t(this,O)),Z(this,O,null),(N=t(this,L))==null||N.classList.remove("show")})}s.classList.add("done"),o.innerText=t(this,k)?await i._l10nPromise.get("editor_alt_text_decorative_tooltip"):t(this,U),o.parentNode||s.append(o)},Pt=new WeakSet,Ei=function(s){if(!this._isDraggable)return;const o=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let c,b;o&&(c={passive:!0,capture:!0},b=N=>{const[tt,Q]=this.screenToPageTranslation(N.movementX,N.movementY);this._uiManager.dragSelectedEditors(tt,Q)},window.addEventListener("pointermove",b,c));const F=()=>{if(window.removeEventListener("pointerup",F),window.removeEventListener("blur",F),o&&window.removeEventListener("pointermove",b,c),Z(this,_,!1),!this._uiManager.endDragSession()){const{isMac:N}=M.FeatureTest.platform;s.ctrlKey&&!N||s.shiftKey||s.metaKey&&N?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",F),window.addEventListener("blur",F)},I(i,T),ee(i,"_borderLineWidth",-1),ee(i,"_colorManager",new h.ColorManager),ee(i,"_zIndex",1),ee(i,"SMALL_EDITOR_SIZE",0);let X=i;d.AnnotationEditor=X;class ft extends X{constructor(s){super(s),this.annotationElementId=s.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(At,d,rt)=>{var x,v,y,u,_,ze,A,a,l,P,p,wi,m,B,z,E,V,it,nt,H,lt,pt,wt,Pt,S,i,n,s,o,c,b,F,N,tt,Q,st,ct,yt,dt,Ft,Bt,St,Dt,ut,K,J,ht,Ci,Ct,Xe,Gt,Ve,Xt,Ce,Wt,$e,ot,qe,G,re,_t,me,Zt,Ti,vt,xi,Tt,Ye,Nt,be,bt,Ke;Object.defineProperty(d,"__esModule",{value:!0}),d.KeyboardManager=d.CommandManager=d.ColorManager=d.AnnotationEditorUIManager=void 0,d.bindEvents=at,d.opacityToHex=X;var h=rt(1),M=rt(6);function at(R,e,f){for(const D of f)e.addEventListener(D,R[D].bind(R))}function X(R){return Math.round(Math.min(255,Math.max(1,255*R))).toString(16).padStart(2,"0")}class ft{constructor(){I(this,x,0)}getId(){return`${h.AnnotationEditorPrefix}${ge(this,x)._++}`}}x=new WeakMap;const C=class C{constructor(){I(this,_);I(this,v,(0,h.getUuid)());I(this,y,0);I(this,u,null)}static get _isSVGFittingCanvas(){const e='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',D=new OffscreenCanvas(1,3).getContext("2d"),j=new Image;j.src=e;const $=j.decode().then(()=>(D.drawImage(j,0,0,1,1,0,0,1,3),new Uint32Array(D.getImageData(0,0,1,1).data.buffer)[0]===0));return(0,h.shadow)(this,"_isSVGFittingCanvas",$)}async getFromFile(e){const{lastModified:f,name:D,size:j,type:$}=e;return W(this,_,ze).call(this,`${f}_${D}_${j}_${$}`,e)}async getFromUrl(e){return W(this,_,ze).call(this,e,e)}async getFromId(e){t(this,u)||Z(this,u,new Map);const f=t(this,u).get(e);return f?f.bitmap?(f.refCounter+=1,f):f.file?this.getFromFile(f.file):this.getFromUrl(f.url):null}getSvgUrl(e){const f=t(this,u).get(e);return f!=null&&f.isSvg?f.svgUrl:null}deleteId(e){t(this,u)||Z(this,u,new Map);const f=t(this,u).get(e);f&&(f.refCounter-=1,f.refCounter===0&&(f.bitmap=null))}isValidId(e){return e.startsWith(`image_${t(this,v)}_`)}};v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakSet,ze=async function(e,f){t(this,u)||Z(this,u,new Map);let D=t(this,u).get(e);if(D===null)return null;if(D!=null&&D.bitmap)return D.refCounter+=1,D;try{D||(D={bitmap:null,id:`image_${t(this,v)}_${ge(this,y)._++}`,refCounter:0,isSvg:!1});let j;if(typeof f=="string"){D.url=f;const $=await fetch(f);if(!$.ok)throw new Error($.statusText);j=await $.blob()}else j=D.file=f;if(j.type==="image/svg+xml"){const $=C._isSVGFittingCanvas,et=new FileReader,gt=new Image,kt=new Promise((xt,zt)=>{gt.onload=()=>{D.bitmap=gt,D.isSvg=!0,xt()},et.onload=async()=>{const Mt=D.svgUrl=et.result;gt.src=await $?`${Mt}#svgView(preserveAspectRatio(none))`:Mt},gt.onerror=et.onerror=zt});et.readAsDataURL(j),await kt}else D.bitmap=await createImageBitmap(j);D.refCounter=1}catch(j){console.error(j),D=null}return t(this,u).set(e,D),D&&t(this,u).set(D.id,D),D};let U=C;class k{constructor(e=128){I(this,A,[]);I(this,a,!1);I(this,l,void 0);I(this,P,-1);Z(this,l,e)}add({cmd:e,undo:f,mustExec:D,type:j=NaN,overwriteIfSameType:$=!1,keepUndo:et=!1}){if(D&&e(),t(this,a))return;const gt={cmd:e,undo:f,type:j};if(t(this,P)===-1){t(this,A).length>0&&(t(this,A).length=0),Z(this,P,0),t(this,A).push(gt);return}if($&&t(this,A)[t(this,P)].type===j){et&&(gt.undo=t(this,A)[t(this,P)].undo),t(this,A)[t(this,P)]=gt;return}const kt=t(this,P)+1;kt===t(this,l)?t(this,A).splice(0,1):(Z(this,P,kt),kt<t(this,A).length&&t(this,A).splice(kt)),t(this,A).push(gt)}undo(){t(this,P)!==-1&&(Z(this,a,!0),t(this,A)[t(this,P)].undo(),Z(this,a,!1),Z(this,P,t(this,P)-1))}redo(){t(this,P)<t(this,A).length-1&&(Z(this,P,t(this,P)+1),Z(this,a,!0),t(this,A)[t(this,P)].cmd(),Z(this,a,!1))}hasSomethingToUndo(){return t(this,P)!==-1}hasSomethingToRedo(){return t(this,P)<t(this,A).length-1}destroy(){Z(this,A,null)}}A=new WeakMap,a=new WeakMap,l=new WeakMap,P=new WeakMap,d.CommandManager=k;class g{constructor(e){I(this,p);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:f}=h.FeatureTest.platform;for(const[D,j,$={}]of e)for(const et of D){const gt=et.startsWith("mac+");f&&gt?(this.callbacks.set(et.slice(4),{callback:j,options:$}),this.allKeys.add(et.split("+").at(-1))):!f&&!gt&&(this.callbacks.set(et,{callback:j,options:$}),this.allKeys.add(et.split("+").at(-1)))}}exec(e,f){if(!this.allKeys.has(f.key))return;const D=this.callbacks.get(W(this,p,wi).call(this,f));if(!D)return;const{callback:j,options:{bubbles:$=!1,args:et=[],checker:gt=null}}=D;gt&&!gt(e,f)||(j.bind(e,...et)(),$||(f.stopPropagation(),f.preventDefault()))}}p=new WeakSet,wi=function(e){e.altKey&&this.buffer.push("alt"),e.ctrlKey&&this.buffer.push("ctrl"),e.metaKey&&this.buffer.push("meta"),e.shiftKey&&this.buffer.push("shift"),this.buffer.push(e.key);const f=this.buffer.join("+");return this.buffer.length=0,f},d.KeyboardManager=g;const T=class T{get _colors(){const e=new Map([["CanvasText",null],["Canvas",null]]);return(0,M.getColorValues)(e),(0,h.shadow)(this,"_colors",e)}convert(e){const f=(0,M.getRGB)(e);if(!window.matchMedia("(forced-colors: active)").matches)return f;for(const[D,j]of this._colors)if(j.every(($,et)=>$===f[et]))return T._colorsMapping.get(D);return f}getHexCode(e){const f=this._colors.get(e);return f?h.Util.makeHexColor(...f):e}};ee(T,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let L=T;d.ColorManager=L;const It=class It{constructor(e,f,D,j,$,et){I(this,ht);I(this,Ct);I(this,Gt);I(this,Xt);I(this,Wt);I(this,ot);I(this,G);I(this,_t);I(this,Zt);I(this,vt);I(this,Tt);I(this,Nt);I(this,bt);I(this,m,null);I(this,B,new Map);I(this,z,new Map);I(this,E,null);I(this,V,null);I(this,it,new k);I(this,nt,0);I(this,H,new Set);I(this,lt,null);I(this,pt,null);I(this,wt,new Set);I(this,Pt,null);I(this,S,new ft);I(this,i,!1);I(this,n,!1);I(this,s,null);I(this,o,h.AnnotationEditorType.NONE);I(this,c,new Set);I(this,b,null);I(this,F,this.blur.bind(this));I(this,N,this.focus.bind(this));I(this,tt,this.copy.bind(this));I(this,Q,this.cut.bind(this));I(this,st,this.paste.bind(this));I(this,ct,this.keydown.bind(this));I(this,yt,this.onEditingAction.bind(this));I(this,dt,this.onPageChanging.bind(this));I(this,Ft,this.onScaleChanging.bind(this));I(this,Bt,this.onRotationChanging.bind(this));I(this,St,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1});I(this,Dt,[0,0]);I(this,ut,null);I(this,K,null);I(this,J,null);Z(this,K,e),Z(this,J,f),Z(this,E,D),this._eventBus=j,this._eventBus._on("editingaction",t(this,yt)),this._eventBus._on("pagechanging",t(this,dt)),this._eventBus._on("scalechanging",t(this,Ft)),this._eventBus._on("rotationchanging",t(this,Bt)),Z(this,V,$.annotationStorage),Z(this,Pt,$.filterFactory),Z(this,b,et),this.viewParameters={realScale:M.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}static get _keyboardManager(){const e=It.prototype,f=$=>{const{activeElement:et}=document;return et&&t($,K).contains(et)&&$.hasSomethingToControl()},D=this.TRANSLATE_SMALL,j=this.TRANSLATE_BIG;return(0,h.shadow)(this,"_keyboardManager",new g([[["ctrl+a","mac+meta+a"],e.selectAll],[["ctrl+z","mac+meta+z"],e.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],e.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],e.delete],[["Escape","mac+Escape"],e.unselectAll],[["ArrowLeft","mac+ArrowLeft"],e.translateSelectedEditors,{args:[-D,0],checker:f}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e.translateSelectedEditors,{args:[-j,0],checker:f}],[["ArrowRight","mac+ArrowRight"],e.translateSelectedEditors,{args:[D,0],checker:f}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e.translateSelectedEditors,{args:[j,0],checker:f}],[["ArrowUp","mac+ArrowUp"],e.translateSelectedEditors,{args:[0,-D],checker:f}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e.translateSelectedEditors,{args:[0,-j],checker:f}],[["ArrowDown","mac+ArrowDown"],e.translateSelectedEditors,{args:[0,D],checker:f}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e.translateSelectedEditors,{args:[0,j],checker:f}]]))}destroy(){W(this,Xt,Ce).call(this),W(this,Ct,Xe).call(this),this._eventBus._off("editingaction",t(this,yt)),this._eventBus._off("pagechanging",t(this,dt)),this._eventBus._off("scalechanging",t(this,Ft)),this._eventBus._off("rotationchanging",t(this,Bt));for(const e of t(this,z).values())e.destroy();t(this,z).clear(),t(this,B).clear(),t(this,wt).clear(),Z(this,m,null),t(this,c).clear(),t(this,it).destroy(),t(this,E).destroy()}get hcmFilter(){return(0,h.shadow)(this,"hcmFilter",t(this,b)?t(this,Pt).addHCMFilter(t(this,b).foreground,t(this,b).background):"none")}get direction(){return(0,h.shadow)(this,"direction",getComputedStyle(t(this,K)).direction)}editAltText(e){var f;(f=t(this,E))==null||f.editAltText(this,e)}onPageChanging({pageNumber:e}){Z(this,nt,e-1)}focusMainContainer(){t(this,K).focus()}findParent(e,f){for(const D of t(this,z).values()){const{x:j,y:$,width:et,height:gt}=D.div.getBoundingClientRect();if(e>=j&&e<=j+et&&f>=$&&f<=$+gt)return D}return null}disableUserSelect(e=!1){t(this,J).classList.toggle("noUserSelect",e)}addShouldRescale(e){t(this,wt).add(e)}removeShouldRescale(e){t(this,wt).delete(e)}onScaleChanging({scale:e}){this.commitOrRemove(),this.viewParameters.realScale=e*M.PixelsPerInch.PDF_TO_CSS_UNITS;for(const f of t(this,wt))f.onScaleChanging()}onRotationChanging({pagesRotation:e}){this.commitOrRemove(),this.viewParameters.rotation=e}addToAnnotationStorage(e){!e.isEmpty()&&t(this,V)&&!t(this,V).has(e.id)&&t(this,V).setValue(e.id,e)}blur(){if(!this.hasSelection)return;const{activeElement:e}=document;for(const f of t(this,c))if(f.div.contains(e)){Z(this,s,[f,e]),f._focusEventsAllowed=!1;break}}focus(){if(!t(this,s))return;const[e,f]=t(this,s);Z(this,s,null),f.addEventListener("focusin",()=>{e._focusEventsAllowed=!0},{once:!0}),f.focus()}addEditListeners(){W(this,Gt,Ve).call(this),W(this,Wt,$e).call(this)}removeEditListeners(){W(this,Xt,Ce).call(this),W(this,ot,qe).call(this)}copy(e){var D;if(e.preventDefault(),(D=t(this,m))==null||D.commitOrRemove(),!this.hasSelection)return;const f=[];for(const j of t(this,c)){const $=j.serialize(!0);$&&f.push($)}f.length!==0&&e.clipboardData.setData("application/pdfjs",JSON.stringify(f))}cut(e){this.copy(e),this.delete()}paste(e){e.preventDefault();const{clipboardData:f}=e;for(const $ of f.items)for(const et of t(this,pt))if(et.isHandlingMimeForPasting($.type)){et.paste($,this.currentLayer);return}let D=f.getData("application/pdfjs");if(!D)return;try{D=JSON.parse(D)}catch($){(0,h.warn)(`paste: "${$.message}".`);return}if(!Array.isArray(D))return;this.unselectAll();const j=this.currentLayer;try{const $=[];for(const kt of D){const xt=j.deserialize(kt);if(!xt)return;$.push(xt)}const et=()=>{for(const kt of $)W(this,Tt,Ye).call(this,kt);W(this,bt,Ke).call(this,$)},gt=()=>{for(const kt of $)kt.remove()};this.addCommands({cmd:et,undo:gt,mustExec:!0})}catch($){(0,h.warn)(`paste: "${$.message}".`)}}keydown(e){var f;(f=this.getActive())!=null&&f.shouldGetKeyboardEvents()||It._keyboardManager.exec(this,e)}onEditingAction(e){["undo","redo","delete","selectAll"].includes(e.name)&&this[e.name]()}setEditingState(e){e?(W(this,ht,Ci).call(this),W(this,Gt,Ve).call(this),W(this,Wt,$e).call(this),W(this,G,re).call(this,{isEditing:t(this,o)!==h.AnnotationEditorType.NONE,isEmpty:W(this,Nt,be).call(this),hasSomethingToUndo:t(this,it).hasSomethingToUndo(),hasSomethingToRedo:t(this,it).hasSomethingToRedo(),hasSelectedEditor:!1})):(W(this,Ct,Xe).call(this),W(this,Xt,Ce).call(this),W(this,ot,qe).call(this),W(this,G,re).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(e){if(!t(this,pt)){Z(this,pt,e);for(const f of t(this,pt))W(this,_t,me).call(this,f.defaultPropertiesToUpdate)}}getId(){return t(this,S).getId()}get currentLayer(){return t(this,z).get(t(this,nt))}getLayer(e){return t(this,z).get(e)}get currentPageIndex(){return t(this,nt)}addLayer(e){t(this,z).set(e.pageIndex,e),t(this,i)?e.enable():e.disable()}removeLayer(e){t(this,z).delete(e.pageIndex)}updateMode(e,f=null){if(t(this,o)!==e){if(Z(this,o,e),e===h.AnnotationEditorType.NONE){this.setEditingState(!1),W(this,vt,xi).call(this);return}this.setEditingState(!0),W(this,Zt,Ti).call(this),this.unselectAll();for(const D of t(this,z).values())D.updateMode(e);if(f){for(const D of t(this,B).values())if(D.annotationElementId===f){this.setSelected(D),D.enterInEditMode();break}}}}updateToolbar(e){e!==t(this,o)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:e})}updateParams(e,f){if(t(this,pt)){if(e===h.AnnotationEditorParamsType.CREATE){this.currentLayer.addNewEditor(e);return}for(const D of t(this,c))D.updateParams(e,f);for(const D of t(this,pt))D.updateDefaultParams(e,f)}}enableWaiting(e=!1){if(t(this,n)!==e){Z(this,n,e);for(const f of t(this,z).values())e?f.disableClick():f.enableClick(),f.div.classList.toggle("waiting",e)}}getEditors(e){const f=[];for(const D of t(this,B).values())D.pageIndex===e&&f.push(D);return f}getEditor(e){return t(this,B).get(e)}addEditor(e){t(this,B).set(e.id,e)}removeEditor(e){var f;t(this,B).delete(e.id),this.unselect(e),(!e.annotationElementId||!t(this,H).has(e.annotationElementId))&&((f=t(this,V))==null||f.remove(e.id))}addDeletedAnnotationElement(e){t(this,H).add(e.annotationElementId),e.deleted=!0}isDeletedAnnotationElement(e){return t(this,H).has(e)}removeDeletedAnnotationElement(e){t(this,H).delete(e.annotationElementId),e.deleted=!1}setActiveEditor(e){t(this,m)!==e&&(Z(this,m,e),e&&W(this,_t,me).call(this,e.propertiesToUpdate))}toggleSelected(e){if(t(this,c).has(e)){t(this,c).delete(e),e.unselect(),W(this,G,re).call(this,{hasSelectedEditor:this.hasSelection});return}t(this,c).add(e),e.select(),W(this,_t,me).call(this,e.propertiesToUpdate),W(this,G,re).call(this,{hasSelectedEditor:!0})}setSelected(e){for(const f of t(this,c))f!==e&&f.unselect();t(this,c).clear(),t(this,c).add(e),e.select(),W(this,_t,me).call(this,e.propertiesToUpdate),W(this,G,re).call(this,{hasSelectedEditor:!0})}isSelected(e){return t(this,c).has(e)}unselect(e){e.unselect(),t(this,c).delete(e),W(this,G,re).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return t(this,c).size!==0}undo(){t(this,it).undo(),W(this,G,re).call(this,{hasSomethingToUndo:t(this,it).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:W(this,Nt,be).call(this)})}redo(){t(this,it).redo(),W(this,G,re).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:t(this,it).hasSomethingToRedo(),isEmpty:W(this,Nt,be).call(this)})}addCommands(e){t(this,it).add(e),W(this,G,re).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:W(this,Nt,be).call(this)})}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const e=[...t(this,c)],f=()=>{for(const j of e)j.remove()},D=()=>{for(const j of e)W(this,Tt,Ye).call(this,j)};this.addCommands({cmd:f,undo:D,mustExec:!0})}commitOrRemove(){var e;(e=t(this,m))==null||e.commitOrRemove()}hasSomethingToControl(){return t(this,m)||this.hasSelection}selectAll(){for(const e of t(this,c))e.commit();W(this,bt,Ke).call(this,t(this,B).values())}unselectAll(){if(t(this,m)){t(this,m).commitOrRemove();return}if(this.hasSelection){for(const e of t(this,c))e.unselect();t(this,c).clear(),W(this,G,re).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(e,f,D=!1){if(D||this.commitOrRemove(),!this.hasSelection)return;t(this,Dt)[0]+=e,t(this,Dt)[1]+=f;const[j,$]=t(this,Dt),et=[...t(this,c)],gt=1e3;t(this,ut)&&clearTimeout(t(this,ut)),Z(this,ut,setTimeout(()=>{Z(this,ut,null),t(this,Dt)[0]=t(this,Dt)[1]=0,this.addCommands({cmd:()=>{for(const kt of et)t(this,B).has(kt.id)&&kt.translateInPage(j,$)},undo:()=>{for(const kt of et)t(this,B).has(kt.id)&&kt.translateInPage(-j,-$)},mustExec:!1})},gt));for(const kt of et)kt.translateInPage(e,f)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),Z(this,lt,new Map);for(const e of t(this,c))t(this,lt).set(e,{savedX:e.x,savedY:e.y,savedPageIndex:e.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!t(this,lt))return!1;this.disableUserSelect(!1);const e=t(this,lt);Z(this,lt,null);let f=!1;for(const[{x:j,y:$,pageIndex:et},gt]of e)gt.newX=j,gt.newY=$,gt.newPageIndex=et,f||(f=j!==gt.savedX||$!==gt.savedY||et!==gt.savedPageIndex);if(!f)return!1;const D=(j,$,et,gt)=>{if(t(this,B).has(j.id)){const kt=t(this,z).get(gt);kt?j._setParentAndPosition(kt,$,et):(j.pageIndex=gt,j.x=$,j.y=et)}};return this.addCommands({cmd:()=>{for(const[j,{newX:$,newY:et,newPageIndex:gt}]of e)D(j,$,et,gt)},undo:()=>{for(const[j,{savedX:$,savedY:et,savedPageIndex:gt}]of e)D(j,$,et,gt)},mustExec:!0}),!0}dragSelectedEditors(e,f){if(t(this,lt))for(const D of t(this,lt).keys())D.drag(e,f)}rebuild(e){if(e.parent===null){const f=this.getLayer(e.pageIndex);f?(f.changeParent(e),f.addOrRebuild(e)):(this.addEditor(e),this.addToAnnotationStorage(e),e.rebuild())}else e.parent.addOrRebuild(e)}isActive(e){return t(this,m)===e}getActive(){return t(this,m)}getMode(){return t(this,o)}get imageManager(){return(0,h.shadow)(this,"imageManager",new U)}};m=new WeakMap,B=new WeakMap,z=new WeakMap,E=new WeakMap,V=new WeakMap,it=new WeakMap,nt=new WeakMap,H=new WeakMap,lt=new WeakMap,pt=new WeakMap,wt=new WeakMap,Pt=new WeakMap,S=new WeakMap,i=new WeakMap,n=new WeakMap,s=new WeakMap,o=new WeakMap,c=new WeakMap,b=new WeakMap,F=new WeakMap,N=new WeakMap,tt=new WeakMap,Q=new WeakMap,st=new WeakMap,ct=new WeakMap,yt=new WeakMap,dt=new WeakMap,Ft=new WeakMap,Bt=new WeakMap,St=new WeakMap,Dt=new WeakMap,ut=new WeakMap,K=new WeakMap,J=new WeakMap,ht=new WeakSet,Ci=function(){window.addEventListener("focus",t(this,N)),window.addEventListener("blur",t(this,F))},Ct=new WeakSet,Xe=function(){window.removeEventListener("focus",t(this,N)),window.removeEventListener("blur",t(this,F))},Gt=new WeakSet,Ve=function(){window.addEventListener("keydown",t(this,ct),{capture:!0})},Xt=new WeakSet,Ce=function(){window.removeEventListener("keydown",t(this,ct),{capture:!0})},Wt=new WeakSet,$e=function(){document.addEventListener("copy",t(this,tt)),document.addEventListener("cut",t(this,Q)),document.addEventListener("paste",t(this,st))},ot=new WeakSet,qe=function(){document.removeEventListener("copy",t(this,tt)),document.removeEventListener("cut",t(this,Q)),document.removeEventListener("paste",t(this,st))},G=new WeakSet,re=function(e){Object.entries(e).some(([D,j])=>t(this,St)[D]!==j)&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(t(this,St),e)})},_t=new WeakSet,me=function(e){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:e})},Zt=new WeakSet,Ti=function(){if(!t(this,i)){Z(this,i,!0);for(const e of t(this,z).values())e.enable()}},vt=new WeakSet,xi=function(){if(this.unselectAll(),t(this,i)){Z(this,i,!1);for(const e of t(this,z).values())e.disable()}},Tt=new WeakSet,Ye=function(e){const f=t(this,z).get(e.pageIndex);f?f.addOrRebuild(e):this.addEditor(e)},Nt=new WeakSet,be=function(){if(t(this,B).size===0)return!0;if(t(this,B).size===1)for(const e of t(this,B).values())return e.isEmpty();return!1},bt=new WeakSet,Ke=function(e){t(this,c).clear();for(const f of e)f.isEmpty()||(t(this,c).add(f),f.select());W(this,G,re).call(this,{hasSelectedEditor:!0})},ee(It,"TRANSLATE_SMALL",1),ee(It,"TRANSLATE_BIG",10);let O=It;d.AnnotationEditorUIManager=O},(At,d,rt)=>{var nt,H,lt,pt,wt,Pt,S,i,n,s,o,c,de,F,ue,tt,Je,st,Te,yt,xe,Ft,_e,St,Ae;Object.defineProperty(d,"__esModule",{value:!0}),d.StatTimer=d.RenderingCancelledException=d.PixelsPerInch=d.PageViewport=d.PDFDateString=d.DOMStandardFontDataFactory=d.DOMSVGFactory=d.DOMFilterFactory=d.DOMCanvasFactory=d.DOMCMapReaderFactory=void 0,d.deprecated=P,d.getColorValues=B,d.getCurrentTransform=z,d.getCurrentTransformInverse=E,d.getFilenameFromUrl=_,d.getPdfFilenameFromUrl=w,d.getRGB=m,d.getXfaPageViewport=T,d.isDataScheme=y,d.isPdfFile=u,d.isValidFetchUrl=A,d.loadScript=l,d.noContextMenu=a,d.setLayerDimensions=V;var h=rt(7),M=rt(1);const at="http://www.w3.org/2000/svg",it=class it{};ee(it,"CSS",96),ee(it,"PDF",72),ee(it,"PDF_TO_CSS_UNITS",it.CSS/it.PDF);let X=it;d.PixelsPerInch=X;class ft extends h.BaseFilterFactory{constructor({docId:J,ownerDocument:ht=globalThis.document}={}){super();I(this,c);I(this,F);I(this,tt);I(this,st);I(this,yt);I(this,Ft);I(this,St);I(this,nt,void 0);I(this,H,void 0);I(this,lt,void 0);I(this,pt,void 0);I(this,wt,void 0);I(this,Pt,void 0);I(this,S,void 0);I(this,i,void 0);I(this,n,void 0);I(this,s,void 0);I(this,o,0);Z(this,lt,J),Z(this,pt,ht)}addFilter(J){if(!J)return"none";let ht=t(this,c,de).get(J);if(ht)return ht;let Et,Ct,jt,Gt;if(J.length===1){const Wt=J[0],qt=new Array(256);for(let ot=0;ot<256;ot++)qt[ot]=Wt[ot]/255;Gt=Et=Ct=jt=qt.join(",")}else{const[Wt,qt,ot]=J,Y=new Array(256),G=new Array(256),mt=new Array(256);for(let _t=0;_t<256;_t++)Y[_t]=Wt[_t]/255,G[_t]=qt[_t]/255,mt[_t]=ot[_t]/255;Et=Y.join(","),Ct=G.join(","),jt=mt.join(","),Gt=`${Et}${Ct}${jt}`}if(ht=t(this,c,de).get(Gt),ht)return t(this,c,de).set(J,ht),ht;const Ht=`g_${t(this,lt)}_transfer_map_${ge(this,o)._++}`,Xt=`url(#${Ht})`;t(this,c,de).set(J,Xt),t(this,c,de).set(Gt,Xt);const Vt=W(this,st,Te).call(this,Ht);return W(this,Ft,_e).call(this,Et,Ct,jt,Vt),Xt}addHCMFilter(J,ht){var qt;const Et=`${J}-${ht}`;if(t(this,Pt)===Et)return t(this,S);if(Z(this,Pt,Et),Z(this,S,"none"),(qt=t(this,wt))==null||qt.remove(),!J||!ht)return t(this,S);const Ct=W(this,St,Ae).call(this,J);J=M.Util.makeHexColor(...Ct);const jt=W(this,St,Ae).call(this,ht);if(ht=M.Util.makeHexColor(...jt),t(this,F,ue).style.color="",J==="#000000"&&ht==="#ffffff"||J===ht)return t(this,S);const Gt=new Array(256);for(let ot=0;ot<=255;ot++){const Y=ot/255;Gt[ot]=Y<=.03928?Y/12.92:((Y+.055)/1.055)**2.4}const Ht=Gt.join(","),Xt=`g_${t(this,lt)}_hcm_filter`,Vt=Z(this,i,W(this,st,Te).call(this,Xt));W(this,Ft,_e).call(this,Ht,Ht,Ht,Vt),W(this,tt,Je).call(this,Vt);const Wt=(ot,Y)=>{const G=Ct[ot]/255,mt=jt[ot]/255,_t=new Array(Y+1);for(let te=0;te<=Y;te++)_t[te]=G+te/Y*(mt-G);return _t.join(",")};return W(this,Ft,_e).call(this,Wt(0,5),Wt(1,5),Wt(2,5),Vt),Z(this,S,`url(#${Xt})`),t(this,S)}addHighlightHCMFilter(J,ht,Et,Ct){var mt;const jt=`${J}-${ht}-${Et}-${Ct}`;if(t(this,n)===jt)return t(this,s);if(Z(this,n,jt),Z(this,s,"none"),(mt=t(this,i))==null||mt.remove(),!J||!ht)return t(this,s);const[Gt,Ht]=[J,ht].map(W(this,St,Ae).bind(this));let Xt=Math.round(.2126*Gt[0]+.7152*Gt[1]+.0722*Gt[2]),Vt=Math.round(.2126*Ht[0]+.7152*Ht[1]+.0722*Ht[2]),[Wt,qt]=[Et,Ct].map(W(this,St,Ae).bind(this));Vt<Xt&&([Xt,Vt,Wt,qt]=[Vt,Xt,qt,Wt]),t(this,F,ue).style.color="";const ot=(_t,te,Zt)=>{const q=new Array(256),vt=(Vt-Xt)/Zt,Lt=_t/255,Tt=(te-_t)/(255*Zt);let Ot=0;for(let Nt=0;Nt<=Zt;Nt++){const Jt=Math.round(Xt+Nt*vt),bt=Lt+Nt*Tt;for(let Yt=Ot;Yt<=Jt;Yt++)q[Yt]=bt;Ot=Jt+1}for(let Nt=Ot;Nt<256;Nt++)q[Nt]=q[Ot-1];return q.join(",")},Y=`g_${t(this,lt)}_hcm_highlight_filter`,G=Z(this,i,W(this,st,Te).call(this,Y));return W(this,tt,Je).call(this,G),W(this,Ft,_e).call(this,ot(Wt[0],qt[0],5),ot(Wt[1],qt[1],5),ot(Wt[2],qt[2],5),G),Z(this,s,`url(#${Y})`),t(this,s)}destroy(J=!1){J&&(t(this,S)||t(this,s))||(t(this,H)&&(t(this,H).parentNode.parentNode.remove(),Z(this,H,null)),t(this,nt)&&(t(this,nt).clear(),Z(this,nt,null)),Z(this,o,0))}}nt=new WeakMap,H=new WeakMap,lt=new WeakMap,pt=new WeakMap,wt=new WeakMap,Pt=new WeakMap,S=new WeakMap,i=new WeakMap,n=new WeakMap,s=new WeakMap,o=new WeakMap,c=new WeakSet,de=function(){return t(this,nt)||Z(this,nt,new Map)},F=new WeakSet,ue=function(){if(!t(this,H)){const J=t(this,pt).createElement("div"),{style:ht}=J;ht.visibility="hidden",ht.contain="strict",ht.width=ht.height=0,ht.position="absolute",ht.top=ht.left=0,ht.zIndex=-1;const Et=t(this,pt).createElementNS(at,"svg");Et.setAttribute("width",0),Et.setAttribute("height",0),Z(this,H,t(this,pt).createElementNS(at,"defs")),J.append(Et),Et.append(t(this,H)),t(this,pt).body.append(J)}return t(this,H)},tt=new WeakSet,Je=function(J){const ht=t(this,pt).createElementNS(at,"feColorMatrix");ht.setAttribute("type","matrix"),ht.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),J.append(ht)},st=new WeakSet,Te=function(J){const ht=t(this,pt).createElementNS(at,"filter");return ht.setAttribute("color-interpolation-filters","sRGB"),ht.setAttribute("id",J),t(this,F,ue).append(ht),ht},yt=new WeakSet,xe=function(J,ht,Et){const Ct=t(this,pt).createElementNS(at,ht);Ct.setAttribute("type","discrete"),Ct.setAttribute("tableValues",Et),J.append(Ct)},Ft=new WeakSet,_e=function(J,ht,Et,Ct){const jt=t(this,pt).createElementNS(at,"feComponentTransfer");Ct.append(jt),W(this,yt,xe).call(this,jt,"feFuncR",J),W(this,yt,xe).call(this,jt,"feFuncG",ht),W(this,yt,xe).call(this,jt,"feFuncB",Et)},St=new WeakSet,Ae=function(J){return t(this,F,ue).style.color=J,m(getComputedStyle(t(this,F,ue)).getPropertyValue("color"))},d.DOMFilterFactory=ft;class U extends h.BaseCanvasFactory{constructor({ownerDocument:K=globalThis.document}={}){super(),this._document=K}_createCanvas(K,J){const ht=this._document.createElement("canvas");return ht.width=K,ht.height=J,ht}}d.DOMCanvasFactory=U;async function k(ut,K=!1){if(A(ut,document.baseURI)){const J=await fetch(ut);if(!J.ok)throw new Error(J.statusText);return K?new Uint8Array(await J.arrayBuffer()):(0,M.stringToBytes)(await J.text())}return new Promise((J,ht)=>{const Et=new XMLHttpRequest;Et.open("GET",ut,!0),K&&(Et.responseType="arraybuffer"),Et.onreadystatechange=()=>{if(Et.readyState===XMLHttpRequest.DONE){if(Et.status===200||Et.status===0){let Ct;if(K&&Et.response?Ct=new Uint8Array(Et.response):!K&&Et.responseText&&(Ct=(0,M.stringToBytes)(Et.responseText)),Ct){J(Ct);return}}ht(new Error(Et.statusText))}},Et.send(null)})}class g extends h.BaseCMapReaderFactory{_fetchData(K,J){return k(K,this.isCompressed).then(ht=>({cMapData:ht,compressionType:J}))}}d.DOMCMapReaderFactory=g;class L extends h.BaseStandardFontDataFactory{_fetchData(K){return k(K,!0)}}d.DOMStandardFontDataFactory=L;class O extends h.BaseSVGFactory{_createSVG(K){return document.createElementNS(at,K)}}d.DOMSVGFactory=O;class x{constructor({viewBox:K,scale:J,rotation:ht,offsetX:Et=0,offsetY:Ct=0,dontFlip:jt=!1}){this.viewBox=K,this.scale=J,this.rotation=ht,this.offsetX=Et,this.offsetY=Ct;const Gt=(K[2]+K[0])/2,Ht=(K[3]+K[1])/2;let Xt,Vt,Wt,qt;switch(ht%=360,ht<0&&(ht+=360),ht){case 180:Xt=-1,Vt=0,Wt=0,qt=1;break;case 90:Xt=0,Vt=1,Wt=1,qt=0;break;case 270:Xt=0,Vt=-1,Wt=-1,qt=0;break;case 0:Xt=1,Vt=0,Wt=0,qt=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}jt&&(Wt=-Wt,qt=-qt);let ot,Y,G,mt;Xt===0?(ot=Math.abs(Ht-K[1])*J+Et,Y=Math.abs(Gt-K[0])*J+Ct,G=(K[3]-K[1])*J,mt=(K[2]-K[0])*J):(ot=Math.abs(Gt-K[0])*J+Et,Y=Math.abs(Ht-K[1])*J+Ct,G=(K[2]-K[0])*J,mt=(K[3]-K[1])*J),this.transform=[Xt*J,Vt*J,Wt*J,qt*J,ot-Xt*J*Gt-Wt*J*Ht,Y-Vt*J*Gt-qt*J*Ht],this.width=G,this.height=mt}get rawDims(){const{viewBox:K}=this;return(0,M.shadow)(this,"rawDims",{pageWidth:K[2]-K[0],pageHeight:K[3]-K[1],pageX:K[0],pageY:K[1]})}clone({scale:K=this.scale,rotation:J=this.rotation,offsetX:ht=this.offsetX,offsetY:Et=this.offsetY,dontFlip:Ct=!1}={}){return new x({viewBox:this.viewBox.slice(),scale:K,rotation:J,offsetX:ht,offsetY:Et,dontFlip:Ct})}convertToViewportPoint(K,J){return M.Util.applyTransform([K,J],this.transform)}convertToViewportRectangle(K){const J=M.Util.applyTransform([K[0],K[1]],this.transform),ht=M.Util.applyTransform([K[2],K[3]],this.transform);return[J[0],J[1],ht[0],ht[1]]}convertToPdfPoint(K,J){return M.Util.applyInverseTransform([K,J],this.transform)}}d.PageViewport=x;class v extends M.BaseException{constructor(K,J=0){super(K,"RenderingCancelledException"),this.extraDelay=J}}d.RenderingCancelledException=v;function y(ut){const K=ut.length;let J=0;for(;J<K&&ut[J].trim()==="";)J++;return ut.substring(J,J+5).toLowerCase()==="data:"}function u(ut){return typeof ut=="string"&&/\.pdf$/i.test(ut)}function _(ut,K=!1){return K||([ut]=ut.split(/[#?]/,1)),ut.substring(ut.lastIndexOf("/")+1)}function w(ut,K="document.pdf"){if(typeof ut!="string")return K;if(y(ut))return(0,M.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),K;const J=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,ht=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,Et=J.exec(ut);let Ct=ht.exec(Et[1])||ht.exec(Et[2])||ht.exec(Et[3]);if(Ct&&(Ct=Ct[0],Ct.includes("%")))try{Ct=ht.exec(decodeURIComponent(Ct))[0]}catch{}return Ct||K}class C{constructor(){ee(this,"started",Object.create(null));ee(this,"times",[])}time(K){K in this.started&&(0,M.warn)(`Timer is already running for ${K}`),this.started[K]=Date.now()}timeEnd(K){K in this.started||(0,M.warn)(`Timer has not been started for ${K}`),this.times.push({name:K,start:this.started[K],end:Date.now()}),delete this.started[K]}toString(){const K=[];let J=0;for(const{name:ht}of this.times)J=Math.max(ht.length,J);for(const{name:ht,start:Et,end:Ct}of this.times)K.push(`${ht.padEnd(J)} ${Ct-Et}ms
`);return K.join("")}}d.StatTimer=C;function A(ut,K){try{const{protocol:J}=K?new URL(ut,K):new URL(ut);return J==="http:"||J==="https:"}catch{return!1}}function a(ut){ut.preventDefault()}function l(ut,K=!1){return new Promise((J,ht)=>{const Et=document.createElement("script");Et.src=ut,Et.onload=function(Ct){K&&Et.remove(),J(Ct)},Et.onerror=function(){ht(new Error(`Cannot load script at: ${Et.src}`))},(document.head||document.documentElement).append(Et)})}function P(ut){console.log("Deprecated API usage: "+ut)}let p;class r{static toDateObject(K){if(!K||typeof K!="string")return null;p||(p=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const J=p.exec(K);if(!J)return null;const ht=parseInt(J[1],10);let Et=parseInt(J[2],10);Et=Et>=1&&Et<=12?Et-1:0;let Ct=parseInt(J[3],10);Ct=Ct>=1&&Ct<=31?Ct:1;let jt=parseInt(J[4],10);jt=jt>=0&&jt<=23?jt:0;let Gt=parseInt(J[5],10);Gt=Gt>=0&&Gt<=59?Gt:0;let Ht=parseInt(J[6],10);Ht=Ht>=0&&Ht<=59?Ht:0;const Xt=J[7]||"Z";let Vt=parseInt(J[8],10);Vt=Vt>=0&&Vt<=23?Vt:0;let Wt=parseInt(J[9],10)||0;return Wt=Wt>=0&&Wt<=59?Wt:0,Xt==="-"?(jt+=Vt,Gt+=Wt):Xt==="+"&&(jt-=Vt,Gt-=Wt),new Date(Date.UTC(ht,Et,Ct,jt,Gt,Ht))}}d.PDFDateString=r;function T(ut,{scale:K=1,rotation:J=0}){const{width:ht,height:Et}=ut.attributes.style,Ct=[0,0,parseInt(ht),parseInt(Et)];return new x({viewBox:Ct,scale:K,rotation:J})}function m(ut){if(ut.startsWith("#")){const K=parseInt(ut.slice(1),16);return[(K&16711680)>>16,(K&65280)>>8,K&255]}return ut.startsWith("rgb(")?ut.slice(4,-1).split(",").map(K=>parseInt(K)):ut.startsWith("rgba(")?ut.slice(5,-1).split(",").map(K=>parseInt(K)).slice(0,3):((0,M.warn)(`Not a valid color format: "${ut}"`),[0,0,0])}function B(ut){const K=document.createElement("span");K.style.visibility="hidden",document.body.append(K);for(const J of ut.keys()){K.style.color=J;const ht=window.getComputedStyle(K).color;ut.set(J,m(ht))}K.remove()}function z(ut){const{a:K,b:J,c:ht,d:Et,e:Ct,f:jt}=ut.getTransform();return[K,J,ht,Et,Ct,jt]}function E(ut){const{a:K,b:J,c:ht,d:Et,e:Ct,f:jt}=ut.getTransform().invertSelf();return[K,J,ht,Et,Ct,jt]}function V(ut,K,J=!1,ht=!0){if(K instanceof x){const{pageWidth:Et,pageHeight:Ct}=K.rawDims,{style:jt}=ut,Gt=M.FeatureTest.isCSSRoundSupported,Ht=`var(--scale-factor) * ${Et}px`,Xt=`var(--scale-factor) * ${Ct}px`,Vt=Gt?`round(${Ht}, 1px)`:`calc(${Ht})`,Wt=Gt?`round(${Xt}, 1px)`:`calc(${Xt})`;!J||K.rotation%180===0?(jt.width=Vt,jt.height=Wt):(jt.width=Wt,jt.height=Vt)}ht&&ut.setAttribute("data-main-rotation",K.rotation)}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.BaseStandardFontDataFactory=d.BaseSVGFactory=d.BaseFilterFactory=d.BaseCanvasFactory=d.BaseCMapReaderFactory=void 0;var h=rt(1);class M{constructor(){this.constructor===M&&(0,h.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(g){return"none"}addHCMFilter(g,L){return"none"}addHighlightHCMFilter(g,L,O,x){return"none"}destroy(g=!1){}}d.BaseFilterFactory=M;class at{constructor(){this.constructor===at&&(0,h.unreachable)("Cannot initialize BaseCanvasFactory.")}create(g,L){if(g<=0||L<=0)throw new Error("Invalid canvas size");const O=this._createCanvas(g,L);return{canvas:O,context:O.getContext("2d")}}reset(g,L,O){if(!g.canvas)throw new Error("Canvas is not specified");if(L<=0||O<=0)throw new Error("Invalid canvas size");g.canvas.width=L,g.canvas.height=O}destroy(g){if(!g.canvas)throw new Error("Canvas is not specified");g.canvas.width=0,g.canvas.height=0,g.canvas=null,g.context=null}_createCanvas(g,L){(0,h.unreachable)("Abstract method `_createCanvas` called.")}}d.BaseCanvasFactory=at;class X{constructor({baseUrl:g=null,isCompressed:L=!0}){this.constructor===X&&(0,h.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=g,this.isCompressed=L}async fetch({name:g}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!g)throw new Error("CMap name must be specified.");const L=this.baseUrl+g+(this.isCompressed?".bcmap":""),O=this.isCompressed?h.CMapCompressionType.BINARY:h.CMapCompressionType.NONE;return this._fetchData(L,O).catch(x=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${L}`)})}_fetchData(g,L){(0,h.unreachable)("Abstract method `_fetchData` called.")}}d.BaseCMapReaderFactory=X;class ft{constructor({baseUrl:g=null}){this.constructor===ft&&(0,h.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=g}async fetch({filename:g}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!g)throw new Error("Font filename must be specified.");const L=`${this.baseUrl}${g}`;return this._fetchData(L).catch(O=>{throw new Error(`Unable to load font data at: ${L}`)})}_fetchData(g){(0,h.unreachable)("Abstract method `_fetchData` called.")}}d.BaseStandardFontDataFactory=ft;class U{constructor(){this.constructor===U&&(0,h.unreachable)("Cannot initialize BaseSVGFactory.")}create(g,L,O=!1){if(g<=0||L<=0)throw new Error("Invalid SVG dimensions");const x=this._createSVG("svg:svg");return x.setAttribute("version","1.1"),O||(x.setAttribute("width",`${g}px`),x.setAttribute("height",`${L}px`)),x.setAttribute("preserveAspectRatio","none"),x.setAttribute("viewBox",`0 0 ${g} ${L}`),x}createElement(g){if(typeof g!="string")throw new Error("Invalid SVG element type");return this._createSVG(g)}_createSVG(g){(0,h.unreachable)("Abstract method `_createSVG` called.")}}d.BaseSVGFactory=U},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.MurmurHash3_64=void 0;var h=rt(1);const M=3285377520,at=4294901760,X=65535;class ft{constructor(k){this.h1=k?k&4294967295:M,this.h2=k?k&4294967295:M}update(k){let g,L;if(typeof k=="string"){g=new Uint8Array(k.length*2),L=0;for(let P=0,p=k.length;P<p;P++){const r=k.charCodeAt(P);r<=255?g[L++]=r:(g[L++]=r>>>8,g[L++]=r&255)}}else if((0,h.isArrayBuffer)(k))g=k.slice(),L=g.byteLength;else throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");const O=L>>2,x=L-O*4,v=new Uint32Array(g.buffer,0,O);let y=0,u=0,_=this.h1,w=this.h2;const C=3432918353,A=461845907,a=C&X,l=A&X;for(let P=0;P<O;P++)P&1?(y=v[P],y=y*C&at|y*a&X,y=y<<15|y>>>17,y=y*A&at|y*l&X,_^=y,_=_<<13|_>>>19,_=_*5+3864292196):(u=v[P],u=u*C&at|u*a&X,u=u<<15|u>>>17,u=u*A&at|u*l&X,w^=u,w=w<<13|w>>>19,w=w*5+3864292196);switch(y=0,x){case 3:y^=g[O*4+2]<<16;case 2:y^=g[O*4+1]<<8;case 1:y^=g[O*4],y=y*C&at|y*a&X,y=y<<15|y>>>17,y=y*A&at|y*l&X,O&1?_^=y:w^=y}this.h1=_,this.h2=w}hexdigest(){let k=this.h1,g=this.h2;return k^=g>>>1,k=k*3981806797&at|k*36045&X,g=g*4283543511&at|((g<<16|k>>>16)*2950163797&at)>>>16,k^=g>>>1,k=k*444984403&at|k*60499&X,g=g*3301882366&at|((g<<16|k>>>16)*3120437893&at)>>>16,k^=g>>>1,(k>>>0).toString(16).padStart(8,"0")+(g>>>0).toString(16).padStart(8,"0")}}d.MurmurHash3_64=ft},(At,d,rt)=>{var X;Object.defineProperty(d,"__esModule",{value:!0}),d.FontLoader=d.FontFaceObject=void 0;var h=rt(1);class M{constructor({ownerDocument:U=globalThis.document,styleElement:k=null}){I(this,X,new Set);this._document=U,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(U){this.nativeFontFaces.add(U),this._document.fonts.add(U)}removeNativeFontFace(U){this.nativeFontFaces.delete(U),this._document.fonts.delete(U)}insertRule(U){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const k=this.styleElement.sheet;k.insertRule(U,k.cssRules.length)}clear(){for(const U of this.nativeFontFaces)this._document.fonts.delete(U);this.nativeFontFaces.clear(),t(this,X).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont(U){if(!(!U||t(this,X).has(U.loadedName))){if((0,h.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:k,src:g,style:L}=U,O=new FontFace(k,g,L);this.addNativeFontFace(O);try{await O.load(),t(this,X).add(k)}catch{(0,h.warn)(`Cannot load system font: ${U.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(O)}return}(0,h.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(U){if(U.attached||U.missingFile&&!U.systemFontInfo)return;if(U.attached=!0,U.systemFontInfo){await this.loadSystemFont(U.systemFontInfo);return}if(this.isFontLoadingAPISupported){const g=U.createNativeFontFace();if(g){this.addNativeFontFace(g);try{await g.loaded}catch(L){throw(0,h.warn)(`Failed to load font '${g.family}': '${L}'.`),U.disableFontFace=!0,L}}return}const k=U.createFontFaceRule();if(k){if(this.insertRule(k),this.isSyncFontLoadingSupported)return;await new Promise(g=>{const L=this._queueLoadingCallback(g);this._prepareFontLoadEvent(U,L)})}}get isFontLoadingAPISupported(){var k;const U=!!((k=this._document)!=null&&k.fonts);return(0,h.shadow)(this,"isFontLoadingAPISupported",U)}get isSyncFontLoadingSupported(){let U=!1;return(h.isNodeJS||typeof navigator<"u"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(U=!0),(0,h.shadow)(this,"isSyncFontLoadingSupported",U)}_queueLoadingCallback(U){function k(){for((0,h.assert)(!L.done,"completeRequest() cannot be called twice."),L.done=!0;g.length>0&&g[0].done;){const O=g.shift();setTimeout(O.callback,0)}}const{loadingRequests:g}=this,L={done:!1,complete:k,callback:U};return g.push(L),L}get _loadTestFont(){const U=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,h.shadow)(this,"_loadTestFont",U)}_prepareFontLoadEvent(U,k){function g(m,B){return m.charCodeAt(B)<<24|m.charCodeAt(B+1)<<16|m.charCodeAt(B+2)<<8|m.charCodeAt(B+3)&255}function L(m,B,z,E){const V=m.substring(0,B),it=m.substring(B+z);return V+E+it}let O,x;const v=this._document.createElement("canvas");v.width=1,v.height=1;const y=v.getContext("2d");let u=0;function _(m,B){if(++u>30){(0,h.warn)("Load test font never loaded."),B();return}if(y.font="30px "+m,y.fillText(".",0,20),y.getImageData(0,0,1,1).data[3]>0){B();return}setTimeout(_.bind(null,m,B))}const w=`lt${Date.now()}${this.loadTestFontId++}`;let C=this._loadTestFont;C=L(C,976,w.length,w);const a=16,l=1482184792;let P=g(C,a);for(O=0,x=w.length-3;O<x;O+=4)P=P-l+g(w,O)|0;O<w.length&&(P=P-l+g(w+"XXX",O)|0),C=L(C,a,4,(0,h.string32)(P));const p=`url(data:font/opentype;base64,${btoa(C)});`,r=`@font-face {font-family:"${w}";src:${p}}`;this.insertRule(r);const T=this._document.createElement("div");T.style.visibility="hidden",T.style.width=T.style.height="10px",T.style.position="absolute",T.style.top=T.style.left="0px";for(const m of[U.loadedName,w]){const B=this._document.createElement("span");B.textContent="Hi",B.style.fontFamily=m,T.append(B)}this._document.body.append(T),_(w,()=>{T.remove(),k.complete()})}}X=new WeakMap,d.FontLoader=M;class at{constructor(U,{isEvalSupported:k=!0,disableFontFace:g=!1,ignoreErrors:L=!1,inspectFont:O=null}){this.compiledGlyphs=Object.create(null);for(const x in U)this[x]=U[x];this.isEvalSupported=k!==!1,this.disableFontFace=g===!0,this.ignoreErrors=L===!0,this._inspectFont=O}createNativeFontFace(){var k;if(!this.data||this.disableFontFace)return null;let U;if(!this.cssFontInfo)U=new FontFace(this.loadedName,this.data,{});else{const g={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(g.style=`oblique ${this.cssFontInfo.italicAngle}deg`),U=new FontFace(this.cssFontInfo.fontFamily,this.data,g)}return(k=this._inspectFont)==null||k.call(this,this),U}createFontFaceRule(){var L;if(!this.data||this.disableFontFace)return null;const U=(0,h.bytesToString)(this.data),k=`url(data:${this.mimetype};base64,${btoa(U)});`;let g;if(!this.cssFontInfo)g=`@font-face {font-family:"${this.loadedName}";src:${k}}`;else{let O=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(O+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),g=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${O}src:${k}}`}return(L=this._inspectFont)==null||L.call(this,this,k),g}getPathGenerator(U,k){if(this.compiledGlyphs[k]!==void 0)return this.compiledGlyphs[k];let g;try{g=U.get(this.loadedName+"_path_"+k)}catch(L){if(!this.ignoreErrors)throw L;return(0,h.warn)(`getPathGenerator - ignoring character: "${L}".`),this.compiledGlyphs[k]=function(O,x){}}if(this.isEvalSupported&&h.FeatureTest.isEvalSupported){const L=[];for(const O of g){const x=O.args!==void 0?O.args.join(","):"";L.push("c.",O.cmd,"(",x,`);
`)}return this.compiledGlyphs[k]=new Function("c","size",L.join(""))}return this.compiledGlyphs[k]=function(L,O){for(const x of g)x.cmd==="scale"&&(x.args=[O,-O]),L[x.cmd].apply(L,x.args)}}}d.FontFaceObject=at},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.NodeStandardFontDataFactory=d.NodeFilterFactory=d.NodeCanvasFactory=d.NodeCMapReaderFactory=void 0;var h=rt(7);rt(1);const M=function(k){return new Promise((g,L)=>{require$$5.readFile(k,(x,v)=>{if(x||!v){L(new Error(x));return}g(new Uint8Array(v))})})};class at extends h.BaseFilterFactory{}d.NodeFilterFactory=at;class X extends h.BaseCanvasFactory{_createCanvas(g,L){return require$$5.createCanvas(g,L)}}d.NodeCanvasFactory=X;class ft extends h.BaseCMapReaderFactory{_fetchData(g,L){return M(g).then(O=>({cMapData:O,compressionType:L}))}}d.NodeCMapReaderFactory=ft;class U extends h.BaseStandardFontDataFactory{_fetchData(g){return M(g)}}d.NodeStandardFontDataFactory=U},(At,d,rt)=>{var H,Qe,pt,Ze;Object.defineProperty(d,"__esModule",{value:!0}),d.CanvasGraphics=void 0;var h=rt(1),M=rt(6),at=rt(12),X=rt(13);const ft=16,U=100,k=4096,g=15,L=10,O=1e3,x=16;function v(S,i){if(S._removeMirroring)throw new Error("Context is already forwarding operations.");S.__originalSave=S.save,S.__originalRestore=S.restore,S.__originalRotate=S.rotate,S.__originalScale=S.scale,S.__originalTranslate=S.translate,S.__originalTransform=S.transform,S.__originalSetTransform=S.setTransform,S.__originalResetTransform=S.resetTransform,S.__originalClip=S.clip,S.__originalMoveTo=S.moveTo,S.__originalLineTo=S.lineTo,S.__originalBezierCurveTo=S.bezierCurveTo,S.__originalRect=S.rect,S.__originalClosePath=S.closePath,S.__originalBeginPath=S.beginPath,S._removeMirroring=()=>{S.save=S.__originalSave,S.restore=S.__originalRestore,S.rotate=S.__originalRotate,S.scale=S.__originalScale,S.translate=S.__originalTranslate,S.transform=S.__originalTransform,S.setTransform=S.__originalSetTransform,S.resetTransform=S.__originalResetTransform,S.clip=S.__originalClip,S.moveTo=S.__originalMoveTo,S.lineTo=S.__originalLineTo,S.bezierCurveTo=S.__originalBezierCurveTo,S.rect=S.__originalRect,S.closePath=S.__originalClosePath,S.beginPath=S.__originalBeginPath,delete S._removeMirroring},S.save=function(){i.save(),this.__originalSave()},S.restore=function(){i.restore(),this.__originalRestore()},S.translate=function(s,o){i.translate(s,o),this.__originalTranslate(s,o)},S.scale=function(s,o){i.scale(s,o),this.__originalScale(s,o)},S.transform=function(s,o,c,b,F,N){i.transform(s,o,c,b,F,N),this.__originalTransform(s,o,c,b,F,N)},S.setTransform=function(s,o,c,b,F,N){i.setTransform(s,o,c,b,F,N),this.__originalSetTransform(s,o,c,b,F,N)},S.resetTransform=function(){i.resetTransform(),this.__originalResetTransform()},S.rotate=function(s){i.rotate(s),this.__originalRotate(s)},S.clip=function(s){i.clip(s),this.__originalClip(s)},S.moveTo=function(n,s){i.moveTo(n,s),this.__originalMoveTo(n,s)},S.lineTo=function(n,s){i.lineTo(n,s),this.__originalLineTo(n,s)},S.bezierCurveTo=function(n,s,o,c,b,F){i.bezierCurveTo(n,s,o,c,b,F),this.__originalBezierCurveTo(n,s,o,c,b,F)},S.rect=function(n,s,o,c){i.rect(n,s,o,c),this.__originalRect(n,s,o,c)},S.closePath=function(){i.closePath(),this.__originalClosePath()},S.beginPath=function(){i.beginPath(),this.__originalBeginPath()}}class y{constructor(i){this.canvasFactory=i,this.cache=Object.create(null)}getCanvas(i,n,s){let o;return this.cache[i]!==void 0?(o=this.cache[i],this.canvasFactory.reset(o,n,s)):(o=this.canvasFactory.create(n,s),this.cache[i]=o),o}delete(i){delete this.cache[i]}clear(){for(const i in this.cache){const n=this.cache[i];this.canvasFactory.destroy(n),delete this.cache[i]}}}function u(S,i,n,s,o,c,b,F,N,tt){const[Q,st,ct,yt,dt,Ft]=(0,M.getCurrentTransform)(S);if(st===0&&ct===0){const Dt=b*Q+dt,ut=Math.round(Dt),K=F*yt+Ft,J=Math.round(K),ht=(b+N)*Q+dt,Et=Math.abs(Math.round(ht)-ut)||1,Ct=(F+tt)*yt+Ft,jt=Math.abs(Math.round(Ct)-J)||1;return S.setTransform(Math.sign(Q),0,0,Math.sign(yt),ut,J),S.drawImage(i,n,s,o,c,0,0,Et,jt),S.setTransform(Q,st,ct,yt,dt,Ft),[Et,jt]}if(Q===0&&yt===0){const Dt=F*ct+dt,ut=Math.round(Dt),K=b*st+Ft,J=Math.round(K),ht=(F+tt)*ct+dt,Et=Math.abs(Math.round(ht)-ut)||1,Ct=(b+N)*st+Ft,jt=Math.abs(Math.round(Ct)-J)||1;return S.setTransform(0,Math.sign(st),Math.sign(ct),0,ut,J),S.drawImage(i,n,s,o,c,0,0,jt,Et),S.setTransform(Q,st,ct,yt,dt,Ft),[jt,Et]}S.drawImage(i,n,s,o,c,b,F,N,tt);const Bt=Math.hypot(Q,st),St=Math.hypot(ct,yt);return[Bt*N,St*tt]}function _(S){const{width:i,height:n}=S;if(i>O||n>O)return null;const s=1e3,o=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),c=i+1;let b=new Uint8Array(c*(n+1)),F,N,tt;const Q=i+7&-8;let st=new Uint8Array(Q*n),ct=0;for(const St of S.data){let Dt=128;for(;Dt>0;)st[ct++]=St&Dt?0:255,Dt>>=1}let yt=0;for(ct=0,st[ct]!==0&&(b[0]=1,++yt),N=1;N<i;N++)st[ct]!==st[ct+1]&&(b[N]=st[ct]?2:1,++yt),ct++;for(st[ct]!==0&&(b[N]=2,++yt),F=1;F<n;F++){ct=F*Q,tt=F*c,st[ct-Q]!==st[ct]&&(b[tt]=st[ct]?1:8,++yt);let St=(st[ct]?4:0)+(st[ct-Q]?8:0);for(N=1;N<i;N++)St=(St>>2)+(st[ct+1]?4:0)+(st[ct-Q+1]?8:0),o[St]&&(b[tt+N]=o[St],++yt),ct++;if(st[ct-Q]!==st[ct]&&(b[tt+N]=st[ct]?2:4,++yt),yt>s)return null}for(ct=Q*(n-1),tt=F*c,st[ct]!==0&&(b[tt]=8,++yt),N=1;N<i;N++)st[ct]!==st[ct+1]&&(b[tt+N]=st[ct]?4:8,++yt),ct++;if(st[ct]!==0&&(b[tt+N]=4,++yt),yt>s)return null;const dt=new Int32Array([0,c,-1,0,-c,0,0,0,1]),Ft=new Path2D;for(F=0;yt&&F<=n;F++){let St=F*c;const Dt=St+i;for(;St<Dt&&!b[St];)St++;if(St===Dt)continue;Ft.moveTo(St%c,F);const ut=St;let K=b[St];do{const J=dt[K];do St+=J;while(!b[St]);const ht=b[St];ht!==5&&ht!==10?(K=ht,b[St]=0):(K=ht&51*K>>4,b[St]&=K>>2|K<<2),Ft.lineTo(St%c,St/c|0),b[St]||--yt}while(ut!==St);--F}return st=null,b=null,function(St){St.save(),St.scale(1/i,-1/n),St.translate(0,-n),St.fill(Ft),St.beginPath(),St.restore()}}class w{constructor(i,n){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=h.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=h.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=h.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,i,n])}clone(){const i=Object.create(this);return i.clipBox=this.clipBox.slice(),i}setCurrentPoint(i,n){this.x=i,this.y=n}updatePathMinMax(i,n,s){[n,s]=h.Util.applyTransform([n,s],i),this.minX=Math.min(this.minX,n),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,n),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(i,n){const s=h.Util.applyTransform(n,i),o=h.Util.applyTransform(n.slice(2),i);this.minX=Math.min(this.minX,s[0],o[0]),this.minY=Math.min(this.minY,s[1],o[1]),this.maxX=Math.max(this.maxX,s[0],o[0]),this.maxY=Math.max(this.maxY,s[1],o[1])}updateScalingPathMinMax(i,n){h.Util.scaleMinMax(i,n),this.minX=Math.min(this.minX,n[0]),this.maxX=Math.max(this.maxX,n[1]),this.minY=Math.min(this.minY,n[2]),this.maxY=Math.max(this.maxY,n[3])}updateCurvePathMinMax(i,n,s,o,c,b,F,N,tt,Q){const st=h.Util.bezierBoundingBox(n,s,o,c,b,F,N,tt);if(Q){Q[0]=Math.min(Q[0],st[0],st[2]),Q[1]=Math.max(Q[1],st[0],st[2]),Q[2]=Math.min(Q[2],st[1],st[3]),Q[3]=Math.max(Q[3],st[1],st[3]);return}this.updateRectMinMax(i,st)}getPathBoundingBox(i=at.PathType.FILL,n=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(i===at.PathType.STROKE){n||(0,h.unreachable)("Stroke bounding box must include transform.");const o=h.Util.singularValueDecompose2dScale(n),c=o[0]*this.lineWidth/2,b=o[1]*this.lineWidth/2;s[0]-=c,s[1]-=b,s[2]+=c,s[3]+=b}return s}updateClipFromPath(){const i=h.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(i||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(i){this.clipBox=i,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(i=at.PathType.FILL,n=null){return h.Util.intersect(this.clipBox,this.getPathBoundingBox(i,n))}}function C(S,i){if(typeof ImageData<"u"&&i instanceof ImageData){S.putImageData(i,0,0);return}const n=i.height,s=i.width,o=n%x,c=(n-o)/x,b=o===0?c:c+1,F=S.createImageData(s,x);let N=0,tt;const Q=i.data,st=F.data;let ct,yt,dt,Ft;if(i.kind===h.ImageKind.GRAYSCALE_1BPP){const Bt=Q.byteLength,St=new Uint32Array(st.buffer,0,st.byteLength>>2),Dt=St.length,ut=s+7>>3,K=4294967295,J=h.FeatureTest.isLittleEndian?4278190080:255;for(ct=0;ct<b;ct++){for(dt=ct<c?x:o,tt=0,yt=0;yt<dt;yt++){const ht=Bt-N;let Et=0;const Ct=ht>ut?s:ht*8-7,jt=Ct&-8;let Gt=0,Ht=0;for(;Et<jt;Et+=8)Ht=Q[N++],St[tt++]=Ht&128?K:J,St[tt++]=Ht&64?K:J,St[tt++]=Ht&32?K:J,St[tt++]=Ht&16?K:J,St[tt++]=Ht&8?K:J,St[tt++]=Ht&4?K:J,St[tt++]=Ht&2?K:J,St[tt++]=Ht&1?K:J;for(;Et<Ct;Et++)Gt===0&&(Ht=Q[N++],Gt=128),St[tt++]=Ht&Gt?K:J,Gt>>=1}for(;tt<Dt;)St[tt++]=0;S.putImageData(F,0,ct*x)}}else if(i.kind===h.ImageKind.RGBA_32BPP){for(yt=0,Ft=s*x*4,ct=0;ct<c;ct++)st.set(Q.subarray(N,N+Ft)),N+=Ft,S.putImageData(F,0,yt),yt+=x;ct<b&&(Ft=s*o*4,st.set(Q.subarray(N,N+Ft)),S.putImageData(F,0,yt))}else if(i.kind===h.ImageKind.RGB_24BPP)for(dt=x,Ft=s*dt,ct=0;ct<b;ct++){for(ct>=c&&(dt=o,Ft=s*dt),tt=0,yt=Ft;yt--;)st[tt++]=Q[N++],st[tt++]=Q[N++],st[tt++]=Q[N++],st[tt++]=255;S.putImageData(F,0,ct*x)}else throw new Error(`bad image kind: ${i.kind}`)}function A(S,i){if(i.bitmap){S.drawImage(i.bitmap,0,0);return}const n=i.height,s=i.width,o=n%x,c=(n-o)/x,b=o===0?c:c+1,F=S.createImageData(s,x);let N=0;const tt=i.data,Q=F.data;for(let st=0;st<b;st++){const ct=st<c?x:o;({srcPos:N}=(0,X.convertBlackAndWhiteToRGBA)({src:tt,srcPos:N,dest:Q,width:s,height:ct,nonBlackColor:0})),S.putImageData(F,0,st*x)}}function a(S,i){const n=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of n)S[s]!==void 0&&(i[s]=S[s]);S.setLineDash!==void 0&&(i.setLineDash(S.getLineDash()),i.lineDashOffset=S.lineDashOffset)}function l(S){if(S.strokeStyle=S.fillStyle="#000000",S.fillRule="nonzero",S.globalAlpha=1,S.lineWidth=1,S.lineCap="butt",S.lineJoin="miter",S.miterLimit=10,S.globalCompositeOperation="source-over",S.font="10px sans-serif",S.setLineDash!==void 0&&(S.setLineDash([]),S.lineDashOffset=0),!h.isNodeJS){const{filter:i}=S;i!=="none"&&i!==""&&(S.filter="none")}}function P(S,i,n,s){const o=S.length;for(let c=3;c<o;c+=4){const b=S[c];if(b===0)S[c-3]=i,S[c-2]=n,S[c-1]=s;else if(b<255){const F=255-b;S[c-3]=S[c-3]*b+i*F>>8,S[c-2]=S[c-2]*b+n*F>>8,S[c-1]=S[c-1]*b+s*F>>8}}}function p(S,i,n){const s=S.length,o=1/255;for(let c=3;c<s;c+=4){const b=n?n[S[c]]:S[c];i[c]=i[c]*b*o|0}}function r(S,i,n){const s=S.length;for(let o=3;o<s;o+=4){const c=S[o-3]*77+S[o-2]*152+S[o-1]*28;i[o]=n?i[o]*n[c>>8]>>8:i[o]*c>>16}}function T(S,i,n,s,o,c,b,F,N,tt,Q){const st=!!c,ct=st?c[0]:0,yt=st?c[1]:0,dt=st?c[2]:0,Ft=o==="Luminosity"?r:p,St=Math.min(s,Math.ceil(1048576/n));for(let Dt=0;Dt<s;Dt+=St){const ut=Math.min(St,s-Dt),K=S.getImageData(F-tt,Dt+(N-Q),n,ut),J=i.getImageData(F,Dt+N,n,ut);st&&P(K.data,ct,yt,dt),Ft(K.data,J.data,b),i.putImageData(J,F,Dt+N)}}function m(S,i,n,s){const o=s[0],c=s[1],b=s[2]-o,F=s[3]-c;b===0||F===0||(T(i.context,n,b,F,i.subtype,i.backdrop,i.transferMap,o,c,i.offsetX,i.offsetY),S.save(),S.globalAlpha=1,S.globalCompositeOperation="source-over",S.setTransform(1,0,0,1,0,0),S.drawImage(n.canvas,0,0),S.restore())}function B(S,i){const n=h.Util.singularValueDecompose2dScale(S);n[0]=Math.fround(n[0]),n[1]=Math.fround(n[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*M.PixelsPerInch.PDF_TO_CSS_UNITS);return i!==void 0?i:n[0]<=s||n[1]<=s}const z=["butt","round","square"],E=["miter","round","bevel"],V={},it={},Pt=class Pt{constructor(i,n,s,o,c,{optionalContentConfig:b,markedContentStack:F=null},N,tt){I(this,H);I(this,pt);this.ctx=i,this.current=new w(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=n,this.objs=s,this.canvasFactory=o,this.filterFactory=c,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=F||[],this.optionalContentConfig=b,this.cachedCanvases=new y(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=N,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=tt,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(i,n=null){return typeof i=="string"?i.startsWith("g_")?this.commonObjs.get(i):this.objs.get(i):n}beginDrawing({transform:i,viewport:n,transparency:s=!1,background:o=null}){const c=this.ctx.canvas.width,b=this.ctx.canvas.height,F=this.ctx.fillStyle;if(this.ctx.fillStyle=o||"#ffffff",this.ctx.fillRect(0,0,c,b),this.ctx.fillStyle=F,s){const N=this.cachedCanvases.getCanvas("transparent",c,b);this.compositeCtx=this.ctx,this.transparentCanvas=N.canvas,this.ctx=N.context,this.ctx.save(),this.ctx.transform(...(0,M.getCurrentTransform)(this.compositeCtx))}this.ctx.save(),l(this.ctx),i&&(this.ctx.transform(...i),this.outputScaleX=i[0],this.outputScaleY=i[0]),this.ctx.transform(...n.transform),this.viewportScale=n.scale,this.baseTransform=(0,M.getCurrentTransform)(this.ctx)}executeOperatorList(i,n,s,o){const c=i.argsArray,b=i.fnArray;let F=n||0;const N=c.length;if(N===F)return F;const tt=N-F>L&&typeof s=="function",Q=tt?Date.now()+g:0;let st=0;const ct=this.commonObjs,yt=this.objs;let dt;for(;;){if(o!==void 0&&F===o.nextBreakPoint)return o.breakIt(F,s),F;if(dt=b[F],dt!==h.OPS.dependency)this[dt].apply(this,c[F]);else for(const Ft of c[F]){const Bt=Ft.startsWith("g_")?ct:yt;if(!Bt.has(Ft))return Bt.get(Ft,s),F}if(F++,F===N)return F;if(tt&&++st>L){if(Date.now()>Q)return s(),F;st=0}}}endDrawing(){W(this,H,Qe).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const i of this._cachedBitmapsMap.values()){for(const n of i.values())typeof HTMLCanvasElement<"u"&&n instanceof HTMLCanvasElement&&(n.width=n.height=0);i.clear()}this._cachedBitmapsMap.clear(),W(this,pt,Ze).call(this)}_scaleImage(i,n){const s=i.width,o=i.height;let c=Math.max(Math.hypot(n[0],n[1]),1),b=Math.max(Math.hypot(n[2],n[3]),1),F=s,N=o,tt="prescale1",Q,st;for(;c>2&&F>1||b>2&&N>1;){let ct=F,yt=N;c>2&&F>1&&(ct=F>=16384?Math.floor(F/2)-1||1:Math.ceil(F/2),c/=F/ct),b>2&&N>1&&(yt=N>=16384?Math.floor(N/2)-1||1:Math.ceil(N)/2,b/=N/yt),Q=this.cachedCanvases.getCanvas(tt,ct,yt),st=Q.context,st.clearRect(0,0,ct,yt),st.drawImage(i,0,0,F,N,0,0,ct,yt),i=Q.canvas,F=ct,N=yt,tt=tt==="prescale1"?"prescale2":"prescale1"}return{img:i,paintWidth:F,paintHeight:N}}_createMaskCanvas(i){const n=this.ctx,{width:s,height:o}=i,c=this.current.fillColor,b=this.current.patternFill,F=(0,M.getCurrentTransform)(n);let N,tt,Q,st;if((i.bitmap||i.data)&&i.count>1){const Et=i.bitmap||i.data.buffer;tt=JSON.stringify(b?F:[F.slice(0,4),c]),N=this._cachedBitmapsMap.get(Et),N||(N=new Map,this._cachedBitmapsMap.set(Et,N));const Ct=N.get(tt);if(Ct&&!b){const jt=Math.round(Math.min(F[0],F[2])+F[4]),Gt=Math.round(Math.min(F[1],F[3])+F[5]);return{canvas:Ct,offsetX:jt,offsetY:Gt}}Q=Ct}Q||(st=this.cachedCanvases.getCanvas("maskCanvas",s,o),A(st.context,i));let ct=h.Util.transform(F,[1/s,0,0,-1/o,0,0]);ct=h.Util.transform(ct,[1,0,0,1,0,-o]);const yt=h.Util.applyTransform([0,0],ct),dt=h.Util.applyTransform([s,o],ct),Ft=h.Util.normalizeRect([yt[0],yt[1],dt[0],dt[1]]),Bt=Math.round(Ft[2]-Ft[0])||1,St=Math.round(Ft[3]-Ft[1])||1,Dt=this.cachedCanvases.getCanvas("fillCanvas",Bt,St),ut=Dt.context,K=Math.min(yt[0],dt[0]),J=Math.min(yt[1],dt[1]);ut.translate(-K,-J),ut.transform(...ct),Q||(Q=this._scaleImage(st.canvas,(0,M.getCurrentTransformInverse)(ut)),Q=Q.img,N&&b&&N.set(tt,Q)),ut.imageSmoothingEnabled=B((0,M.getCurrentTransform)(ut),i.interpolate),u(ut,Q,0,0,Q.width,Q.height,0,0,s,o),ut.globalCompositeOperation="source-in";const ht=h.Util.transform((0,M.getCurrentTransformInverse)(ut),[1,0,0,1,-K,-J]);return ut.fillStyle=b?c.getPattern(n,this,ht,at.PathType.FILL):c,ut.fillRect(0,0,s,o),N&&!b&&(this.cachedCanvases.delete("fillCanvas"),N.set(tt,Dt.canvas)),{canvas:Dt.canvas,offsetX:Math.round(K),offsetY:Math.round(J)}}setLineWidth(i){i!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=i,this.ctx.lineWidth=i}setLineCap(i){this.ctx.lineCap=z[i]}setLineJoin(i){this.ctx.lineJoin=E[i]}setMiterLimit(i){this.ctx.miterLimit=i}setDash(i,n){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(i),s.lineDashOffset=n)}setRenderingIntent(i){}setFlatness(i){}setGState(i){for(const[n,s]of i)switch(n){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.current.fillAlpha=s,this.ctx.globalAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const i=this.inSMaskMode;this.current.activeSMask&&!i?this.beginSMaskMode():!this.current.activeSMask&&i&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const i=this.ctx.canvas.width,n=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,o=this.cachedCanvases.getCanvas(s,i,n);this.suspendedCtx=this.ctx,this.ctx=o.context;const c=this.ctx;c.setTransform(...(0,M.getCurrentTransform)(this.suspendedCtx)),a(this.suspendedCtx,c),v(c,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),a(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(i){if(!this.current.activeSMask)return;i?(i[0]=Math.floor(i[0]),i[1]=Math.floor(i[1]),i[2]=Math.ceil(i[2]),i[3]=Math.ceil(i[3])):i=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const n=this.current.activeSMask,s=this.suspendedCtx;m(s,n,this.ctx,i),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(a(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const i=this.current;this.stateStack.push(i),this.current=i.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),a(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(i,n,s,o,c,b){this.ctx.transform(i,n,s,o,c,b),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(i,n,s){const o=this.ctx,c=this.current;let b=c.x,F=c.y,N,tt;const Q=(0,M.getCurrentTransform)(o),st=Q[0]===0&&Q[3]===0||Q[1]===0&&Q[2]===0,ct=st?s.slice(0):null;for(let yt=0,dt=0,Ft=i.length;yt<Ft;yt++)switch(i[yt]|0){case h.OPS.rectangle:b=n[dt++],F=n[dt++];const Bt=n[dt++],St=n[dt++],Dt=b+Bt,ut=F+St;o.moveTo(b,F),Bt===0||St===0?o.lineTo(Dt,ut):(o.lineTo(Dt,F),o.lineTo(Dt,ut),o.lineTo(b,ut)),st||c.updateRectMinMax(Q,[b,F,Dt,ut]),o.closePath();break;case h.OPS.moveTo:b=n[dt++],F=n[dt++],o.moveTo(b,F),st||c.updatePathMinMax(Q,b,F);break;case h.OPS.lineTo:b=n[dt++],F=n[dt++],o.lineTo(b,F),st||c.updatePathMinMax(Q,b,F);break;case h.OPS.curveTo:N=b,tt=F,b=n[dt+4],F=n[dt+5],o.bezierCurveTo(n[dt],n[dt+1],n[dt+2],n[dt+3],b,F),c.updateCurvePathMinMax(Q,N,tt,n[dt],n[dt+1],n[dt+2],n[dt+3],b,F,ct),dt+=6;break;case h.OPS.curveTo2:N=b,tt=F,o.bezierCurveTo(b,F,n[dt],n[dt+1],n[dt+2],n[dt+3]),c.updateCurvePathMinMax(Q,N,tt,b,F,n[dt],n[dt+1],n[dt+2],n[dt+3],ct),b=n[dt+2],F=n[dt+3],dt+=4;break;case h.OPS.curveTo3:N=b,tt=F,b=n[dt+2],F=n[dt+3],o.bezierCurveTo(n[dt],n[dt+1],b,F,b,F),c.updateCurvePathMinMax(Q,N,tt,n[dt],n[dt+1],b,F,b,F,ct),dt+=4;break;case h.OPS.closePath:o.closePath();break}st&&c.updateScalingPathMinMax(Q,ct),c.setCurrentPoint(b,F)}closePath(){this.ctx.closePath()}stroke(i=!0){const n=this.ctx,s=this.current.strokeColor;n.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof s=="object"&&(s!=null&&s.getPattern)?(n.save(),n.strokeStyle=s.getPattern(n,this,(0,M.getCurrentTransformInverse)(n),at.PathType.STROKE),this.rescaleAndStroke(!1),n.restore()):this.rescaleAndStroke(!0)),i&&this.consumePath(this.current.getClippedPathBoundingBox()),n.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(i=!0){const n=this.ctx,s=this.current.fillColor,o=this.current.patternFill;let c=!1;o&&(n.save(),n.fillStyle=s.getPattern(n,this,(0,M.getCurrentTransformInverse)(n),at.PathType.FILL),c=!0);const b=this.current.getClippedPathBoundingBox();this.contentVisible&&b!==null&&(this.pendingEOFill?(n.fill("evenodd"),this.pendingEOFill=!1):n.fill()),c&&n.restore(),i&&this.consumePath(b)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=V}eoClip(){this.pendingClip=it}beginText(){this.current.textMatrix=h.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const i=this.pendingTextPaths,n=this.ctx;if(i===void 0){n.beginPath();return}n.save(),n.beginPath();for(const s of i)n.setTransform(...s.transform),n.translate(s.x,s.y),s.addToPath(n,s.fontSize);n.restore(),n.clip(),n.beginPath(),delete this.pendingTextPaths}setCharSpacing(i){this.current.charSpacing=i}setWordSpacing(i){this.current.wordSpacing=i}setHScale(i){this.current.textHScale=i/100}setLeading(i){this.current.leading=-i}setFont(i,n){var Q;const s=this.commonObjs.get(i),o=this.current;if(!s)throw new Error(`Can't find font for ${i}`);if(o.fontMatrix=s.fontMatrix||h.FONT_IDENTITY_MATRIX,(o.fontMatrix[0]===0||o.fontMatrix[3]===0)&&(0,h.warn)("Invalid font matrix for font "+i),n<0?(n=-n,o.fontDirection=-1):o.fontDirection=1,this.current.font=s,this.current.fontSize=n,s.isType3Font)return;const c=s.loadedName||"sans-serif",b=((Q=s.systemFontInfo)==null?void 0:Q.css)||`"${c}", ${s.fallbackName}`;let F="normal";s.black?F="900":s.bold&&(F="bold");const N=s.italic?"italic":"normal";let tt=n;n<ft?tt=ft:n>U&&(tt=U),this.current.fontSizeScale=n/tt,this.ctx.font=`${N} ${F} ${tt}px ${b}`}setTextRenderingMode(i){this.current.textRenderingMode=i}setTextRise(i){this.current.textRise=i}moveText(i,n){this.current.x=this.current.lineX+=i,this.current.y=this.current.lineY+=n}setLeadingMoveText(i,n){this.setLeading(-n),this.moveText(i,n)}setTextMatrix(i,n,s,o,c,b){this.current.textMatrix=[i,n,s,o,c,b],this.current.textMatrixScale=Math.hypot(i,n),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(i,n,s,o){const c=this.ctx,b=this.current,F=b.font,N=b.textRenderingMode,tt=b.fontSize/b.fontSizeScale,Q=N&h.TextRenderingMode.FILL_STROKE_MASK,st=!!(N&h.TextRenderingMode.ADD_TO_PATH_FLAG),ct=b.patternFill&&!F.missingFile;let yt;(F.disableFontFace||st||ct)&&(yt=F.getPathGenerator(this.commonObjs,i)),F.disableFontFace||ct?(c.save(),c.translate(n,s),c.beginPath(),yt(c,tt),o&&c.setTransform(...o),(Q===h.TextRenderingMode.FILL||Q===h.TextRenderingMode.FILL_STROKE)&&c.fill(),(Q===h.TextRenderingMode.STROKE||Q===h.TextRenderingMode.FILL_STROKE)&&c.stroke(),c.restore()):((Q===h.TextRenderingMode.FILL||Q===h.TextRenderingMode.FILL_STROKE)&&c.fillText(i,n,s),(Q===h.TextRenderingMode.STROKE||Q===h.TextRenderingMode.FILL_STROKE)&&c.strokeText(i,n,s)),st&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,M.getCurrentTransform)(c),x:n,y:s,fontSize:tt,addToPath:yt})}get isFontSubpixelAAEnabled(){const{context:i}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);i.scale(1.5,1),i.fillText("I",0,10);const n=i.getImageData(0,0,10,10).data;let s=!1;for(let o=3;o<n.length;o+=4)if(n[o]>0&&n[o]<255){s=!0;break}return(0,h.shadow)(this,"isFontSubpixelAAEnabled",s)}showText(i){const n=this.current,s=n.font;if(s.isType3Font)return this.showType3Text(i);const o=n.fontSize;if(o===0)return;const c=this.ctx,b=n.fontSizeScale,F=n.charSpacing,N=n.wordSpacing,tt=n.fontDirection,Q=n.textHScale*tt,st=i.length,ct=s.vertical,yt=ct?1:-1,dt=s.defaultVMetrics,Ft=o*n.fontMatrix[0],Bt=n.textRenderingMode===h.TextRenderingMode.FILL&&!s.disableFontFace&&!n.patternFill;c.save(),c.transform(...n.textMatrix),c.translate(n.x,n.y+n.textRise),tt>0?c.scale(Q,-1):c.scale(Q,1);let St;if(n.patternFill){c.save();const ht=n.fillColor.getPattern(c,this,(0,M.getCurrentTransformInverse)(c),at.PathType.FILL);St=(0,M.getCurrentTransform)(c),c.restore(),c.fillStyle=ht}let Dt=n.lineWidth;const ut=n.textMatrixScale;if(ut===0||Dt===0){const ht=n.textRenderingMode&h.TextRenderingMode.FILL_STROKE_MASK;(ht===h.TextRenderingMode.STROKE||ht===h.TextRenderingMode.FILL_STROKE)&&(Dt=this.getSinglePixelWidth())}else Dt/=ut;if(b!==1&&(c.scale(b,b),Dt/=b),c.lineWidth=Dt,s.isInvalidPDFjsFont){const ht=[];let Et=0;for(const Ct of i)ht.push(Ct.unicode),Et+=Ct.width;c.fillText(ht.join(""),0,0),n.x+=Et*Ft*Q,c.restore(),this.compose();return}let K=0,J;for(J=0;J<st;++J){const ht=i[J];if(typeof ht=="number"){K+=yt*ht*o/1e3;continue}let Et=!1;const Ct=(ht.isSpace?N:0)+F,jt=ht.fontChar,Gt=ht.accent;let Ht,Xt,Vt=ht.width;if(ct){const qt=ht.vmetric||dt,ot=-(ht.vmetric?qt[1]:Vt*.5)*Ft,Y=qt[2]*Ft;Vt=qt?-qt[0]:Vt,Ht=ot/b,Xt=(K+Y)/b}else Ht=K/b,Xt=0;if(s.remeasure&&Vt>0){const qt=c.measureText(jt).width*1e3/o*b;if(Vt<qt&&this.isFontSubpixelAAEnabled){const ot=Vt/qt;Et=!0,c.save(),c.scale(ot,1),Ht/=ot}else Vt!==qt&&(Ht+=(Vt-qt)/2e3*o/b)}if(this.contentVisible&&(ht.isInFont||s.missingFile)){if(Bt&&!Gt)c.fillText(jt,Ht,Xt);else if(this.paintChar(jt,Ht,Xt,St),Gt){const qt=Ht+o*Gt.offset.x/b,ot=Xt-o*Gt.offset.y/b;this.paintChar(Gt.fontChar,qt,ot,St)}}const Wt=ct?Vt*Ft-Ct*tt:Vt*Ft+Ct*tt;K+=Wt,Et&&c.restore()}ct?n.y-=K:n.x+=K*Q,c.restore(),this.compose()}showType3Text(i){const n=this.ctx,s=this.current,o=s.font,c=s.fontSize,b=s.fontDirection,F=o.vertical?1:-1,N=s.charSpacing,tt=s.wordSpacing,Q=s.textHScale*b,st=s.fontMatrix||h.FONT_IDENTITY_MATRIX,ct=i.length,yt=s.textRenderingMode===h.TextRenderingMode.INVISIBLE;let dt,Ft,Bt,St;if(!(yt||c===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,n.save(),n.transform(...s.textMatrix),n.translate(s.x,s.y),n.scale(Q,b),dt=0;dt<ct;++dt){if(Ft=i[dt],typeof Ft=="number"){St=F*Ft*c/1e3,this.ctx.translate(St,0),s.x+=St*Q;continue}const Dt=(Ft.isSpace?tt:0)+N,ut=o.charProcOperatorList[Ft.operatorListId];if(!ut){(0,h.warn)(`Type3 character "${Ft.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=Ft,this.save(),n.scale(c,c),n.transform(...st),this.executeOperatorList(ut),this.restore()),Bt=h.Util.applyTransform([Ft.width,0],st)[0]*c+Dt,n.translate(Bt,0),s.x+=Bt*Q}n.restore(),this.processingType3=null}}setCharWidth(i,n){}setCharWidthAndBounds(i,n,s,o,c,b){this.ctx.rect(s,o,c-s,b-o),this.ctx.clip(),this.endPath()}getColorN_Pattern(i){let n;if(i[0]==="TilingPattern"){const s=i[1],o=this.baseTransform||(0,M.getCurrentTransform)(this.ctx),c={createCanvasGraphics:b=>new Pt(b,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};n=new at.TilingPattern(i,s,this.ctx,c,o)}else n=this._getPattern(i[1],i[2]);return n}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(i,n,s){const o=h.Util.makeHexColor(i,n,s);this.ctx.strokeStyle=o,this.current.strokeColor=o}setFillRGBColor(i,n,s){const o=h.Util.makeHexColor(i,n,s);this.ctx.fillStyle=o,this.current.fillColor=o,this.current.patternFill=!1}_getPattern(i,n=null){let s;return this.cachedPatterns.has(i)?s=this.cachedPatterns.get(i):(s=(0,at.getShadingPattern)(this.getObject(i)),this.cachedPatterns.set(i,s)),n&&(s.matrix=n),s}shadingFill(i){if(!this.contentVisible)return;const n=this.ctx;this.save();const s=this._getPattern(i);n.fillStyle=s.getPattern(n,this,(0,M.getCurrentTransformInverse)(n),at.PathType.SHADING);const o=(0,M.getCurrentTransformInverse)(n);if(o){const{width:c,height:b}=n.canvas,[F,N,tt,Q]=h.Util.getAxialAlignedBoundingBox([0,0,c,b],o);this.ctx.fillRect(F,N,tt-F,Q-N)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,h.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,h.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(i,n){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(i)&&i.length===6&&this.transform(...i),this.baseTransform=(0,M.getCurrentTransform)(this.ctx),n)){const s=n[2]-n[0],o=n[3]-n[1];this.ctx.rect(n[0],n[1],s,o),this.current.updateRectMinMax((0,M.getCurrentTransform)(this.ctx),n),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(i){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const n=this.ctx;i.isolated||(0,h.info)("TODO: Support non-isolated groups."),i.knockout&&(0,h.warn)("Knockout groups not supported.");const s=(0,M.getCurrentTransform)(n);if(i.matrix&&n.transform(...i.matrix),!i.bbox)throw new Error("Bounding box is required.");let o=h.Util.getAxialAlignedBoundingBox(i.bbox,(0,M.getCurrentTransform)(n));const c=[0,0,n.canvas.width,n.canvas.height];o=h.Util.intersect(o,c)||[0,0,0,0];const b=Math.floor(o[0]),F=Math.floor(o[1]);let N=Math.max(Math.ceil(o[2])-b,1),tt=Math.max(Math.ceil(o[3])-F,1),Q=1,st=1;N>k&&(Q=N/k,N=k),tt>k&&(st=tt/k,tt=k),this.current.startNewPathAndClipBox([0,0,N,tt]);let ct="groupAt"+this.groupLevel;i.smask&&(ct+="_smask_"+this.smaskCounter++%2);const yt=this.cachedCanvases.getCanvas(ct,N,tt),dt=yt.context;dt.scale(1/Q,1/st),dt.translate(-b,-F),dt.transform(...s),i.smask?this.smaskStack.push({canvas:yt.canvas,context:dt,offsetX:b,offsetY:F,scaleX:Q,scaleY:st,subtype:i.smask.subtype,backdrop:i.smask.backdrop,transferMap:i.smask.transferMap||null,startTransformInverse:null}):(n.setTransform(1,0,0,1,0,0),n.translate(b,F),n.scale(Q,st),n.save()),a(n,dt),this.ctx=dt,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(n),this.groupLevel++}endGroup(i){if(!this.contentVisible)return;this.groupLevel--;const n=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,i.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const o=(0,M.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...o);const c=h.Util.getAxialAlignedBoundingBox([0,0,n.canvas.width,n.canvas.height],o);this.ctx.drawImage(n.canvas,0,0),this.ctx.restore(),this.compose(c)}}beginAnnotation(i,n,s,o,c){if(W(this,H,Qe).call(this),l(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),Array.isArray(n)&&n.length===4){const b=n[2]-n[0],F=n[3]-n[1];if(c&&this.annotationCanvasMap){s=s.slice(),s[4]-=n[0],s[5]-=n[1],n=n.slice(),n[0]=n[1]=0,n[2]=b,n[3]=F;const[N,tt]=h.Util.singularValueDecompose2dScale((0,M.getCurrentTransform)(this.ctx)),{viewportScale:Q}=this,st=Math.ceil(b*this.outputScaleX*Q),ct=Math.ceil(F*this.outputScaleY*Q);this.annotationCanvas=this.canvasFactory.create(st,ct);const{canvas:yt,context:dt}=this.annotationCanvas;this.annotationCanvasMap.set(i,yt),this.annotationCanvas.savedCtx=this.ctx,this.ctx=dt,this.ctx.save(),this.ctx.setTransform(N,0,0,-tt,0,F*tt),l(this.ctx)}else l(this.ctx),this.ctx.rect(n[0],n[1],b,F),this.ctx.clip(),this.endPath()}this.current=new w(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...o)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),W(this,pt,Ze).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(i){if(!this.contentVisible)return;const n=i.count;i=this.getObject(i.data,i),i.count=n;const s=this.ctx,o=this.processingType3;if(o&&(o.compiled===void 0&&(o.compiled=_(i)),o.compiled)){o.compiled(s);return}const c=this._createMaskCanvas(i),b=c.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(b,c.offsetX,c.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(i,n,s=0,o=0,c,b){if(!this.contentVisible)return;i=this.getObject(i.data,i);const F=this.ctx;F.save();const N=(0,M.getCurrentTransform)(F);F.transform(n,s,o,c,0,0);const tt=this._createMaskCanvas(i);F.setTransform(1,0,0,1,tt.offsetX-N[4],tt.offsetY-N[5]);for(let Q=0,st=b.length;Q<st;Q+=2){const ct=h.Util.transform(N,[n,s,o,c,b[Q],b[Q+1]]),[yt,dt]=h.Util.applyTransform([0,0],ct);F.drawImage(tt.canvas,yt,dt)}F.restore(),this.compose()}paintImageMaskXObjectGroup(i){if(!this.contentVisible)return;const n=this.ctx,s=this.current.fillColor,o=this.current.patternFill;for(const c of i){const{data:b,width:F,height:N,transform:tt}=c,Q=this.cachedCanvases.getCanvas("maskCanvas",F,N),st=Q.context;st.save();const ct=this.getObject(b,c);A(st,ct),st.globalCompositeOperation="source-in",st.fillStyle=o?s.getPattern(st,this,(0,M.getCurrentTransformInverse)(n),at.PathType.FILL):s,st.fillRect(0,0,F,N),st.restore(),n.save(),n.transform(...tt),n.scale(1,-1),u(n,Q.canvas,0,0,F,N,0,-1,1,1),n.restore()}this.compose()}paintImageXObject(i){if(!this.contentVisible)return;const n=this.getObject(i);if(!n){(0,h.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(n)}paintImageXObjectRepeat(i,n,s,o){if(!this.contentVisible)return;const c=this.getObject(i);if(!c){(0,h.warn)("Dependent image isn't ready yet");return}const b=c.width,F=c.height,N=[];for(let tt=0,Q=o.length;tt<Q;tt+=2)N.push({transform:[n,0,0,s,o[tt],o[tt+1]],x:0,y:0,w:b,h:F});this.paintInlineImageXObjectGroup(c,N)}applyTransferMapsToCanvas(i){return this.current.transferMaps!=="none"&&(i.filter=this.current.transferMaps,i.drawImage(i.canvas,0,0),i.filter="none"),i.canvas}applyTransferMapsToBitmap(i){if(this.current.transferMaps==="none")return i.bitmap;const{bitmap:n,width:s,height:o}=i,c=this.cachedCanvases.getCanvas("inlineImage",s,o),b=c.context;return b.filter=this.current.transferMaps,b.drawImage(n,0,0),b.filter="none",c.canvas}paintInlineImageXObject(i){if(!this.contentVisible)return;const n=i.width,s=i.height,o=this.ctx;if(this.save(),!h.isNodeJS){const{filter:F}=o;F!=="none"&&F!==""&&(o.filter="none")}o.scale(1/n,-1/s);let c;if(i.bitmap)c=this.applyTransferMapsToBitmap(i);else if(typeof HTMLElement=="function"&&i instanceof HTMLElement||!i.data)c=i;else{const N=this.cachedCanvases.getCanvas("inlineImage",n,s).context;C(N,i),c=this.applyTransferMapsToCanvas(N)}const b=this._scaleImage(c,(0,M.getCurrentTransformInverse)(o));o.imageSmoothingEnabled=B((0,M.getCurrentTransform)(o),i.interpolate),u(o,b.img,0,0,b.paintWidth,b.paintHeight,0,-s,n,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(i,n){if(!this.contentVisible)return;const s=this.ctx;let o;if(i.bitmap)o=i.bitmap;else{const c=i.width,b=i.height,N=this.cachedCanvases.getCanvas("inlineImage",c,b).context;C(N,i),o=this.applyTransferMapsToCanvas(N)}for(const c of n)s.save(),s.transform(...c.transform),s.scale(1,-1),u(s,o,c.x,c.y,c.w,c.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(i){}markPointProps(i,n){}beginMarkedContent(i){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(i,n){i==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(n)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(i){const n=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(i);const s=this.ctx;this.pendingClip&&(n||(this.pendingClip===it?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const i=(0,M.getCurrentTransform)(this.ctx);if(i[1]===0&&i[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(i[0]),Math.abs(i[3]));else{const n=Math.abs(i[0]*i[3]-i[2]*i[1]),s=Math.hypot(i[0],i[2]),o=Math.hypot(i[1],i[3]);this._cachedGetSinglePixelWidth=Math.max(s,o)/n}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:i}=this.current,{a:n,b:s,c:o,d:c}=this.ctx.getTransform();let b,F;if(s===0&&o===0){const N=Math.abs(n),tt=Math.abs(c);if(N===tt)if(i===0)b=F=1/N;else{const Q=N*i;b=F=Q<1?1/Q:1}else if(i===0)b=1/N,F=1/tt;else{const Q=N*i,st=tt*i;b=Q<1?1/Q:1,F=st<1?1/st:1}}else{const N=Math.abs(n*c-s*o),tt=Math.hypot(n,s),Q=Math.hypot(o,c);if(i===0)b=Q/N,F=tt/N;else{const st=i*N;b=Q>st?Q/st:1,F=tt>st?tt/st:1}}this._cachedScaleForStroking[0]=b,this._cachedScaleForStroking[1]=F}return this._cachedScaleForStroking}rescaleAndStroke(i){const{ctx:n}=this,{lineWidth:s}=this.current,[o,c]=this.getScaleForStroking();if(n.lineWidth=s||1,o===1&&c===1){n.stroke();return}const b=n.getLineDash();if(i&&n.save(),n.scale(o,c),b.length>0){const F=Math.max(o,c);n.setLineDash(b.map(N=>N/F)),n.lineDashOffset/=F}n.stroke(),i&&n.restore()}isContentVisible(){for(let i=this.markedContentStack.length-1;i>=0;i--)if(!this.markedContentStack[i].visible)return!1;return!0}};H=new WeakSet,Qe=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},pt=new WeakSet,Ze=function(){if(this.pageColors){const i=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(i!=="none"){const n=this.ctx.filter;this.ctx.filter=i,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=n}}};let nt=Pt;d.CanvasGraphics=nt;for(const S in h.OPS)nt.prototype[S]!==void 0&&(nt.prototype[h.OPS[S]]=nt.prototype[S])},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.TilingPattern=d.PathType=void 0,d.getShadingPattern=x;var h=rt(1),M=rt(6);const at={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};d.PathType=at;function X(_,w){if(!w)return;const C=w[2]-w[0],A=w[3]-w[1],a=new Path2D;a.rect(w[0],w[1],C,A),_.clip(a)}class ft{constructor(){this.constructor===ft&&(0,h.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,h.unreachable)("Abstract method `getPattern` called.")}}class U extends ft{constructor(w){super(),this._type=w[1],this._bbox=w[2],this._colorStops=w[3],this._p0=w[4],this._p1=w[5],this._r0=w[6],this._r1=w[7],this.matrix=null}_createGradient(w){let C;this._type==="axial"?C=w.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(C=w.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const A of this._colorStops)C.addColorStop(A[0],A[1]);return C}getPattern(w,C,A,a){let l;if(a===at.STROKE||a===at.FILL){const P=C.current.getClippedPathBoundingBox(a,(0,M.getCurrentTransform)(w))||[0,0,0,0],p=Math.ceil(P[2]-P[0])||1,r=Math.ceil(P[3]-P[1])||1,T=C.cachedCanvases.getCanvas("pattern",p,r,!0),m=T.context;m.clearRect(0,0,m.canvas.width,m.canvas.height),m.beginPath(),m.rect(0,0,m.canvas.width,m.canvas.height),m.translate(-P[0],-P[1]),A=h.Util.transform(A,[1,0,0,1,P[0],P[1]]),m.transform(...C.baseTransform),this.matrix&&m.transform(...this.matrix),X(m,this._bbox),m.fillStyle=this._createGradient(m),m.fill(),l=w.createPattern(T.canvas,"no-repeat");const B=new DOMMatrix(A);l.setTransform(B)}else X(w,this._bbox),l=this._createGradient(w);return l}}function k(_,w,C,A,a,l,P,p){const r=w.coords,T=w.colors,m=_.data,B=_.width*4;let z;r[C+1]>r[A+1]&&(z=C,C=A,A=z,z=l,l=P,P=z),r[A+1]>r[a+1]&&(z=A,A=a,a=z,z=P,P=p,p=z),r[C+1]>r[A+1]&&(z=C,C=A,A=z,z=l,l=P,P=z);const E=(r[C]+w.offsetX)*w.scaleX,V=(r[C+1]+w.offsetY)*w.scaleY,it=(r[A]+w.offsetX)*w.scaleX,nt=(r[A+1]+w.offsetY)*w.scaleY,H=(r[a]+w.offsetX)*w.scaleX,lt=(r[a+1]+w.offsetY)*w.scaleY;if(V>=lt)return;const pt=T[l],wt=T[l+1],Pt=T[l+2],S=T[P],i=T[P+1],n=T[P+2],s=T[p],o=T[p+1],c=T[p+2],b=Math.round(V),F=Math.round(lt);let N,tt,Q,st,ct,yt,dt,Ft;for(let Bt=b;Bt<=F;Bt++){if(Bt<nt){const J=Bt<V?0:(V-Bt)/(V-nt);N=E-(E-it)*J,tt=pt-(pt-S)*J,Q=wt-(wt-i)*J,st=Pt-(Pt-n)*J}else{let J;Bt>lt?J=1:nt===lt?J=0:J=(nt-Bt)/(nt-lt),N=it-(it-H)*J,tt=S-(S-s)*J,Q=i-(i-o)*J,st=n-(n-c)*J}let St;Bt<V?St=0:Bt>lt?St=1:St=(V-Bt)/(V-lt),ct=E-(E-H)*St,yt=pt-(pt-s)*St,dt=wt-(wt-o)*St,Ft=Pt-(Pt-c)*St;const Dt=Math.round(Math.min(N,ct)),ut=Math.round(Math.max(N,ct));let K=B*Bt+Dt*4;for(let J=Dt;J<=ut;J++)St=(N-J)/(N-ct),St<0?St=0:St>1&&(St=1),m[K++]=tt-(tt-yt)*St|0,m[K++]=Q-(Q-dt)*St|0,m[K++]=st-(st-Ft)*St|0,m[K++]=255}}function g(_,w,C){const A=w.coords,a=w.colors;let l,P;switch(w.type){case"lattice":const p=w.verticesPerRow,r=Math.floor(A.length/p)-1,T=p-1;for(l=0;l<r;l++){let m=l*p;for(let B=0;B<T;B++,m++)k(_,C,A[m],A[m+1],A[m+p],a[m],a[m+1],a[m+p]),k(_,C,A[m+p+1],A[m+1],A[m+p],a[m+p+1],a[m+1],a[m+p])}break;case"triangles":for(l=0,P=A.length;l<P;l+=3)k(_,C,A[l],A[l+1],A[l+2],a[l],a[l+1],a[l+2]);break;default:throw new Error("illegal figure")}}class L extends ft{constructor(w){super(),this._coords=w[2],this._colors=w[3],this._figures=w[4],this._bounds=w[5],this._bbox=w[7],this._background=w[8],this.matrix=null}_createMeshCanvas(w,C,A){const p=Math.floor(this._bounds[0]),r=Math.floor(this._bounds[1]),T=Math.ceil(this._bounds[2])-p,m=Math.ceil(this._bounds[3])-r,B=Math.min(Math.ceil(Math.abs(T*w[0]*1.1)),3e3),z=Math.min(Math.ceil(Math.abs(m*w[1]*1.1)),3e3),E=T/B,V=m/z,it={coords:this._coords,colors:this._colors,offsetX:-p,offsetY:-r,scaleX:1/E,scaleY:1/V},nt=B+2*2,H=z+2*2,lt=A.getCanvas("mesh",nt,H,!1),pt=lt.context,wt=pt.createImageData(B,z);if(C){const S=wt.data;for(let i=0,n=S.length;i<n;i+=4)S[i]=C[0],S[i+1]=C[1],S[i+2]=C[2],S[i+3]=255}for(const S of this._figures)g(wt,S,it);return pt.putImageData(wt,2,2),{canvas:lt.canvas,offsetX:p-2*E,offsetY:r-2*V,scaleX:E,scaleY:V}}getPattern(w,C,A,a){X(w,this._bbox);let l;if(a===at.SHADING)l=h.Util.singularValueDecompose2dScale((0,M.getCurrentTransform)(w));else if(l=h.Util.singularValueDecompose2dScale(C.baseTransform),this.matrix){const p=h.Util.singularValueDecompose2dScale(this.matrix);l=[l[0]*p[0],l[1]*p[1]]}const P=this._createMeshCanvas(l,a===at.SHADING?null:this._background,C.cachedCanvases);return a!==at.SHADING&&(w.setTransform(...C.baseTransform),this.matrix&&w.transform(...this.matrix)),w.translate(P.offsetX,P.offsetY),w.scale(P.scaleX,P.scaleY),w.createPattern(P.canvas,"no-repeat")}}class O extends ft{getPattern(){return"hotpink"}}function x(_){switch(_[0]){case"RadialAxial":return new U(_);case"Mesh":return new L(_);case"Dummy":return new O}throw new Error(`Unknown IR type: ${_[0]}`)}const v={COLORED:1,UNCOLORED:2},u=class u{constructor(w,C,A,a,l){this.operatorList=w[2],this.matrix=w[3]||[1,0,0,1,0,0],this.bbox=w[4],this.xstep=w[5],this.ystep=w[6],this.paintType=w[7],this.tilingType=w[8],this.color=C,this.ctx=A,this.canvasGraphicsFactory=a,this.baseTransform=l}createPatternCanvas(w){const C=this.operatorList,A=this.bbox,a=this.xstep,l=this.ystep,P=this.paintType,p=this.tilingType,r=this.color,T=this.canvasGraphicsFactory;(0,h.info)("TilingType: "+p);const m=A[0],B=A[1],z=A[2],E=A[3],V=h.Util.singularValueDecompose2dScale(this.matrix),it=h.Util.singularValueDecompose2dScale(this.baseTransform),nt=[V[0]*it[0],V[1]*it[1]],H=this.getSizeAndScale(a,this.ctx.canvas.width,nt[0]),lt=this.getSizeAndScale(l,this.ctx.canvas.height,nt[1]),pt=w.cachedCanvases.getCanvas("pattern",H.size,lt.size,!0),wt=pt.context,Pt=T.createCanvasGraphics(wt);Pt.groupLevel=w.groupLevel,this.setFillAndStrokeStyleToContext(Pt,P,r);let S=m,i=B,n=z,s=E;return m<0&&(S=0,n+=Math.abs(m)),B<0&&(i=0,s+=Math.abs(B)),wt.translate(-(H.scale*S),-(lt.scale*i)),Pt.transform(H.scale,0,0,lt.scale,0,0),wt.save(),this.clipBbox(Pt,S,i,n,s),Pt.baseTransform=(0,M.getCurrentTransform)(Pt.ctx),Pt.executeOperatorList(C),Pt.endDrawing(),{canvas:pt.canvas,scaleX:H.scale,scaleY:lt.scale,offsetX:S,offsetY:i}}getSizeAndScale(w,C,A){w=Math.abs(w);const a=Math.max(u.MAX_PATTERN_SIZE,C);let l=Math.ceil(w*A);return l>=a?l=a:A=l/w,{scale:A,size:l}}clipBbox(w,C,A,a,l){const P=a-C,p=l-A;w.ctx.rect(C,A,P,p),w.current.updateRectMinMax((0,M.getCurrentTransform)(w.ctx),[C,A,a,l]),w.clip(),w.endPath()}setFillAndStrokeStyleToContext(w,C,A){const a=w.ctx,l=w.current;switch(C){case v.COLORED:const P=this.ctx;a.fillStyle=P.fillStyle,a.strokeStyle=P.strokeStyle,l.fillColor=P.fillStyle,l.strokeColor=P.strokeStyle;break;case v.UNCOLORED:const p=h.Util.makeHexColor(A[0],A[1],A[2]);a.fillStyle=p,a.strokeStyle=p,l.fillColor=p,l.strokeColor=p;break;default:throw new h.FormatError(`Unsupported paint type: ${C}`)}}getPattern(w,C,A,a){let l=A;a!==at.SHADING&&(l=h.Util.transform(l,C.baseTransform),this.matrix&&(l=h.Util.transform(l,this.matrix)));const P=this.createPatternCanvas(C);let p=new DOMMatrix(l);p=p.translate(P.offsetX,P.offsetY),p=p.scale(1/P.scaleX,1/P.scaleY);const r=w.createPattern(P.canvas,"repeat");return r.setTransform(p),r}};ee(u,"MAX_PATTERN_SIZE",3e3);let y=u;d.TilingPattern=y},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.convertBlackAndWhiteToRGBA=at,d.convertToRGBA=M,d.grayToRGBA=ft;var h=rt(1);function M(U){switch(U.kind){case h.ImageKind.GRAYSCALE_1BPP:return at(U);case h.ImageKind.RGB_24BPP:return X(U)}return null}function at({src:U,srcPos:k=0,dest:g,width:L,height:O,nonBlackColor:x=4294967295,inverseDecode:v=!1}){const y=h.FeatureTest.isLittleEndian?4278190080:255,[u,_]=v?[x,y]:[y,x],w=L>>3,C=L&7,A=U.length;g=new Uint32Array(g.buffer);let a=0;for(let l=0;l<O;l++){for(const p=k+w;k<p;k++){const r=k<A?U[k]:255;g[a++]=r&128?_:u,g[a++]=r&64?_:u,g[a++]=r&32?_:u,g[a++]=r&16?_:u,g[a++]=r&8?_:u,g[a++]=r&4?_:u,g[a++]=r&2?_:u,g[a++]=r&1?_:u}if(C===0)continue;const P=k<A?U[k++]:255;for(let p=0;p<C;p++)g[a++]=P&1<<7-p?_:u}return{srcPos:k,destPos:a}}function X({src:U,srcPos:k=0,dest:g,destPos:L=0,width:O,height:x}){let v=0;const y=U.length>>2,u=new Uint32Array(U.buffer,k,y);if(h.FeatureTest.isLittleEndian){for(;v<y-2;v+=3,L+=4){const _=u[v],w=u[v+1],C=u[v+2];g[L]=_|4278190080,g[L+1]=_>>>24|w<<8|4278190080,g[L+2]=w>>>16|C<<16|4278190080,g[L+3]=C>>>8|4278190080}for(let _=v*4,w=U.length;_<w;_+=3)g[L++]=U[_]|U[_+1]<<8|U[_+2]<<16|4278190080}else{for(;v<y-2;v+=3,L+=4){const _=u[v],w=u[v+1],C=u[v+2];g[L]=_|255,g[L+1]=_<<24|w>>>8|255,g[L+2]=w<<16|C>>>16|255,g[L+3]=C<<8|255}for(let _=v*4,w=U.length;_<w;_+=3)g[L++]=U[_]<<24|U[_+1]<<16|U[_+2]<<8|255}return{srcPos:k,destPos:L}}function ft(U,k){if(h.FeatureTest.isLittleEndian)for(let g=0,L=U.length;g<L;g++)k[g]=U[g]*65793|4278190080;else for(let g=0,L=U.length;g<L;g++)k[g]=U[g]*16843008|255}},(At,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.GlobalWorkerOptions=void 0;const rt=Object.create(null);d.GlobalWorkerOptions=rt,rt.workerPort=null,rt.workerSrc=""},(At,d,rt)=>{var U,Pi,g,ki,O,Pe;Object.defineProperty(d,"__esModule",{value:!0}),d.MessageHandler=void 0;var h=rt(1);const M={UNKNOWN:0,DATA:1,ERROR:2},at={UNKNOWN:0,CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function X(v){switch(v instanceof Error||typeof v=="object"&&v!==null||(0,h.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),v.name){case"AbortException":return new h.AbortException(v.message);case"MissingPDFException":return new h.MissingPDFException(v.message);case"PasswordException":return new h.PasswordException(v.message,v.code);case"UnexpectedResponseException":return new h.UnexpectedResponseException(v.message,v.status);case"UnknownErrorException":return new h.UnknownErrorException(v.message,v.details);default:return new h.UnknownErrorException(v.message,v.toString())}}class ft{constructor(y,u,_){I(this,U);I(this,g);I(this,O);this.sourceName=y,this.targetName=u,this.comObj=_,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=w=>{const C=w.data;if(C.targetName!==this.sourceName)return;if(C.stream){W(this,g,ki).call(this,C);return}if(C.callback){const a=C.callbackId,l=this.callbackCapabilities[a];if(!l)throw new Error(`Cannot resolve callback ${a}`);if(delete this.callbackCapabilities[a],C.callback===M.DATA)l.resolve(C.data);else if(C.callback===M.ERROR)l.reject(X(C.reason));else throw new Error("Unexpected callback case");return}const A=this.actionHandler[C.action];if(!A)throw new Error(`Unknown action from worker: ${C.action}`);if(C.callbackId){const a=this.sourceName,l=C.sourceName;new Promise(function(P){P(A(C.data))}).then(function(P){_.postMessage({sourceName:a,targetName:l,callback:M.DATA,callbackId:C.callbackId,data:P})},function(P){_.postMessage({sourceName:a,targetName:l,callback:M.ERROR,callbackId:C.callbackId,reason:X(P)})});return}if(C.streamId){W(this,U,Pi).call(this,C);return}A(C.data)},_.addEventListener("message",this._onComObjOnMessage)}on(y,u){const _=this.actionHandler;if(_[y])throw new Error(`There is already an actionName called "${y}"`);_[y]=u}send(y,u,_){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:y,data:u},_)}sendWithPromise(y,u,_){const w=this.callbackId++,C=new h.PromiseCapability;this.callbackCapabilities[w]=C;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:y,callbackId:w,data:u},_)}catch(A){C.reject(A)}return C.promise}sendWithStream(y,u,_,w){const C=this.streamId++,A=this.sourceName,a=this.targetName,l=this.comObj;return new ReadableStream({start:P=>{const p=new h.PromiseCapability;return this.streamControllers[C]={controller:P,startCall:p,pullCall:null,cancelCall:null,isClosed:!1},l.postMessage({sourceName:A,targetName:a,action:y,streamId:C,data:u,desiredSize:P.desiredSize},w),p.promise},pull:P=>{const p=new h.PromiseCapability;return this.streamControllers[C].pullCall=p,l.postMessage({sourceName:A,targetName:a,stream:at.PULL,streamId:C,desiredSize:P.desiredSize}),p.promise},cancel:P=>{(0,h.assert)(P instanceof Error,"cancel must have a valid reason");const p=new h.PromiseCapability;return this.streamControllers[C].cancelCall=p,this.streamControllers[C].isClosed=!0,l.postMessage({sourceName:A,targetName:a,stream:at.CANCEL,streamId:C,reason:X(P)}),p.promise}},_)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}U=new WeakSet,Pi=function(y){const u=y.streamId,_=this.sourceName,w=y.sourceName,C=this.comObj,A=this,a=this.actionHandler[y.action],l={enqueue(P,p=1,r){if(this.isCancelled)return;const T=this.desiredSize;this.desiredSize-=p,T>0&&this.desiredSize<=0&&(this.sinkCapability=new h.PromiseCapability,this.ready=this.sinkCapability.promise),C.postMessage({sourceName:_,targetName:w,stream:at.ENQUEUE,streamId:u,chunk:P},r)},close(){this.isCancelled||(this.isCancelled=!0,C.postMessage({sourceName:_,targetName:w,stream:at.CLOSE,streamId:u}),delete A.streamSinks[u])},error(P){(0,h.assert)(P instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,C.postMessage({sourceName:_,targetName:w,stream:at.ERROR,streamId:u,reason:X(P)}))},sinkCapability:new h.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:y.desiredSize,ready:null};l.sinkCapability.resolve(),l.ready=l.sinkCapability.promise,this.streamSinks[u]=l,new Promise(function(P){P(a(y.data,l))}).then(function(){C.postMessage({sourceName:_,targetName:w,stream:at.START_COMPLETE,streamId:u,success:!0})},function(P){C.postMessage({sourceName:_,targetName:w,stream:at.START_COMPLETE,streamId:u,reason:X(P)})})},g=new WeakSet,ki=function(y){const u=y.streamId,_=this.sourceName,w=y.sourceName,C=this.comObj,A=this.streamControllers[u],a=this.streamSinks[u];switch(y.stream){case at.START_COMPLETE:y.success?A.startCall.resolve():A.startCall.reject(X(y.reason));break;case at.PULL_COMPLETE:y.success?A.pullCall.resolve():A.pullCall.reject(X(y.reason));break;case at.PULL:if(!a){C.postMessage({sourceName:_,targetName:w,stream:at.PULL_COMPLETE,streamId:u,success:!0});break}a.desiredSize<=0&&y.desiredSize>0&&a.sinkCapability.resolve(),a.desiredSize=y.desiredSize,new Promise(function(l){var P;l((P=a.onPull)==null?void 0:P.call(a))}).then(function(){C.postMessage({sourceName:_,targetName:w,stream:at.PULL_COMPLETE,streamId:u,success:!0})},function(l){C.postMessage({sourceName:_,targetName:w,stream:at.PULL_COMPLETE,streamId:u,reason:X(l)})});break;case at.ENQUEUE:if((0,h.assert)(A,"enqueue should have stream controller"),A.isClosed)break;A.controller.enqueue(y.chunk);break;case at.CLOSE:if((0,h.assert)(A,"close should have stream controller"),A.isClosed)break;A.isClosed=!0,A.controller.close(),W(this,O,Pe).call(this,A,u);break;case at.ERROR:(0,h.assert)(A,"error should have stream controller"),A.controller.error(X(y.reason)),W(this,O,Pe).call(this,A,u);break;case at.CANCEL_COMPLETE:y.success?A.cancelCall.resolve():A.cancelCall.reject(X(y.reason)),W(this,O,Pe).call(this,A,u);break;case at.CANCEL:if(!a)break;new Promise(function(l){var P;l((P=a.onCancel)==null?void 0:P.call(a,X(y.reason)))}).then(function(){C.postMessage({sourceName:_,targetName:w,stream:at.CANCEL_COMPLETE,streamId:u,success:!0})},function(l){C.postMessage({sourceName:_,targetName:w,stream:at.CANCEL_COMPLETE,streamId:u,reason:X(l)})}),a.sinkCapability.reject(X(y.reason)),a.isCancelled=!0,delete this.streamSinks[u];break;default:throw new Error("Unexpected stream case")}},O=new WeakSet,Pe=async function(y,u){var _,w,C;await Promise.allSettled([(_=y.startCall)==null?void 0:_.promise,(w=y.pullCall)==null?void 0:w.promise,(C=y.cancelCall)==null?void 0:C.promise]),delete this.streamControllers[u]},d.MessageHandler=ft},(At,d,rt)=>{var at,X;Object.defineProperty(d,"__esModule",{value:!0}),d.Metadata=void 0;var h=rt(1);class M{constructor({parsedData:U,rawData:k}){I(this,at,void 0);I(this,X,void 0);Z(this,at,U),Z(this,X,k)}getRaw(){return t(this,X)}get(U){return t(this,at).get(U)??null}getAll(){return(0,h.objectFromMap)(t(this,at))}has(U){return t(this,at).has(U)}}at=new WeakMap,X=new WeakMap,d.Metadata=M},(At,d,rt)=>{var U,k,g,L,O,x,ti;Object.defineProperty(d,"__esModule",{value:!0}),d.OptionalContentConfig=void 0;var h=rt(1),M=rt(8);const at=Symbol("INTERNAL");class X{constructor(u,_){I(this,U,!0);this.name=u,this.intent=_}get visible(){return t(this,U)}_setVisible(u,_){u!==at&&(0,h.unreachable)("Internal method `_setVisible` called."),Z(this,U,_)}}U=new WeakMap;class ft{constructor(u){I(this,x);I(this,k,null);I(this,g,new Map);I(this,L,null);I(this,O,null);if(this.name=null,this.creator=null,u!==null){this.name=u.name,this.creator=u.creator,Z(this,O,u.order);for(const _ of u.groups)t(this,g).set(_.id,new X(_.name,_.intent));if(u.baseState==="OFF")for(const _ of t(this,g).values())_._setVisible(at,!1);for(const _ of u.on)t(this,g).get(_)._setVisible(at,!0);for(const _ of u.off)t(this,g).get(_)._setVisible(at,!1);Z(this,L,this.getHash())}}isVisible(u){if(t(this,g).size===0)return!0;if(!u)return(0,h.warn)("Optional content group not defined."),!0;if(u.type==="OCG")return t(this,g).has(u.id)?t(this,g).get(u.id).visible:((0,h.warn)(`Optional content group not found: ${u.id}`),!0);if(u.type==="OCMD"){if(u.expression)return W(this,x,ti).call(this,u.expression);if(!u.policy||u.policy==="AnyOn"){for(const _ of u.ids){if(!t(this,g).has(_))return(0,h.warn)(`Optional content group not found: ${_}`),!0;if(t(this,g).get(_).visible)return!0}return!1}else if(u.policy==="AllOn"){for(const _ of u.ids){if(!t(this,g).has(_))return(0,h.warn)(`Optional content group not found: ${_}`),!0;if(!t(this,g).get(_).visible)return!1}return!0}else if(u.policy==="AnyOff"){for(const _ of u.ids){if(!t(this,g).has(_))return(0,h.warn)(`Optional content group not found: ${_}`),!0;if(!t(this,g).get(_).visible)return!0}return!1}else if(u.policy==="AllOff"){for(const _ of u.ids){if(!t(this,g).has(_))return(0,h.warn)(`Optional content group not found: ${_}`),!0;if(t(this,g).get(_).visible)return!1}return!0}return(0,h.warn)(`Unknown optional content policy ${u.policy}.`),!0}return(0,h.warn)(`Unknown group type ${u.type}.`),!0}setVisibility(u,_=!0){if(!t(this,g).has(u)){(0,h.warn)(`Optional content group not found: ${u}`);return}t(this,g).get(u)._setVisible(at,!!_),Z(this,k,null)}get hasInitialVisibility(){return t(this,L)===null||this.getHash()===t(this,L)}getOrder(){return t(this,g).size?t(this,O)?t(this,O).slice():[...t(this,g).keys()]:null}getGroups(){return t(this,g).size>0?(0,h.objectFromMap)(t(this,g)):null}getGroup(u){return t(this,g).get(u)||null}getHash(){if(t(this,k)!==null)return t(this,k);const u=new M.MurmurHash3_64;for(const[_,w]of t(this,g))u.update(`${_}:${w.visible}`);return Z(this,k,u.hexdigest())}}k=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakSet,ti=function(u){const _=u.length;if(_<2)return!0;const w=u[0];for(let C=1;C<_;C++){const A=u[C];let a;if(Array.isArray(A))a=W(this,x,ti).call(this,A);else if(t(this,g).has(A))a=t(this,g).get(A).visible;else return(0,h.warn)(`Optional content group not found: ${A}`),!0;switch(w){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return w==="And"},d.OptionalContentConfig=ft},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFDataTransportStream=void 0;var h=rt(1),M=rt(6);class at{constructor({length:k,initialData:g,progressiveDone:L=!1,contentDispositionFilename:O=null,disableRange:x=!1,disableStream:v=!1},y){if((0,h.assert)(y,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=L,this._contentDispositionFilename=O,(g==null?void 0:g.length)>0){const u=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;this._queuedChunks.push(u)}this._pdfDataRangeTransport=y,this._isStreamingSupported=!v,this._isRangeSupported=!x,this._contentLength=k,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((u,_)=>{this._onReceiveData({begin:u,chunk:_})}),this._pdfDataRangeTransport.addProgressListener((u,_)=>{this._onProgress({loaded:u,total:_})}),this._pdfDataRangeTransport.addProgressiveReadListener(u=>{this._onReceiveData({chunk:u})}),this._pdfDataRangeTransport.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}_onReceiveData({begin:k,chunk:g}){const L=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;if(k===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(L):this._queuedChunks.push(L);else{const O=this._rangeReaders.some(function(x){return x._begin!==k?!1:(x._enqueue(L),!0)});(0,h.assert)(O,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var k;return((k=this._fullRequestReader)==null?void 0:k._loaded)??0}_onProgress(k){var g,L,O,x;k.total===void 0?(L=(g=this._rangeReaders[0])==null?void 0:g.onProgress)==null||L.call(g,{loaded:k.loaded}):(x=(O=this._fullRequestReader)==null?void 0:O.onProgress)==null||x.call(O,{loaded:k.loaded,total:k.total})}_onProgressiveDone(){var k;(k=this._fullRequestReader)==null||k.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(k){const g=this._rangeReaders.indexOf(k);g>=0&&this._rangeReaders.splice(g,1)}getFullReader(){(0,h.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const k=this._queuedChunks;return this._queuedChunks=null,new X(this,k,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(k,g){if(g<=this._progressiveDataLength)return null;const L=new ft(this,k,g);return this._pdfDataRangeTransport.requestDataRange(k,g),this._rangeReaders.push(L),L}cancelAllRequests(k){var g;(g=this._fullRequestReader)==null||g.cancel(k);for(const L of this._rangeReaders.slice(0))L.cancel(k);this._pdfDataRangeTransport.abort()}}d.PDFDataTransportStream=at;class X{constructor(k,g,L=!1,O=null){this._stream=k,this._done=L||!1,this._filename=(0,M.isPdfFile)(O)?O:null,this._queuedChunks=g||[],this._loaded=0;for(const x of this._queuedChunks)this._loaded+=x.byteLength;this._requests=[],this._headersReady=Promise.resolve(),k._fullRequestReader=this,this.onProgress=null}_enqueue(k){this._done||(this._requests.length>0?this._requests.shift().resolve({value:k,done:!1}):this._queuedChunks.push(k),this._loaded+=k.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const k=new h.PromiseCapability;return this._requests.push(k),k.promise}cancel(k){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class ft{constructor(k,g,L){this._stream=k,this._begin=g,this._end=L,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(k){if(!this._done){if(this._requests.length===0)this._queuedChunk=k;else{this._requests.shift().resolve({value:k,done:!1});for(const L of this._requests)L.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const g=this._queuedChunk;return this._queuedChunk=null,{value:g,done:!1}}if(this._done)return{value:void 0,done:!0};const k=new h.PromiseCapability;return this._requests.push(k),k.promise}cancel(k){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFFetchStream=void 0;var h=rt(1),M=rt(20);function at(L,O,x){return{method:"GET",headers:L,signal:x.signal,mode:"cors",credentials:O?"include":"same-origin",redirect:"follow"}}function X(L){const O=new Headers;for(const x in L){const v=L[x];v!==void 0&&O.append(x,v)}return O}function ft(L){return L instanceof Uint8Array?L.buffer:L instanceof ArrayBuffer?L:((0,h.warn)(`getArrayBuffer - unexpected data format: ${L}`),new Uint8Array(L).buffer)}class U{constructor(O){this.source=O,this.isHttp=/^https?:/i.test(O.url),this.httpHeaders=this.isHttp&&O.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var O;return((O=this._fullRequestReader)==null?void 0:O._loaded)??0}getFullReader(){return(0,h.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new k(this),this._fullRequestReader}getRangeReader(O,x){if(x<=this._progressiveDataLength)return null;const v=new g(this,O,x);return this._rangeRequestReaders.push(v),v}cancelAllRequests(O){var x;(x=this._fullRequestReader)==null||x.cancel(O);for(const v of this._rangeRequestReaders.slice(0))v.cancel(O)}}d.PDFFetchStream=U;class k{constructor(O){this._stream=O,this._reader=null,this._loaded=0,this._filename=null;const x=O.source;this._withCredentials=x.withCredentials||!1,this._contentLength=x.length,this._headersCapability=new h.PromiseCapability,this._disableRange=x.disableRange||!1,this._rangeChunkSize=x.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!x.disableStream,this._isRangeSupported=!x.disableRange,this._headers=X(this._stream.httpHeaders);const v=x.url;fetch(v,at(this._headers,this._withCredentials,this._abortController)).then(y=>{if(!(0,M.validateResponseStatus)(y.status))throw(0,M.createResponseStatusError)(y.status,v);this._reader=y.body.getReader(),this._headersCapability.resolve();const u=C=>y.headers.get(C),{allowRangeRequests:_,suggestedLength:w}=(0,M.validateRangeRequestCapabilities)({getResponseHeader:u,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=_,this._contentLength=w||this._contentLength,this._filename=(0,M.extractFilenameFromHeader)(u),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new h.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var v;await this._headersCapability.promise;const{value:O,done:x}=await this._reader.read();return x?{value:O,done:x}:(this._loaded+=O.byteLength,(v=this.onProgress)==null||v.call(this,{loaded:this._loaded,total:this._contentLength}),{value:ft(O),done:!1})}cancel(O){var x;(x=this._reader)==null||x.cancel(O),this._abortController.abort()}}class g{constructor(O,x,v){this._stream=O,this._reader=null,this._loaded=0;const y=O.source;this._withCredentials=y.withCredentials||!1,this._readCapability=new h.PromiseCapability,this._isStreamingSupported=!y.disableStream,this._abortController=new AbortController,this._headers=X(this._stream.httpHeaders),this._headers.append("Range",`bytes=${x}-${v-1}`);const u=y.url;fetch(u,at(this._headers,this._withCredentials,this._abortController)).then(_=>{if(!(0,M.validateResponseStatus)(_.status))throw(0,M.createResponseStatusError)(_.status,u);this._readCapability.resolve(),this._reader=_.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var v;await this._readCapability.promise;const{value:O,done:x}=await this._reader.read();return x?{value:O,done:x}:(this._loaded+=O.byteLength,(v=this.onProgress)==null||v.call(this,{loaded:this._loaded}),{value:ft(O),done:!1})}cancel(O){var x;(x=this._reader)==null||x.cancel(O),this._abortController.abort()}}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.createResponseStatusError=U,d.extractFilenameFromHeader=ft,d.validateRangeRequestCapabilities=X,d.validateResponseStatus=k;var h=rt(1),M=rt(21),at=rt(6);function X({getResponseHeader:g,isHttp:L,rangeChunkSize:O,disableRange:x}){const v={allowRangeRequests:!1,suggestedLength:void 0},y=parseInt(g("Content-Length"),10);return!Number.isInteger(y)||(v.suggestedLength=y,y<=2*O)||x||!L||g("Accept-Ranges")!=="bytes"||(g("Content-Encoding")||"identity")!=="identity"||(v.allowRangeRequests=!0),v}function ft(g){const L=g("Content-Disposition");if(L){let O=(0,M.getFilenameFromContentDispositionHeader)(L);if(O.includes("%"))try{O=decodeURIComponent(O)}catch{}if((0,at.isPdfFile)(O))return O}return null}function U(g,L){return g===404||g===0&&L.startsWith("file:")?new h.MissingPDFException('Missing PDF "'+L+'".'):new h.UnexpectedResponseException(`Unexpected server response (${g}) while retrieving PDF "${L}".`,g)}function k(g){return g===200||g===206}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.getFilenameFromContentDispositionHeader=M;var h=rt(1);function M(at){let X=!0,ft=U("filename\\*","i").exec(at);if(ft){ft=ft[1];let y=O(ft);return y=unescape(y),y=x(y),y=v(y),g(y)}if(ft=L(at),ft){const y=v(ft);return g(y)}if(ft=U("filename","i").exec(at),ft){ft=ft[1];let y=O(ft);return y=v(y),g(y)}function U(y,u){return new RegExp("(?:^|;)\\s*"+y+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',u)}function k(y,u){if(y){if(!/^[\x00-\xFF]+$/.test(u))return u;try{const _=new TextDecoder(y,{fatal:!0}),w=(0,h.stringToBytes)(u);u=_.decode(w),X=!1}catch{}}return u}function g(y){return X&&/[\x80-\xff]/.test(y)&&(y=k("utf-8",y),X&&(y=k("iso-8859-1",y))),y}function L(y){const u=[];let _;const w=U("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(_=w.exec(y))!==null;){let[,A,a,l]=_;if(A=parseInt(A,10),A in u){if(A===0)break;continue}u[A]=[a,l]}const C=[];for(let A=0;A<u.length&&A in u;++A){let[a,l]=u[A];l=O(l),a&&(l=unescape(l),A===0&&(l=x(l))),C.push(l)}return C.join("")}function O(y){if(y.startsWith('"')){const u=y.slice(1).split('\\"');for(let _=0;_<u.length;++_){const w=u[_].indexOf('"');w!==-1&&(u[_]=u[_].slice(0,w),u.length=_+1),u[_]=u[_].replaceAll(/\\(.)/g,"$1")}y=u.join('"')}return y}function x(y){const u=y.indexOf("'");if(u===-1)return y;const _=y.slice(0,u),C=y.slice(u+1).replace(/^[^']*'/,"");return k(_,C)}function v(y){return!y.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(y)?y:y.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(u,_,w,C){if(w==="q"||w==="Q")return C=C.replaceAll("_"," "),C=C.replaceAll(/=([0-9a-fA-F]{2})/g,function(A,a){return String.fromCharCode(parseInt(a,16))}),k(_,C);try{C=atob(C)}catch{}return k(_,C)})}return""}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFNetworkStream=void 0;var h=rt(1),M=rt(20);const at=200,X=206;function ft(O){const x=O.response;return typeof x!="string"?x:(0,h.stringToBytes)(x).buffer}class U{constructor(x,v={}){this.url=x,this.isHttp=/^https?:/i.test(x),this.httpHeaders=this.isHttp&&v.httpHeaders||Object.create(null),this.withCredentials=v.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(x,v,y){const u={begin:x,end:v};for(const _ in y)u[_]=y[_];return this.request(u)}requestFull(x){return this.request(x)}request(x){const v=new XMLHttpRequest,y=this.currXhrId++,u=this.pendingRequests[y]={xhr:v};v.open("GET",this.url),v.withCredentials=this.withCredentials;for(const _ in this.httpHeaders){const w=this.httpHeaders[_];w!==void 0&&v.setRequestHeader(_,w)}return this.isHttp&&"begin"in x&&"end"in x?(v.setRequestHeader("Range",`bytes=${x.begin}-${x.end-1}`),u.expectedStatus=X):u.expectedStatus=at,v.responseType="arraybuffer",x.onError&&(v.onerror=function(_){x.onError(v.status)}),v.onreadystatechange=this.onStateChange.bind(this,y),v.onprogress=this.onProgress.bind(this,y),u.onHeadersReceived=x.onHeadersReceived,u.onDone=x.onDone,u.onError=x.onError,u.onProgress=x.onProgress,v.send(null),y}onProgress(x,v){var u;const y=this.pendingRequests[x];y&&((u=y.onProgress)==null||u.call(y,v))}onStateChange(x,v){var A,a,l;const y=this.pendingRequests[x];if(!y)return;const u=y.xhr;if(u.readyState>=2&&y.onHeadersReceived&&(y.onHeadersReceived(),delete y.onHeadersReceived),u.readyState!==4||!(x in this.pendingRequests))return;if(delete this.pendingRequests[x],u.status===0&&this.isHttp){(A=y.onError)==null||A.call(y,u.status);return}const _=u.status||at;if(!(_===at&&y.expectedStatus===X)&&_!==y.expectedStatus){(a=y.onError)==null||a.call(y,u.status);return}const C=ft(u);if(_===X){const P=u.getResponseHeader("Content-Range"),p=/bytes (\d+)-(\d+)\/(\d+)/.exec(P);y.onDone({begin:parseInt(p[1],10),chunk:C})}else C?y.onDone({begin:0,chunk:C}):(l=y.onError)==null||l.call(y,u.status)}getRequestXhr(x){return this.pendingRequests[x].xhr}isPendingRequest(x){return x in this.pendingRequests}abortRequest(x){const v=this.pendingRequests[x].xhr;delete this.pendingRequests[x],v.abort()}}class k{constructor(x){this._source=x,this._manager=new U(x.url,{httpHeaders:x.httpHeaders,withCredentials:x.withCredentials}),this._rangeChunkSize=x.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(x){const v=this._rangeRequestReaders.indexOf(x);v>=0&&this._rangeRequestReaders.splice(v,1)}getFullReader(){return(0,h.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new g(this._manager,this._source),this._fullRequestReader}getRangeReader(x,v){const y=new L(this._manager,x,v);return y.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(y),y}cancelAllRequests(x){var v;(v=this._fullRequestReader)==null||v.cancel(x);for(const y of this._rangeRequestReaders.slice(0))y.cancel(x)}}d.PDFNetworkStream=k;class g{constructor(x,v){this._manager=x;const y={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=v.url,this._fullRequestId=x.requestFull(y),this._headersReceivedCapability=new h.PromiseCapability,this._disableRange=v.disableRange||!1,this._contentLength=v.length,this._rangeChunkSize=v.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const x=this._fullRequestId,v=this._manager.getRequestXhr(x),y=w=>v.getResponseHeader(w),{allowRangeRequests:u,suggestedLength:_}=(0,M.validateRangeRequestCapabilities)({getResponseHeader:y,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});u&&(this._isRangeSupported=!0),this._contentLength=_||this._contentLength,this._filename=(0,M.extractFilenameFromHeader)(y),this._isRangeSupported&&this._manager.abortRequest(x),this._headersReceivedCapability.resolve()}_onDone(x){if(x&&(this._requests.length>0?this._requests.shift().resolve({value:x.chunk,done:!1}):this._cachedChunks.push(x.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const v of this._requests)v.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(x){this._storedError=(0,M.createResponseStatusError)(x,this._url),this._headersReceivedCapability.reject(this._storedError);for(const v of this._requests)v.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(x){var v;(v=this.onProgress)==null||v.call(this,{loaded:x.loaded,total:x.lengthComputable?x.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const x=new h.PromiseCapability;return this._requests.push(x),x.promise}cancel(x){this._done=!0,this._headersReceivedCapability.reject(x);for(const v of this._requests)v.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class L{constructor(x,v,y){this._manager=x;const u={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=x.url,this._requestId=x.requestRange(v,y,u),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var x;(x=this.onClosed)==null||x.call(this,this)}_onDone(x){const v=x.chunk;this._requests.length>0?this._requests.shift().resolve({value:v,done:!1}):this._queuedChunk=v,this._done=!0;for(const y of this._requests)y.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(x){this._storedError=(0,M.createResponseStatusError)(x,this._url);for(const v of this._requests)v.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(x){var v;this.isStreamingSupported||(v=this.onProgress)==null||v.call(this,{loaded:x.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const v=this._queuedChunk;return this._queuedChunk=null,{value:v,done:!1}}if(this._done)return{value:void 0,done:!0};const x=new h.PromiseCapability;return this._requests.push(x),x.promise}cancel(x){this._done=!0;for(const v of this._requests)v.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFNodeStream=void 0;var h=rt(1),M=rt(20);const at=/^file:\/\/\/[a-zA-Z]:\//;function X(y){const u=require$$5,_=u.parse(y);return _.protocol==="file:"||_.host?_:/^[a-z]:[/\\]/i.test(y)?u.parse(`file:///${y}`):(_.host||(_.protocol="file:"),_)}class ft{constructor(u){this.source=u,this.url=X(u.url),this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:",this.isFsUrl=this.url.protocol==="file:",this.httpHeaders=this.isHttp&&u.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var u;return((u=this._fullRequestReader)==null?void 0:u._loaded)??0}getFullReader(){return(0,h.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new x(this):new L(this),this._fullRequestReader}getRangeReader(u,_){if(_<=this._progressiveDataLength)return null;const w=this.isFsUrl?new v(this,u,_):new O(this,u,_);return this._rangeRequestReaders.push(w),w}cancelAllRequests(u){var _;(_=this._fullRequestReader)==null||_.cancel(u);for(const w of this._rangeRequestReaders.slice(0))w.cancel(u)}}d.PDFNodeStream=ft;class U{constructor(u){this._url=u.url,this._done=!1,this._storedError=null,this.onProgress=null;const _=u.source;this._contentLength=_.length,this._loaded=0,this._filename=null,this._disableRange=_.disableRange||!1,this._rangeChunkSize=_.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!_.disableStream,this._isRangeSupported=!_.disableRange,this._readableStream=null,this._readCapability=new h.PromiseCapability,this._headersCapability=new h.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var w;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const u=this._readableStream.read();return u===null?(this._readCapability=new h.PromiseCapability,this.read()):(this._loaded+=u.length,(w=this.onProgress)==null||w.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(u).buffer,done:!1})}cancel(u){if(!this._readableStream){this._error(u);return}this._readableStream.destroy(u)}_error(u){this._storedError=u,this._readCapability.resolve()}_setReadableStream(u){this._readableStream=u,u.on("readable",()=>{this._readCapability.resolve()}),u.on("end",()=>{u.destroy(),this._done=!0,this._readCapability.resolve()}),u.on("error",_=>{this._error(_)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new h.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class k{constructor(u){this._url=u.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new h.PromiseCapability;const _=u.source;this._isStreamingSupported=!_.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var w;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const u=this._readableStream.read();return u===null?(this._readCapability=new h.PromiseCapability,this.read()):(this._loaded+=u.length,(w=this.onProgress)==null||w.call(this,{loaded:this._loaded}),{value:new Uint8Array(u).buffer,done:!1})}cancel(u){if(!this._readableStream){this._error(u);return}this._readableStream.destroy(u)}_error(u){this._storedError=u,this._readCapability.resolve()}_setReadableStream(u){this._readableStream=u,u.on("readable",()=>{this._readCapability.resolve()}),u.on("end",()=>{u.destroy(),this._done=!0,this._readCapability.resolve()}),u.on("error",_=>{this._error(_)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function g(y,u){return{protocol:y.protocol,auth:y.auth,host:y.hostname,port:y.port,path:y.path,method:"GET",headers:u}}class L extends U{constructor(u){super(u);const _=w=>{if(w.statusCode===404){const l=new h.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=l,this._headersCapability.reject(l);return}this._headersCapability.resolve(),this._setReadableStream(w);const C=l=>this._readableStream.headers[l.toLowerCase()],{allowRangeRequests:A,suggestedLength:a}=(0,M.validateRangeRequestCapabilities)({getResponseHeader:C,isHttp:u.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=A,this._contentLength=a||this._contentLength,this._filename=(0,M.extractFilenameFromHeader)(C)};if(this._request=null,this._url.protocol==="http:"){const w=require$$5;this._request=w.request(g(this._url,u.httpHeaders),_)}else{const w=require$$5;this._request=w.request(g(this._url,u.httpHeaders),_)}this._request.on("error",w=>{this._storedError=w,this._headersCapability.reject(w)}),this._request.end()}}class O extends k{constructor(u,_,w){super(u),this._httpHeaders={};for(const A in u.httpHeaders){const a=u.httpHeaders[A];a!==void 0&&(this._httpHeaders[A]=a)}this._httpHeaders.Range=`bytes=${_}-${w-1}`;const C=A=>{if(A.statusCode===404){const a=new h.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=a;return}this._setReadableStream(A)};if(this._request=null,this._url.protocol==="http:"){const A=require$$5;this._request=A.request(g(this._url,this._httpHeaders),C)}else{const A=require$$5;this._request=A.request(g(this._url,this._httpHeaders),C)}this._request.on("error",A=>{this._storedError=A}),this._request.end()}}class x extends U{constructor(u){super(u);let _=decodeURIComponent(this._url.path);at.test(this._url.href)&&(_=_.replace(/^\//,""));const w=require$$5;w.lstat(_,(C,A)=>{if(C){C.code==="ENOENT"&&(C=new h.MissingPDFException(`Missing PDF "${_}".`)),this._storedError=C,this._headersCapability.reject(C);return}this._contentLength=A.size,this._setReadableStream(w.createReadStream(_)),this._headersCapability.resolve()})}}class v extends k{constructor(u,_,w){super(u);let C=decodeURIComponent(this._url.path);at.test(this._url.href)&&(C=C.replace(/^\//,""));const A=require$$5;this._setReadableStream(A.createReadStream(C,{start:_,end:w-1}))}}},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.SVGGraphics=void 0;var h=rt(6),M=rt(1);const at={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},X="http://www.w3.org/XML/1998/namespace",ft="http://www.w3.org/1999/xlink",U=["butt","round","square"],k=["miter","round","bevel"],g=function(A,a="",l=!1){if(URL.createObjectURL&&typeof Blob<"u"&&!l)return URL.createObjectURL(new Blob([A],{type:a}));const P="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let p=`data:${a};base64,`;for(let r=0,T=A.length;r<T;r+=3){const m=A[r]&255,B=A[r+1]&255,z=A[r+2]&255,E=m>>2,V=(m&3)<<4|B>>4,it=r+1<T?(B&15)<<2|z>>6:64,nt=r+2<T?z&63:64;p+=P[E]+P[V]+P[it]+P[nt]}return p},L=function(){const A=new Uint8Array([137,80,78,71,13,10,26,10]),a=12,l=new Int32Array(256);for(let z=0;z<256;z++){let E=z;for(let V=0;V<8;V++)E=E&1?3988292384^E>>1&2147483647:E>>1&2147483647;l[z]=E}function P(z,E,V){let it=-1;for(let nt=E;nt<V;nt++){const H=(it^z[nt])&255,lt=l[H];it=it>>>8^lt}return it^-1}function p(z,E,V,it){let nt=it;const H=E.length;V[nt]=H>>24&255,V[nt+1]=H>>16&255,V[nt+2]=H>>8&255,V[nt+3]=H&255,nt+=4,V[nt]=z.charCodeAt(0)&255,V[nt+1]=z.charCodeAt(1)&255,V[nt+2]=z.charCodeAt(2)&255,V[nt+3]=z.charCodeAt(3)&255,nt+=4,V.set(E,nt),nt+=E.length;const lt=P(V,it+4,nt);V[nt]=lt>>24&255,V[nt+1]=lt>>16&255,V[nt+2]=lt>>8&255,V[nt+3]=lt&255}function r(z,E,V){let it=1,nt=0;for(let H=E;H<V;++H)it=(it+(z[H]&255))%65521,nt=(nt+it)%65521;return nt<<16|it}function T(z){if(!M.isNodeJS)return m(z);try{const E=parseInt(process.versions.node)>=8?z:Buffer.from(z),V=require$$5.deflateSync(E,{level:9});return V instanceof Uint8Array?V:new Uint8Array(V)}catch(E){(0,M.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+E)}return m(z)}function m(z){let E=z.length;const V=65535,it=Math.ceil(E/V),nt=new Uint8Array(2+E+it*5+4);let H=0;nt[H++]=120,nt[H++]=156;let lt=0;for(;E>V;)nt[H++]=0,nt[H++]=255,nt[H++]=255,nt[H++]=0,nt[H++]=0,nt.set(z.subarray(lt,lt+V),H),H+=V,lt+=V,E-=V;nt[H++]=1,nt[H++]=E&255,nt[H++]=E>>8&255,nt[H++]=~E&65535&255,nt[H++]=(~E&65535)>>8&255,nt.set(z.subarray(lt),H),H+=z.length-lt;const pt=r(z,0,z.length);return nt[H++]=pt>>24&255,nt[H++]=pt>>16&255,nt[H++]=pt>>8&255,nt[H++]=pt&255,nt}function B(z,E,V,it){const nt=z.width,H=z.height;let lt,pt,wt;const Pt=z.data;switch(E){case M.ImageKind.GRAYSCALE_1BPP:pt=0,lt=1,wt=nt+7>>3;break;case M.ImageKind.RGB_24BPP:pt=2,lt=8,wt=nt*3;break;case M.ImageKind.RGBA_32BPP:pt=6,lt=8,wt=nt*4;break;default:throw new Error("invalid format")}const S=new Uint8Array((1+wt)*H);let i=0,n=0;for(let N=0;N<H;++N)S[i++]=0,S.set(Pt.subarray(n,n+wt),i),n+=wt,i+=wt;if(E===M.ImageKind.GRAYSCALE_1BPP&&it){i=0;for(let N=0;N<H;N++){i++;for(let tt=0;tt<wt;tt++)S[i++]^=255}}const s=new Uint8Array([nt>>24&255,nt>>16&255,nt>>8&255,nt&255,H>>24&255,H>>16&255,H>>8&255,H&255,lt,pt,0,0,0]),o=T(S),c=A.length+a*3+s.length+o.length,b=new Uint8Array(c);let F=0;return b.set(A,F),F+=A.length,p("IHDR",s,b,F),F+=a+s.length,p("IDATA",o,b,F),F+=a+o.length,p("IEND",new Uint8Array(0),b,F),g(b,"image/png",V)}return function(E,V,it){const nt=E.kind===void 0?M.ImageKind.GRAYSCALE_1BPP:E.kind;return B(E,nt,V,it)}}();class O{constructor(){this.fontSizeScale=1,this.fontWeight=at.fontWeight,this.fontSize=0,this.textMatrix=M.IDENTITY_MATRIX,this.fontMatrix=M.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=M.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=at.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(a,l){this.x=a,this.y=l}}function x(A){let a=[];const l=[];for(const P of A){if(P.fn==="save"){a.push({fnId:92,fn:"group",items:[]}),l.push(a),a=a.at(-1).items;continue}P.fn==="restore"?a=l.pop():a.push(P)}return a}function v(A){if(Number.isInteger(A))return A.toString();const a=A.toFixed(10);let l=a.length-1;if(a[l]!=="0")return a;do l--;while(a[l]==="0");return a.substring(0,a[l]==="."?l:l+1)}function y(A){if(A[4]===0&&A[5]===0){if(A[1]===0&&A[2]===0)return A[0]===1&&A[3]===1?"":`scale(${v(A[0])} ${v(A[3])})`;if(A[0]===A[3]&&A[1]===-A[2]){const a=Math.acos(A[0])*180/Math.PI;return`rotate(${v(a)})`}}else if(A[0]===1&&A[1]===0&&A[2]===0&&A[3]===1)return`translate(${v(A[4])} ${v(A[5])})`;return`matrix(${v(A[0])} ${v(A[1])} ${v(A[2])} ${v(A[3])} ${v(A[4])} ${v(A[5])})`}let u=0,_=0,w=0;class C{constructor(a,l,P=!1){(0,h.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new h.DOMSVGFactory,this.current=new O,this.transformMatrix=M.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=a,this.objs=l,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!P,this._operatorIdMapping=[];for(const p in M.OPS)this._operatorIdMapping[M.OPS[p]]=p}getObject(a,l=null){return typeof a=="string"?a.startsWith("g_")?this.commonObjs.get(a):this.objs.get(a):l}save(){this.transformStack.push(this.transformMatrix);const a=this.current;this.extraStack.push(a),this.current=a.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(a){this.save(),this.executeOpTree(a),this.restore()}loadDependencies(a){const l=a.fnArray,P=a.argsArray;for(let p=0,r=l.length;p<r;p++)if(l[p]===M.OPS.dependency)for(const T of P[p]){const m=T.startsWith("g_")?this.commonObjs:this.objs,B=new Promise(z=>{m.get(T,z)});this.current.dependencies.push(B)}return Promise.all(this.current.dependencies)}transform(a,l,P,p,r,T){const m=[a,l,P,p,r,T];this.transformMatrix=M.Util.transform(this.transformMatrix,m),this.tgrp=null}getSVG(a,l){this.viewport=l;const P=this._initialize(l);return this.loadDependencies(a).then(()=>(this.transformMatrix=M.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(a)),P))}convertOpList(a){const l=this._operatorIdMapping,P=a.argsArray,p=a.fnArray,r=[];for(let T=0,m=p.length;T<m;T++){const B=p[T];r.push({fnId:B,fn:l[B],args:P[T]})}return x(r)}executeOpTree(a){for(const l of a){const P=l.fn,p=l.fnId,r=l.args;switch(p|0){case M.OPS.beginText:this.beginText();break;case M.OPS.dependency:break;case M.OPS.setLeading:this.setLeading(r);break;case M.OPS.setLeadingMoveText:this.setLeadingMoveText(r[0],r[1]);break;case M.OPS.setFont:this.setFont(r);break;case M.OPS.showText:this.showText(r[0]);break;case M.OPS.showSpacedText:this.showText(r[0]);break;case M.OPS.endText:this.endText();break;case M.OPS.moveText:this.moveText(r[0],r[1]);break;case M.OPS.setCharSpacing:this.setCharSpacing(r[0]);break;case M.OPS.setWordSpacing:this.setWordSpacing(r[0]);break;case M.OPS.setHScale:this.setHScale(r[0]);break;case M.OPS.setTextMatrix:this.setTextMatrix(r[0],r[1],r[2],r[3],r[4],r[5]);break;case M.OPS.setTextRise:this.setTextRise(r[0]);break;case M.OPS.setTextRenderingMode:this.setTextRenderingMode(r[0]);break;case M.OPS.setLineWidth:this.setLineWidth(r[0]);break;case M.OPS.setLineJoin:this.setLineJoin(r[0]);break;case M.OPS.setLineCap:this.setLineCap(r[0]);break;case M.OPS.setMiterLimit:this.setMiterLimit(r[0]);break;case M.OPS.setFillRGBColor:this.setFillRGBColor(r[0],r[1],r[2]);break;case M.OPS.setStrokeRGBColor:this.setStrokeRGBColor(r[0],r[1],r[2]);break;case M.OPS.setStrokeColorN:this.setStrokeColorN(r);break;case M.OPS.setFillColorN:this.setFillColorN(r);break;case M.OPS.shadingFill:this.shadingFill(r[0]);break;case M.OPS.setDash:this.setDash(r[0],r[1]);break;case M.OPS.setRenderingIntent:this.setRenderingIntent(r[0]);break;case M.OPS.setFlatness:this.setFlatness(r[0]);break;case M.OPS.setGState:this.setGState(r[0]);break;case M.OPS.fill:this.fill();break;case M.OPS.eoFill:this.eoFill();break;case M.OPS.stroke:this.stroke();break;case M.OPS.fillStroke:this.fillStroke();break;case M.OPS.eoFillStroke:this.eoFillStroke();break;case M.OPS.clip:this.clip("nonzero");break;case M.OPS.eoClip:this.clip("evenodd");break;case M.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case M.OPS.paintImageXObject:this.paintImageXObject(r[0]);break;case M.OPS.paintInlineImageXObject:this.paintInlineImageXObject(r[0]);break;case M.OPS.paintImageMaskXObject:this.paintImageMaskXObject(r[0]);break;case M.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(r[0],r[1]);break;case M.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case M.OPS.closePath:this.closePath();break;case M.OPS.closeStroke:this.closeStroke();break;case M.OPS.closeFillStroke:this.closeFillStroke();break;case M.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case M.OPS.nextLine:this.nextLine();break;case M.OPS.transform:this.transform(r[0],r[1],r[2],r[3],r[4],r[5]);break;case M.OPS.constructPath:this.constructPath(r[0],r[1]);break;case M.OPS.endPath:this.endPath();break;case 92:this.group(l.items);break;default:(0,M.warn)(`Unimplemented operator ${P}`);break}}}setWordSpacing(a){this.current.wordSpacing=a}setCharSpacing(a){this.current.charSpacing=a}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(a,l,P,p,r,T){const m=this.current;m.textMatrix=m.lineMatrix=[a,l,P,p,r,T],m.textMatrixScale=Math.hypot(a,l),m.x=m.lineX=0,m.y=m.lineY=0,m.xcoords=[],m.ycoords=[],m.tspan=this.svgFactory.createElement("svg:tspan"),m.tspan.setAttributeNS(null,"font-family",m.fontFamily),m.tspan.setAttributeNS(null,"font-size",`${v(m.fontSize)}px`),m.tspan.setAttributeNS(null,"y",v(-m.y)),m.txtElement=this.svgFactory.createElement("svg:text"),m.txtElement.append(m.tspan)}beginText(){const a=this.current;a.x=a.lineX=0,a.y=a.lineY=0,a.textMatrix=M.IDENTITY_MATRIX,a.lineMatrix=M.IDENTITY_MATRIX,a.textMatrixScale=1,a.tspan=this.svgFactory.createElement("svg:tspan"),a.txtElement=this.svgFactory.createElement("svg:text"),a.txtgrp=this.svgFactory.createElement("svg:g"),a.xcoords=[],a.ycoords=[]}moveText(a,l){const P=this.current;P.x=P.lineX+=a,P.y=P.lineY+=l,P.xcoords=[],P.ycoords=[],P.tspan=this.svgFactory.createElement("svg:tspan"),P.tspan.setAttributeNS(null,"font-family",P.fontFamily),P.tspan.setAttributeNS(null,"font-size",`${v(P.fontSize)}px`),P.tspan.setAttributeNS(null,"y",v(-P.y))}showText(a){const l=this.current,P=l.font,p=l.fontSize;if(p===0)return;const r=l.fontSizeScale,T=l.charSpacing,m=l.wordSpacing,B=l.fontDirection,z=l.textHScale*B,E=P.vertical,V=E?1:-1,it=P.defaultVMetrics,nt=p*l.fontMatrix[0];let H=0;for(const wt of a){if(wt===null){H+=B*m;continue}else if(typeof wt=="number"){H+=V*wt*p/1e3;continue}const Pt=(wt.isSpace?m:0)+T,S=wt.fontChar;let i,n,s=wt.width;if(E){let c;const b=wt.vmetric||it;c=wt.vmetric?b[1]:s*.5,c=-c*nt;const F=b[2]*nt;s=b?-b[0]:s,i=c/r,n=(H+F)/r}else i=H/r,n=0;(wt.isInFont||P.missingFile)&&(l.xcoords.push(l.x+i),E&&l.ycoords.push(-l.y+n),l.tspan.textContent+=S);const o=E?s*nt-Pt*B:s*nt+Pt*B;H+=o}l.tspan.setAttributeNS(null,"x",l.xcoords.map(v).join(" ")),E?l.tspan.setAttributeNS(null,"y",l.ycoords.map(v).join(" ")):l.tspan.setAttributeNS(null,"y",v(-l.y)),E?l.y-=H:l.x+=H*z,l.tspan.setAttributeNS(null,"font-family",l.fontFamily),l.tspan.setAttributeNS(null,"font-size",`${v(l.fontSize)}px`),l.fontStyle!==at.fontStyle&&l.tspan.setAttributeNS(null,"font-style",l.fontStyle),l.fontWeight!==at.fontWeight&&l.tspan.setAttributeNS(null,"font-weight",l.fontWeight);const lt=l.textRenderingMode&M.TextRenderingMode.FILL_STROKE_MASK;if(lt===M.TextRenderingMode.FILL||lt===M.TextRenderingMode.FILL_STROKE?(l.fillColor!==at.fillColor&&l.tspan.setAttributeNS(null,"fill",l.fillColor),l.fillAlpha<1&&l.tspan.setAttributeNS(null,"fill-opacity",l.fillAlpha)):l.textRenderingMode===M.TextRenderingMode.ADD_TO_PATH?l.tspan.setAttributeNS(null,"fill","transparent"):l.tspan.setAttributeNS(null,"fill","none"),lt===M.TextRenderingMode.STROKE||lt===M.TextRenderingMode.FILL_STROKE){const wt=1/(l.textMatrixScale||1);this._setStrokeAttributes(l.tspan,wt)}let pt=l.textMatrix;l.textRise!==0&&(pt=pt.slice(),pt[5]+=l.textRise),l.txtElement.setAttributeNS(null,"transform",`${y(pt)} scale(${v(z)}, -1)`),l.txtElement.setAttributeNS(X,"xml:space","preserve"),l.txtElement.append(l.tspan),l.txtgrp.append(l.txtElement),this._ensureTransformGroup().append(l.txtElement)}setLeadingMoveText(a,l){this.setLeading(-l),this.moveText(a,l)}addFontStyle(a){if(!a.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const l=g(a.data,a.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${a.loadedName}"; src: url(${l}); }
`}setFont(a){const l=this.current,P=this.commonObjs.get(a[0]);let p=a[1];l.font=P,this.embedFonts&&!P.missingFile&&!this.embeddedFonts[P.loadedName]&&(this.addFontStyle(P),this.embeddedFonts[P.loadedName]=P),l.fontMatrix=P.fontMatrix||M.FONT_IDENTITY_MATRIX;let r="normal";P.black?r="900":P.bold&&(r="bold");const T=P.italic?"italic":"normal";p<0?(p=-p,l.fontDirection=-1):l.fontDirection=1,l.fontSize=p,l.fontFamily=P.loadedName,l.fontWeight=r,l.fontStyle=T,l.tspan=this.svgFactory.createElement("svg:tspan"),l.tspan.setAttributeNS(null,"y",v(-l.y)),l.xcoords=[],l.ycoords=[]}endText(){var l;const a=this.current;a.textRenderingMode&M.TextRenderingMode.ADD_TO_PATH_FLAG&&((l=a.txtElement)!=null&&l.hasChildNodes())&&(a.element=a.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(a){a>0&&(this.current.lineWidth=a)}setLineCap(a){this.current.lineCap=U[a]}setLineJoin(a){this.current.lineJoin=k[a]}setMiterLimit(a){this.current.miterLimit=a}setStrokeAlpha(a){this.current.strokeAlpha=a}setStrokeRGBColor(a,l,P){this.current.strokeColor=M.Util.makeHexColor(a,l,P)}setFillAlpha(a){this.current.fillAlpha=a}setFillRGBColor(a,l,P){this.current.fillColor=M.Util.makeHexColor(a,l,P),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(a){this.current.strokeColor=this._makeColorN_Pattern(a)}setFillColorN(a){this.current.fillColor=this._makeColorN_Pattern(a)}shadingFill(a){const{width:l,height:P}=this.viewport,p=M.Util.inverseTransform(this.transformMatrix),[r,T,m,B]=M.Util.getAxialAlignedBoundingBox([0,0,l,P],p),z=this.svgFactory.createElement("svg:rect");z.setAttributeNS(null,"x",r),z.setAttributeNS(null,"y",T),z.setAttributeNS(null,"width",m-r),z.setAttributeNS(null,"height",B-T),z.setAttributeNS(null,"fill",this._makeShadingPattern(a)),this.current.fillAlpha<1&&z.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(z)}_makeColorN_Pattern(a){return a[0]==="TilingPattern"?this._makeTilingPattern(a):this._makeShadingPattern(a)}_makeTilingPattern(a){const l=a[1],P=a[2],p=a[3]||M.IDENTITY_MATRIX,[r,T,m,B]=a[4],z=a[5],E=a[6],V=a[7],it=`shading${w++}`,[nt,H,lt,pt]=M.Util.normalizeRect([...M.Util.applyTransform([r,T],p),...M.Util.applyTransform([m,B],p)]),[wt,Pt]=M.Util.singularValueDecompose2dScale(p),S=z*wt,i=E*Pt,n=this.svgFactory.createElement("svg:pattern");n.setAttributeNS(null,"id",it),n.setAttributeNS(null,"patternUnits","userSpaceOnUse"),n.setAttributeNS(null,"width",S),n.setAttributeNS(null,"height",i),n.setAttributeNS(null,"x",`${nt}`),n.setAttributeNS(null,"y",`${H}`);const s=this.svg,o=this.transformMatrix,c=this.current.fillColor,b=this.current.strokeColor,F=this.svgFactory.create(lt-nt,pt-H);if(this.svg=F,this.transformMatrix=p,V===2){const N=M.Util.makeHexColor(...l);this.current.fillColor=N,this.current.strokeColor=N}return this.executeOpTree(this.convertOpList(P)),this.svg=s,this.transformMatrix=o,this.current.fillColor=c,this.current.strokeColor=b,n.append(F.childNodes[0]),this.defs.append(n),`url(#${it})`}_makeShadingPattern(a){switch(typeof a=="string"&&(a=this.objs.get(a)),a[0]){case"RadialAxial":const l=`shading${w++}`,P=a[3];let p;switch(a[1]){case"axial":const r=a[4],T=a[5];p=this.svgFactory.createElement("svg:linearGradient"),p.setAttributeNS(null,"id",l),p.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),p.setAttributeNS(null,"x1",r[0]),p.setAttributeNS(null,"y1",r[1]),p.setAttributeNS(null,"x2",T[0]),p.setAttributeNS(null,"y2",T[1]);break;case"radial":const m=a[4],B=a[5],z=a[6],E=a[7];p=this.svgFactory.createElement("svg:radialGradient"),p.setAttributeNS(null,"id",l),p.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),p.setAttributeNS(null,"cx",B[0]),p.setAttributeNS(null,"cy",B[1]),p.setAttributeNS(null,"r",E),p.setAttributeNS(null,"fx",m[0]),p.setAttributeNS(null,"fy",m[1]),p.setAttributeNS(null,"fr",z);break;default:throw new Error(`Unknown RadialAxial type: ${a[1]}`)}for(const r of P){const T=this.svgFactory.createElement("svg:stop");T.setAttributeNS(null,"offset",r[0]),T.setAttributeNS(null,"stop-color",r[1]),p.append(T)}return this.defs.append(p),`url(#${l})`;case"Mesh":return(0,M.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${a[0]}`)}}setDash(a,l){this.current.dashArray=a,this.current.dashPhase=l}constructPath(a,l){const P=this.current;let p=P.x,r=P.y,T=[],m=0;for(const B of a)switch(B|0){case M.OPS.rectangle:p=l[m++],r=l[m++];const z=l[m++],E=l[m++],V=p+z,it=r+E;T.push("M",v(p),v(r),"L",v(V),v(r),"L",v(V),v(it),"L",v(p),v(it),"Z");break;case M.OPS.moveTo:p=l[m++],r=l[m++],T.push("M",v(p),v(r));break;case M.OPS.lineTo:p=l[m++],r=l[m++],T.push("L",v(p),v(r));break;case M.OPS.curveTo:p=l[m+4],r=l[m+5],T.push("C",v(l[m]),v(l[m+1]),v(l[m+2]),v(l[m+3]),v(p),v(r)),m+=6;break;case M.OPS.curveTo2:T.push("C",v(p),v(r),v(l[m]),v(l[m+1]),v(l[m+2]),v(l[m+3])),p=l[m+2],r=l[m+3],m+=4;break;case M.OPS.curveTo3:p=l[m+2],r=l[m+3],T.push("C",v(l[m]),v(l[m+1]),v(p),v(r),v(p),v(r)),m+=4;break;case M.OPS.closePath:T.push("Z");break}T=T.join(" "),P.path&&a.length>0&&a[0]!==M.OPS.rectangle&&a[0]!==M.OPS.moveTo?T=P.path.getAttributeNS(null,"d")+T:(P.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(P.path)),P.path.setAttributeNS(null,"d",T),P.path.setAttributeNS(null,"fill","none"),P.element=P.path,P.setCurrentPoint(p,r)}endPath(){const a=this.current;if(a.path=null,!this.pendingClip)return;if(!a.element){this.pendingClip=null;return}const l=`clippath${u++}`,P=this.svgFactory.createElement("svg:clipPath");P.setAttributeNS(null,"id",l),P.setAttributeNS(null,"transform",y(this.transformMatrix));const p=a.element.cloneNode(!0);if(this.pendingClip==="evenodd"?p.setAttributeNS(null,"clip-rule","evenodd"):p.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,P.append(p),this.defs.append(P),a.activeClipUrl){a.clipGroup=null;for(const r of this.extraStack)r.clipGroup=null;P.setAttributeNS(null,"clip-path",a.activeClipUrl)}a.activeClipUrl=`url(#${l})`,this.tgrp=null}clip(a){this.pendingClip=a}closePath(){const a=this.current;if(a.path){const l=`${a.path.getAttributeNS(null,"d")}Z`;a.path.setAttributeNS(null,"d",l)}}setLeading(a){this.current.leading=-a}setTextRise(a){this.current.textRise=a}setTextRenderingMode(a){this.current.textRenderingMode=a}setHScale(a){this.current.textHScale=a/100}setRenderingIntent(a){}setFlatness(a){}setGState(a){for(const[l,P]of a)switch(l){case"LW":this.setLineWidth(P);break;case"LC":this.setLineCap(P);break;case"LJ":this.setLineJoin(P);break;case"ML":this.setMiterLimit(P);break;case"D":this.setDash(P[0],P[1]);break;case"RI":this.setRenderingIntent(P);break;case"FL":this.setFlatness(P);break;case"Font":this.setFont(P);break;case"CA":this.setStrokeAlpha(P);break;case"ca":this.setFillAlpha(P);break;default:(0,M.warn)(`Unimplemented graphic state operator ${l}`);break}}fill(){const a=this.current;a.element&&(a.element.setAttributeNS(null,"fill",a.fillColor),a.element.setAttributeNS(null,"fill-opacity",a.fillAlpha),this.endPath())}stroke(){const a=this.current;a.element&&(this._setStrokeAttributes(a.element),a.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(a,l=1){const P=this.current;let p=P.dashArray;l!==1&&p.length>0&&(p=p.map(function(r){return l*r})),a.setAttributeNS(null,"stroke",P.strokeColor),a.setAttributeNS(null,"stroke-opacity",P.strokeAlpha),a.setAttributeNS(null,"stroke-miterlimit",v(P.miterLimit)),a.setAttributeNS(null,"stroke-linecap",P.lineCap),a.setAttributeNS(null,"stroke-linejoin",P.lineJoin),a.setAttributeNS(null,"stroke-width",v(l*P.lineWidth)+"px"),a.setAttributeNS(null,"stroke-dasharray",p.map(v).join(" ")),a.setAttributeNS(null,"stroke-dashoffset",v(l*P.dashPhase)+"px")}eoFill(){var a;(a=this.current.element)==null||a.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){var a;(a=this.current.element)==null||a.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y","0"),a.setAttributeNS(null,"width","1px"),a.setAttributeNS(null,"height","1px"),a.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(a)}paintImageXObject(a){const l=this.getObject(a);if(!l){(0,M.warn)(`Dependent image with object ID ${a} is not ready yet`);return}this.paintInlineImageXObject(l)}paintInlineImageXObject(a,l){const P=a.width,p=a.height,r=L(a,this.forceDataSchema,!!l),T=this.svgFactory.createElement("svg:rect");T.setAttributeNS(null,"x","0"),T.setAttributeNS(null,"y","0"),T.setAttributeNS(null,"width",v(P)),T.setAttributeNS(null,"height",v(p)),this.current.element=T,this.clip("nonzero");const m=this.svgFactory.createElement("svg:image");m.setAttributeNS(ft,"xlink:href",r),m.setAttributeNS(null,"x","0"),m.setAttributeNS(null,"y",v(-p)),m.setAttributeNS(null,"width",v(P)+"px"),m.setAttributeNS(null,"height",v(p)+"px"),m.setAttributeNS(null,"transform",`scale(${v(1/P)} ${v(-1/p)})`),l?l.append(m):this._ensureTransformGroup().append(m)}paintImageMaskXObject(a){const l=this.getObject(a.data,a);if(l.bitmap){(0,M.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const P=this.current,p=l.width,r=l.height,T=P.fillColor;P.maskId=`mask${_++}`;const m=this.svgFactory.createElement("svg:mask");m.setAttributeNS(null,"id",P.maskId);const B=this.svgFactory.createElement("svg:rect");B.setAttributeNS(null,"x","0"),B.setAttributeNS(null,"y","0"),B.setAttributeNS(null,"width",v(p)),B.setAttributeNS(null,"height",v(r)),B.setAttributeNS(null,"fill",T),B.setAttributeNS(null,"mask",`url(#${P.maskId})`),this.defs.append(m),this._ensureTransformGroup().append(B),this.paintInlineImageXObject(l,m)}paintFormXObjectBegin(a,l){if(Array.isArray(a)&&a.length===6&&this.transform(a[0],a[1],a[2],a[3],a[4],a[5]),l){const P=l[2]-l[0],p=l[3]-l[1],r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x",l[0]),r.setAttributeNS(null,"y",l[1]),r.setAttributeNS(null,"width",v(P)),r.setAttributeNS(null,"height",v(p)),this.current.element=r,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(a){const l=this.svgFactory.create(a.width,a.height),P=this.svgFactory.createElement("svg:defs");l.append(P),this.defs=P;const p=this.svgFactory.createElement("svg:g");return p.setAttributeNS(null,"transform",y(a.transform)),l.append(p),this.svg=p,l}_ensureClipGroup(){if(!this.current.clipGroup){const a=this.svgFactory.createElement("svg:g");a.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(a),this.current.clipGroup=a}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",y(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}d.SVGGraphics=C},(At,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.XfaText=void 0;class rt{static textContent(M){const at=[],X={items:at,styles:Object.create(null)};function ft(U){var L;if(!U)return;let k=null;const g=U.name;if(g==="#text")k=U.value;else if(rt.shouldBuildText(g))(L=U==null?void 0:U.attributes)!=null&&L.textContent?k=U.attributes.textContent:U.value&&(k=U.value);else return;if(k!==null&&at.push({str:k}),!!U.children)for(const O of U.children)ft(O)}return ft(M),X}static shouldBuildText(M){return!(M==="textarea"||M==="input"||M==="option"||M==="select")}}d.XfaText=rt},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.TextLayerRenderTask=void 0,d.renderTextLayer=y,d.updateTextLayer=u;var h=rt(1),M=rt(6);const at=1e5,X=30,ft=.8,U=new Map;function k(_,w){let C;if(w&&h.FeatureTest.isOffscreenCanvasSupported)C=new OffscreenCanvas(_,_).getContext("2d",{alpha:!1});else{const A=document.createElement("canvas");A.width=A.height=_,C=A.getContext("2d",{alpha:!1})}return C}function g(_,w){const C=U.get(_);if(C)return C;const A=k(X,w);A.font=`${X}px ${_}`;const a=A.measureText("");let l=a.fontBoundingBoxAscent,P=Math.abs(a.fontBoundingBoxDescent);if(l){const r=l/(l+P);return U.set(_,r),A.canvas.width=A.canvas.height=0,r}A.strokeStyle="red",A.clearRect(0,0,X,X),A.strokeText("g",0,0);let p=A.getImageData(0,0,X,X).data;P=0;for(let r=p.length-1-3;r>=0;r-=4)if(p[r]>0){P=Math.ceil(r/4/X);break}A.clearRect(0,0,X,X),A.strokeText("A",0,X),p=A.getImageData(0,0,X,X).data,l=0;for(let r=0,T=p.length;r<T;r+=4)if(p[r]>0){l=X-Math.floor(r/4/X);break}if(A.canvas.width=A.canvas.height=0,l){const r=l/(l+P);return U.set(_,r),r}return U.set(_,ft),ft}function L(_,w,C){const A=document.createElement("span"),a={angle:0,canvasWidth:0,hasText:w.str!=="",hasEOL:w.hasEOL,fontSize:0};_._textDivs.push(A);const l=h.Util.transform(_._transform,w.transform);let P=Math.atan2(l[1],l[0]);const p=C[w.fontName];p.vertical&&(P+=Math.PI/2);const r=Math.hypot(l[2],l[3]),T=r*g(p.fontFamily,_._isOffscreenCanvasSupported);let m,B;P===0?(m=l[4],B=l[5]-T):(m=l[4]+T*Math.sin(P),B=l[5]-T*Math.cos(P));const z="calc(var(--scale-factor)*",E=A.style;_._container===_._rootContainer?(E.left=`${(100*m/_._pageWidth).toFixed(2)}%`,E.top=`${(100*B/_._pageHeight).toFixed(2)}%`):(E.left=`${z}${m.toFixed(2)}px)`,E.top=`${z}${B.toFixed(2)}px)`),E.fontSize=`${z}${r.toFixed(2)}px)`,E.fontFamily=p.fontFamily,a.fontSize=r,A.setAttribute("role","presentation"),A.textContent=w.str,A.dir=w.dir,_._fontInspectorEnabled&&(A.dataset.fontName=w.fontName),P!==0&&(a.angle=P*(180/Math.PI));let V=!1;if(w.str.length>1)V=!0;else if(w.str!==" "&&w.transform[0]!==w.transform[3]){const it=Math.abs(w.transform[0]),nt=Math.abs(w.transform[3]);it!==nt&&Math.max(it,nt)/Math.min(it,nt)>1.5&&(V=!0)}V&&(a.canvasWidth=p.vertical?w.height:w.width),_._textDivProperties.set(A,a),_._isReadableStream&&_._layoutText(A)}function O(_){const{div:w,scale:C,properties:A,ctx:a,prevFontSize:l,prevFontFamily:P}=_,{style:p}=w;let r="";if(A.canvasWidth!==0&&A.hasText){const{fontFamily:T}=p,{canvasWidth:m,fontSize:B}=A;(l!==B||P!==T)&&(a.font=`${B*C}px ${T}`,_.prevFontSize=B,_.prevFontFamily=T);const{width:z}=a.measureText(w.textContent);z>0&&(r=`scaleX(${m*C/z})`)}A.angle!==0&&(r=`rotate(${A.angle}deg) ${r}`),r.length>0&&(p.transform=r)}function x(_){if(_._canceled)return;const w=_._textDivs,C=_._capability;if(w.length>at){C.resolve();return}if(!_._isReadableStream)for(const a of w)_._layoutText(a);C.resolve()}class v{constructor({textContentSource:w,container:C,viewport:A,textDivs:a,textDivProperties:l,textContentItemsStr:P,isOffscreenCanvasSupported:p}){var z;this._textContentSource=w,this._isReadableStream=w instanceof ReadableStream,this._container=this._rootContainer=C,this._textDivs=a||[],this._textContentItemsStr=P||[],this._isOffscreenCanvasSupported=p,this._fontInspectorEnabled=!!((z=globalThis.FontInspector)!=null&&z.enabled),this._reader=null,this._textDivProperties=l||new WeakMap,this._canceled=!1,this._capability=new h.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:A.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:k(0,p)};const{pageWidth:r,pageHeight:T,pageX:m,pageY:B}=A.rawDims;this._transform=[1,0,0,-1,-m,B+T],this._pageWidth=r,this._pageHeight=T,(0,M.setLayerDimensions)(C,A),this._capability.promise.finally(()=>{this._layoutTextParams=null}).catch(()=>{})}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new h.AbortException("TextLayer task cancelled.")).catch(()=>{}),this._reader=null),this._capability.reject(new h.AbortException("TextLayer task cancelled."))}_processItems(w,C){for(const A of w){if(A.str===void 0){if(A.type==="beginMarkedContentProps"||A.type==="beginMarkedContent"){const a=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),A.id!==null&&this._container.setAttribute("id",`${A.id}`),a.append(this._container)}else A.type==="endMarkedContent"&&(this._container=this._container.parentNode);continue}this._textContentItemsStr.push(A.str),L(this,A,C)}}_layoutText(w){const C=this._layoutTextParams.properties=this._textDivProperties.get(w);if(this._layoutTextParams.div=w,O(this._layoutTextParams),C.hasText&&this._container.append(w),C.hasEOL){const A=document.createElement("br");A.setAttribute("role","presentation"),this._container.append(A)}}_render(){const w=new h.PromiseCapability;let C=Object.create(null);if(this._isReadableStream){const A=()=>{this._reader.read().then(({value:a,done:l})=>{if(l){w.resolve();return}Object.assign(C,a.styles),this._processItems(a.items,C),A()},w.reject)};this._reader=this._textContentSource.getReader(),A()}else if(this._textContentSource){const{items:A,styles:a}=this._textContentSource;this._processItems(A,a),w.resolve()}else throw new Error('No "textContentSource" parameter specified.');w.promise.then(()=>{C=null,x(this)},this._capability.reject)}}d.TextLayerRenderTask=v;function y(_){!_.textContentSource&&(_.textContent||_.textContentStream)&&((0,M.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),_.textContentSource=_.textContent||_.textContentStream);const{container:w,viewport:C}=_,A=getComputedStyle(w),a=A.getPropertyValue("visibility"),l=parseFloat(A.getPropertyValue("--scale-factor"));a==="visible"&&(!l||Math.abs(l-C.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const P=new v(_);return P._render(),P}function u({container:_,viewport:w,textDivs:C,textDivProperties:A,isOffscreenCanvasSupported:a,mustRotate:l=!0,mustRescale:P=!0}){if(l&&(0,M.setLayerDimensions)(_,{rotation:w.rotation}),P){const p=k(0,a),T={prevFontSize:null,prevFontFamily:null,div:null,scale:w.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:p};for(const m of C)T.properties=A.get(m),T.div=m,O(T)}}},(At,d,rt)=>{var g,L,O,x,v,y,u,_,w,C,A,ei,l,ke,p,ii,T,si;Object.defineProperty(d,"__esModule",{value:!0}),d.AnnotationEditorLayer=void 0;var h=rt(1),M=rt(4),at=rt(28),X=rt(33),ft=rt(6),U=rt(34);const B=class B{constructor({uiManager:E,pageIndex:V,div:it,accessibilityManager:nt,annotationLayer:H,viewport:lt,l10n:pt}){I(this,A);I(this,l);I(this,p);I(this,T);I(this,g,void 0);I(this,L,!1);I(this,O,null);I(this,x,this.pointerup.bind(this));I(this,v,this.pointerdown.bind(this));I(this,y,new Map);I(this,u,!1);I(this,_,!1);I(this,w,!1);I(this,C,void 0);const wt=[at.FreeTextEditor,X.InkEditor,U.StampEditor];if(!B._initialized){B._initialized=!0;for(const Pt of wt)Pt.initialize(pt)}E.registerEditorTypes(wt),Z(this,C,E),this.pageIndex=V,this.div=it,Z(this,g,nt),Z(this,O,H),this.viewport=lt,t(this,C).addLayer(this)}get isEmpty(){return t(this,y).size===0}updateToolbar(E){t(this,C).updateToolbar(E)}updateMode(E=t(this,C).getMode()){W(this,T,si).call(this),E===h.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),E!==h.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",E===h.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",E===h.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",E===h.AnnotationEditorType.STAMP),this.div.hidden=!1)}addInkEditorIfNeeded(E){if(!E&&t(this,C).getMode()!==h.AnnotationEditorType.INK)return;if(!E){for(const it of t(this,y).values())if(it.isEmpty()){it.setInBackground();return}}W(this,l,ke).call(this,{offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(E){t(this,C).setEditingState(E)}addCommands(E){t(this,C).addCommands(E)}enable(){this.div.style.pointerEvents="auto";const E=new Set;for(const it of t(this,y).values())it.enableEditing(),it.annotationElementId&&E.add(it.annotationElementId);if(!t(this,O))return;const V=t(this,O).getEditableAnnotations();for(const it of V){if(it.hide(),t(this,C).isDeletedAnnotationElement(it.data.id)||E.has(it.data.id))continue;const nt=this.deserialize(it);nt&&(this.addOrRebuild(nt),nt.enableEditing())}}disable(){var V;Z(this,w,!0),this.div.style.pointerEvents="none";const E=new Set;for(const it of t(this,y).values()){if(it.disableEditing(),!it.annotationElementId||it.serialize()!==null){E.add(it.annotationElementId);continue}(V=this.getEditableAnnotation(it.annotationElementId))==null||V.show(),it.remove()}if(t(this,O)){const it=t(this,O).getEditableAnnotations();for(const nt of it){const{id:H}=nt.data;E.has(H)||t(this,C).isDeletedAnnotationElement(H)||nt.show()}}W(this,T,si).call(this),this.isEmpty&&(this.div.hidden=!0),Z(this,w,!1)}getEditableAnnotation(E){var V;return((V=t(this,O))==null?void 0:V.getEditableAnnotation(E))||null}setActiveEditor(E){t(this,C).getActive()!==E&&t(this,C).setActiveEditor(E)}enableClick(){this.div.addEventListener("pointerdown",t(this,v)),this.div.addEventListener("pointerup",t(this,x))}disableClick(){this.div.removeEventListener("pointerdown",t(this,v)),this.div.removeEventListener("pointerup",t(this,x))}attach(E){t(this,y).set(E.id,E);const{annotationElementId:V}=E;V&&t(this,C).isDeletedAnnotationElement(V)&&t(this,C).removeDeletedAnnotationElement(E)}detach(E){var V;t(this,y).delete(E.id),(V=t(this,g))==null||V.removePointerInTextLayer(E.contentDiv),!t(this,w)&&E.annotationElementId&&t(this,C).addDeletedAnnotationElement(E)}remove(E){this.detach(E),t(this,C).removeEditor(E),E.div.contains(document.activeElement)&&setTimeout(()=>{t(this,C).focusMainContainer()},0),E.div.remove(),E.isAttachedToDOM=!1,t(this,_)||this.addInkEditorIfNeeded(!1)}changeParent(E){var V;E.parent!==this&&(E.annotationElementId&&(t(this,C).addDeletedAnnotationElement(E.annotationElementId),M.AnnotationEditor.deleteAnnotationElement(E),E.annotationElementId=null),this.attach(E),(V=E.parent)==null||V.detach(E),E.setParent(this),E.div&&E.isAttachedToDOM&&(E.div.remove(),this.div.append(E.div)))}add(E){if(this.changeParent(E),t(this,C).addEditor(E),this.attach(E),!E.isAttachedToDOM){const V=E.render();this.div.append(V),E.isAttachedToDOM=!0}E.fixAndSetPosition(),E.onceAdded(),t(this,C).addToAnnotationStorage(E)}moveEditorInDOM(E){var it;if(!E.isAttachedToDOM)return;const{activeElement:V}=document;E.div.contains(V)&&(E._focusEventsAllowed=!1,setTimeout(()=>{E.div.contains(document.activeElement)?E._focusEventsAllowed=!0:(E.div.addEventListener("focusin",()=>{E._focusEventsAllowed=!0},{once:!0}),V.focus())},0)),E._structTreeParentId=(it=t(this,g))==null?void 0:it.moveElementInDOM(this.div,E.div,E.contentDiv,!0)}addOrRebuild(E){E.needsToBeRebuilt()?E.rebuild():this.add(E)}addUndoableEditor(E){const V=()=>E._uiManager.rebuild(E),it=()=>{E.remove()};this.addCommands({cmd:V,undo:it,mustExec:!1})}getNextId(){return t(this,C).getId()}pasteEditor(E,V){t(this,C).updateToolbar(E),t(this,C).updateMode(E);const{offsetX:it,offsetY:nt}=W(this,p,ii).call(this),H=this.getNextId(),lt=W(this,A,ei).call(this,{parent:this,id:H,x:it,y:nt,uiManager:t(this,C),isCentered:!0,...V});lt&&this.add(lt)}deserialize(E){switch(E.annotationType??E.annotationEditorType){case h.AnnotationEditorType.FREETEXT:return at.FreeTextEditor.deserialize(E,this,t(this,C));case h.AnnotationEditorType.INK:return X.InkEditor.deserialize(E,this,t(this,C));case h.AnnotationEditorType.STAMP:return U.StampEditor.deserialize(E,this,t(this,C))}return null}addNewEditor(){W(this,l,ke).call(this,W(this,p,ii).call(this),!0)}setSelected(E){t(this,C).setSelected(E)}toggleSelected(E){t(this,C).toggleSelected(E)}isSelected(E){return t(this,C).isSelected(E)}unselect(E){t(this,C).unselect(E)}pointerup(E){const{isMac:V}=h.FeatureTest.platform;if(!(E.button!==0||E.ctrlKey&&V)&&E.target===this.div&&t(this,u)){if(Z(this,u,!1),!t(this,L)){Z(this,L,!0);return}if(t(this,C).getMode()===h.AnnotationEditorType.STAMP){t(this,C).unselectAll();return}W(this,l,ke).call(this,E,!1)}}pointerdown(E){if(t(this,u)){Z(this,u,!1);return}const{isMac:V}=h.FeatureTest.platform;if(E.button!==0||E.ctrlKey&&V||E.target!==this.div)return;Z(this,u,!0);const it=t(this,C).getActive();Z(this,L,!it||it.isEmpty())}findNewParent(E,V,it){const nt=t(this,C).findParent(V,it);return nt===null||nt===this?!1:(nt.changeParent(E),!0)}destroy(){var E,V;((E=t(this,C).getActive())==null?void 0:E.parent)===this&&(t(this,C).commitOrRemove(),t(this,C).setActiveEditor(null));for(const it of t(this,y).values())(V=t(this,g))==null||V.removePointerInTextLayer(it.contentDiv),it.setParent(null),it.isAttachedToDOM=!1,it.div.remove();this.div=null,t(this,y).clear(),t(this,C).removeLayer(this)}render({viewport:E}){this.viewport=E,(0,ft.setLayerDimensions)(this.div,E);for(const V of t(this,C).getEditors(this.pageIndex))this.add(V);this.updateMode()}update({viewport:E}){t(this,C).commitOrRemove(),this.viewport=E,(0,ft.setLayerDimensions)(this.div,{rotation:E.rotation}),this.updateMode()}get pageDimensions(){const{pageWidth:E,pageHeight:V}=this.viewport.rawDims;return[E,V]}};g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakMap,w=new WeakMap,C=new WeakMap,A=new WeakSet,ei=function(E){switch(t(this,C).getMode()){case h.AnnotationEditorType.FREETEXT:return new at.FreeTextEditor(E);case h.AnnotationEditorType.INK:return new X.InkEditor(E);case h.AnnotationEditorType.STAMP:return new U.StampEditor(E)}return null},l=new WeakSet,ke=function(E,V){const it=this.getNextId(),nt=W(this,A,ei).call(this,{parent:this,id:it,x:E.offsetX,y:E.offsetY,uiManager:t(this,C),isCentered:V});return nt&&this.add(nt),nt},p=new WeakSet,ii=function(){const{x:E,y:V,width:it,height:nt}=this.div.getBoundingClientRect(),H=Math.max(0,E),lt=Math.max(0,V),pt=Math.min(window.innerWidth,E+it),wt=Math.min(window.innerHeight,V+nt),Pt=(H+pt)/2-E,S=(lt+wt)/2-V,[i,n]=this.viewport.rotation%180===0?[Pt,S]:[S,Pt];return{offsetX:i,offsetY:n}},T=new WeakSet,si=function(){Z(this,_,!0);for(const E of t(this,y).values())E.isEmpty()&&E.remove();Z(this,_,!1)},ee(B,"_initialized",!1);let k=B;d.AnnotationEditorLayer=k},(At,d,rt)=>{var U,k,g,L,O,x,v,y,u,_,Fi,C,Mi,a,Ri,P,ye,r,ni,m,Di,z,ri;Object.defineProperty(d,"__esModule",{value:!0}),d.FreeTextEditor=void 0;var h=rt(1),M=rt(5),at=rt(4),X=rt(29);const V=class V extends at.AnnotationEditor{constructor(H){super({...H,name:"freeTextEditor"});I(this,_);I(this,C);I(this,a);I(this,P);I(this,r);I(this,m);I(this,z);I(this,U,this.editorDivBlur.bind(this));I(this,k,this.editorDivFocus.bind(this));I(this,g,this.editorDivInput.bind(this));I(this,L,this.editorDivKeydown.bind(this));I(this,O,void 0);I(this,x,"");I(this,v,`${this.id}-editor`);I(this,y,void 0);I(this,u,null);Z(this,O,H.color||V._defaultColor||at.AnnotationEditor._defaultLineColor),Z(this,y,H.fontSize||V._defaultFontSize)}static get _keyboardManager(){const H=V.prototype,lt=Pt=>Pt.isEmpty(),pt=M.AnnotationEditorUIManager.TRANSLATE_SMALL,wt=M.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,h.shadow)(this,"_keyboardManager",new M.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],H.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],H.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],H._translateEmpty,{args:[-pt,0],checker:lt}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],H._translateEmpty,{args:[-wt,0],checker:lt}],[["ArrowRight","mac+ArrowRight"],H._translateEmpty,{args:[pt,0],checker:lt}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],H._translateEmpty,{args:[wt,0],checker:lt}],[["ArrowUp","mac+ArrowUp"],H._translateEmpty,{args:[0,-pt],checker:lt}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],H._translateEmpty,{args:[0,-wt],checker:lt}],[["ArrowDown","mac+ArrowDown"],H._translateEmpty,{args:[0,pt],checker:lt}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],H._translateEmpty,{args:[0,wt],checker:lt}]]))}static initialize(H){at.AnnotationEditor.initialize(H,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const lt=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(lt.getPropertyValue("--freetext-padding"))}static updateDefaultParams(H,lt){switch(H){case h.AnnotationEditorParamsType.FREETEXT_SIZE:V._defaultFontSize=lt;break;case h.AnnotationEditorParamsType.FREETEXT_COLOR:V._defaultColor=lt;break}}updateParams(H,lt){switch(H){case h.AnnotationEditorParamsType.FREETEXT_SIZE:W(this,_,Fi).call(this,lt);break;case h.AnnotationEditorParamsType.FREETEXT_COLOR:W(this,C,Mi).call(this,lt);break}}static get defaultPropertiesToUpdate(){return[[h.AnnotationEditorParamsType.FREETEXT_SIZE,V._defaultFontSize],[h.AnnotationEditorParamsType.FREETEXT_COLOR,V._defaultColor||at.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[h.AnnotationEditorParamsType.FREETEXT_SIZE,t(this,y)],[h.AnnotationEditorParamsType.FREETEXT_COLOR,t(this,O)]]}_translateEmpty(H,lt){this._uiManager.translateSelectedEditors(H,lt,!0)}getInitialTranslation(){const H=this.parentScale;return[-V._internalPadding*H,-(V._internalPadding+t(this,y))*H]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(h.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",t(this,L)),this.editorDiv.addEventListener("focus",t(this,k)),this.editorDiv.addEventListener("blur",t(this,U)),this.editorDiv.addEventListener("input",t(this,g)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",t(this,v)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",t(this,L)),this.editorDiv.removeEventListener("focus",t(this,k)),this.editorDiv.removeEventListener("blur",t(this,U)),this.editorDiv.removeEventListener("input",t(this,g)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}focusin(H){this._focusEventsAllowed&&(super.focusin(H),H.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){var H;if(this.width){W(this,z,ri).call(this);return}this.enableEditMode(),this.editorDiv.focus(),(H=this._initialOptions)!=null&&H.isCentered&&this.center(),this._initialOptions=null}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const H=t(this,x),lt=Z(this,x,W(this,a,Ri).call(this).trimEnd());if(H===lt)return;const pt=wt=>{if(Z(this,x,wt),!wt){this.remove();return}W(this,r,ni).call(this),this._uiManager.rebuild(this),W(this,P,ye).call(this)};this.addCommands({cmd:()=>{pt(lt)},undo:()=>{pt(H)},mustExec:!1}),W(this,P,ye).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(H){this.enterInEditMode()}keydown(H){H.target===this.div&&H.key==="Enter"&&(this.enterInEditMode(),H.preventDefault())}editorDivKeydown(H){V._keyboardManager.exec(this,H)}editorDivFocus(H){this.isEditing=!0}editorDivBlur(H){this.isEditing=!1}editorDivInput(H){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let H,lt;this.width&&(H=this.x,lt=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",t(this,v)),this.enableEditing(),at.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then(wt=>{var Pt;return(Pt=this.editorDiv)==null?void 0:Pt.setAttribute("aria-label",wt)}),at.AnnotationEditor._l10nPromise.get("free_text2_default_content").then(wt=>{var Pt;return(Pt=this.editorDiv)==null?void 0:Pt.setAttribute("default-content",wt)}),this.editorDiv.contentEditable=!0;const{style:pt}=this.editorDiv;if(pt.fontSize=`calc(${t(this,y)}px * var(--scale-factor))`,pt.color=t(this,O),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,M.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[wt,Pt]=this.parentDimensions;if(this.annotationElementId){const{position:S}=t(this,u);let[i,n]=this.getInitialTranslation();[i,n]=this.pageTranslationToScreen(i,n);const[s,o]=this.pageDimensions,[c,b]=this.pageTranslation;let F,N;switch(this.rotation){case 0:F=H+(S[0]-c)/s,N=lt+this.height-(S[1]-b)/o;break;case 90:F=H+(S[0]-c)/s,N=lt-(S[1]-b)/o,[i,n]=[n,-i];break;case 180:F=H-this.width+(S[0]-c)/s,N=lt-(S[1]-b)/o,[i,n]=[-i,-n];break;case 270:F=H+(S[0]-c-this.height*o)/s,N=lt+(S[1]-b-this.width*s)/o,[i,n]=[-n,i];break}this.setAt(F*wt,N*Pt,i,n)}else this.setAt(H*wt,lt*Pt,this.width*wt,this.height*Pt);W(this,r,ni).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}get contentDiv(){return this.editorDiv}static deserialize(H,lt,pt){let wt=null;if(H instanceof X.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:S,fontColor:i},rect:n,rotation:s,id:o},textContent:c,textPosition:b,parent:{page:{pageNumber:F}}}=H;if(!c||c.length===0)return null;wt=H={annotationType:h.AnnotationEditorType.FREETEXT,color:Array.from(i),fontSize:S,value:c.join(`
`),position:b,pageIndex:F-1,rect:n,rotation:s,id:o,deleted:!1}}const Pt=super.deserialize(H,lt,pt);return Z(Pt,y,H.fontSize),Z(Pt,O,h.Util.makeHexColor(...H.color)),Z(Pt,x,H.value),Pt.annotationElementId=H.id||null,Z(Pt,u,wt),Pt}serialize(H=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const lt=V._internalPadding*this.parentScale,pt=this.getRect(lt,lt),wt=at.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:t(this,O)),Pt={annotationType:h.AnnotationEditorType.FREETEXT,color:wt,fontSize:t(this,y),value:t(this,x),pageIndex:this.pageIndex,rect:pt,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return H?Pt:this.annotationElementId&&!W(this,m,Di).call(this,Pt)?null:(Pt.id=this.annotationElementId,Pt)}};U=new WeakMap,k=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakSet,Fi=function(H){const lt=wt=>{this.editorDiv.style.fontSize=`calc(${wt}px * var(--scale-factor))`,this.translate(0,-(wt-t(this,y))*this.parentScale),Z(this,y,wt),W(this,P,ye).call(this)},pt=t(this,y);this.addCommands({cmd:()=>{lt(H)},undo:()=>{lt(pt)},mustExec:!0,type:h.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},C=new WeakSet,Mi=function(H){const lt=t(this,O);this.addCommands({cmd:()=>{Z(this,O,this.editorDiv.style.color=H)},undo:()=>{Z(this,O,this.editorDiv.style.color=lt)},mustExec:!0,type:h.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},a=new WeakSet,Ri=function(){const H=this.editorDiv.getElementsByTagName("div");if(H.length===0)return this.editorDiv.innerText;const lt=[];for(const pt of H)lt.push(pt.innerText.replace(/\r\n?|\n/,""));return lt.join(`
`)},P=new WeakSet,ye=function(){const[H,lt]=this.parentDimensions;let pt;if(this.isAttachedToDOM)pt=this.div.getBoundingClientRect();else{const{currentLayer:wt,div:Pt}=this,S=Pt.style.display;Pt.style.display="hidden",wt.div.append(this.div),pt=Pt.getBoundingClientRect(),Pt.remove(),Pt.style.display=S}this.rotation%180===this.parentRotation%180?(this.width=pt.width/H,this.height=pt.height/lt):(this.width=pt.height/H,this.height=pt.width/lt),this.fixAndSetPosition()},r=new WeakSet,ni=function(){if(this.editorDiv.replaceChildren(),!!t(this,x))for(const H of t(this,x).split(`
`)){const lt=document.createElement("div");lt.append(H?document.createTextNode(H):document.createElement("br")),this.editorDiv.append(lt)}},m=new WeakSet,Di=function(H){const{value:lt,fontSize:pt,color:wt,rect:Pt,pageIndex:S}=t(this,u);return H.value!==lt||H.fontSize!==pt||H.rect.some((i,n)=>Math.abs(i-Pt[n])>=1)||H.color.some((i,n)=>i!==wt[n])||H.pageIndex!==S},z=new WeakSet,ri=function(H=!1){if(!this.annotationElementId)return;if(W(this,P,ye).call(this),!H&&(this.width===0||this.height===0)){setTimeout(()=>W(this,z,ri).call(this,!0),0);return}const lt=V._internalPadding*this.parentScale;t(this,u).rect=this.getRect(lt,lt)},ee(V,"_freeTextDefaultContent",""),ee(V,"_internalPadding",0),ee(V,"_defaultColor",null),ee(V,"_defaultFontSize",10),ee(V,"_type","freetext");let ft=V;d.FreeTextEditor=ft},(At,d,rt)=>{var n,o,ce,b,Ii,N,tt,Q,st,ct,yt,dt,Ft,Bt,St,Dt,ut,K,J,ht,Et,Ct,jt,Li,Ht,Fe,Vt,ai,qt,oi,Y,G,mt,_t,te,Zt,q,li,Lt,Tt,Ot,Nt,Oi,bt,ci;Object.defineProperty(d,"__esModule",{value:!0}),d.StampAnnotationElement=d.InkAnnotationElement=d.FreeTextAnnotationElement=d.AnnotationLayer=void 0;var h=rt(1),M=rt(6),at=rt(3),X=rt(30),ft=rt(31),U=rt(32);const k=1e3,g=9,L=new WeakSet;function O(It){return{width:It[2]-It[0],height:It[3]-It[1]}}class x{static create(R){switch(R.data.annotationType){case h.AnnotationType.LINK:return new y(R);case h.AnnotationType.TEXT:return new u(R);case h.AnnotationType.WIDGET:switch(R.data.fieldType){case"Tx":return new w(R);case"Btn":return R.data.radioButton?new a(R):R.data.checkBox?new A(R):new l(R);case"Ch":return new P(R);case"Sig":return new C(R)}return new _(R);case h.AnnotationType.POPUP:return new p(R);case h.AnnotationType.FREETEXT:return new T(R);case h.AnnotationType.LINE:return new m(R);case h.AnnotationType.SQUARE:return new B(R);case h.AnnotationType.CIRCLE:return new z(R);case h.AnnotationType.POLYLINE:return new E(R);case h.AnnotationType.CARET:return new it(R);case h.AnnotationType.INK:return new nt(R);case h.AnnotationType.POLYGON:return new V(R);case h.AnnotationType.HIGHLIGHT:return new H(R);case h.AnnotationType.UNDERLINE:return new lt(R);case h.AnnotationType.SQUIGGLY:return new pt(R);case h.AnnotationType.STRIKEOUT:return new wt(R);case h.AnnotationType.STAMP:return new Pt(R);case h.AnnotationType.FILEATTACHMENT:return new S(R);default:return new v(R)}}}const s=class s{constructor(R,{isRenderable:e=!1,ignoreBorder:f=!1,createQuadrilaterals:D=!1}={}){I(this,n,!1);this.isRenderable=e,this.data=R.data,this.layer=R.layer,this.linkService=R.linkService,this.downloadManager=R.downloadManager,this.imageResourcesPath=R.imageResourcesPath,this.renderForms=R.renderForms,this.svgFactory=R.svgFactory,this.annotationStorage=R.annotationStorage,this.enableScripting=R.enableScripting,this.hasJSActions=R.hasJSActions,this._fieldObjects=R.fieldObjects,this.parent=R.parent,e&&(this.container=this._createContainer(f)),D&&this._createQuadrilaterals()}static _hasPopupData({titleObj:R,contentsObj:e,richText:f}){return!!(R!=null&&R.str||e!=null&&e.str||f!=null&&f.str)}get hasPopupData(){return s._hasPopupData(this.data)}_createContainer(R){const{data:e,parent:{page:f,viewport:D}}=this,j=document.createElement("section");j.setAttribute("data-annotation-id",e.id),this instanceof _||(j.tabIndex=k),j.style.zIndex=this.parent.zIndex++,this.data.popupRef&&j.setAttribute("aria-haspopup","dialog"),e.noRotate&&j.classList.add("norotate");const{pageWidth:$,pageHeight:et,pageX:gt,pageY:kt}=D.rawDims;if(!e.rect||this instanceof p){const{rotation:Ut}=e;return!e.hasOwnCanvas&&Ut!==0&&this.setRotation(Ut,j),j}const{width:xt,height:zt}=O(e.rect),Mt=h.Util.normalizeRect([e.rect[0],f.view[3]-e.rect[1]+f.view[1],e.rect[2],f.view[3]-e.rect[3]+f.view[1]]);if(!R&&e.borderStyle.width>0){j.style.borderWidth=`${e.borderStyle.width}px`;const Ut=e.borderStyle.horizontalCornerRadius,$t=e.borderStyle.verticalCornerRadius;if(Ut>0||$t>0){const Qt=`calc(${Ut}px * var(--scale-factor)) / calc(${$t}px * var(--scale-factor))`;j.style.borderRadius=Qt}else if(this instanceof a){const Qt=`calc(${xt}px * var(--scale-factor)) / calc(${zt}px * var(--scale-factor))`;j.style.borderRadius=Qt}switch(e.borderStyle.style){case h.AnnotationBorderStyleType.SOLID:j.style.borderStyle="solid";break;case h.AnnotationBorderStyleType.DASHED:j.style.borderStyle="dashed";break;case h.AnnotationBorderStyleType.BEVELED:(0,h.warn)("Unimplemented border style: beveled");break;case h.AnnotationBorderStyleType.INSET:(0,h.warn)("Unimplemented border style: inset");break;case h.AnnotationBorderStyleType.UNDERLINE:j.style.borderBottomStyle="solid";break}const Kt=e.borderColor||null;Kt?(Z(this,n,!0),j.style.borderColor=h.Util.makeHexColor(Kt[0]|0,Kt[1]|0,Kt[2]|0)):j.style.borderWidth=0}j.style.left=`${100*(Mt[0]-gt)/$}%`,j.style.top=`${100*(Mt[1]-kt)/et}%`;const{rotation:Rt}=e;return e.hasOwnCanvas||Rt===0?(j.style.width=`${100*xt/$}%`,j.style.height=`${100*zt/et}%`):this.setRotation(Rt,j),j}setRotation(R,e=this.container){if(!this.data.rect)return;const{pageWidth:f,pageHeight:D}=this.parent.viewport.rawDims,{width:j,height:$}=O(this.data.rect);let et,gt;R%180===0?(et=100*j/f,gt=100*$/D):(et=100*$/f,gt=100*j/D),e.style.width=`${et}%`,e.style.height=`${gt}%`,e.setAttribute("data-main-rotation",(360-R)%360)}get _commonActions(){const R=(e,f,D)=>{const j=D.detail[e],$=j[0],et=j.slice(1);D.target.style[f]=X.ColorConverters[`${$}_HTML`](et),this.annotationStorage.setValue(this.data.id,{[f]:X.ColorConverters[`${$}_rgb`](et)})};return(0,h.shadow)(this,"_commonActions",{display:e=>{const{display:f}=e.detail,D=f%2===1;this.container.style.visibility=D?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:D,noPrint:f===1||f===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:f}=e.detail;this.container.style.visibility=f?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:f,noView:f})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{R("bgColor","backgroundColor",e)},fillColor:e=>{R("fillColor","backgroundColor",e)},fgColor:e=>{R("fgColor","color",e)},textColor:e=>{R("textColor","color",e)},borderColor:e=>{R("borderColor","borderColor",e)},strokeColor:e=>{R("strokeColor","borderColor",e)},rotation:e=>{const f=e.detail.rotation;this.setRotation(f),this.annotationStorage.setValue(this.data.id,{rotation:f})}})}_dispatchEventFromSandbox(R,e){const f=this._commonActions;for(const D of Object.keys(e.detail)){const j=R[D]||f[D];j==null||j(e)}}_setDefaultPropertiesFromJS(R){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const f=this._commonActions;for(const[D,j]of Object.entries(e)){const $=f[D];if($){const et={detail:{[D]:j},target:R};$(et),delete e[D]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:R}=this.data;if(!R)return;const[e,f,D,j]=this.data.rect;if(R.length===1){const[,{x:$t,y:Kt},{x:Qt,y:se}]=R[0];if(D===$t&&j===Kt&&e===Qt&&f===se)return}const{style:$}=this.container;let et;if(t(this,n)){const{borderColor:$t,borderWidth:Kt}=$;$.borderWidth=0,et=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${$t}" stroke-width="${Kt}">`],this.container.classList.add("hasBorder")}const gt=D-e,kt=j-f,{svgFactory:xt}=this,zt=xt.createElement("svg");zt.classList.add("quadrilateralsContainer"),zt.setAttribute("width",0),zt.setAttribute("height",0);const Mt=xt.createElement("defs");zt.append(Mt);const Rt=xt.createElement("clipPath"),Ut=`clippath_${this.data.id}`;Rt.setAttribute("id",Ut),Rt.setAttribute("clipPathUnits","objectBoundingBox"),Mt.append(Rt);for(const[,{x:$t,y:Kt},{x:Qt,y:se}]of R){const ie=xt.createElement("rect"),ne=(Qt-e)/gt,oe=(j-Kt)/kt,le=($t-Qt)/gt,_i=(Kt-se)/kt;ie.setAttribute("x",ne),ie.setAttribute("y",oe),ie.setAttribute("width",le),ie.setAttribute("height",_i),Rt.append(ie),et==null||et.push(`<rect vector-effect="non-scaling-stroke" x="${ne}" y="${oe}" width="${le}" height="${_i}"/>`)}t(this,n)&&(et.push("</g></svg>')"),$.backgroundImage=et.join("")),this.container.append(zt),this.container.style.clipPath=`url(#${Ut})`}_createPopup(){const{container:R,data:e}=this;R.setAttribute("aria-haspopup","dialog");const f=new p({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(f.render())}render(){(0,h.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(R,e=null){const f=[];if(this._fieldObjects){const D=this._fieldObjects[R];if(D)for(const{page:j,id:$,exportValues:et}of D){if(j===-1||$===e)continue;const gt=typeof et=="string"?et:null,kt=document.querySelector(`[data-element-id="${$}"]`);if(kt&&!L.has(kt)){(0,h.warn)(`_getElementsByName - element not allowed: ${$}`);continue}f.push({id:$,exportValue:gt,domElement:kt})}return f}for(const D of document.getElementsByName(R)){const{exportValue:j}=D,$=D.getAttribute("data-element-id");$!==e&&L.has(D)&&f.push({id:$,exportValue:j,domElement:D})}return f}show(){var R;this.container&&(this.container.hidden=!1),(R=this.popup)==null||R.maybeShow()}hide(){var R;this.container&&(this.container.hidden=!0),(R=this.popup)==null||R.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const R=this.getElementsToTriggerPopup();if(Array.isArray(R))for(const e of R)e.classList.add("highlightArea");else R.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:R,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{var f;(f=this.linkService.eventBus)==null||f.dispatch("switchannotationeditormode",{source:this,mode:R,editId:e})})}};n=new WeakMap;let v=s;class y extends v{constructor(e,f=null){super(e,{isRenderable:!0,ignoreBorder:!!(f!=null&&f.ignoreBorder),createQuadrilaterals:!0});I(this,o);I(this,b);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:f}=this,D=document.createElement("a");D.setAttribute("data-element-id",e.id);let j=!1;return e.url?(f.addLinkAttributes(D,e.url,e.newWindow),j=!0):e.action?(this._bindNamedAction(D,e.action),j=!0):e.attachment?(this._bindAttachment(D,e.attachment),j=!0):e.setOCGState?(W(this,b,Ii).call(this,D,e.setOCGState),j=!0):e.dest?(this._bindLink(D,e.dest),j=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(D,e),j=!0),e.resetForm?(this._bindResetFormAction(D,e.resetForm),j=!0):this.isTooltipOnly&&!j&&(this._bindLink(D,""),j=!0)),this.container.classList.add("linkAnnotation"),j&&this.container.append(D),this.container}_bindLink(e,f){e.href=this.linkService.getDestinationHash(f),e.onclick=()=>(f&&this.linkService.goToDestination(f),!1),(f||f==="")&&W(this,o,ce).call(this)}_bindNamedAction(e,f){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(f),!1),W(this,o,ce).call(this)}_bindAttachment(e,f){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>{var D;return(D=this.downloadManager)==null||D.openOrDownloadData(this.container,f.content,f.filename),!1},W(this,o,ce).call(this)}_bindJSAction(e,f){e.href=this.linkService.getAnchorUrl("");const D=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const j of Object.keys(f.actions)){const $=D.get(j);$&&(e[$]=()=>{var et;return(et=this.linkService.eventBus)==null||et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:f.id,name:j}}),!1})}e.onclick||(e.onclick=()=>!1),W(this,o,ce).call(this)}_bindResetFormAction(e,f){const D=e.onclick;if(D||(e.href=this.linkService.getAnchorUrl("")),W(this,o,ce).call(this),!this._fieldObjects){(0,h.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),D||(e.onclick=()=>!1);return}e.onclick=()=>{var zt;D==null||D();const{fields:j,refs:$,include:et}=f,gt=[];if(j.length!==0||$.length!==0){const Mt=new Set($);for(const Rt of j){const Ut=this._fieldObjects[Rt]||[];for(const{id:$t}of Ut)Mt.add($t)}for(const Rt of Object.values(this._fieldObjects))for(const Ut of Rt)Mt.has(Ut.id)===et&&gt.push(Ut)}else for(const Mt of Object.values(this._fieldObjects))gt.push(...Mt);const kt=this.annotationStorage,xt=[];for(const Mt of gt){const{id:Rt}=Mt;switch(xt.push(Rt),Mt.type){case"text":{const $t=Mt.defaultValue||"";kt.setValue(Rt,{value:$t});break}case"checkbox":case"radiobutton":{const $t=Mt.defaultValue===Mt.exportValues;kt.setValue(Rt,{value:$t});break}case"combobox":case"listbox":{const $t=Mt.defaultValue||"";kt.setValue(Rt,{value:$t});break}default:continue}const Ut=document.querySelector(`[data-element-id="${Rt}"]`);if(Ut){if(!L.has(Ut)){(0,h.warn)(`_bindResetFormAction - element not allowed: ${Rt}`);continue}}else continue;Ut.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((zt=this.linkService.eventBus)==null||zt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:xt,name:"ResetForm"}})),!1}}}o=new WeakSet,ce=function(){this.container.setAttribute("data-internal-link","")},b=new WeakSet,Ii=function(e,f){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(f),!1),W(this,o,ce).call(this)};class u extends v{constructor(R){super(R,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const R=document.createElement("img");return R.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",R.alt="[{{type}} Annotation]",R.dataset.l10nId="text_annotation_type",R.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(R),this.container}}class _ extends v{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}showElementAndHideCanvas(R){var e;this.data.hasOwnCanvas&&(((e=R.previousSibling)==null?void 0:e.nodeName)==="CANVAS"&&(R.previousSibling.hidden=!0),R.hidden=!1)}_getKeyModifier(R){const{isWin:e,isMac:f}=h.FeatureTest.platform;return e&&R.ctrlKey||f&&R.metaKey}_setEventListener(R,e,f,D,j){f.includes("mouse")?R.addEventListener(f,$=>{var et;(et=this.linkService.eventBus)==null||et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:D,value:j($),shift:$.shiftKey,modifier:this._getKeyModifier($)}})}):R.addEventListener(f,$=>{var et;if(f==="blur"){if(!e.focused||!$.relatedTarget)return;e.focused=!1}else if(f==="focus"){if(e.focused)return;e.focused=!0}j&&((et=this.linkService.eventBus)==null||et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:D,value:j($)}}))})}_setEventListeners(R,e,f,D){var j,$,et;for(const[gt,kt]of f)(kt==="Action"||(j=this.data.actions)!=null&&j[kt])&&((kt==="Focus"||kt==="Blur")&&(e||(e={focused:!1})),this._setEventListener(R,e,gt,kt,D),kt==="Focus"&&!(($=this.data.actions)!=null&&$.Blur)?this._setEventListener(R,e,"blur","Blur",null):kt==="Blur"&&!((et=this.data.actions)!=null&&et.Focus)&&this._setEventListener(R,e,"focus","Focus",null))}_setBackgroundColor(R){const e=this.data.backgroundColor||null;R.style.backgroundColor=e===null?"transparent":h.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(R){const e=["left","center","right"],{fontColor:f}=this.data.defaultAppearanceData,D=this.data.defaultAppearanceData.fontSize||g,j=R.style;let $;const et=2,gt=kt=>Math.round(10*kt)/10;if(this.data.multiLine){const kt=Math.abs(this.data.rect[3]-this.data.rect[1]-et),xt=Math.round(kt/(h.LINE_FACTOR*D))||1,zt=kt/xt;$=Math.min(D,gt(zt/h.LINE_FACTOR))}else{const kt=Math.abs(this.data.rect[3]-this.data.rect[1]-et);$=Math.min(D,gt(kt/h.LINE_FACTOR))}j.fontSize=`calc(${$}px * var(--scale-factor))`,j.color=h.Util.makeHexColor(f[0],f[1],f[2]),this.data.textAlignment!==null&&(j.textAlign=e[this.data.textAlignment])}_setRequired(R,e){e?R.setAttribute("required",!0):R.removeAttribute("required"),R.setAttribute("aria-required",e)}}class w extends _{constructor(R){const e=R.renderForms||!R.data.hasAppearance&&!!R.data.fieldValue;super(R,{isRenderable:e})}setPropertyOnSiblings(R,e,f,D){const j=this.annotationStorage;for(const $ of this._getElementsByName(R.name,R.id))$.domElement&&($.domElement[e]=f),j.setValue($.id,{[D]:f})}render(){var D,j;const R=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let f=null;if(this.renderForms){const $=R.getValue(e,{value:this.data.fieldValue});let et=$.value||"";const gt=R.getValue(e,{charLimit:this.data.maxLen}).charLimit;gt&&et.length>gt&&(et=et.slice(0,gt));let kt=$.formattedValue||((D=this.data.textContent)==null?void 0:D.join(`
`))||null;kt&&this.data.comb&&(kt=kt.replaceAll(/\s+/g,""));const xt={userValue:et,formattedValue:kt,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(f=document.createElement("textarea"),f.textContent=kt??et,this.data.doNotScroll&&(f.style.overflowY="hidden")):(f=document.createElement("input"),f.type="text",f.setAttribute("value",kt??et),this.data.doNotScroll&&(f.style.overflowX="hidden")),this.data.hasOwnCanvas&&(f.hidden=!0),L.add(f),f.setAttribute("data-element-id",e),f.disabled=this.data.readOnly,f.name=this.data.fieldName,f.tabIndex=k,this._setRequired(f,this.data.required),gt&&(f.maxLength=gt),f.addEventListener("input",Mt=>{R.setValue(e,{value:Mt.target.value}),this.setPropertyOnSiblings(f,"value",Mt.target.value,"value"),xt.formattedValue=null}),f.addEventListener("resetform",Mt=>{const Rt=this.data.defaultFieldValue??"";f.value=xt.userValue=Rt,xt.formattedValue=null});let zt=Mt=>{const{formattedValue:Rt}=xt;Rt!=null&&(Mt.target.value=Rt),Mt.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){f.addEventListener("focus",Rt=>{if(xt.focused)return;const{target:Ut}=Rt;xt.userValue&&(Ut.value=xt.userValue),xt.lastCommittedValue=Ut.value,xt.commitKey=1,xt.focused=!0}),f.addEventListener("updatefromsandbox",Rt=>{this.showElementAndHideCanvas(Rt.target);const Ut={value($t){xt.userValue=$t.detail.value??"",R.setValue(e,{value:xt.userValue.toString()}),$t.target.value=xt.userValue},formattedValue($t){const{formattedValue:Kt}=$t.detail;xt.formattedValue=Kt,Kt!=null&&$t.target!==document.activeElement&&($t.target.value=Kt),R.setValue(e,{formattedValue:Kt})},selRange($t){$t.target.setSelectionRange(...$t.detail.selRange)},charLimit:$t=>{var ie;const{charLimit:Kt}=$t.detail,{target:Qt}=$t;if(Kt===0){Qt.removeAttribute("maxLength");return}Qt.setAttribute("maxLength",Kt);let se=xt.userValue;!se||se.length<=Kt||(se=se.slice(0,Kt),Qt.value=xt.userValue=se,R.setValue(e,{value:se}),(ie=this.linkService.eventBus)==null||ie.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:se,willCommit:!0,commitKey:1,selStart:Qt.selectionStart,selEnd:Qt.selectionEnd}}))}};this._dispatchEventFromSandbox(Ut,Rt)}),f.addEventListener("keydown",Rt=>{var Kt;xt.commitKey=1;let Ut=-1;if(Rt.key==="Escape"?Ut=0:Rt.key==="Enter"&&!this.data.multiLine?Ut=2:Rt.key==="Tab"&&(xt.commitKey=3),Ut===-1)return;const{value:$t}=Rt.target;xt.lastCommittedValue!==$t&&(xt.lastCommittedValue=$t,xt.userValue=$t,(Kt=this.linkService.eventBus)==null||Kt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:$t,willCommit:!0,commitKey:Ut,selStart:Rt.target.selectionStart,selEnd:Rt.target.selectionEnd}}))});const Mt=zt;zt=null,f.addEventListener("blur",Rt=>{var $t;if(!xt.focused||!Rt.relatedTarget)return;xt.focused=!1;const{value:Ut}=Rt.target;xt.userValue=Ut,xt.lastCommittedValue!==Ut&&(($t=this.linkService.eventBus)==null||$t.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:Ut,willCommit:!0,commitKey:xt.commitKey,selStart:Rt.target.selectionStart,selEnd:Rt.target.selectionEnd}})),Mt(Rt)}),(j=this.data.actions)!=null&&j.Keystroke&&f.addEventListener("beforeinput",Rt=>{var oe;xt.lastCommittedValue=null;const{data:Ut,target:$t}=Rt,{value:Kt,selectionStart:Qt,selectionEnd:se}=$t;let ie=Qt,ne=se;switch(Rt.inputType){case"deleteWordBackward":{const le=Kt.substring(0,Qt).match(/\w*[^\w]*$/);le&&(ie-=le[0].length);break}case"deleteWordForward":{const le=Kt.substring(Qt).match(/^[^\w]*\w*/);le&&(ne+=le[0].length);break}case"deleteContentBackward":Qt===se&&(ie-=1);break;case"deleteContentForward":Qt===se&&(ne+=1);break}Rt.preventDefault(),(oe=this.linkService.eventBus)==null||oe.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:Kt,change:Ut||"",willCommit:!1,selStart:ie,selEnd:ne}})}),this._setEventListeners(f,xt,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],Rt=>Rt.target.value)}if(zt&&f.addEventListener("blur",zt),this.data.comb){const Rt=(this.data.rect[2]-this.data.rect[0])/gt;f.classList.add("comb"),f.style.letterSpacing=`calc(${Rt}px * var(--scale-factor) - 1ch)`}}else f=document.createElement("div"),f.textContent=this.data.fieldValue,f.style.verticalAlign="middle",f.style.display="table-cell";return this._setTextStyle(f),this._setBackgroundColor(f),this._setDefaultPropertiesFromJS(f),this.container.append(f),this.container}}class C extends _{constructor(R){super(R,{isRenderable:!!R.data.hasOwnCanvas})}}class A extends _{constructor(R){super(R,{isRenderable:R.renderForms})}render(){const R=this.annotationStorage,e=this.data,f=e.id;let D=R.getValue(f,{value:e.exportValue===e.fieldValue}).value;typeof D=="string"&&(D=D!=="Off",R.setValue(f,{value:D})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const j=document.createElement("input");return L.add(j),j.setAttribute("data-element-id",f),j.disabled=e.readOnly,this._setRequired(j,this.data.required),j.type="checkbox",j.name=e.fieldName,D&&j.setAttribute("checked",!0),j.setAttribute("exportValue",e.exportValue),j.tabIndex=k,j.addEventListener("change",$=>{const{name:et,checked:gt}=$.target;for(const kt of this._getElementsByName(et,f)){const xt=gt&&kt.exportValue===e.exportValue;kt.domElement&&(kt.domElement.checked=xt),R.setValue(kt.id,{value:xt})}R.setValue(f,{value:gt})}),j.addEventListener("resetform",$=>{const et=e.defaultFieldValue||"Off";$.target.checked=et===e.exportValue}),this.enableScripting&&this.hasJSActions&&(j.addEventListener("updatefromsandbox",$=>{const et={value(gt){gt.target.checked=gt.detail.value!=="Off",R.setValue(f,{value:gt.target.checked})}};this._dispatchEventFromSandbox(et,$)}),this._setEventListeners(j,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],$=>$.target.checked)),this._setBackgroundColor(j),this._setDefaultPropertiesFromJS(j),this.container.append(j),this.container}}class a extends _{constructor(R){super(R,{isRenderable:R.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const R=this.annotationStorage,e=this.data,f=e.id;let D=R.getValue(f,{value:e.fieldValue===e.buttonValue}).value;typeof D=="string"&&(D=D!==e.buttonValue,R.setValue(f,{value:D}));const j=document.createElement("input");if(L.add(j),j.setAttribute("data-element-id",f),j.disabled=e.readOnly,this._setRequired(j,this.data.required),j.type="radio",j.name=e.fieldName,D&&j.setAttribute("checked",!0),j.tabIndex=k,j.addEventListener("change",$=>{const{name:et,checked:gt}=$.target;for(const kt of this._getElementsByName(et,f))R.setValue(kt.id,{value:!1});R.setValue(f,{value:gt})}),j.addEventListener("resetform",$=>{const et=e.defaultFieldValue;$.target.checked=et!=null&&et===e.buttonValue}),this.enableScripting&&this.hasJSActions){const $=e.buttonValue;j.addEventListener("updatefromsandbox",et=>{const gt={value:kt=>{const xt=$===kt.detail.value;for(const zt of this._getElementsByName(kt.target.name)){const Mt=xt&&zt.id===f;zt.domElement&&(zt.domElement.checked=Mt),R.setValue(zt.id,{value:Mt})}}};this._dispatchEventFromSandbox(gt,et)}),this._setEventListeners(j,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],et=>et.target.checked)}return this._setBackgroundColor(j),this._setDefaultPropertiesFromJS(j),this.container.append(j),this.container}}class l extends y{constructor(R){super(R,{ignoreBorder:R.data.hasAppearance})}render(){const R=super.render();R.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(R.title=this.data.alternativeText);const e=R.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",f=>{this._dispatchEventFromSandbox({},f)})),R}}class P extends _{constructor(R){super(R,{isRenderable:R.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const R=this.annotationStorage,e=this.data.id,f=R.getValue(e,{value:this.data.fieldValue}),D=document.createElement("select");L.add(D),D.setAttribute("data-element-id",e),D.disabled=this.data.readOnly,this._setRequired(D,this.data.required),D.name=this.data.fieldName,D.tabIndex=k;let j=this.data.combo&&this.data.options.length>0;this.data.combo||(D.size=this.data.options.length,this.data.multiSelect&&(D.multiple=!0)),D.addEventListener("resetform",xt=>{const zt=this.data.defaultFieldValue;for(const Mt of D.options)Mt.selected=Mt.value===zt});for(const xt of this.data.options){const zt=document.createElement("option");zt.textContent=xt.displayValue,zt.value=xt.exportValue,f.value.includes(xt.exportValue)&&(zt.setAttribute("selected",!0),j=!1),D.append(zt)}let $=null;if(j){const xt=document.createElement("option");xt.value=" ",xt.setAttribute("hidden",!0),xt.setAttribute("selected",!0),D.prepend(xt),$=()=>{xt.remove(),D.removeEventListener("input",$),$=null},D.addEventListener("input",$)}const et=xt=>{const zt=xt?"value":"textContent",{options:Mt,multiple:Rt}=D;return Rt?Array.prototype.filter.call(Mt,Ut=>Ut.selected).map(Ut=>Ut[zt]):Mt.selectedIndex===-1?null:Mt[Mt.selectedIndex][zt]};let gt=et(!1);const kt=xt=>{const zt=xt.target.options;return Array.prototype.map.call(zt,Mt=>({displayValue:Mt.textContent,exportValue:Mt.value}))};return this.enableScripting&&this.hasJSActions?(D.addEventListener("updatefromsandbox",xt=>{const zt={value(Mt){$==null||$();const Rt=Mt.detail.value,Ut=new Set(Array.isArray(Rt)?Rt:[Rt]);for(const $t of D.options)$t.selected=Ut.has($t.value);R.setValue(e,{value:et(!0)}),gt=et(!1)},multipleSelection(Mt){D.multiple=!0},remove(Mt){const Rt=D.options,Ut=Mt.detail.remove;Rt[Ut].selected=!1,D.remove(Ut),Rt.length>0&&Array.prototype.findIndex.call(Rt,Kt=>Kt.selected)===-1&&(Rt[0].selected=!0),R.setValue(e,{value:et(!0),items:kt(Mt)}),gt=et(!1)},clear(Mt){for(;D.length!==0;)D.remove(0);R.setValue(e,{value:null,items:[]}),gt=et(!1)},insert(Mt){const{index:Rt,displayValue:Ut,exportValue:$t}=Mt.detail.insert,Kt=D.children[Rt],Qt=document.createElement("option");Qt.textContent=Ut,Qt.value=$t,Kt?Kt.before(Qt):D.append(Qt),R.setValue(e,{value:et(!0),items:kt(Mt)}),gt=et(!1)},items(Mt){const{items:Rt}=Mt.detail;for(;D.length!==0;)D.remove(0);for(const Ut of Rt){const{displayValue:$t,exportValue:Kt}=Ut,Qt=document.createElement("option");Qt.textContent=$t,Qt.value=Kt,D.append(Qt)}D.options.length>0&&(D.options[0].selected=!0),R.setValue(e,{value:et(!0),items:kt(Mt)}),gt=et(!1)},indices(Mt){const Rt=new Set(Mt.detail.indices);for(const Ut of Mt.target.options)Ut.selected=Rt.has(Ut.index);R.setValue(e,{value:et(!0)}),gt=et(!1)},editable(Mt){Mt.target.disabled=!Mt.detail.editable}};this._dispatchEventFromSandbox(zt,xt)}),D.addEventListener("input",xt=>{var Mt;const zt=et(!0);R.setValue(e,{value:zt}),xt.preventDefault(),(Mt=this.linkService.eventBus)==null||Mt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:gt,changeEx:zt,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(D,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],xt=>xt.target.value)):D.addEventListener("input",function(xt){R.setValue(e,{value:et(!0)})}),this.data.combo&&this._setTextStyle(D),this._setBackgroundColor(D),this._setDefaultPropertiesFromJS(D),this.container.append(D),this.container}}class p extends v{constructor(R){const{data:e,elements:f}=R;super(R,{isRenderable:v._hasPopupData(e)}),this.elements=f}render(){this.container.classList.add("popupAnnotation");const R=new r({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const f of this.elements)f.popup=R,e.push(f.data.id),f.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(f=>`${h.AnnotationPrefix}${f}`).join(",")),this.container}}class r{constructor({container:R,color:e,elements:f,titleObj:D,modificationDate:j,contentsObj:$,richText:et,parent:gt,rect:kt,parentRect:xt,open:zt}){I(this,jt);I(this,Ht);I(this,Vt);I(this,qt);I(this,N,null);I(this,tt,W(this,jt,Li).bind(this));I(this,Q,W(this,qt,oi).bind(this));I(this,st,W(this,Vt,ai).bind(this));I(this,ct,W(this,Ht,Fe).bind(this));I(this,yt,null);I(this,dt,null);I(this,Ft,null);I(this,Bt,null);I(this,St,null);I(this,Dt,null);I(this,ut,!1);I(this,K,null);I(this,J,null);I(this,ht,null);I(this,Et,null);I(this,Ct,!1);var Rt;Z(this,dt,R),Z(this,Et,D),Z(this,Ft,$),Z(this,ht,et),Z(this,St,gt),Z(this,yt,e),Z(this,J,kt),Z(this,Dt,xt),Z(this,Bt,f);const Mt=M.PDFDateString.toDateObject(j);Mt&&Z(this,N,gt.l10n.get("annotation_date_string",{date:Mt.toLocaleDateString(),time:Mt.toLocaleTimeString()})),this.trigger=f.flatMap(Ut=>Ut.getElementsToTriggerPopup());for(const Ut of this.trigger)Ut.addEventListener("click",t(this,ct)),Ut.addEventListener("mouseenter",t(this,st)),Ut.addEventListener("mouseleave",t(this,Q)),Ut.classList.add("popupTriggerArea");for(const Ut of f)(Rt=Ut.container)==null||Rt.addEventListener("keydown",t(this,tt));t(this,dt).hidden=!0,zt&&W(this,Ht,Fe).call(this)}render(){if(t(this,K))return;const{page:{view:R},viewport:{rawDims:{pageWidth:e,pageHeight:f,pageX:D,pageY:j}}}=t(this,St),$=Z(this,K,document.createElement("div"));if($.className="popup",t(this,yt)){const ie=$.style.outlineColor=h.Util.makeHexColor(...t(this,yt));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?$.style.backgroundColor=`color-mix(in srgb, ${ie} 30%, white)`:$.style.backgroundColor=h.Util.makeHexColor(...t(this,yt).map(oe=>Math.floor(.7*(255-oe)+oe)))}const et=document.createElement("span");et.className="header";const gt=document.createElement("h1");if(et.append(gt),{dir:gt.dir,str:gt.textContent}=t(this,Et),$.append(et),t(this,N)){const ie=document.createElement("span");ie.classList.add("popupDate"),t(this,N).then(ne=>{ie.textContent=ne}),et.append(ie)}const kt=t(this,Ft),xt=t(this,ht);if(xt!=null&&xt.str&&(!(kt!=null&&kt.str)||kt.str===xt.str))U.XfaLayer.render({xfaHtml:xt.html,intent:"richText",div:$}),$.lastChild.classList.add("richText","popupContent");else{const ie=this._formatContents(kt);$.append(ie)}let zt=!!t(this,Dt),Mt=zt?t(this,Dt):t(this,J);for(const ie of t(this,Bt))if(!Mt||h.Util.intersect(ie.data.rect,Mt)!==null){Mt=ie.data.rect,zt=!0;break}const Rt=h.Util.normalizeRect([Mt[0],R[3]-Mt[1]+R[1],Mt[2],R[3]-Mt[3]+R[1]]),Ut=5,$t=zt?Mt[2]-Mt[0]+Ut:0,Kt=Rt[0]+$t,Qt=Rt[1],{style:se}=t(this,dt);se.left=`${100*(Kt-D)/e}%`,se.top=`${100*(Qt-j)/f}%`,t(this,dt).append($)}_formatContents({str:R,dir:e}){const f=document.createElement("p");f.classList.add("popupContent"),f.dir=e;const D=R.split(/(?:\r\n?|\n)/);for(let j=0,$=D.length;j<$;++j){const et=D[j];f.append(document.createTextNode(et)),j<$-1&&f.append(document.createElement("br"))}return f}forceHide(){Z(this,Ct,this.isVisible),t(this,Ct)&&(t(this,dt).hidden=!0)}maybeShow(){t(this,Ct)&&(Z(this,Ct,!1),t(this,dt).hidden=!1)}get isVisible(){return t(this,dt).hidden===!1}}N=new WeakMap,tt=new WeakMap,Q=new WeakMap,st=new WeakMap,ct=new WeakMap,yt=new WeakMap,dt=new WeakMap,Ft=new WeakMap,Bt=new WeakMap,St=new WeakMap,Dt=new WeakMap,ut=new WeakMap,K=new WeakMap,J=new WeakMap,ht=new WeakMap,Et=new WeakMap,Ct=new WeakMap,jt=new WeakSet,Li=function(R){R.altKey||R.shiftKey||R.ctrlKey||R.metaKey||(R.key==="Enter"||R.key==="Escape"&&t(this,ut))&&W(this,Ht,Fe).call(this)},Ht=new WeakSet,Fe=function(){Z(this,ut,!t(this,ut)),t(this,ut)?(W(this,Vt,ai).call(this),t(this,dt).addEventListener("click",t(this,ct)),t(this,dt).addEventListener("keydown",t(this,tt))):(W(this,qt,oi).call(this),t(this,dt).removeEventListener("click",t(this,ct)),t(this,dt).removeEventListener("keydown",t(this,tt)))},Vt=new WeakSet,ai=function(){t(this,K)||this.render(),this.isVisible?t(this,ut)&&t(this,dt).classList.add("focused"):(t(this,dt).hidden=!1,t(this,dt).style.zIndex=parseInt(t(this,dt).style.zIndex)+1e3)},qt=new WeakSet,oi=function(){t(this,dt).classList.remove("focused"),!(t(this,ut)||!this.isVisible)&&(t(this,dt).hidden=!0,t(this,dt).style.zIndex=parseInt(t(this,dt).style.zIndex)-1e3)};class T extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0}),this.textContent=R.data.textContent,this.textPosition=R.data.textPosition,this.annotationEditorType=h.AnnotationEditorType.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const R=document.createElement("div");R.classList.add("annotationTextContent"),R.setAttribute("role","comment");for(const e of this.textContent){const f=document.createElement("span");f.textContent=e,R.append(f)}this.container.append(R)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}d.FreeTextAnnotationElement=T;class m extends v{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});I(this,Y,null)}render(){this.container.classList.add("lineAnnotation");const e=this.data,{width:f,height:D}=O(e.rect),j=this.svgFactory.create(f,D,!0),$=Z(this,Y,this.svgFactory.createElement("svg:line"));return $.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),$.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),$.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),$.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),$.setAttribute("stroke-width",e.borderStyle.width||1),$.setAttribute("stroke","transparent"),$.setAttribute("fill","transparent"),j.append($),this.container.append(j),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,Y)}addHighlightArea(){this.container.classList.add("highlightArea")}}Y=new WeakMap;class B extends v{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});I(this,G,null)}render(){this.container.classList.add("squareAnnotation");const e=this.data,{width:f,height:D}=O(e.rect),j=this.svgFactory.create(f,D,!0),$=e.borderStyle.width,et=Z(this,G,this.svgFactory.createElement("svg:rect"));return et.setAttribute("x",$/2),et.setAttribute("y",$/2),et.setAttribute("width",f-$),et.setAttribute("height",D-$),et.setAttribute("stroke-width",$||1),et.setAttribute("stroke","transparent"),et.setAttribute("fill","transparent"),j.append(et),this.container.append(j),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,G)}addHighlightArea(){this.container.classList.add("highlightArea")}}G=new WeakMap;class z extends v{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});I(this,mt,null)}render(){this.container.classList.add("circleAnnotation");const e=this.data,{width:f,height:D}=O(e.rect),j=this.svgFactory.create(f,D,!0),$=e.borderStyle.width,et=Z(this,mt,this.svgFactory.createElement("svg:ellipse"));return et.setAttribute("cx",f/2),et.setAttribute("cy",D/2),et.setAttribute("rx",f/2-$/2),et.setAttribute("ry",D/2-$/2),et.setAttribute("stroke-width",$||1),et.setAttribute("stroke","transparent"),et.setAttribute("fill","transparent"),j.append(et),this.container.append(j),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,mt)}addHighlightArea(){this.container.classList.add("highlightArea")}}mt=new WeakMap;class E extends v{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});I(this,_t,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const e=this.data,{width:f,height:D}=O(e.rect),j=this.svgFactory.create(f,D,!0);let $=[];for(const gt of e.vertices){const kt=gt.x-e.rect[0],xt=e.rect[3]-gt.y;$.push(kt+","+xt)}$=$.join(" ");const et=Z(this,_t,this.svgFactory.createElement(this.svgElementName));return et.setAttribute("points",$),et.setAttribute("stroke-width",e.borderStyle.width||1),et.setAttribute("stroke","transparent"),et.setAttribute("fill","transparent"),j.append(et),this.container.append(j),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,_t)}addHighlightArea(){this.container.classList.add("highlightArea")}}_t=new WeakMap;class V extends E{constructor(R){super(R),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class it extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class nt extends v{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});I(this,te,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=h.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const e=this.data,{width:f,height:D}=O(e.rect),j=this.svgFactory.create(f,D,!0);for(const $ of e.inkLists){let et=[];for(const kt of $){const xt=kt.x-e.rect[0],zt=e.rect[3]-kt.y;et.push(`${xt},${zt}`)}et=et.join(" ");const gt=this.svgFactory.createElement(this.svgElementName);t(this,te).push(gt),gt.setAttribute("points",et),gt.setAttribute("stroke-width",e.borderStyle.width||1),gt.setAttribute("stroke","transparent"),gt.setAttribute("fill","transparent"),!e.popupRef&&this.hasPopupData&&this._createPopup(),j.append(gt)}return this.container.append(j),this.container}getElementsToTriggerPopup(){return t(this,te)}addHighlightArea(){this.container.classList.add("highlightArea")}}te=new WeakMap,d.InkAnnotationElement=nt;class H extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class lt extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class pt extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class wt extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Pt extends v{constructor(R){super(R,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}d.StampAnnotationElement=Pt;class S extends v{constructor(e){var j;super(e,{isRenderable:!0});I(this,q);I(this,Zt,null);const{filename:f,content:D}=this.data.file;this.filename=(0,M.getFilenameFromUrl)(f,!0),this.content=D,(j=this.linkService.eventBus)==null||j.dispatch("fileattachmentannotation",{source:this,filename:f,content:D})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:f}=this;let D;f.hasAppearance||f.fillAlpha===0?D=document.createElement("div"):(D=document.createElement("img"),D.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(f.name)?"paperclip":"pushpin"}.svg`,f.fillAlpha&&f.fillAlpha<1&&(D.style=`filter: opacity(${Math.round(f.fillAlpha*100)}%);`)),D.addEventListener("dblclick",W(this,q,li).bind(this)),Z(this,Zt,D);const{isMac:j}=h.FeatureTest.platform;return e.addEventListener("keydown",$=>{$.key==="Enter"&&(j?$.metaKey:$.ctrlKey)&&W(this,q,li).call(this)}),!f.popupRef&&this.hasPopupData?this._createPopup():D.classList.add("popupTriggerArea"),e.append(D),e}getElementsToTriggerPopup(){return t(this,Zt)}addHighlightArea(){this.container.classList.add("highlightArea")}}Zt=new WeakMap,q=new WeakSet,li=function(){var e;(e=this.downloadManager)==null||e.openOrDownloadData(this.container,this.content,this.filename)};class i{constructor({div:R,accessibilityManager:e,annotationCanvasMap:f,l10n:D,page:j,viewport:$}){I(this,Nt);I(this,bt);I(this,Lt,null);I(this,Tt,null);I(this,Ot,new Map);this.div=R,Z(this,Lt,e),Z(this,Tt,f),this.l10n=D,this.page=j,this.viewport=$,this.zIndex=0,this.l10n||(this.l10n=ft.NullL10n)}async render(R){const{annotations:e}=R,f=this.div;(0,M.setLayerDimensions)(f,this.viewport);const D=new Map,j={data:null,layer:f,linkService:R.linkService,downloadManager:R.downloadManager,imageResourcesPath:R.imageResourcesPath||"",renderForms:R.renderForms!==!1,svgFactory:new M.DOMSVGFactory,annotationStorage:R.annotationStorage||new at.AnnotationStorage,enableScripting:R.enableScripting===!0,hasJSActions:R.hasJSActions,fieldObjects:R.fieldObjects,parent:this,elements:null};for(const $ of e){if($.noHTML)continue;const et=$.annotationType===h.AnnotationType.POPUP;if(et){const xt=D.get($.id);if(!xt)continue;j.elements=xt}else{const{width:xt,height:zt}=O($.rect);if(xt<=0||zt<=0)continue}j.data=$;const gt=x.create(j);if(!gt.isRenderable)continue;if(!et&&$.popupRef){const xt=D.get($.popupRef);xt?xt.push(gt):D.set($.popupRef,[gt])}gt.annotationEditorType>0&&t(this,Ot).set(gt.data.id,gt);const kt=gt.render();$.hidden&&(kt.style.visibility="hidden"),W(this,Nt,Oi).call(this,kt,$.id)}W(this,bt,ci).call(this),await this.l10n.translate(f)}update({viewport:R}){const e=this.div;this.viewport=R,(0,M.setLayerDimensions)(e,{rotation:R.rotation}),W(this,bt,ci).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(t(this,Ot).values())}getEditableAnnotation(R){return t(this,Ot).get(R)}}Lt=new WeakMap,Tt=new WeakMap,Ot=new WeakMap,Nt=new WeakSet,Oi=function(R,e){var D;const f=R.firstChild||R;f.id=`${h.AnnotationPrefix}${e}`,this.div.append(R),(D=t(this,Lt))==null||D.moveElementInDOM(this.div,R,f,!1)},bt=new WeakSet,ci=function(){if(!t(this,Tt))return;const R=this.div;for(const[e,f]of t(this,Tt)){const D=R.querySelector(`[data-annotation-id="${e}"]`);if(!D)continue;const{firstChild:j}=D;j?j.nodeName==="CANVAS"?j.replaceWith(f):j.before(f):D.append(f)}t(this,Tt).clear()},d.AnnotationLayer=i},(At,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.ColorConverters=void 0;function rt(at){return Math.floor(Math.max(0,Math.min(1,at))*255).toString(16).padStart(2,"0")}function h(at){return Math.max(0,Math.min(255,255*at))}class M{static CMYK_G([X,ft,U,k]){return["G",1-Math.min(1,.3*X+.59*U+.11*ft+k)]}static G_CMYK([X]){return["CMYK",0,0,0,1-X]}static G_RGB([X]){return["RGB",X,X,X]}static G_rgb([X]){return X=h(X),[X,X,X]}static G_HTML([X]){const ft=rt(X);return`#${ft}${ft}${ft}`}static RGB_G([X,ft,U]){return["G",.3*X+.59*ft+.11*U]}static RGB_rgb(X){return X.map(h)}static RGB_HTML(X){return`#${X.map(rt).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([X,ft,U,k]){return["RGB",1-Math.min(1,X+k),1-Math.min(1,U+k),1-Math.min(1,ft+k)]}static CMYK_rgb([X,ft,U,k]){return[h(1-Math.min(1,X+k)),h(1-Math.min(1,U+k)),h(1-Math.min(1,ft+k))]}static CMYK_HTML(X){const ft=this.CMYK_RGB(X).slice(1);return this.RGB_HTML(ft)}static RGB_CMYK([X,ft,U]){const k=1-X,g=1-ft,L=1-U,O=Math.min(k,g,L);return["CMYK",k,g,L,O]}}d.ColorConverters=M},(At,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.NullL10n=void 0,d.getL10nFallback=h;const rt={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};rt.print_progress_percent="{{progress}}%";function h(X,ft){switch(X){case"find_match_count":X=`find_match_count[${ft.total===1?"one":"other"}]`;break;case"find_match_count_limit":X=`find_match_count_limit[${ft.limit===1?"one":"other"}]`;break}return rt[X]||""}function M(X,ft){return ft?X.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,(U,k)=>k in ft?ft[k]:"{{"+k+"}}"):X}const at={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(X,ft=null,U=h(X,ft)){return M(U,ft)},async translate(X){}};d.NullL10n=at},(At,d,rt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.XfaLayer=void 0;var h=rt(25);class M{static setupStorage(X,ft,U,k,g){const L=k.getValue(ft,{value:null});switch(U.name){case"textarea":if(L.value!==null&&(X.textContent=L.value),g==="print")break;X.addEventListener("input",O=>{k.setValue(ft,{value:O.target.value})});break;case"input":if(U.attributes.type==="radio"||U.attributes.type==="checkbox"){if(L.value===U.attributes.xfaOn?X.setAttribute("checked",!0):L.value===U.attributes.xfaOff&&X.removeAttribute("checked"),g==="print")break;X.addEventListener("change",O=>{k.setValue(ft,{value:O.target.checked?O.target.getAttribute("xfaOn"):O.target.getAttribute("xfaOff")})})}else{if(L.value!==null&&X.setAttribute("value",L.value),g==="print")break;X.addEventListener("input",O=>{k.setValue(ft,{value:O.target.value})})}break;case"select":if(L.value!==null){X.setAttribute("value",L.value);for(const O of U.children)O.attributes.value===L.value?O.attributes.selected=!0:O.attributes.hasOwnProperty("selected")&&delete O.attributes.selected}X.addEventListener("input",O=>{const x=O.target.options,v=x.selectedIndex===-1?"":x[x.selectedIndex].value;k.setValue(ft,{value:v})});break}}static setAttributes({html:X,element:ft,storage:U=null,intent:k,linkService:g}){const{attributes:L}=ft,O=X instanceof HTMLAnchorElement;L.type==="radio"&&(L.name=`${L.name}-${k}`);for(const[x,v]of Object.entries(L))if(v!=null)switch(x){case"class":v.length&&X.setAttribute(x,v.join(" "));break;case"dataId":break;case"id":X.setAttribute("data-element-id",v);break;case"style":Object.assign(X.style,v);break;case"textContent":X.textContent=v;break;default:(!O||x!=="href"&&x!=="newWindow")&&X.setAttribute(x,v)}O&&g.addLinkAttributes(X,L.href,L.newWindow),U&&L.dataId&&this.setupStorage(X,L.dataId,ft,U)}static render(X){var y;const ft=X.annotationStorage,U=X.linkService,k=X.xfaHtml,g=X.intent||"display",L=document.createElement(k.name);k.attributes&&this.setAttributes({html:L,element:k,intent:g,linkService:U});const O=[[k,-1,L]],x=X.div;if(x.append(L),X.viewport){const u=`matrix(${X.viewport.transform.join(",")})`;x.style.transform=u}g!=="richText"&&x.setAttribute("class","xfaLayer xfaFont");const v=[];for(;O.length>0;){const[u,_,w]=O.at(-1);if(_+1===u.children.length){O.pop();continue}const C=u.children[++O.at(-1)[1]];if(C===null)continue;const{name:A}=C;if(A==="#text"){const l=document.createTextNode(C.value);v.push(l),w.append(l);continue}const a=(y=C==null?void 0:C.attributes)!=null&&y.xmlns?document.createElementNS(C.attributes.xmlns,A):document.createElement(A);if(w.append(a),C.attributes&&this.setAttributes({html:a,element:C,storage:ft,intent:g,linkService:U}),C.children&&C.children.length>0)O.push([C,-1,a]);else if(C.value){const l=document.createTextNode(C.value);h.XfaText.shouldBuildText(A)&&v.push(l),a.append(l)}}for(const u of x.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))u.setAttribute("readOnly",!0);return{textDivs:v}}static update(X){const ft=`matrix(${X.viewport.transform.join(",")})`;X.div.style.transform=ft,X.div.hidden=!1}}d.XfaLayer=M},(At,d,rt)=>{var k,g,L,O,x,v,y,u,_,w,C,A,a,l,P,Ni,r,Bi,m,Ui,z,ji,V,hi,nt,Hi,lt,di,wt,Wi,S,Gi,n,zi,o,Xi,b,Vi,N,ae,Q,ui,ct,Me,dt,Re,Bt,fe,Dt,fi,K,De,ht,$i,Ct,pi,Gt,qi,Xt,Yi,Wt,gi,ot,Ie,G,pe;Object.defineProperty(d,"__esModule",{value:!0}),d.InkEditor=void 0;var h=rt(1),M=rt(4),at=rt(29),X=rt(6),ft=rt(5);const _t=class _t extends M.AnnotationEditor{constructor(q){super({...q,name:"inkEditor"});I(this,P);I(this,r);I(this,m);I(this,z);I(this,V);I(this,nt);I(this,lt);I(this,wt);I(this,S);I(this,n);I(this,o);I(this,b);I(this,N);I(this,Q);I(this,ct);I(this,dt);I(this,Bt);I(this,Dt);I(this,K);I(this,Xt);I(this,Wt);I(this,ot);I(this,G);I(this,k,0);I(this,g,0);I(this,L,this.canvasPointermove.bind(this));I(this,O,this.canvasPointerleave.bind(this));I(this,x,this.canvasPointerup.bind(this));I(this,v,this.canvasPointerdown.bind(this));I(this,y,new Path2D);I(this,u,!1);I(this,_,!1);I(this,w,!1);I(this,C,null);I(this,A,0);I(this,a,0);I(this,l,null);this.color=q.color||null,this.thickness=q.thickness||null,this.opacity=q.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(q){M.AnnotationEditor.initialize(q,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(q,vt){switch(q){case h.AnnotationEditorParamsType.INK_THICKNESS:_t._defaultThickness=vt;break;case h.AnnotationEditorParamsType.INK_COLOR:_t._defaultColor=vt;break;case h.AnnotationEditorParamsType.INK_OPACITY:_t._defaultOpacity=vt/100;break}}updateParams(q,vt){switch(q){case h.AnnotationEditorParamsType.INK_THICKNESS:W(this,P,Ni).call(this,vt);break;case h.AnnotationEditorParamsType.INK_COLOR:W(this,r,Bi).call(this,vt);break;case h.AnnotationEditorParamsType.INK_OPACITY:W(this,m,Ui).call(this,vt);break}}static get defaultPropertiesToUpdate(){return[[h.AnnotationEditorParamsType.INK_THICKNESS,_t._defaultThickness],[h.AnnotationEditorParamsType.INK_COLOR,_t._defaultColor||M.AnnotationEditor._defaultLineColor],[h.AnnotationEditorParamsType.INK_OPACITY,Math.round(_t._defaultOpacity*100)]]}get propertiesToUpdate(){return[[h.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||_t._defaultThickness],[h.AnnotationEditorParamsType.INK_COLOR,this.color||_t._defaultColor||M.AnnotationEditor._defaultLineColor],[h.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??_t._defaultOpacity))]]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.canvas||(W(this,ct,Me).call(this),W(this,dt,Re).call(this)),this.isAttachedToDOM||(this.parent.add(this),W(this,Bt,fe).call(this)),W(this,G,pe).call(this)))}remove(){this.canvas!==null&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,t(this,C).disconnect(),Z(this,C,null),super.remove())}setParent(q){!this.parent&&q?this._uiManager.removeShouldRescale(this):this.parent&&q===null&&this._uiManager.addShouldRescale(this),super.setParent(q)}onScaleChanging(){const[q,vt]=this.parentDimensions,Lt=this.width*q,Tt=this.height*vt;this.setDimensions(Lt,Tt)}enableEditMode(){t(this,u)||this.canvas===null||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",t(this,v)))}disableEditMode(){!this.isInEditMode()||this.canvas===null||(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",t(this,v)))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return this.paths.length===0||this.paths.length===1&&this.paths[0].length===0}commit(){t(this,u)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),Z(this,u,!0),this.div.classList.add("disabled"),W(this,G,pe).call(this,!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(q){this._focusEventsAllowed&&(super.focusin(q),this.enableEditMode())}canvasPointerdown(q){q.button!==0||!this.isInEditMode()||t(this,u)||(this.setInForeground(),q.preventDefault(),q.type!=="mouse"&&this.div.focus(),W(this,nt,Hi).call(this,q.offsetX,q.offsetY))}canvasPointermove(q){q.preventDefault(),W(this,lt,di).call(this,q.offsetX,q.offsetY)}canvasPointerup(q){q.preventDefault(),W(this,Q,ui).call(this,q)}canvasPointerleave(q){W(this,Q,ui).call(this,q)}get isResizable(){return!this.isEmpty()&&t(this,u)}render(){if(this.div)return this.div;let q,vt;this.width&&(q=this.x,vt=this.y),super.render(),M.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then(Jt=>{var bt;return(bt=this.div)==null?void 0:bt.setAttribute("aria-label",Jt)});const[Lt,Tt,Ot,Nt]=W(this,z,ji).call(this);if(this.setAt(Lt,Tt,0,0),this.setDims(Ot,Nt),W(this,ct,Me).call(this),this.width){const[Jt,bt]=this.parentDimensions;this.setAspectRatio(this.width*Jt,this.height*bt),this.setAt(q*Jt,vt*bt,this.width*Jt,this.height*bt),Z(this,w,!0),W(this,Bt,fe).call(this),this.setDims(this.width*Jt,this.height*bt),W(this,N,ae).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return W(this,dt,Re).call(this),this.div}setDimensions(q,vt){const Lt=Math.round(q),Tt=Math.round(vt);if(t(this,A)===Lt&&t(this,a)===Tt)return;Z(this,A,Lt),Z(this,a,Tt),this.canvas.style.visibility="hidden";const[Ot,Nt]=this.parentDimensions;this.width=q/Ot,this.height=vt/Nt,this.fixAndSetPosition(),t(this,u)&&W(this,Dt,fi).call(this,q,vt),W(this,Bt,fe).call(this),W(this,N,ae).call(this),this.canvas.style.visibility="visible",this.fixDims()}static deserialize(q,vt,Lt){var j,$,et;if(q instanceof at.InkAnnotationElement)return null;const Tt=super.deserialize(q,vt,Lt);Tt.thickness=q.thickness,Tt.color=h.Util.makeHexColor(...q.color),Tt.opacity=q.opacity;const[Ot,Nt]=Tt.pageDimensions,Jt=Tt.width*Ot,bt=Tt.height*Nt,Yt=Tt.parentScale,It=q.thickness/2;Z(Tt,u,!0),Z(Tt,A,Math.round(Jt)),Z(Tt,a,Math.round(bt));const{paths:R,rect:e,rotation:f}=q;for(let{bezier:gt}of R){gt=W(j=_t,Gt,qi).call(j,gt,e,f);const kt=[];Tt.paths.push(kt);let xt=Yt*(gt[0]-It),zt=Yt*(gt[1]-It);for(let Rt=2,Ut=gt.length;Rt<Ut;Rt+=6){const $t=Yt*(gt[Rt]-It),Kt=Yt*(gt[Rt+1]-It),Qt=Yt*(gt[Rt+2]-It),se=Yt*(gt[Rt+3]-It),ie=Yt*(gt[Rt+4]-It),ne=Yt*(gt[Rt+5]-It);kt.push([[xt,zt],[$t,Kt],[Qt,se],[ie,ne]]),xt=ie,zt=ne}const Mt=W(this,ht,$i).call(this,kt);Tt.bezierPath2D.push(Mt)}const D=W($=Tt,Wt,gi).call($);return Z(Tt,g,Math.max(M.AnnotationEditor.MIN_SIZE,D[2]-D[0])),Z(Tt,k,Math.max(M.AnnotationEditor.MIN_SIZE,D[3]-D[1])),W(et=Tt,Dt,fi).call(et,Jt,bt),Tt}serialize(){if(this.isEmpty())return null;const q=this.getRect(0,0),vt=M.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:h.AnnotationEditorType.INK,color:vt,thickness:this.thickness,opacity:this.opacity,paths:W(this,Xt,Yi).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,q),pageIndex:this.pageIndex,rect:q,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}};k=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakMap,w=new WeakMap,C=new WeakMap,A=new WeakMap,a=new WeakMap,l=new WeakMap,P=new WeakSet,Ni=function(q){const vt=this.thickness;this.addCommands({cmd:()=>{this.thickness=q,W(this,G,pe).call(this)},undo:()=>{this.thickness=vt,W(this,G,pe).call(this)},mustExec:!0,type:h.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})},r=new WeakSet,Bi=function(q){const vt=this.color;this.addCommands({cmd:()=>{this.color=q,W(this,N,ae).call(this)},undo:()=>{this.color=vt,W(this,N,ae).call(this)},mustExec:!0,type:h.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})},m=new WeakSet,Ui=function(q){q/=100;const vt=this.opacity;this.addCommands({cmd:()=>{this.opacity=q,W(this,N,ae).call(this)},undo:()=>{this.opacity=vt,W(this,N,ae).call(this)},mustExec:!0,type:h.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})},z=new WeakSet,ji=function(){const{parentRotation:q,parentDimensions:[vt,Lt]}=this;switch(q){case 90:return[0,Lt,Lt,vt];case 180:return[vt,Lt,vt,Lt];case 270:return[vt,0,Lt,vt];default:return[0,0,vt,Lt]}},V=new WeakSet,hi=function(){const{ctx:q,color:vt,opacity:Lt,thickness:Tt,parentScale:Ot,scaleFactor:Nt}=this;q.lineWidth=Tt*Ot/Nt,q.lineCap="round",q.lineJoin="round",q.miterLimit=10,q.strokeStyle=`${vt}${(0,ft.opacityToHex)(Lt)}`},nt=new WeakSet,Hi=function(q,vt){this.canvas.addEventListener("contextmenu",X.noContextMenu),this.canvas.addEventListener("pointerleave",t(this,O)),this.canvas.addEventListener("pointermove",t(this,L)),this.canvas.addEventListener("pointerup",t(this,x)),this.canvas.removeEventListener("pointerdown",t(this,v)),this.isEditing=!0,t(this,w)||(Z(this,w,!0),W(this,Bt,fe).call(this),this.thickness||(this.thickness=_t._defaultThickness),this.color||(this.color=_t._defaultColor||M.AnnotationEditor._defaultLineColor),this.opacity??(this.opacity=_t._defaultOpacity)),this.currentPath.push([q,vt]),Z(this,_,!1),W(this,V,hi).call(this),Z(this,l,()=>{W(this,n,zi).call(this),t(this,l)&&window.requestAnimationFrame(t(this,l))}),window.requestAnimationFrame(t(this,l))},lt=new WeakSet,di=function(q,vt){const[Lt,Tt]=this.currentPath.at(-1);if(this.currentPath.length>1&&q===Lt&&vt===Tt)return;const Ot=this.currentPath;let Nt=t(this,y);if(Ot.push([q,vt]),Z(this,_,!0),Ot.length<=2){Nt.moveTo(...Ot[0]),Nt.lineTo(q,vt);return}Ot.length===3&&(Z(this,y,Nt=new Path2D),Nt.moveTo(...Ot[0])),W(this,o,Xi).call(this,Nt,...Ot.at(-3),...Ot.at(-2),q,vt)},wt=new WeakSet,Wi=function(){if(this.currentPath.length===0)return;const q=this.currentPath.at(-1);t(this,y).lineTo(...q)},S=new WeakSet,Gi=function(q,vt){Z(this,l,null),q=Math.min(Math.max(q,0),this.canvas.width),vt=Math.min(Math.max(vt,0),this.canvas.height),W(this,lt,di).call(this,q,vt),W(this,wt,Wi).call(this);let Lt;if(this.currentPath.length!==1)Lt=W(this,b,Vi).call(this);else{const bt=[q,vt];Lt=[[bt,bt.slice(),bt.slice(),bt]]}const Tt=t(this,y),Ot=this.currentPath;this.currentPath=[],Z(this,y,new Path2D);const Nt=()=>{this.allRawPaths.push(Ot),this.paths.push(Lt),this.bezierPath2D.push(Tt),this.rebuild()},Jt=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),this.paths.length===0?this.remove():(this.canvas||(W(this,ct,Me).call(this),W(this,dt,Re).call(this)),W(this,G,pe).call(this))};this.addCommands({cmd:Nt,undo:Jt,mustExec:!0})},n=new WeakSet,zi=function(){if(!t(this,_))return;Z(this,_,!1);const q=Math.ceil(this.thickness*this.parentScale),vt=this.currentPath.slice(-3),Lt=vt.map(Nt=>Nt[0]),Tt=vt.map(Nt=>Nt[1]);Math.min(...Lt)-q,Math.max(...Lt)+q,Math.min(...Tt)-q,Math.max(...Tt)+q;const{ctx:Ot}=this;Ot.save(),Ot.clearRect(0,0,this.canvas.width,this.canvas.height);for(const Nt of this.bezierPath2D)Ot.stroke(Nt);Ot.stroke(t(this,y)),Ot.restore()},o=new WeakSet,Xi=function(q,vt,Lt,Tt,Ot,Nt,Jt){const bt=(vt+Tt)/2,Yt=(Lt+Ot)/2,It=(Tt+Nt)/2,R=(Ot+Jt)/2;q.bezierCurveTo(bt+2*(Tt-bt)/3,Yt+2*(Ot-Yt)/3,It+2*(Tt-It)/3,R+2*(Ot-R)/3,It,R)},b=new WeakSet,Vi=function(){const q=this.currentPath;if(q.length<=2)return[[q[0],q[0],q.at(-1),q.at(-1)]];const vt=[];let Lt,[Tt,Ot]=q[0];for(Lt=1;Lt<q.length-2;Lt++){const[e,f]=q[Lt],[D,j]=q[Lt+1],$=(e+D)/2,et=(f+j)/2,gt=[Tt+2*(e-Tt)/3,Ot+2*(f-Ot)/3],kt=[$+2*(e-$)/3,et+2*(f-et)/3];vt.push([[Tt,Ot],gt,kt,[$,et]]),[Tt,Ot]=[$,et]}const[Nt,Jt]=q[Lt],[bt,Yt]=q[Lt+1],It=[Tt+2*(Nt-Tt)/3,Ot+2*(Jt-Ot)/3],R=[bt+2*(Nt-bt)/3,Yt+2*(Jt-Yt)/3];return vt.push([[Tt,Ot],It,R,[bt,Yt]]),vt},N=new WeakSet,ae=function(){if(this.isEmpty()){W(this,K,De).call(this);return}W(this,V,hi).call(this);const{canvas:q,ctx:vt}=this;vt.setTransform(1,0,0,1,0,0),vt.clearRect(0,0,q.width,q.height),W(this,K,De).call(this);for(const Lt of this.bezierPath2D)vt.stroke(Lt)},Q=new WeakSet,ui=function(q){this.canvas.removeEventListener("pointerleave",t(this,O)),this.canvas.removeEventListener("pointermove",t(this,L)),this.canvas.removeEventListener("pointerup",t(this,x)),this.canvas.addEventListener("pointerdown",t(this,v)),setTimeout(()=>{this.canvas.removeEventListener("contextmenu",X.noContextMenu)},10),W(this,S,Gi).call(this,q.offsetX,q.offsetY),this.addToAnnotationStorage(),this.setInBackground()},ct=new WeakSet,Me=function(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",M.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then(q=>{var vt;return(vt=this.canvas)==null?void 0:vt.setAttribute("aria-label",q)}),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")},dt=new WeakSet,Re=function(){Z(this,C,new ResizeObserver(q=>{const vt=q[0].contentRect;vt.width&&vt.height&&this.setDimensions(vt.width,vt.height)})),t(this,C).observe(this.div)},Bt=new WeakSet,fe=function(){if(!t(this,w))return;const[q,vt]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*q),this.canvas.height=Math.ceil(this.height*vt),W(this,K,De).call(this)},Dt=new WeakSet,fi=function(q,vt){const Lt=W(this,ot,Ie).call(this),Tt=(q-Lt)/t(this,g),Ot=(vt-Lt)/t(this,k);this.scaleFactor=Math.min(Tt,Ot)},K=new WeakSet,De=function(){const q=W(this,ot,Ie).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+q,this.translationY*this.scaleFactor+q)},ht=new WeakSet,$i=function(q){const vt=new Path2D;for(let Lt=0,Tt=q.length;Lt<Tt;Lt++){const[Ot,Nt,Jt,bt]=q[Lt];Lt===0&&vt.moveTo(...Ot),vt.bezierCurveTo(Nt[0],Nt[1],Jt[0],Jt[1],bt[0],bt[1])}return vt},Ct=new WeakSet,pi=function(q,vt,Lt){const[Tt,Ot,Nt,Jt]=vt;switch(Lt){case 0:for(let bt=0,Yt=q.length;bt<Yt;bt+=2)q[bt]+=Tt,q[bt+1]=Jt-q[bt+1];break;case 90:for(let bt=0,Yt=q.length;bt<Yt;bt+=2){const It=q[bt];q[bt]=q[bt+1]+Tt,q[bt+1]=It+Ot}break;case 180:for(let bt=0,Yt=q.length;bt<Yt;bt+=2)q[bt]=Nt-q[bt],q[bt+1]+=Ot;break;case 270:for(let bt=0,Yt=q.length;bt<Yt;bt+=2){const It=q[bt];q[bt]=Nt-q[bt+1],q[bt+1]=Jt-It}break;default:throw new Error("Invalid rotation")}return q},Gt=new WeakSet,qi=function(q,vt,Lt){const[Tt,Ot,Nt,Jt]=vt;switch(Lt){case 0:for(let bt=0,Yt=q.length;bt<Yt;bt+=2)q[bt]-=Tt,q[bt+1]=Jt-q[bt+1];break;case 90:for(let bt=0,Yt=q.length;bt<Yt;bt+=2){const It=q[bt];q[bt]=q[bt+1]-Ot,q[bt+1]=It-Tt}break;case 180:for(let bt=0,Yt=q.length;bt<Yt;bt+=2)q[bt]=Nt-q[bt],q[bt+1]-=Ot;break;case 270:for(let bt=0,Yt=q.length;bt<Yt;bt+=2){const It=q[bt];q[bt]=Jt-q[bt+1],q[bt+1]=Nt-It}break;default:throw new Error("Invalid rotation")}return q},Xt=new WeakSet,Yi=function(q,vt,Lt,Tt){var Yt,It;const Ot=[],Nt=this.thickness/2,Jt=q*vt+Nt,bt=q*Lt+Nt;for(const R of this.paths){const e=[],f=[];for(let D=0,j=R.length;D<j;D++){const[$,et,gt,kt]=R[D],xt=q*$[0]+Jt,zt=q*$[1]+bt,Mt=q*et[0]+Jt,Rt=q*et[1]+bt,Ut=q*gt[0]+Jt,$t=q*gt[1]+bt,Kt=q*kt[0]+Jt,Qt=q*kt[1]+bt;D===0&&(e.push(xt,zt),f.push(xt,zt)),e.push(Mt,Rt,Ut,$t,Kt,Qt),f.push(Mt,Rt),D===j-1&&f.push(Kt,Qt)}Ot.push({bezier:W(Yt=_t,Ct,pi).call(Yt,e,Tt,this.rotation),points:W(It=_t,Ct,pi).call(It,f,Tt,this.rotation)})}return Ot},Wt=new WeakSet,gi=function(){let q=1/0,vt=-1/0,Lt=1/0,Tt=-1/0;for(const Ot of this.paths)for(const[Nt,Jt,bt,Yt]of Ot){const It=h.Util.bezierBoundingBox(...Nt,...Jt,...bt,...Yt);q=Math.min(q,It[0]),Lt=Math.min(Lt,It[1]),vt=Math.max(vt,It[2]),Tt=Math.max(Tt,It[3])}return[q,Lt,vt,Tt]},ot=new WeakSet,Ie=function(){return t(this,u)?Math.ceil(this.thickness*this.parentScale):0},G=new WeakSet,pe=function(q=!1){if(this.isEmpty())return;if(!t(this,u)){W(this,N,ae).call(this);return}const vt=W(this,Wt,gi).call(this),Lt=W(this,ot,Ie).call(this);Z(this,g,Math.max(M.AnnotationEditor.MIN_SIZE,vt[2]-vt[0])),Z(this,k,Math.max(M.AnnotationEditor.MIN_SIZE,vt[3]-vt[1]));const Tt=Math.ceil(Lt+t(this,g)*this.scaleFactor),Ot=Math.ceil(Lt+t(this,k)*this.scaleFactor),[Nt,Jt]=this.parentDimensions;this.width=Tt/Nt,this.height=Ot/Jt,this.setAspectRatio(Tt,Ot);const bt=this.translationX,Yt=this.translationY;this.translationX=-vt[0],this.translationY=-vt[1],W(this,Bt,fe).call(this),W(this,N,ae).call(this),Z(this,A,Tt),Z(this,a,Ot),this.setDims(Tt,Ot);const It=q?Lt/this.scaleFactor/2:0;this.translate(bt-this.translationX-It,Yt-this.translationY-It)},I(_t,ht),I(_t,Ct),I(_t,Gt),ee(_t,"_defaultColor",null),ee(_t,"_defaultOpacity",1),ee(_t,"_defaultThickness",1),ee(_t,"_type","ink");let U=_t;d.InkEditor=U},(At,d,rt)=>{var U,k,g,L,O,x,v,y,u,_,w,ve,A,Se,l,Le,p,mi,T,Ki,B,Ji,E,bi,it,Oe,H,Qi;Object.defineProperty(d,"__esModule",{value:!0}),d.StampEditor=void 0;var h=rt(1),M=rt(4),at=rt(6),X=rt(29);const pt=class pt extends M.AnnotationEditor{constructor(S){super({...S,name:"stampEditor"});I(this,w);I(this,A);I(this,l);I(this,p);I(this,T);I(this,B);I(this,E);I(this,it);I(this,H);I(this,U,null);I(this,k,null);I(this,g,null);I(this,L,null);I(this,O,null);I(this,x,null);I(this,v,null);I(this,y,null);I(this,u,!1);I(this,_,!1);Z(this,L,S.bitmapUrl),Z(this,O,S.bitmapFile)}static initialize(S){M.AnnotationEditor.initialize(S)}static get supportedTypes(){const S=["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"];return(0,h.shadow)(this,"supportedTypes",S.map(i=>`image/${i}`))}static get supportedTypesStr(){return(0,h.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(S){return this.supportedTypes.includes(S)}static paste(S,i){i.pasteEditor(h.AnnotationEditorType.STAMP,{bitmapFile:S.getAsFile()})}remove(){var S,i;t(this,k)&&(Z(this,U,null),this._uiManager.imageManager.deleteId(t(this,k)),(S=t(this,x))==null||S.remove(),Z(this,x,null),(i=t(this,v))==null||i.disconnect(),Z(this,v,null)),super.remove()}rebuild(){if(!this.parent){t(this,k)&&W(this,l,Le).call(this);return}super.rebuild(),this.div!==null&&(t(this,k)&&W(this,l,Le).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(t(this,g)||t(this,U)||t(this,L)||t(this,O))}get isResizable(){return!0}render(){if(this.div)return this.div;let S,i;if(this.width&&(S=this.x,i=this.y),super.render(),this.div.hidden=!0,t(this,U)?W(this,p,mi).call(this):W(this,l,Le).call(this),this.width){const[n,s]=this.parentDimensions;this.setAt(S*n,i*s,this.width*n,this.height*s)}return this.div}static deserialize(S,i,n){if(S instanceof X.StampAnnotationElement)return null;const s=super.deserialize(S,i,n),{rect:o,bitmapUrl:c,bitmapId:b,isSvg:F,accessibilityData:N}=S;b&&n.imageManager.isValidId(b)?Z(s,k,b):Z(s,L,c),Z(s,u,F);const[tt,Q]=s.pageDimensions;return s.width=(o[2]-o[0])/tt,s.height=(o[3]-o[1])/Q,N&&(s.altTextData=N),s}serialize(S=!1,i=null){if(this.isEmpty())return null;const n={annotationType:h.AnnotationEditorType.STAMP,bitmapId:t(this,k),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:t(this,u),structTreeParentId:this._structTreeParentId};if(S)return n.bitmapUrl=W(this,it,Oe).call(this,!0),n.accessibilityData=this.altTextData,n;const{decorative:s,altText:o}=this.altTextData;if(!s&&o&&(n.accessibilityData={type:"Figure",alt:o}),i===null)return n;i.stamps||(i.stamps=new Map);const c=t(this,u)?(n.rect[2]-n.rect[0])*(n.rect[3]-n.rect[1]):null;if(!i.stamps.has(t(this,k)))i.stamps.set(t(this,k),{area:c,serialized:n}),n.bitmap=W(this,it,Oe).call(this,!1);else if(t(this,u)){const b=i.stamps.get(t(this,k));c>b.area&&(b.area=c,b.serialized.bitmap.close(),b.serialized.bitmap=W(this,it,Oe).call(this,!1))}return n}};U=new WeakMap,k=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,x=new WeakMap,v=new WeakMap,y=new WeakMap,u=new WeakMap,_=new WeakMap,w=new WeakSet,ve=function(S,i=!1){if(!S){this.remove();return}Z(this,U,S.bitmap),i||(Z(this,k,S.id),Z(this,u,S.isSvg)),W(this,p,mi).call(this)},A=new WeakSet,Se=function(){Z(this,g,null),this._uiManager.enableWaiting(!1),t(this,x)&&this.div.focus()},l=new WeakSet,Le=function(){if(t(this,k)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(t(this,k)).then(i=>W(this,w,ve).call(this,i,!0)).finally(()=>W(this,A,Se).call(this));return}if(t(this,L)){const i=t(this,L);Z(this,L,null),this._uiManager.enableWaiting(!0),Z(this,g,this._uiManager.imageManager.getFromUrl(i).then(n=>W(this,w,ve).call(this,n)).finally(()=>W(this,A,Se).call(this)));return}if(t(this,O)){const i=t(this,O);Z(this,O,null),this._uiManager.enableWaiting(!0),Z(this,g,this._uiManager.imageManager.getFromFile(i).then(n=>W(this,w,ve).call(this,n)).finally(()=>W(this,A,Se).call(this)));return}const S=document.createElement("input");S.type="file",S.accept=pt.supportedTypesStr,Z(this,g,new Promise(i=>{S.addEventListener("change",async()=>{if(!S.files||S.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const n=await this._uiManager.imageManager.getFromFile(S.files[0]);W(this,w,ve).call(this,n)}i()}),S.addEventListener("cancel",()=>{this.remove(),i()})}).finally(()=>W(this,A,Se).call(this))),S.click()},p=new WeakSet,mi=function(){const{div:S}=this;let{width:i,height:n}=t(this,U);const[s,o]=this.pageDimensions,c=.75;if(this.width)i=this.width*s,n=this.height*o;else if(i>c*s||n>c*o){const tt=Math.min(c*s/i,c*o/n);i*=tt,n*=tt}const[b,F]=this.parentDimensions;this.setDims(i*b/s,n*F/o),this._uiManager.enableWaiting(!1);const N=Z(this,x,document.createElement("canvas"));S.append(N),S.hidden=!1,W(this,E,bi).call(this,i,n),W(this,H,Qi).call(this),t(this,_)||(this.parent.addUndoableEditor(this),Z(this,_,!0)),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()},T=new WeakSet,Ki=function(S,i){var c;const[n,s]=this.parentDimensions;this.width=S/n,this.height=i/s,this.setDims(S,i),(c=this._initialOptions)!=null&&c.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,t(this,y)!==null&&clearTimeout(t(this,y)),Z(this,y,setTimeout(()=>{Z(this,y,null),W(this,E,bi).call(this,S,i)},200))},B=new WeakSet,Ji=function(S,i){const{width:n,height:s}=t(this,U);let o=n,c=s,b=t(this,U);for(;o>2*S||c>2*i;){const F=o,N=c;o>2*S&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2)),c>2*i&&(c=c>=16384?Math.floor(c/2)-1:Math.ceil(c/2));const tt=new OffscreenCanvas(o,c);tt.getContext("2d").drawImage(b,0,0,F,N,0,0,o,c),b=tt.transferToImageBitmap()}return b},E=new WeakSet,bi=function(S,i){S=Math.ceil(S),i=Math.ceil(i);const n=t(this,x);if(!n||n.width===S&&n.height===i)return;n.width=S,n.height=i;const s=t(this,u)?t(this,U):W(this,B,Ji).call(this,S,i),o=n.getContext("2d");o.filter=this._uiManager.hcmFilter,o.drawImage(s,0,0,s.width,s.height,0,0,S,i)},it=new WeakSet,Oe=function(S){if(S){if(t(this,u)){const s=this._uiManager.imageManager.getSvgUrl(t(this,k));if(s)return s}const i=document.createElement("canvas");return{width:i.width,height:i.height}=t(this,U),i.getContext("2d").drawImage(t(this,U),0,0),i.toDataURL()}if(t(this,u)){const[i,n]=this.pageDimensions,s=Math.round(this.width*i*at.PixelsPerInch.PDF_TO_CSS_UNITS),o=Math.round(this.height*n*at.PixelsPerInch.PDF_TO_CSS_UNITS),c=new OffscreenCanvas(s,o);return c.getContext("2d").drawImage(t(this,U),0,0,t(this,U).width,t(this,U).height,0,0,s,o),c.transferToImageBitmap()}return structuredClone(t(this,U))},H=new WeakSet,Qi=function(){Z(this,v,new ResizeObserver(S=>{const i=S[0].contentRect;i.width&&i.height&&W(this,T,Ki).call(this,i.width,i.height)})),t(this,v).observe(this.div)},ee(pt,"_type","stamp");let ft=pt;d.StampEditor=ft}],__webpack_module_cache__={};function __w_pdfjs_require__(At){var d=__webpack_module_cache__[At];if(d!==void 0)return d.exports;var rt=__webpack_module_cache__[At]={exports:{}};return __webpack_modules__[At](rt,rt.exports,__w_pdfjs_require__),rt.exports}var __webpack_exports__={};return(()=>{var At=__webpack_exports__;Object.defineProperty(At,"__esModule",{value:!0}),Object.defineProperty(At,"AbortException",{enumerable:!0,get:function(){return d.AbortException}}),Object.defineProperty(At,"AnnotationEditorLayer",{enumerable:!0,get:function(){return at.AnnotationEditorLayer}}),Object.defineProperty(At,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return d.AnnotationEditorParamsType}}),Object.defineProperty(At,"AnnotationEditorType",{enumerable:!0,get:function(){return d.AnnotationEditorType}}),Object.defineProperty(At,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return X.AnnotationEditorUIManager}}),Object.defineProperty(At,"AnnotationLayer",{enumerable:!0,get:function(){return ft.AnnotationLayer}}),Object.defineProperty(At,"AnnotationMode",{enumerable:!0,get:function(){return d.AnnotationMode}}),Object.defineProperty(At,"CMapCompressionType",{enumerable:!0,get:function(){return d.CMapCompressionType}}),Object.defineProperty(At,"DOMSVGFactory",{enumerable:!0,get:function(){return h.DOMSVGFactory}}),Object.defineProperty(At,"FeatureTest",{enumerable:!0,get:function(){return d.FeatureTest}}),Object.defineProperty(At,"GlobalWorkerOptions",{enumerable:!0,get:function(){return U.GlobalWorkerOptions}}),Object.defineProperty(At,"ImageKind",{enumerable:!0,get:function(){return d.ImageKind}}),Object.defineProperty(At,"InvalidPDFException",{enumerable:!0,get:function(){return d.InvalidPDFException}}),Object.defineProperty(At,"MissingPDFException",{enumerable:!0,get:function(){return d.MissingPDFException}}),Object.defineProperty(At,"OPS",{enumerable:!0,get:function(){return d.OPS}}),Object.defineProperty(At,"PDFDataRangeTransport",{enumerable:!0,get:function(){return rt.PDFDataRangeTransport}}),Object.defineProperty(At,"PDFDateString",{enumerable:!0,get:function(){return h.PDFDateString}}),Object.defineProperty(At,"PDFWorker",{enumerable:!0,get:function(){return rt.PDFWorker}}),Object.defineProperty(At,"PasswordResponses",{enumerable:!0,get:function(){return d.PasswordResponses}}),Object.defineProperty(At,"PermissionFlag",{enumerable:!0,get:function(){return d.PermissionFlag}}),Object.defineProperty(At,"PixelsPerInch",{enumerable:!0,get:function(){return h.PixelsPerInch}}),Object.defineProperty(At,"PromiseCapability",{enumerable:!0,get:function(){return d.PromiseCapability}}),Object.defineProperty(At,"RenderingCancelledException",{enumerable:!0,get:function(){return h.RenderingCancelledException}}),Object.defineProperty(At,"SVGGraphics",{enumerable:!0,get:function(){return rt.SVGGraphics}}),Object.defineProperty(At,"UnexpectedResponseException",{enumerable:!0,get:function(){return d.UnexpectedResponseException}}),Object.defineProperty(At,"Util",{enumerable:!0,get:function(){return d.Util}}),Object.defineProperty(At,"VerbosityLevel",{enumerable:!0,get:function(){return d.VerbosityLevel}}),Object.defineProperty(At,"XfaLayer",{enumerable:!0,get:function(){return k.XfaLayer}}),Object.defineProperty(At,"build",{enumerable:!0,get:function(){return rt.build}}),Object.defineProperty(At,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return d.createValidAbsoluteUrl}}),Object.defineProperty(At,"getDocument",{enumerable:!0,get:function(){return rt.getDocument}}),Object.defineProperty(At,"getFilenameFromUrl",{enumerable:!0,get:function(){return h.getFilenameFromUrl}}),Object.defineProperty(At,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return h.getPdfFilenameFromUrl}}),Object.defineProperty(At,"getXfaPageViewport",{enumerable:!0,get:function(){return h.getXfaPageViewport}}),Object.defineProperty(At,"isDataScheme",{enumerable:!0,get:function(){return h.isDataScheme}}),Object.defineProperty(At,"isPdfFile",{enumerable:!0,get:function(){return h.isPdfFile}}),Object.defineProperty(At,"loadScript",{enumerable:!0,get:function(){return h.loadScript}}),Object.defineProperty(At,"noContextMenu",{enumerable:!0,get:function(){return h.noContextMenu}}),Object.defineProperty(At,"normalizeUnicode",{enumerable:!0,get:function(){return d.normalizeUnicode}}),Object.defineProperty(At,"renderTextLayer",{enumerable:!0,get:function(){return M.renderTextLayer}}),Object.defineProperty(At,"setLayerDimensions",{enumerable:!0,get:function(){return h.setLayerDimensions}}),Object.defineProperty(At,"shadow",{enumerable:!0,get:function(){return d.shadow}}),Object.defineProperty(At,"updateTextLayer",{enumerable:!0,get:function(){return M.updateTextLayer}}),Object.defineProperty(At,"version",{enumerable:!0,get:function(){return rt.version}});var d=__w_pdfjs_require__(1),rt=__w_pdfjs_require__(2),h=__w_pdfjs_require__(6),M=__w_pdfjs_require__(26),at=__w_pdfjs_require__(27),X=__w_pdfjs_require__(5),ft=__w_pdfjs_require__(29),U=__w_pdfjs_require__(14),k=__w_pdfjs_require__(32)})(),__webpack_exports__})())})(pdf);var pdfExports=pdf.exports;export{pdfExports as p};
