import React, { useState, useContext, useEffect } from "react";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import {
  FaPhoneAlt,
  FaClock,
  FaCalendarCheck,
  FaExclamationTriangle,
  FaRedoAlt,
  FaMoneyCheckAlt,
} from "react-icons/fa";
import {
  MdPhoneMissed,
  MdCallEnd,
  MdPersonAddAlt,
  MdCallReceived,
} from "react-icons/md";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router";
import { useSearchParams } from "react-router-dom";
import { SkeletonLoader } from "Components/Skeleton";

const CustomUserSummaryPage = () => {
  const navigate = useNavigate();
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [searchParams] = useSearchParams();
  const campaign_id = searchParams.get("campaign_id");
  const [summaryData, setSummaryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const sdk = new MkdSDK();

  const ChatHistory = ({ chatHistory }) => {
    const [visibleMessages, setVisibleMessages] = React.useState(4);

    const handleViewMore = () => {
      setVisibleMessages((prev) => prev + 4);
    };

    return (
      <>
        {chatHistory.slice(0, visibleMessages).map((chat, index) => (
          <div
            key={index}
            className={`rounded-md p-3 ${
              chat.role === "user"
                ? "bg-blue-100 text-blue-900"
                : "bg-gray-100 text-gray-900"
            }`}
          >
            <p className="font-medium capitalize">{chat.role}:</p>
            <p>{chat.content}</p>
          </div>
        ))}
        {visibleMessages < chatHistory.length && (
          <button
            onClick={handleViewMore}
            className="mt-4 text-blue-500 hover:underline"
          >
            View More
          </button>
        )}
      </>
    );
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "outbound_campaigns",
      },
    });

    async function fetchData() {
      try {
        setLoading(true);
        sdk.setTable("campaign");
        const result = await sdk.callRawAPI(
          `/v3/api/custom/voiceoutreach/user/campaign-summary/${campaign_id}`,
          {},
          "GET"
        );
        setSummaryData(result.model); // Set API data to summaryData
      } catch (error) {
        console.log("ERROR", error);
        setError(error.message);
        tokenExpireError(authDispatch, error.message);
        showToast(globalDispatch, error.message, 5000, "error");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [campaign_id, authDispatch, globalDispatch]);

  if (loading) {
    return <SkeletonLoader />;
  }

  if (error) {
    showToast(globalDispatch, error, 5000, "error");
  }

  return (
    <div className="overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5">
      <h3 className="px-8 mb-8 text-2xl font-bold text-white">
        Campaign Summary Report
      </h3>

      <div className="px-8 py-4">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <FaPhoneAlt className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Total Calls</h3>
              <p className="text-gray-400">{summaryData.total_calls}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <MdCallReceived className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Pickups</h3>
              <p className="text-gray-400">{summaryData.total_answered}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <MdPhoneMissed className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">No Answers</h3>
              <p className="text-gray-400">
                {summaryData.total_calls - summaryData.total_answered}
              </p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <FaRedoAlt className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Retries</h3>
              <p className="text-gray-400">{summaryData.retries}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <FaClock className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">
                Average Call Duration
              </h3>
              <p className="text-gray-400">
                {summaryData.average_call_duration} seconds
              </p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <FaMoneyCheckAlt className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Usage Cost</h3>
              <p className="text-gray-400">${summaryData.total_usage_cost}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <MdPersonAddAlt className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">
                Appointments Booked
              </h3>
              <p className="text-gray-400">{summaryData.appointments_booked}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <MdCallEnd className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Hangups</h3>
              <p className="text-gray-400">{summaryData.hangups}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <MdPhoneMissed className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Callbacks</h3>
              <p className="text-gray-400">{summaryData.callbacks}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4">
            <FaCalendarCheck className="mr-4 text-[#19b2f6]" size={30} />
            <div>
              <h3 className="text-lg font-medium text-white">Follow-ups</h3>
              <p className="text-gray-400">{summaryData.follow_ups}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomUserSummaryPage;
