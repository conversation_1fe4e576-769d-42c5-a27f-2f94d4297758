import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as n,R as i,f as E,i as _}from"./vendor-2ae44a2e.js";import{A,G as M,M as I,s as o,t as O}from"./index-d0a8f5da.js";import{X as j,M as R}from"./XMarkIcon-5ab6cac4.js";import{_ as q}from"./MoonLoader-62b0139a.js";import{q as m,_ as y}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const ie=()=>{const{dispatch:b}=n.useContext(A),{state:U,dispatch:s}=n.useContext(M),[r,p]=i.useState([]),[v,l]=i.useState(!1),[w,u]=i.useState(!1),[d,N]=i.useState(null),[C,S]=i.useState(null),x=E(),[h]=_(),F=new I,k=n.useId(),D=async()=>{if(l(!0),!d||d.trim().length<=0){o(s,"Please enter a name!",2e3,"error"),l(!1);return}if(!(r!=null&&r.length)){o(s,"No audio/video files uploaded!",2e3,"error"),l(!1);return}const a=r==null?void 0:r.map(t=>t.file);try{await T(a),l(!1),x("/user/voice_list")}catch(t){l(!1),o(s,t.message||"An error occurred",2e3,"error")}},T=async a=>{try{const t=new FormData;d||o(s,"Please enter a name",2e3,"error"),a.forEach(c=>{t.append("file",c)}),t.append("name",d),t.append("description",C),await F.sendOnboardingData(t,"POST"),o(s,"Onboarding complete"),h.get("redirect_uri")&&x(h.get("redirect_uri"))}catch(t){O(b,t.message),o(s,t.message,2e3,"error")}},f=async(a,t=1)=>{t===1?N(a.target.value):S(a.target.value)};return i.useEffect(()=>{s({type:"SETPATH",payload:{path:"onboarding"}})},[]),e.jsx("div",{className:"mx-auto flex  items-center justify-center  rounded-md  bg-[#1d2937]    px-5 text-white  md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl",children:e.jsxs("div",{className:"mx-auto rounded",children:[e.jsx(m.Root,{show:w,as:n.Fragment,children:e.jsxs(y,{as:"div",className:"fixed inset-0 z-50 overflow-hidden",onClose:u,children:[e.jsx(m.Child,{as:n.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-gray-900/80"})}),e.jsx("div",{className:"fixed inset-0 flex justify-center",children:e.jsx(m.Child,{as:n.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:e.jsxs(y.Panel,{className:"fixed inset-y-0 right-0 flex w-full justify-center",children:[e.jsx(m.Child,{as:n.Fragment,enter:"ease-in-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"absolute right-8 top-8 flex",children:e.jsxs("button",{type:"button",className:"p-2.5 text-white",onClick:()=>{u(!1),x("/user/knowledge_bank")},children:[e.jsx("span",{className:"sr-only",children:"Close sidebar"}),e.jsx(j,{className:"h-6 w-6","aria-hidden":"true"})]})})}),e.jsx("div",{className:"absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]",children:e.jsx("div",{className:"flex min-h-[300px] w-full min-w-[500px] items-center justify-center rounded-[3px] bg-white",children:e.jsx("h4",{children:"Uploaded successfully"})})})]})})})]})}),e.jsxs("div",{className:" rounded-lg  bg-[#1d2937]  p-10 text-white  md:max-w-2xl  lg:max-w-2xl 2xl:max-w-2xl",children:[e.jsx("p",{className:"text-gray-200",children:'Enter a unique name and description for the voice. Upload the audio file by selecting or dragging it into the box. Supported formats: MP3, WAV. Click "Save" to submit.'}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Voice Name"}),e.jsx("input",{onChange:f,type:"text",className:"h-[30px] w-full rounded-md bg-transparent xl:h-[43px]"})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Description"}),e.jsx("input",{onChange:a=>f(a,2),type:"text",className:"h-[30px] w-full rounded-md bg-transparent xl:h-[43px] "})]}),e.jsx("div",{className:"mt-3",children:e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Voice(s)"})}),e.jsx(R,{multiple:!0,name:"fileData",fileType:"audio",onAddSuccess:a=>{p(a.fileData)},removeWidthStyles:!0}),e.jsx("div",{className:"mt-4 space-y-4",children:r.map((a,t)=>{var c;return e.jsxs("div",{className:"flex items-center justify-between rounded-xl border-2 border-gray-200 p-4 font-medium",children:[e.jsx("p",{children:(c=a.file)==null?void 0:c.name}),e.jsx("button",{type:"button",onClick:()=>{p(P=>{const g=[...P];return g.splice(t,1),g})},children:e.jsx(j,{className:"h-5 w-5 2xl:w-7"})})]},k)})}),e.jsx("div",{className:"flex w-full items-center justify-center",children:e.jsxs("button",{onClick:D,className:"mt-3 flex w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90",children:[v?e.jsx(q,{color:"white",loading:!0,size:20}):null,"Save"]})})]})]})})};export{ie as default};
