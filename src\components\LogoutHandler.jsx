import React, { useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "Context/Auth";

const LogoutHandler = () => {
  const { dispatch } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Dispatch logout action
    dispatch({ type: "LOGOUT" });

    // Redirect to login page
    navigate("/user/login");
  }, []);

  return null; // This component doesn't render anything
};

export default LogoutHandler;
