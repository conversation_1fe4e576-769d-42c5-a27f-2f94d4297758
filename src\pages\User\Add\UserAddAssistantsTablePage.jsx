import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext } from "Context/Auth";
import { PiXBold } from "react-icons/pi";

let sdk = new MkdSDK();

const AddAssistantsTablePage = ({ closeSidebar }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const schema = yup
    .object({
      script: yup.string().required("Script is required"),
      voicemail_script: yup.string().required("Voicemail script is required"),
      assistant_name: yup.string().required("Assistant name is required"),
      first_message: yup.string().required("First message is required"),
      voice_id: yup.string().required("Voice selection is required"),
      notes: yup.string().required("Notes are required"),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [voices, setVoices] = React.useState([]);
  const [templates, setTemplates] = React.useState([]);
  const [selectedTemplate, setSelectedTemplate] = React.useState(null);
  const [isLoadingTemplates, setIsLoadingTemplates] = React.useState(false);
  const [systemPrompt, setSystemPrompt] = React.useState("");

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    try {
      const payload = {
        assistant_name: _data.assistant_name,
        script: _data.script,
        voicemail_script: _data.voicemail_script,
        voice_id: _data.voice_id,
        first_message: _data.first_message,
        status: 1,
        notes: _data.notes,
      };

      // Add assistant_settings if template is selected
      if (selectedTemplate && selectedTemplate.assistant_settings) {
        payload.assistant_settings = selectedTemplate.assistant_settings;
      } else {
        payload.assistant_settings = "";
      }

      await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/create_assistant",
        payload,
        "POST"
      );

      showToast(globalDispatch, "Added");
      navigate("/user/assistants");
      if (closeSidebar) {
        closeSidebar();
      }
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
      showToast(globalDispatch, error.message, 5000, "error");
      setError("script", {
        type: "manual",
        message: error.message,
      });
    }
  };

  // Function to fetch template assistants
  const fetchTemplates = async () => {
    setIsLoadingTemplates(true);
    try {
      sdk.setTable("template_assistants");
      const result = await sdk.callRestAPI({}, "GETALL");
      if (!result.error) {
        console.log("templates", result);
        setTemplates(result.list || []);
      }
    } catch (error) {
      console.log("Error fetching templates:", error);
      showToast(globalDispatch, "Failed to load templates", 5000, "error");
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  // Function to handle template selection
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);

    // Auto-populate form fields with template data
    setValue("assistant_name", template.assistant_name || "");
    setValue("script", template.script || "");
    setValue("voicemail_script", template.voicemail_script || "");
    setValue("first_message", template.first_message || "");
    setValue("notes", template.notes || "");

    // Extract system prompt from assistant_settings if available
    if (template.assistant_settings) {
      try {
        const settings = JSON.parse(template.assistant_settings);
        const systemPromptText = settings.llm_settings?.system_prompt || "";
        console.log("systemPromptText", systemPromptText);
        setSystemPrompt(systemPromptText);
      } catch (error) {
        console.log("Error parsing assistant_settings:", error);
        setSystemPrompt("");
      }
    } else {
      setSystemPrompt("");
    }

    // Handle voice_id mapping - check if template voice_id exists in user's voices
    if (template.voice_id) {
      const matchingVoice = voices.find(
        (v) => v.voice_id === template.voice_id
      );
      if (matchingVoice) {
        setValue("voice_id", template.voice_id);
      } else {
        // If template voice_id doesn't match user's voices, don't set it
        // User will need to select a voice manually
        console.log(
          "Template voice_id not found in user's voices:",
          template.voice_id
        );
      }
    }
  };

  // Function to clear template selection
  const clearTemplateSelection = () => {
    setSelectedTemplate(null);
    setSystemPrompt("");
    // Clear form fields
    setValue("assistant_name", "");
    setValue("script", "");
    setValue("voicemail_script", "");
    setValue("first_message", "");
    setValue("notes", "");
    setValue("voice_id", "");
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "assistants",
      },
    });

    (async function getVoices() {
      sdk.setTable("voice_list");
      const result = await sdk.callRestAPI(
        { user_id: state.user, filter: [`user_id,eq,${state.user}`] },
        "GETALL"
      );
      if (!result.error) {
        console.log("voices", result);
        setVoices(
          result.list
            ? result.list.map((script) => {
                return {
                  voice_id: script.voice_id,
                  name: script.name,
                };
              })
            : []
        );
      }
    })();

    // Fetch templates
    fetchTemplates();
  }, []);

  return (
    <div className="mx-auto  bg-[#1d2937] px-4 py-5">
      <div className="flex flex-row justify-between">
        <h4 className="text-3xl font-semibold text-white">
          Add Voice Assistant
        </h4>
        <button onClick={closeSidebar}>
          <PiXBold className="text-2xl text-white" />
        </button>
      </div>

      {/* Template Selection Section */}
      <div className="mb-6 mt-6">
        <h5 className="mb-4 text-xl font-semibold text-white">
          Choose Starting Point
        </h5>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Create from Scratch Option */}
          <div
            className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${
              selectedTemplate === null
                ? "border-[#19b2f6] bg-[#19b2f6]/10"
                : "border-gray-600 bg-[#1d2937] hover:border-gray-500"
            }`}
            onClick={clearTemplateSelection}
          >
            <h6 className="mb-2 text-lg font-medium text-white">
              Create from Scratch
            </h6>
            <p className="text-sm text-gray-300">
              Start with a blank assistant and customize everything yourself
            </p>
          </div>

          {/* Template Selection */}
          {templates.length > 0 && (
            <div className="rounded-lg border-2 border-gray-600 bg-[#1d2937] p-4">
              <h6 className="mb-2 text-lg font-medium text-white">
                Use Template
              </h6>
              {isLoadingTemplates ? (
                <p className="text-sm text-gray-300">Loading templates...</p>
              ) : (
                <select
                  className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white"
                  onChange={(e) => {
                    const template = templates.find(
                      (t) => t.id === parseInt(e.target.value)
                    );
                    if (template) {
                      handleTemplateSelect(template);
                    } else {
                      clearTemplateSelection();
                    }
                  }}
                  value={selectedTemplate?.id || ""}
                >
                  <option value="">Select a template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.assistant_name} ({template.language || "en"}) -{" "}
                      {template.voice_id || "No voice"}
                    </option>
                  ))}
                </select>
              )}
            </div>
          )}
        </div>

        {/* Selected Template Info */}
        {selectedTemplate && (
          <div className="mt-4 rounded-lg border border-[#19b2f6]/30 bg-[#19b2f6]/10 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h6 className="mb-2 text-lg font-medium text-white">
                  Selected Template: {selectedTemplate.assistant_name}
                </h6>
                <div className="mb-2 text-sm text-gray-300">
                  <p>
                    <strong>Language:</strong>{" "}
                    {selectedTemplate.language || "Not specified"}
                  </p>
                  <p>
                    <strong>Voice ID:</strong>{" "}
                    {selectedTemplate.voice_id || "Not specified"}
                  </p>
                  {selectedTemplate.voice_id &&
                    !voices.find(
                      (v) => v.voice_id === selectedTemplate.voice_id
                    ) && (
                      <p className="mt-1 text-yellow-400">
                        ⚠️ This template uses voice "{selectedTemplate.voice_id}
                        " which is not available in your account. You'll need to
                        select a different voice.
                      </p>
                    )}
                </div>
                <p className="mb-2 text-sm text-gray-300">
                  <strong>Description:</strong>{" "}
                  {selectedTemplate.script?.substring(0, 150)}...
                </p>
              </div>
              <button
                onClick={clearTemplateSelection}
                className="ml-4 text-sm text-gray-400 hover:text-white"
              >
                Clear Template
              </button>
            </div>
          </div>
        )}
      </div>

      <form
        className="mt-5 w-full overflow-y-auto pb-10"
        onSubmit={handleSubmit(onSubmit)}
      >
        <MkdInput
          type={"text"}
          page={"edit"}
          name={"assistant_name"}
          errors={errors}
          label={"Assistant Name"}
          placeholder={"name"}
          register={register}
          className={"bg-transparent text-white placeholder:text-gray-300"}
        />

        <MkdInput
          type={"mapping"}
          page={"add"}
          name={"voice_id"}
          errors={errors}
          label={"Voice"}
          placeholder={"voice_id"}
          options={voices.map((sc) => sc.voice_id)}
          mapping={voices.reduce((g, sc) => {
            g[sc.voice_id] = sc.name;
            return g;
          }, {})}
          register={register}
          className={"bg-[#1d2937]  text-white placeholder:text-gray-300"}
        />

        <MkdInput
          type={"text"}
          page={"edit"}
          name={"first_message"}
          errors={errors}
          label={"First Message"}
          placeholder={"First Message"}
          register={register}
          className={"bg-transparent text-white placeholder:text-gray-300"}
        />
        <MkdInput
          type={"textarea"}
          page={"edit"}
          name={"script"}
          errors={errors}
          label={"script"}
          placeholder={"script"}
          register={register}
          className={
            "flex-grow bg-transparent text-white placeholder:text-gray-300"
          }
          containerClassName="flex flex-col "
          rows={10}
        />
        <MkdInput
          type={"textarea"}
          page={"edit"}
          name={"voicemail_script"}
          errors={errors}
          label={"Voicemail script"}
          placeholder={"Voicemail script"}
          register={register}
          className={
            "flex-grow bg-transparent text-white placeholder:text-gray-300"
          }
          containerClassName="flex flex-col flex-grow"
          rows={10}
        />
        <MkdInput
          type={"textarea"}
          page={"edit"}
          name={"notes"}
          errors={errors}
          label={"Notes"}
          placeholder={"Notes"}
          register={register}
          className={"bg-transparent text-white placeholder:text-gray-300"}
          rows={5}
        />

        {/* System Prompt Display (Read-only) */}
        {systemPrompt && (
          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium text-white">
              System Prompt (Template)
            </label>
            <textarea
              value={systemPrompt}
              readOnly
              className="w-full resize-none rounded border border-gray-600 bg-[#1d2937] p-3 text-gray-300"
              rows={8}
              style={{ fontFamily: "monospace", fontSize: "12px" }}
            />
            {/* <p className="mt-1 text-xs text-gray-400">
              This system prompt is automatically included from the selected
              template and cannot be edited.
            </p> */}
          </div>
        )}

        <InteractiveButton
          type="submit"
          loading={isSubmitting}
          disabled={isSubmitting}
          className="focus:shadow-outline mt-6 w-full rounded bg-[#19b2f6]/80  px-4 py-2 font-semibold text-white focus:outline-none"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default AddAssistantsTablePage;
