
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/src/favicon.svg"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>voiceoutreach</title>
    <script type="module" crossorigin src="/assets/index-d0a8f5da.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-2ae44a2e.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-google-maps/api-c55ecefa.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-783bc3ae.js">
    <link rel="modulepreload" crossorigin href="/assets/qr-scanner-cf010ec4.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-7bce1936.js">
    <link rel="modulepreload" crossorigin href="/assets/react-icons-f29df01f.js">
    <link rel="modulepreload" crossorigin href="/assets/lucide-react-f66dbccf.js">
    <link rel="modulepreload" crossorigin href="/assets/react-loading-skeleton-f53ed7d1.js">
    <link rel="modulepreload" crossorigin href="/assets/papaparse-2d1475f9.js">
    <link rel="modulepreload" crossorigin href="/assets/react-papaparse-b60a38ab.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-294d29ff.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-27c5bed3.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-9cc92aaf.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-11dbc67c.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-3e88f209.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-2414b431.js">
    <link rel="stylesheet" href="/assets/index-f3f42218.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>
    
    
  </body>
</html>
