
    import React, { useEffect, useState } from "react";
    import { useForm } from "react-hook-form";
    import { yupResolver } from "@hookform/resolvers/yup";
    import * as yup from "yup";
    import MkdSDK from "Utils/MkdSDK";
    import { useNavigate, useParams } from "react-router-dom";
    import { AuthContext, tokenExpireError } from "Context/Auth";
    import { GlobalContext, showToast } from "Context/Global";
    import ReactQuill from 'react-quill';
    import 'react-quill/dist/quill.snow.css';
    import { isImage, empty, isVideo, isPdf } from "Utils/utils";
    import {MkdInput} from "Components/MkdInput";
import {InteractiveButton} from "Components/InteractiveButton"
import { SkeletonLoader } from "Components/Skeleton";


    let sdk = new MkdSDK();

    const EditKnowledgeBankPage = () => {
    const { dispatch } = React.useContext(AuthContext);
    const schema = yup
        .object({
    
        	transcript: yup.string(),
        	temp974: yup.string(),
        	filename: yup.string(),
        	status: yup.string(),
        })
        .required();
    const { dispatch: globalDispatch } = React.useContext(GlobalContext);
    const [fileObj, setFileObj] = React.useState({});
    const [isLoading, setIsLoading] = React.useState(false)
    const [loading, setLoading] = React.useState(false)

    const navigate = useNavigate();
    
          	const [transcript, setTranscript] = useState('');
          	const [temp974, setTemp974] = useState('');
          	const [filename, setFilename] = useState('');
          	const [status, setStatus] = useState('');
    // const [id, setId] = useState(0);
    const {
        register,
        handleSubmit,
        setError,
        setValue,
        formState: { errors },
    } = useForm({
        resolver: yupResolver(schema),
    });

    const params = useParams();

    useEffect(function () {
        (async function () {
        try {
            setLoading(true)

            sdk.setTable("knowledge_bank");
            const result = await sdk.callRestAPI({ id: Number(params?.id)}, "GET");
            if (!result.error) {
    
        	 setValue('transcript', result.model.transcript);
        	 setValue('temp974', result.model.temp974);
        	 setValue('filename', result.model.filename);
        	 setValue('status', result.model.status);

    
      	 setTranscript(result.model.transcript);
      	 setTemp974(result.model.temp974);
      	 setFilename(result.model.filename);
      	 setStatus(result.model.status);
                setId(result.model.id);
                setLoading(false)

            }
        } catch (error) {
            setLoading(false)

            console.log("error", error);
            tokenExpireError(dispatch, error.message);
        }
        })();
    }, []);

    const previewImage = (field, target) => {
        let tempFileObj = fileObj;
        tempFileObj[field] = {
        file: target.files[0],
        tempURL: URL.createObjectURL(target.files[0]),
        };
        setFileObj({ ...tempFileObj });
    };


    const onSubmit = async (_data) => {
        setIsLoading(true)
        try {
            sdk.setTable("knowledge_bank");
        for (let item in fileObj) {
            let formData = new FormData();
            formData.append('file', fileObj[item].file);
            let uploadResult = await sdk.uploadImage(formData);
            _data[item] = uploadResult.url;
            
        }
        const result = await sdk.callRestAPI(
            {
            id: id,
            
        		transcript: _data.transcript,
        		temp974: _data.temp974,
        		filename: _data.filename,
        		status: _data.status,

       
            },
            "PUT"
        );

        if (!result.error) {
            showToast(globalDispatch, "Updated");
            navigate("/user/knowledge_bank");
        } else {
            if (result.validation) {
            const keys = Object.keys(result.validation);
            for (let i = 0; i < keys.length; i++) {
                const field = keys[i];
                setError(field, {
                type: "manual",
                message: result.validation[field],
                });
            }
            }
        }
        setIsLoading(false)
        } catch (error) {
            setIsLoading(false)
        console.log("Error", error);
        setError("transcript", {
            type: "manual",
            message: error.message,
        });
        }
    };
    React.useEffect(() => {
        globalDispatch({
        type: "SETPATH",
        payload: {
            path: "knowledge_bank",
        },
        });
    }, []);

    return (
        <div className=" shadow-md rounded   mx-auto p-5">
        <h4 className="text-2xl font-medium">Edit Knowledge Bank</h4>
       {loading? (<SkeletonLoader/>) :  (<form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
            
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"transcript"}
      errors={errors}
      label={"Transcript"}
      placeholder={"Transcript"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"temp974"}
      errors={errors}
      label={"Temp974"}
      placeholder={"Temp974"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"filename"}
      errors={errors}
      label={"Filename"}
      placeholder={"Filename"}
      register={register}
      className={""}
    />
        
            
      <MkdInput
      type={"text"}
      page={"edit"}
      name={"status"}
      errors={errors}
      label={"Status"}
      placeholder={"Status"}
      register={register}
      className={""}
    />
        
            <InteractiveButton
            type="submit"
            className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            loading={isLoading}
            disable={isLoading}
            >
            Submit
            </InteractiveButton>
        </form>)}
        </div>
    );
    };

    export default EditKnowledgeBankPage;

    