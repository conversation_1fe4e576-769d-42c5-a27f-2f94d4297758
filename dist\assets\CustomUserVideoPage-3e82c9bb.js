import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as s,R as n,f as E}from"./vendor-2ae44a2e.js";import{A as T,G as D,M as S,s as l}from"./index-d0a8f5da.js";import"./index-e429b426.js";import{_ as v}from"./qr-scanner-cf010ec4.js";import{X as A,M as P}from"./XMarkIcon-5ab6cac4.js";import{_ as R}from"./MoonLoader-62b0139a.js";import{q as m,_ as h}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";s.lazy(()=>v(()=>import("./videoItem-9f768090.js"),["assets/videoItem-9f768090.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js"]));s.lazy(()=>v(()=>import("./video-b86c0a76.js"),["assets/video-b86c0a76.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/twilio-video-59a889f9.js","assets/videoItem-9f768090.js"]));const se=()=>{s.useContext(T);const{state:L,dispatch:r}=s.useContext(D),[o,g]=n.useState([]),[c,y]=n.useState([]),[j,d]=n.useState(!1),[w,f]=n.useState(!1),p=E(),b=()=>{const t=document.createElement("input");t.type="file",t.id="imagefile",t.setAttribute("multiple",!0),t.accept=".txt, .docx, .pdf",t.value=null,t.click(),t.addEventListener("change",async a=>{try{let i=a.target.files;const k=[".wav",".mp3"];let u=[];for(let x of i){const C=`.${x.name.split(".").pop().toLowerCase()}`;k.includes(C)&&u.push(x)}u.length!==i.length&&l(r,"Some files have invalid extensions. Only  .wav, and .mp3 files are allowed.",2e3,"error"),y(u)}catch(i){console.error("Error processing files:",i),l(r,"An error occurred while processing the files. Please try again.",2e3,"error")}})},_=new S,F=async()=>{if(d(!0),!(o!=null&&o.length)){l(r,"No audio/video files uploaded!",2e3,"error"),d(!1);return}const t=o==null?void 0:o.map(a=>a.file);t.concat(c);try{await N(t),d(!1),l(r,"Success"),p("/user/knowledge_bank")}catch(a){d(!1),p("/user/video_generation"),l(r,a.message||"An error occurred",2e3,"error")}},N=async t=>{const a=new FormData;t.forEach(i=>{a.append("files",i)}),console.log(a.getAll("files"),"FormData files"),await _.sendFormData("/v3/api/custom/voiceoutreach/video_upload",a,"POST")};return n.useEffect(()=>{r({type:"SETPATH",payload:{path:"video"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx(m.Root,{show:w,as:s.Fragment,children:e.jsxs(h,{as:"div",className:"fixed inset-0 z-50 overflow-hidden",onClose:f,children:[e.jsx(m.Child,{as:s.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-gray-900/80"})}),e.jsx("div",{className:"fixed inset-0 flex justify-center",children:e.jsx(m.Child,{as:s.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:e.jsxs(h.Panel,{className:"fixed inset-y-0 right-0 flex w-full justify-center ",children:[e.jsx(m.Child,{as:s.Fragment,enter:"ease-in-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"absolute right-8 top-8 flex ",children:e.jsxs("button",{type:"button",className:"p-2.5 text-white",onClick:()=>{f(!1),p("/user/chat")},children:[e.jsx("span",{className:"sr-only",children:"Close sidebar"}),e.jsx(A,{className:"h-6 w-6","aria-hidden":"true"})]})})}),e.jsx("div",{className:"absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]",children:e.jsx("div",{className:"flex min-h-[300px] w-full min-w-[500px] items-center justify-center rounded-[3px] bg-white",children:e.jsx("h4",{children:"Uploaded successfully"})})})]})})})]})}),e.jsxs("div",{className:"min-h-screen ",children:[e.jsx(P,{multiple:!0,name:"fileData",fileType:"video/audio",onAddSuccess:t=>{console.log(t.fileData.file),g(t.fileData)}}),o.map(t=>{var a;return e.jsx("span",{className:"block text-[1rem] text-[red]",children:(a=t==null?void 0:t.file)==null?void 0:a.name})}),e.jsx("button",{onClick:()=>b(),className:" mt-10 block min-h-[70px] w-[100%] rounded-[10px] border-4 border-dashed border-blue-500 font-bold md:w-[41rem]",children:"Upload Voice Clip"}),e.jsx("span",{className:"block text-[1rem] text-[red]",children:c==null?void 0:c.name}),e.jsxs("button",{onClick:F,className:"mt-7 flex items-center justify-center gap-2 rounded-[3px] bg-[black]  px-10 py-3 text-white",children:[j?e.jsx(R,{color:"white",loading:!0,size:20}):null,"Save"]})]})]})};export{se as default};
