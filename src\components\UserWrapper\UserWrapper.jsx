import React, { Suspense, memo } from "react";
import { UserHeader } from "Components/UserHeader";
import { TopHeader } from "Components/TopHeader";
import { Spinner } from "Assets/svgs";
import { bg } from "Assets/images";

const UserWrapper = ({ children }) => {
  return (
    <div
      style={{
        backgroundImage: `url(${bg})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
      }}
      className="flex min-h-screen w-full"
    >
      <div className="flex w-full flex-1 flex-col py-20">
        <TopHeader />
        {/* <main className="flex justify-center items-center px-6 py-2 h-full"> */}
        <main className="sm:px-400 w-full overflow-auto px-6 py-4 sm:overflow-y-auto">
          <Suspense
            fallback={
              <div className="flex h-full items-center justify-center">
                <Spinner size={100} color="#0EA5E9" />
              </div>
            }
          >
            {children}
          </Suspense>
        </main>
      </div>
    </div>
  );
};

export default memo(UserWrapper);
