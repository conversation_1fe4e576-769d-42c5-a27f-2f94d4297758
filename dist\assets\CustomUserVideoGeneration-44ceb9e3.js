import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{r as o,R as e}from"./vendor-2ae44a2e.js";import{A as i,G as p}from"./index-d0a8f5da.js";import"./index-a6a43551.js";import{_ as m}from"./MoonLoader-62b0139a.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const k=()=>{o.useContext(i);const{state:l,dispatch:r}=o.useContext(p),[s,c]=e.useState(!1);e.useEffect(()=>{r({type:"SETPATH",payload:{path:"video"}})},[]);const a="https://www.w3schools.com/html/mov_bbb.mp4";return t.jsxs("div",{className:"mx-auto rounded p-5",children:[t.jsxs("video",{className:"w-[90%] md:w-[700px]",controls:!0,children:[t.jsx("source",{src:a,type:"video/mp4"}),"Your browser does not support the video tag."]}),t.jsx("textarea",{placeholder:"type here",className:"mt-10 w-[90%] border-black md:w-[700px]",rows:6}),t.jsxs("button",{className:"mt-4 flex items-center justify-center gap-2 rounded-[3px] bg-[black]  px-10 py-3 text-white",children:[s?t.jsx(m,{color:"white",loading:!0,size:20}):null,"Generate Video"]})]})};export{k as default};
