import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as n,R as i}from"./vendor-2ae44a2e.js";import{u as Me}from"./react-hook-form-47c010f8.js";import{o as De}from"./yup-5abd4662.js";import{c as Ie,a as k}from"./yup-c2e87575.js";import{M as ne,G as Pe,A as Le,X as te,s as U,t as ze}from"./index-d0a8f5da.js";import{M as S}from"./MkdInput-c12da351.js";import{I as Oe}from"./InteractiveButton-bff38983.js";import{z as Re}from"./react-papaparse-b60a38ab.js";import{u as Ge}from"./react-dropzone-7ee839ba.js";import{P as Ue}from"./pizzip-fcee35b8.js";import{D as He}from"./@xmldom/xmldom-6a8067e2.js";import{_ as $e}from"./react-pdftotext-3aea4f3a.js";import{m as se}from"./moment-timezone-69cd48a8.js";import{q as j,_}from"./@headlessui/react-7bce1936.js";function Ze({title:b,titleId:h,...w},p){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:p,"aria-labelledby":h},w),b?n.createElement("title",{id:h},b):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const Ve=n.forwardRef(Ze),qe=Ve;function Be({title:b,titleId:h,...w},p){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:p,"aria-labelledby":h},w),b?n.createElement("title",{id:h},b):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const Xe=n.forwardRef(Be),v=Xe;function We({title:b,titleId:h,...w},p){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:p,"aria-labelledby":h},w),b?n.createElement("title",{id:h},b):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}const Ke=n.forwardRef(We),Je=Ke;let F=new ne;const H=["phone","first_name","last_name"],ae={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},Ye=({isOpen:b,closeSidebar:h})=>{const{dispatch:w}=i.useContext(Pe),{state:p,dispatch:le}=i.useContext(Le),oe=Ie({name:k(),assistant_id:k(),start_time:k(),valid_time_opening:k(),valid_time_closing:k(),days:k(),language:k().required("Language is required")}).required(),[$,re]=i.useState([]),{CSVReader:ie}=Re(),[N,Z]=i.useState(null),[V,I]=i.useState(!1),[z,ce]=i.useState(null),[q,P]=i.useState(!1),[B,de]=i.useState([]),[me,X]=i.useState(""),[ue,W]=i.useState(!1),[Qe,he]=i.useState([]),[L,pe]=i.useState(2e3),[A,xe]=i.useState(""),[et,ge]=i.useState(""),[M,fe]=i.useState(""),[O,be]=i.useState(""),[R,we]=i.useState(""),K={anthropic:{totalTokens:15e4,usableTokensForPrompt:112500,MAX_CONTENT_SIZE_LIMIT:84375},openai:{totalTokens:4096,usableTokensForPrompt:3072,MAX_CONTENT_SIZE_LIMIT:2304},gpt_4:{totalTokens:8192,usableTokensForPrompt:6144,MAX_CONTENT_SIZE_LIMIT:4608},gpt_4_extended:{totalTokens:32768,usableTokensForPrompt:24576,MAX_CONTENT_SIZE_LIMIT:18432}},J={0:"All",1:"Sundays only",2:"Mondays only",3:"Tuesdays only",4:"Wednesdays only",5:"Thursdays only",6:"Fridays only",7:"Saturdays only",8:"Weekdays only",9:"Weekends only"},D=n.useMemo(()=>{const t=(N==null?void 0:N.data[0])??[];return{headers:t,data:(N==null?void 0:N.data.slice(1).filter(s=>!(s.length==1&&s[0]==="")).map(s=>{let a={};return s.forEach((l,o)=>{a[t[o]]=l}),a}))??[]}},[N]),je=({allow_preview:t})=>{const s=d=>new Promise((x,T)=>{const g=new FileReader;g.onload=async u=>{try{const r=u.target.result,m=o(r),c=m.split(/\s+/).length;console.log(m,"from docx"),x({content:m,wordCount:c})}catch(r){T(r)}},g.onerror=u=>T(u),g.readAsArrayBuffer(d)}),a=d=>new Promise((x,T)=>{const g=new FileReader;g.onload=function(){try{const u=g.result,r=u.split(/\s+/).length;console.log(u,"from docx"),x({content:u,wordCount:r})}catch(u){T(u)}},g.readAsText(d)});function l(d){return d.charCodeAt(0)===65279&&(d=d.substr(1)),new He().parseFromString(d,"text/xml")}function o(d){const x=new Ue(d),g=l(x.files["word/document.xml"].asText()).getElementsByTagName("w:p"),u=[];for(let r=0,f=g.length;r<f;r++){let m="";const c=g[r].getElementsByTagName("w:t");for(let G=0,Ae=c.length;G<Ae;G++){const ee=c[G];ee.childNodes&&(m+=ee.childNodes[0].nodeValue)}m&&u.push(m)}return u.join(" ")}const E=n.useCallback(async d=>{const x=[];let T="";for(const r of d){let f=0,m="";if(r.type==="application/pdf")try{const c=await $e(r);f=c.split(/\s+/).length,m=c}catch(c){console.error("Error reading PDF file:",c)}else if(r.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"){const c=await s(r);m=c.content,f=c.wordCount}else if(r.type==="text/plain"){const c=await a(r);m=c.content,f=c.wordCount,console.log("Word Count:",f,m,"from txt")}console.log(f,"wordCount"),T+=m,x.push({name:r.name,size:r.size,wordCount:f,content:m})}x.reduce((r,f)=>r+f.wordCount,0)>L?(X(""),W(!1),U(w,"Word Limit Exceeded",5e3,"error")):(X(T),W(!0)),ce(x),P(!0)},[]),{getRootProps:Q,getInputProps:Fe}=Ge({onDrop:E,accept:{"application/pdf":[".pdf"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"]}});return e.jsxs("div",{className:"mt-5 cursor-pointer",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-white cursor-pointer"}),e.jsxs("div",{...Q({className:"dropzone"}),children:[e.jsx("input",{...Fe()}),e.jsx("div",{className:"flex justify-center items-center p-4 w-full h-60 rounded-md border-2 border-gray-600",children:e.jsx("p",{className:"text-center text-white",children:"Drag 'n' drop some files here, or click to select files"})})]}),t&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Uploaded Documents:"}),e.jsx("ul",{children:z.map((d,x)=>e.jsx("li",{className:"mt-2 text-sm text-white",children:d.name},x))}),e.jsx("button",{type:"button",className:"mt-4 rounded-md bg-[#2cc9d5] px-4 py-2 text-white hover:bg-blue-600 focus:outline-none",onClick:()=>P(!0),children:"Preview Documents"})]})]})},{register:y,handleSubmit:ve,setError:Ne,formState:{errors:C,isSubmitting:Y},reset:ye}=Me({resolver:De(oe),defaultValues:{name:"",assistant_id:"",from_number_id:"",days:8}}),Ce=()=>{const s=z.reduce((a,l)=>a+l.wordCount,0)>L;return e.jsx("div",{className:"fixed relative inset-0 z-[100] overflow-y-auto bg-[#1d2937] bg-opacity-90 p-4",children:e.jsxs("div",{className:"mx-auto max-w-4xl",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-xl font-bold",children:"Document Preview"}),e.jsxs("p",{children:["Max Words Allowed: ",L," words."]})]}),s&&e.jsxs("div",{className:"p-2 mt-4 text-red-700 bg-red-200 rounded",children:[e.jsxs("p",{children:["Content size exceeds the limit of ",L," words."]}),e.jsx("p",{children:"Consider reducing the number of files."})]}),e.jsx("div",{className:"mt-4",children:z.map((a,l)=>e.jsxs("div",{className:"flex justify-between items-center py-2 border-b",children:[e.jsx("div",{children:a.name}),e.jsxs("div",{children:[(a.size/1024).toFixed(2)," KB"]}),e.jsxs("div",{children:[a.wordCount," words"]})]},l))})]})})},Te=async t=>{let s=new ne;try{console.log(t),await s.callRawAPI("/v3/api/custom/voiceoutreach/user/outbound_campaign/create",{name:t.name,contacts:D.data.filter(a=>H.map(l=>a[l]).some(l=>l!==""&&l!==void 0)),assistant_id:t.assistant_id,from_number_id:t.from_number_id,start_time:new Date(t.start_time).toISOString(),valid_time_opening:O,valid_time_closing:R,days:t.days,knowledgeField:me,language:t.language},"POST"),U(w,"Added"),h&&h(),ye()}catch(a){console.log("Error",a),ze(le,a.message),U(w,a.message,5e3,"error"),Ne("name",{type:"manual",message:a.message})}};i.useEffect(()=>{if(A){console.log("conversion started");const s=se(A,"HH:mm").clone().tz("Etc/GMT");be(s.format("HH:mm")),console.log(s,O)}if(M){console.log("conversion started");const s=se(M,"HH:mm").clone().tz("Etc/GMT");we(s.format("HH:mm")),console.log(s,R)}},[A,M]);const Se=t=>{xe(t.target.value)},_e=t=>{ge(t.target.value)},ke=t=>{fe(t.target.value)};i.useEffect(()=>{w({type:"SETPATH",payload:{path:"outbound_campaigns"}}),async function(){F.setTable("assistants");const s=await F.callRestAPI({user_id:p.user,filter:[`user_id,eq,${p.user}`]},"GETALL");s.error||re(s.list)}(),async function(){F.setTable("numbers");const s=await F.callRestAPI({user_id:p.user,filter:["status,eq,1"]},"GETALL");s.error||de(s.list)}(),async function(){var a,l;F.setTable("user_settings");const s=await F.callRestAPI({user_id:p.user,filter:[`user_id,eq,${p.user}`]},"GETALL");if(!s.error){he(s.list);const o=(a=s.list[0])==null?void 0:a.llm_settings;let E="";try{E=(l=JSON.parse(o))==null?void 0:l.provider}catch{E="anthropic"}console.log("setting",K[E].MAX_CONTENT_SIZE_LIMIT),pe(K[E].MAX_CONTENT_SIZE_LIMIT)}}()},[]);const Ee=()=>{const t=new Date,s=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),l=t.getDate().toString().padStart(2,"0");return`${s}-${a}-${l}`};return e.jsxs("div",{className:"",children:[e.jsx(j,{appear:!1,show:b,as:n.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[100]",onClose:()=>{!V&&!q&&h()},children:[e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center min-h-full",children:e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-x-full",enterTo:"opacity-100 translate-x-0",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-x-0",leaveTo:"opacity-0 translate-x-full",children:e.jsx(_.Panel,{className:" h-[95vh] w-full max-w-3xl transform overflow-y-auto  bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:e.jsxs("div",{className:"p-5",children:[e.jsx("h4",{className:"text-3xl font-medium text-white",children:"Add New Outbound Campaign"}),e.jsxs("form",{className:"flex flex-col gap-2 mt-7 w-full",onSubmit:ve(Te),children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Campaign Name",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Enter the name of the campaign."})]})]}),e.jsx(S,{type:"text",page:"add",name:"name",errors:C,placeholder:"Name of the campaign",register:y,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Assistant",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the assistant responsible for the campaign."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"assistant_id",errors:C,placeholder:"Assistant",options:$.map(t=>t.id),mapping:$.reduce((t,s)=>(t[s.id]=s.assistant_name,t),{}),register:y,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Choose a number",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the phone number to be used for the campaign."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"from_number_id",errors:C,placeholder:"Choose a number",options:B.map(t=>t.id),mapping:B.reduce((t,s)=>(t[s.id]=s.number,t),{}),register:y,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Start Date",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the start date for the campaign."})]})]}),e.jsx(S,{type:"date",page:"add",name:"start_time",errors:C,placeholder:"Start Date",min:Ee(),onChange:_e,dateTime:!0,register:y,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Calling window start time",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the start time for the calling window."})]})]}),e.jsx(S,{type:"time",page:"add",name:"valid_time_opening",errors:C,label:"",onChange:Se,time:!0,placeholder:"Calling window start time",register:y,className:"bg-[#1d2937] placeholder:text-gray-300"}),A&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",A," (GMT: ",O,"; The Call is made according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Calling window end time",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the end time for the calling window."})]})]}),e.jsx(S,{type:"time",page:"add",name:"valid_time_closing",errors:C,label:"",onChange:ke,time:!0,placeholder:"Calling window end time",register:y,className:"bg-[#1d2937] placeholder:text-gray-300"}),M&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Local Time: ",M," (GMT: ",R,"; The Call is made according to GMT Time.)"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Language",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Select the language for the campaign"})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"language",errors:C,placeholder:"Select Language",options:Object.keys(ae),mapping:ae,register:y,className:"bg-[#1d2937] placeholder:text-gray-300"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Days AI can call",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Choose the specific days when the AI is allowed to make calls."})]})]}),e.jsx(S,{type:"mapping",page:"add",name:"days",errors:C,label:"",placeholder:"Days AI can call",options:Object.keys(J),mapping:J,register:y,className:"bg-[#1d2937] placeholder:text-gray-300"}),e.jsxs("p",{className:"mt-2 text-sm text-white",children:["Need a template?"," ",e.jsx("a",{href:"/contact_template.csv",download:!0,className:"text-blue-600 underline",children:"Download sample CSV format"})]})]}),e.jsxs("div",{className:"mt-5",children:[e.jsxs("label",{className:"block mb-2 text-sm font-bold text-white cursor-pointer",children:["Upload CSV"," ",e.jsx("span",{className:"text-sm font-normal text-white",children:"(Please ensure to include the 'phone' and 'name' columns, as they are required.)"})]}),e.jsx(ie,{onUploadAccepted:t=>{console.log("---------------------------"),console.log(t),console.log("---------------------------"),Z(t),I(!0)},children:({getRootProps:t,acceptedFile:s,ProgressBar:a,getRemoveFileProps:l})=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("button",{type:"button",...t(),className:"flex h-[10.375rem] w-full cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-600 bg-transparent text-white ",children:s?e.jsxs("div",{className:"flex flex-col items-center font-bold",children:[e.jsx(qe,{className:"w-5 h-5"}),s.name]}):e.jsx("div",{className:"font-bold bg-transparent",children:"Select CSV File"})}),s?e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{className:"px-4 py-2 mt-6 font-bold text-white bg-transparent rounded focus:shadow-outline focus:outline-none",type:"button",onClick:()=>I(!0),children:"Preview CSV"}),e.jsxs("button",{...l(),onClick:o=>{Z(null),l().onClick(o)},type:"button",className:"flex gap-3 items-center mt-3",children:["Remove ",e.jsx(Je,{className:"w-6 h-6"})]})]}):null]}),e.jsx(a,{})]})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center text-base font-semibold text-white",children:["Upload Documents ",e.jsx("br",{}),"(Documents forming the companies knowledge base)",e.jsxs("span",{className:"relative ml-1 group",children:[e.jsx(v,{className:"w-5 h-5 text-white cursor-pointer"}),e.jsx("div",{className:"hidden absolute bottom-full p-2 mb-2 w-64 text-sm text-white bg-transparent rounded-md group-hover:block",children:"Upload Documents to build the Assistants knowledge base for the call."})]})]}),e.jsx(je,{allow_preview:ue})]}),e.jsx(Oe,{type:"submit",loading:Y,disabled:Y,className:"focus:shadow-outline  mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none",children:"Submit"})]})]})})})})})]})}),e.jsx(j,{appear:!1,show:V&&N!==null,as:n.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[100]",onClose:()=>I(!1),children:[e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full",children:e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(_.Title,{className:"text-3xl font-medium",children:"Preview CSV"}),e.jsx("button",{type:"button",onClick:()=>I(!1),children:e.jsx(te,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"mt-5 space-y-3",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"w-6 h-6 bg-red-300"}),"Missing one or more required columns"]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"w-6 h-6 bg-yellow-300"}),"Missing one or more optional columns"]})]}),e.jsx("div",{className:"small-scroll mt-5 max-h-[60vh] overflow-x-auto overflow-y-auto",children:e.jsxs("table",{className:"w-full border",children:[e.jsx("thead",{className:"bg-gray-50 border-b",children:e.jsx("tr",{children:D.headers.map(t=>{const s=H.includes(t);return e.jsxs("th",{className:"px-4 py-2 whitespace-nowrap",children:[s?"*":""," ",t]},t)})})}),e.jsx("tbody",{children:D.data.map((t,s)=>{const a=D.headers.map(o=>t[o]).some(o=>o===""||o===void 0),l=H.map(o=>t[o]).some(o=>o===""||o===void 0);return e.jsx("tr",{className:`border-b ${l?"!bg-red-300":""} ${a?"bg-yellow-300":""}`,children:D.headers.map(o=>e.jsx("td",{className:"px-4 py-2 whitespace-nowrap",children:t[o]},o))},s)})})]})})]})})})})]})}),e.jsx(j,{appear:!1,show:q,as:n.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[100]",onClose:()=>P(!1),children:[e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full",children:e.jsx(j.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"mx-auto w-full max-w-4xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(_.Title,{className:"text-3xl font-medium",children:"Preview Document"}),e.jsx("button",{type:"button",onClick:()=>P(!1),children:e.jsx(te,{className:"w-6 h-6"})})]}),e.jsx(Ce,{})]})})})})]})})]})},gt=Object.freeze(Object.defineProperty({__proto__:null,default:Ye},Symbol.toStringTag,{value:"Module"}));export{qe as D,v as I,Ye as U,gt as a};
