import React, { Fragment, useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { Dialog, Popover, Transition } from "@headlessui/react";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { MkdDebounceInput } from "Components/MkdDebounceInput";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { SkeletonLoader } from "Components/Skeleton";
import { CheckIcon } from "@heroicons/react/24/outline";
import { XIcon } from "lucide-react";

let sdk = new MkdSDK();

const columns = [
  { header: "Date", accessor: "datetime" },
  { header: "Campaign Name", accessor: "campaign_name" },
  { header: "Campaign Type", accessor: "campaign_type" },
  { header: "Call Id", accessor: "call_id" },
  { header: "Credits Used", accessor: "credits_used" },
  { header: "Total Duration (seconds)", accessor: "total_duration" },
];

const filterableColumns = columns.filter((col) => {
  const excludedColumns = [""];
  return !excludedColumns.includes(col.accessor);
});

const creditBundles = ["20", "40", "50", "100", "500"];
const autoloadAmounts = ["5", "10", "15", "20"];

const bundleDetails = [
  {
    id: 4,
    currency: "usd",
    stripe_id: "price_1PeCvYBgOlWo0lDUiy42q15D",
    name: "Basic",
    amount: 20,
    type: "one_time",
    status: 1,
    product_name: "calls",
  },
  // ... add other bundle details
];

function formatDate(dateString) {
  const date = new Date(dateString);
  const options = { year: "numeric", month: "long", day: "numeric" };
  return date.toLocaleDateString("en-US", options);
}

const checkout = async ({
  priceId,
  priceCurrency,
  priceStripeId,
  priceName,
  productName,
  price,
  quantity = 1,
}) => {
  let params = {
    success_url: `${sdk.fe_baseurl}/user/stripe_subscription?success=true&session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${sdk.fe_baseurl}/user/stripe_subscription?success=false&session_id={CHECKOUT_SESSION_ID}`,
    mode: "payment",
    payment_method_types: ["card"],
    shipping_address_collection: {
      allowed_countries: ["CA", "US"],
    },
    shipping_options: [
      {
        shipping_rate_data: {
          type: "fixed_amount",
          display_name: "Shipping fees",
          fixed_amount: {
            currency: priceCurrency,
            amount: 0,
          },
        },
      },
    ],
    locale: "en",
    line_items: [
      {
        price: priceStripeId,
        quantity,
      },
    ],
    phone_number_collection: {
      enabled: false,
    },
    payment_intent_data: {
      metadata: {
        app_price_id: priceId,
        app_product_name: priceName,
        app_highlevel_product_name: productName,
        is_order: "true",
      },
    },
    price,
  };

  try {
    const {
      error,
      model: checkout,
      message,
    } = await sdk.initCheckoutSession(params);
    if (error) {
      return;
    }
    if (checkout?.url) location.href = checkout.url;
  } catch (error) {
    console.error("Error", error);
  }
};

const UsagePage = () => {
  const params = useLocation();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [usageData, setUsageData] = useState([]);
  const [totalCost, setTotalCost] = useState(0);
  const [credits, setCredits] = useState(0);
  const [plans, setPlans] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filterValues, setFilterValues] = React.useState({});
  const [optionValue, setOptionValue] = React.useState("eq");
  const navigate = useNavigate();
  const [loadingCheckout, setLoadingCheckout] = useState(false);
  const [selectedBundle, setSelectedBundle] = useState("20");
  const [selectedAutoload, setSelectedAutoload] = useState("5");
  const [autoloadEnabled, setAutoloadEnabled] = useState(false);
  const [userCards, setUserCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [loadingCards, setLoadingCards] = useState(false);
  const [newCardNumber, setNewCardNumber] = useState("");
  const [newExpMonth, setNewExpMonth] = useState("");
  const [newExpYear, setNewExpYear] = useState("");
  const [newCVC, setNewCVC] = useState("");

  const schema = yup.object({
    start_date: yup.string(),
    end_date: yup.string(),
    campaign_name: yup.string(),
    campaign_type: yup.string(),
  });

  const handleModalOpen = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const addFilterCondition = (option, selectedValue, inputValue) => {
    if (!inputValue) {
      setFilterConditions((prevConditions) => {
        return prevConditions.filter((cond) => !cond.startsWith(option + ","));
      });
      return;
    }

    let value = inputValue;
    let operator = selectedValue || "eq";

    const condition = `${option},${operator},${value}`;
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (cond) => !cond.startsWith(option + ",")
      );
      return [...newConditions, condition];
    });
  };

  const campaignTypeMapping = [
    { key: "", value: "All" },
    { key: 1, value: "Outbound" },
    { key: 2, value: "Inbound" },
    { key: 3, value: "Test" },
  ];

  async function getData() {
    setLoading(true);
    let queryString = "?allow=true";

    if (filterConditions.length > 0) {
      filterConditions.forEach((condition) => {
        const [field, operator, value] = condition.split(",");
        queryString += `&${field}=${value}`;
      });
    }

    reset();
    try {
      const response = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/usage" + queryString,
        {},
        "GET"
      );
      if (!response.error) {
        setTotalCost(response.total_credits);
        setCredits(response.credits);
        setUsageData(response.usage);
      }
    } catch (e) {
      console.log(e);
    }
    setLoading(false);
  }

  const handleClearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    setFilterValues({});
    getData();
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "subscription",
      },
    });

    getData();
  }, [filterConditions]);

  useEffect(() => {
    if (autoloadEnabled) {
      fetchUserCards();
    }
  }, [autoloadEnabled]);

  const fetchUserCards = async () => {
    setLoadingCards(true);
    try {
      const response = await sdk.callRawAPI(
        "/v2/api/lambda/stripe/customer/cards",
        {},
        "GET"
      );
      if (response && response.data?.data) {
        setUserCards(response.data?.data);
        if (response.data?.data?.length > 0) {
          setSelectedCard(response.data?.data[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching cards:", error);
    }
    setLoadingCards(false);
  };

  const handleBundleSelect = (bundle) => setSelectedBundle(bundle);
  const handleAutoloadSelect = (autoload) => setSelectedAutoload(autoload);

  const handlePurchase = async () => {
    setLoadingCheckout(true);

    if (autoloadEnabled) {
      let paymentPayload = {
        autoload_amount: selectedAutoload,
        credit_bundle: selectedBundle,
      };

      if (userCards.length > 0) {
        paymentPayload.card_id = selectedCard;
      } else {
        if (!newCardNumber || !newExpMonth || !newExpYear || !newCVC) {
          console.error("Please enter complete card details.");
          setLoadingCheckout(false);
          return;
        }

        const tokenResponse = await sdk.callRawAPI(
          "/v2/api/lambda/stripe/customer/card/create_token",
          {
            card_number: newCardNumber,
            exp_month: newExpMonth,
            exp_year: newExpYear,
            cvc: newCVC,
          },
          "POST"
        );
        if (tokenResponse.error) {
          console.error("Error creating card token:", tokenResponse.message);
          setLoadingCheckout(false);
          return;
        }
        const cardToken = tokenResponse.token;

        const addCardResponse = await sdk.callRawAPI(
          "/v2/api/lambda/stripe/customer/card/",
          { sourceToken: cardToken },
          "POST"
        );
        if (addCardResponse.error) {
          console.error("Error adding card to user:", addCardResponse.message);
          setLoadingCheckout(false);
          return;
        }
        const { model } = addCardResponse;
        paymentPayload.card_id = model.card_id || model.default_source;
      }

      try {
        const response = await sdk.callRawAPI(
          "/v3/api/custom/voiceoutreach/user/autoreload",
          paymentPayload,
          "POST"
        );
        if (response.error) {
          console.error("Auto reload setup failed:", response.message);
          showToast(globalDispatch, "Error purchasing credits", 4000);
        } else {
          console.log("Auto reload enabled:", response);
          showToast(
            globalDispatch,
            "Credit added and auto reload enabled",
            4000
          );
          window.location.reload();
        }
      } catch (err) {
        console.error("Error setting up auto reload:", err);
        showToast(globalDispatch, "Error setting up auto reload", 4000);
      } finally {
        handleModalClose();
        setLoadingCheckout(false);
      }
    } else {
      const bundleInfo = bundleDetails.find(
        (bundle) => bundle.amount == selectedBundle
      );
      await checkout({
        priceId: bundleInfo.id,
        priceCurrency: "usd",
        priceStripeId: bundleInfo.stripe_id,
        priceName: bundleInfo.name,
        productName: bundleInfo.product_name,
        price: bundleInfo.amount,
        quantity: 1,
      });
    }
    handleModalClose();
    setLoadingCheckout(false);
  };

  const handleCardNumberChange = (e) => {
    const input = e.target.value;
    const cleaned = input.replace(/\D+/g, "");
    const formatted = cleaned.match(/.{1,4}/g)?.join(" ") || "";
    setNewCardNumber(formatted);
  };

  return (
    <div className="p-8" style={{ overflowY: "auto", maxHeight: "100vh" }}>
      <div className="flex items-center justify-between text-white">
        <h2>Automate Intel</h2>
        <div>
          <span>Credits: {credits.toFixed(2)}</span>
          <button
            onClick={handleModalOpen}
            className="ml-4 rounded-md bg-[#19b2f6]/80 px-4 py-2 text-white transition-colors hover:bg-[#19b2f6]"
          >
            Buy Credits
          </button>
        </div>
      </div>

      <Transition appear show={isModalOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={handleModalClose}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                  <div className="mb-4 flex items-center justify-between">
                    <Dialog.Title className="text-xl font-medium text-white">
                      Buy Credits
                    </Dialog.Title>
                    <button onClick={handleModalClose}>
                      <XIcon className="h-6 w-6 text-white/70 hover:text-white" />
                    </button>
                  </div>

                  <div className="space-y-6">
                    {/* Credit Bundles */}
                    <div className="space-y-3">
                      <h3 className="text-lg font-semibold text-white">
                        Credit Bundles
                      </h3>
                      <div className="grid grid-cols-5 gap-2">
                        {creditBundles.map((bundle) => (
                          <button
                            key={bundle}
                            onClick={() => handleBundleSelect(bundle)}
                            className={`rounded-lg border-2 px-4 py-2 transition-all ${
                              selectedBundle === bundle
                                ? "border-[#19b2f6] bg-[#19b2f6]/20 text-[#19b2f6]"
                                : "border-gray-600 text-white hover:border-[#19b2f6]/50"
                            }`}
                          >
                            ${bundle}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Autoload Section */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold text-white">
                          Autoload
                        </h3>
                        <div className="flex items-center">
                          <label className="relative inline-flex cursor-pointer items-center">
                            <input
                              type="checkbox"
                              className="peer sr-only"
                              checked={autoloadEnabled}
                              onChange={(e) =>
                                setAutoloadEnabled(e.target.checked)
                              }
                            />
                            <div className="peer h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#19b2f6] peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#19b2f6]/30"></div>
                          </label>
                        </div>
                      </div>

                      <p className="text-sm text-gray-400">
                        Autoload if balance drops below
                      </p>
                      <div className="grid grid-cols-5 gap-2">
                        {autoloadAmounts.map((amount) => (
                          <button
                            key={amount}
                            onClick={() => handleAutoloadSelect(amount)}
                            disabled={!autoloadEnabled}
                            className={`rounded-lg border-2 px-4 py-2 transition-all ${
                              !autoloadEnabled
                                ? "cursor-not-allowed border-gray-600 text-gray-400 opacity-50"
                                : selectedAutoload === amount
                                ? "border-[#19b2f6] bg-[#19b2f6]/20 text-[#19b2f6]"
                                : "border-gray-600 text-white hover:border-[#19b2f6]/50"
                            }`}
                          >
                            ${amount}
                          </button>
                        ))}
                      </div>

                      {/* Card selection/entry section */}
                      {autoloadEnabled && (
                        <div className="mt-4 space-y-4">
                          <h4 className="text-lg font-semibold text-white">
                            {userCards.length > 0
                              ? "Select a Card for Autoload"
                              : "Enter Card Details"}
                          </h4>
                          {loadingCards ? (
                            <p className="text-gray-400">Loading cards...</p>
                          ) : userCards.length > 0 ? (
                            <select
                              value={selectedCard}
                              onChange={(e) => setSelectedCard(e.target.value)}
                              className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white focus:border-[#19b2f6] focus:outline-none"
                            >
                              {userCards.map((card) => (
                                <option key={card.id} value={card.id}>
                                  {card.brand} ending in {card.last4}
                                </option>
                              ))}
                            </select>
                          ) : (
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-400">
                                  Card Number
                                </label>
                                <input
                                  type="text"
                                  placeholder="1234 5678 9012 3456"
                                  value={newCardNumber}
                                  maxLength={19}
                                  onChange={handleCardNumberChange}
                                  className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"
                                />
                              </div>
                              <div className="grid grid-cols-3 gap-4">
                                <div className="space-y-2">
                                  <label className="block text-sm font-medium text-gray-400">
                                    Exp Month
                                  </label>
                                  <input
                                    type="text"
                                    placeholder="MM"
                                    maxLength={2}
                                    value={newExpMonth}
                                    onChange={(e) =>
                                      setNewExpMonth(e.target.value)
                                    }
                                    className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <label className="block text-sm font-medium text-gray-400">
                                    Exp Year
                                  </label>
                                  <input
                                    type="text"
                                    placeholder="YYYY"
                                    value={newExpYear}
                                    maxLength={4}
                                    onChange={(e) =>
                                      setNewExpYear(e.target.value)
                                    }
                                    className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <label className="block text-sm font-medium text-gray-400">
                                    CVC
                                  </label>
                                  <input
                                    type="text"
                                    placeholder="CVC"
                                    maxLength={3}
                                    value={newCVC}
                                    onChange={(e) => setNewCVC(e.target.value)}
                                    className="w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-6 border-t border-gray-600 pt-6">
                    <button
                      className="w-full rounded-lg bg-[#19b2f6]/80 py-3 font-semibold text-white transition-colors hover:bg-[#19b2f6] disabled:cursor-not-allowed disabled:opacity-50"
                      onClick={handlePurchase}
                      disabled={loadingCheckout}
                    >
                      {loadingCheckout ? "Processing..." : "Buy Credits"}
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="mt-8 rounded-lg bg-[#1d2937] p-5 px-12">
          <div className="flex items-start justify-between">
            <div className="mt-4 rounded-lg bg-[#1d2937] py-5">
              <div className="flex h-fit items-center justify-between gap-3">
                <div className="flex w-[200px] min-w-[200px] items-center justify-between">
                  <div className="relative z-10 rounded bg-[#1d2937]">
                    <Popover>
                      <div className="flex items-center gap-4 bg-[#1d2937] text-white">
                        <Popover.Button className="border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white  focus-visible:outline-0 focus-visible:outline-transparent">
                          <BiFilterAlt />
                          <span>Filters</span>
                          {selectedOptions.length > 0 && (
                            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                              {selectedOptions.length}
                            </span>
                          )}
                        </Popover.Button>
                        <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400">
                          <BiSearch className="text-xl text-white" />
                          <input
                            type="text"
                            placeholder="Search by campaign name"
                            className="border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none"
                            style={{ boxShadow: "0 0 transparent" }}
                            value={searchValue}
                            onChange={(e) => {
                              setSearchValue(e.target.value);
                              addFilterCondition(
                                "campaign_name",
                                "cs",
                                e.target.value
                              );
                            }}
                          />
                          {searchValue && (
                            <AiOutlineClose
                              className="cursor-pointer text-lg text-white"
                              onClick={() => {
                                setSearchValue("");
                                addFilterCondition("campaign_name", "cs", "");
                              }}
                            />
                          )}
                        </div>
                      </div>

                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                      >
                        <Popover.Panel>
                          <div className="filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10">
                            <span className="absolute left-5 top-2 font-medium text-white">
                              Filters
                            </span>
                            <Popover.Button
                              onClick={() => {
                                console.log("clicked");
                                setSelectedOptions([]);
                                setFilterConditions([]);
                                setFilterValues({});
                              }}
                            >
                              <XIcon className="absolute right-2 top-2 cursor-pointer text-white" />
                            </Popover.Button>
                            {selectedOptions?.map((option, index) => (
                              <div
                                key={index}
                                className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600"
                              >
                                <button
                                  type="button"
                                  className="block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none"
                                  title={option}
                                  style={{ WebkitTouchCallout: "none" }}
                                >
                                  {filterableColumns.find(
                                    (col) => col.accessor === option
                                  )?.header || option}
                                </button>

                                <select
                                  className="w-1/3 appearance-none rounded-md border-none bg-gray-100 capitalize text-gray-600 outline-0"
                                  value={optionValue}
                                  onChange={(e) => {
                                    setOptionValue(e.target.value);
                                    addFilterCondition(
                                      option,
                                      e.target.value,
                                      filterValues[option]
                                    );
                                  }}
                                >
                                  <option value="eq">equals</option>
                                  <option value="cs">contains</option>
                                  <option value="sw">start with</option>
                                  <option value="ew">ends with</option>
                                  <option value="lt">lower than</option>
                                  <option value="le">lower or equal</option>
                                  <option value="ge">greater or equal</option>
                                  <option value="gt">greater than</option>
                                  <option value="bt">between</option>
                                  <option value="in">in</option>
                                  <option value="is">is null</option>
                                </select>

                                {option === "status" ? (
                                  <select
                                    value={filterValues[option] || ""}
                                    onChange={(e) => {
                                      setFilterValues((prev) => ({
                                        ...prev,
                                        [option]: e.target.value,
                                      }));
                                      addFilterCondition(
                                        option,
                                        optionValue,
                                        e.target.value
                                      );
                                    }}
                                    className="h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none"
                                  >
                                    <option value="">Select Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                  </select>
                                ) : (
                                  <MkdDebounceInput
                                    type="text"
                                    labelClassName="!mb-0"
                                    placeholder="Enter value"
                                    setValue={() => {}}
                                    showIcon={false}
                                    className=" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none"
                                    onReady={(value) =>
                                      addFilterCondition(
                                        option,
                                        optionValue,
                                        value
                                      )
                                    }
                                  />
                                )}

                                <RiDeleteBin5Line
                                  className="cursor-pointer text-2xl text-red-600"
                                  onClick={() => {
                                    setSelectedOptions((prevOptions) =>
                                      prevOptions.filter((op) => op !== option)
                                    );
                                    setFilterConditions((prevConditions) =>
                                      prevConditions.filter(
                                        (condition) =>
                                          !condition.includes(option)
                                      )
                                    );
                                    setFilterValues((prev) => {
                                      const newValues = { ...prev };
                                      delete newValues[option];
                                      return newValues;
                                    });
                                  }}
                                />
                              </div>
                            ))}

                            <div className="search-buttons relative flex items-center justify-between font-semibold">
                              <div
                                className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out"
                                onClick={() =>
                                  setShowFilterOptions(!showFilterOptions)
                                }
                              >
                                <AiOutlinePlus />
                                Add filter
                              </div>

                              {showFilterOptions && (
                                <div className="absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3">
                                  <ul className="flex flex-col gap-2 text-gray-500">
                                    {filterableColumns.map((column) => (
                                      <li
                                        key={column.accessor}
                                        className={`${
                                          selectedOptions.includes(
                                            column.accessor
                                          )
                                            ? "cursor-not-allowed text-gray-100"
                                            : "cursor-pointer text-gray-400 hover:text-white"
                                        }`}
                                        onClick={() => {
                                          if (
                                            !selectedOptions.includes(
                                              column.accessor
                                            )
                                          ) {
                                            setSelectedOptions((prev) => [
                                              ...prev,
                                              column.accessor,
                                            ]);
                                            setFilterValues((prev) => ({
                                              ...prev,
                                              [column.accessor]: "",
                                            }));
                                          }
                                          setShowFilterOptions(false);
                                        }}
                                      >
                                        {column.header}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {selectedOptions.length > 0 && (
                                <div
                                  onClick={handleClearAllFilters}
                                  className="inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white"
                                >
                                  Clear all filter
                                </div>
                              )}
                            </div>
                          </div>
                        </Popover.Panel>
                      </Transition>
                    </Popover>
                  </div>
                </div>
              </div>
            </div>
            <div className="align-right text-white">
              <h4 className="text-right text-white">
                Total: ${totalCost.toFixed(2)}
              </h4>
            </div>
          </div>

          <h4 className="my-4 text-2xl font-bold text-white">Usage</h4>

          <div className="w-full overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-400 border  border-gray-400 bg-[#1d2937]">
              <thead className="bg-[#1d2937]">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-400 bg-[#1d2937]">
                {usageData.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {columns.map((column, colIndex) => {
                      if (column.accessor === "datetime") {
                        return (
                          <td
                            key={colIndex}
                            className="whitespace-nowrap px-6 py-4 text-sm text-white"
                          >
                            {formatDate(row[column.accessor])}
                          </td>
                        );
                      } else if (column.accessor === "credits_used") {
                        return (
                          <td
                            key={colIndex}
                            className="whitespace-nowrap px-6 py-4 text-sm text-white"
                          >
                            {(row[column.accessor] || 0)?.toFixed(2)}
                          </td>
                        );
                      } else {
                        return (
                          <td
                            key={colIndex}
                            className="whitespace-nowrap px-6 py-4 text-sm text-white"
                          >
                            {row[column.accessor] ?? "N/A"}
                          </td>
                        );
                      }
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsagePage;
