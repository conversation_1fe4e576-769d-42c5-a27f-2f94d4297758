import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as s,f,r as h}from"./vendor-2ae44a2e.js";import{M as S,A as g,G as b,d as x,U as w}from"./index-d0a8f5da.js";import{M as E}from"./index-9aa09a5c.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";new S;const D=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"campaign",accessor:"campaign_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Call Id",accessor:"call_id",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"Outbound",1:"Inbound"}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"Outbound",1:"Inbound"}},{header:"Recording Link",accessor:"recording_link",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Duration (seconds)",accessor:"duration",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Number",accessor:"number",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"inactive",1:"active"}},{header:"Action",accessor:""}],V=()=>{const{state:l}=s.useContext(g),{dispatch:d}=s.useContext(b);f();const[n,t]=s.useState(!1),[A,c]=s.useState(!1),[v,p]=s.useState(),m=h.useRef(null),[I,u]=s.useState([]);s.useEffect(()=>{d({type:"SETPATH",payload:{path:"outbound_call_logs"}})},[]);const i=(a,o,r=[])=>{switch(a){case"add":t(o);break;case"edit":c(o),u(r),p(r[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto rounded bg-white pt-5 shadow md:p-5",children:e.jsx(x,{columns:D,tableRole:"user",table:"call_logs",actionId:"id",defaultFilter:["type,eq,0",`user_id,eq,${l.user}`],actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:a=>i("edit",!0,a)},delete:{show:!0,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>i("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"onTable",refreshRef:m})})}),e.jsx(E,{isModalActive:n,closeModalFn:()=>t(!1),children:e.jsx(w,{setSidebar:t})})]})};export{V as default};
