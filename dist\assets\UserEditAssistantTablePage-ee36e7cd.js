import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as q,h as S,r as A}from"./vendor-2ae44a2e.js";import{u as T}from"./react-hook-form-47c010f8.js";import{o as k}from"./yup-5abd4662.js";import{c as C,a as l}from"./yup-c2e87575.js";import{M as I,A as P,G as M,t as R,s as F}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as n}from"./MkdInput-c12da351.js";import{I as L}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import{b as G}from"./index.esm-4b383179.js";import{S as U}from"./index-f2c2b086.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let m=new I;const ge=({activeId:d,closeSidebar:c})=>{const{dispatch:y}=a.useContext(P),N=C({script:l().required("Script is required"),voicemail_script:l().required("Voicemail script is required"),assistant_name:l().required("Assistant name is required"),first_message:l().required("First message is required"),notes:l().required("Notes are required")}).required(),{dispatch:x}=a.useContext(M);a.useState({});const[u,p]=a.useState(!1),[j,g]=a.useState(!1),v=q(),{register:r,handleSubmit:E,setError:h,setValue:i,formState:{errors:o}}=T({resolver:k(N)});S(),A.useEffect(function(){(async function(){g(!0);try{m.setTable("assistants".trim(""));const e=await m.callRestAPI({id:Number(d)},"GET");e.error||(i("assistant_name",e.model.assistant_name),i("script",e.model.script),i("voicemail_script",e.model.voicemail_script),i("first_message",e.model.first_message),i("notes",e.model.notes))}catch(e){console.log("error",e),R(y,e.message)}g(!1)})()},[]);const _=async e=>{console.log("data",e),p(!0);try{m.setTable("assistants");const s=await m.callRestAPI({id:d,assistant_name:e.assistant_name,script:e.script,voicemail_script:e.voicemail_script,first_message:e.first_message,notes:e.notes},"PUT");if(!s.error)F(x,"Updated"),v("/user/assistants");else if(s.validation){const b=Object.keys(s.validation);for(let f=0;f<b.length;f++){const w=b[f];h(w,{type:"manual",message:s.validation[w]})}}p(!1),c&&c()}catch(s){p(!1),console.log("Error",s),h("name",{type:"manual",message:s.message})}};return a.useEffect(()=>{x({type:"SETPATH",payload:{path:"assistants"}})},[]),t.jsxs("div",{className:"flex flex-col pb-5 h-screen",children:[t.jsxs("div",{className:"flex flex-row justify-between",children:[t.jsx("h4",{className:"mt-4 text-3xl font-semibold text-white",children:"Edit Assistant"}),t.jsx("button",{onClick:c,children:t.jsx(G,{className:"text-2xl text-white"})})]}),j?t.jsx("div",{className:"flex justify-center items-center py-5 w-full min-w-full max-w-full max-h-fit min-h-[70vh]",children:t.jsx(U,{size:100,color:"#0EA5E9"})}):t.jsxs("form",{className:"flex flex-col flex-grow pb-10 mt-7 w-full",onSubmit:E(_),children:[t.jsx(n,{type:"text",page:"edit",name:"assistant_name",errors:o,label:"Assistant Name",placeholder:"name",register:r,className:"text-white bg-transparent placeholder:text-gray-300"}),t.jsx(n,{type:"text",page:"edit",name:"first_message",errors:o,label:"First Message",placeholder:"First Message",register:r,className:"text-white bg-transparent placeholder:text-gray-300"}),t.jsx(n,{type:"textarea",page:"edit",name:"script",errors:o,label:"script",placeholder:"script",register:r,className:"flex-grow text-white bg-transparent placeholder:text-gray-300",containerClassName:"flex flex-col flex-grow",rows:10}),t.jsx(n,{type:"textarea",page:"edit",name:"voicemail_script",errors:o,label:"Voicemail script",placeholder:"voicemail script",register:r,className:"flex-grow text-white bg-transparent placeholder:text-gray-300",containerClassName:"flex flex-col flex-grow",maxLength:500,rows:10}),t.jsx(n,{type:"textarea",page:"edit",name:"notes",errors:o,label:"Notes",placeholder:"Notes",register:r,className:"text-white bg-transparent placeholder:text-gray-300",rows:5}),t.jsx(L,{type:"submit",disabled:u,className:"focus:shadow-outline w-full  rounded bg-[#19b2f6]/80   px-4 py-2 font-semibold text-white focus:outline-none",loading:u,children:"Submit"})]})]})};export{ge as default};
