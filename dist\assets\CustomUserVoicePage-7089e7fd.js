import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as t,R as Y}from"./vendor-2ae44a2e.js";import{u as Z}from"./react-hook-form-47c010f8.js";import{M as k,A as ee,G as se,s as A,t as ae}from"./index-d0a8f5da.js";import{o as te}from"./yup-5abd4662.js";import{c as ne,a as u}from"./yup-c2e87575.js";import{b as re}from"./websocket-791f88c1.js";import{_ as ie}from"./MoonLoader-62b0139a.js";import{$ as i}from"./@headlessui/react-7bce1936.js";import{M as E,S as oe}from"./SpeakerWaveIcon-4df22e9a.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";const o={INACTIVE:"inactive",IN_PROGRESS:"recording"},P={"English (USA)":"English (USA)","English (UK)":"English (UK)","English (Australia)":"English (Australia)","English (Canada)":"English (Canada)",Japanese:"Japanese",Chinese:"Chinese",German:"German",Hindi:"Hindi","French (France)":"French (France)","French (Canada)":"French (Canada)",Korean:"Korean","Portuguese (Brazil)":"Portuguese (Brazil)","Portuguese (Portugal)":"Portuguese (Portugal)",Italian:"Italian","Spanish (Spain)":"Spanish (Spain)","Spanish (Mexico)":"Spanish (Mexico)",Indonesian:"Indonesian",Dutch:"Dutch",Turkish:"Turkish",Filipino:"Filipino",Polish:"Polish",Swedish:"Swedish",Bulgarian:"Bulgarian",Romanian:"Romanian","Arabic (Saudi Arabia)":"Arabic (Saudi Arabia)","Arabic (UAE)":"Arabic (UAE)",Czech:"Czech",Greek:"Greek",Finnish:"Finnish",Croatian:"Croatian",Malay:"Malay",Slovak:"Slovak",Danish:"Danish",Tamil:"Tamil",Ukrainian:"Ukrainian",Russian:"Russian"},R="audio/webm";let m=new k;const Ge=()=>{var S,N,y,v,_,C;const{state:h,dispatch:I}=t.useContext(ee),{dispatch:x}=t.useContext(se),[g,le]=t.useState(!1),c=t.useRef(null),[p,b]=t.useState(o.INACTIVE),[T,ce]=t.useState(null);t.useState([]);const[U,F]=t.useState("+1"),[G,M]=t.useState([]),[$,L]=t.useState([]),[f,V]=t.useState(null),[d,w]=t.useState(null),O=ne({assistant_id:u().required("Please select an assistant"),number_id:u().required("Please select the inbound number"),to_number:u().required("Please enter the destination number"),language:u().required("Please select a language")}),{register:l,handleSubmit:D,setError:K,setValue:de,formState:{errors:r,isSubmitting:z,isValid:B},reset:q}=Z({mode:"onChange",resolver:te(O),defaultValues:{assistant_id:"",number_id:"",to_number:""}}),W=async s=>{let a=new k;try{if(a.setTable("numbers"),!s.number_id||!s.assistant_id)throw new Error("Missing values");await a.callRawAPI("/v3/api/custom/voiceoutreach/user/test_call",{from_number:s.number_id,assistant_id:s.assistant_id,to_number:U+s.to_number,language:s.language},"POST"),A(x,"Successful"),q()}catch(n){ae(I,n.message),A(x,n.message,1e4,"error"),K("assistant_id",{type:"manual",message:n.message})}},H=async()=>{b(o.IN_PROGRESS);const s=new MediaRecorder(T,{mimeType:R});c.current=s,c.current.start(),c.current.ondataavailable=a=>{typeof a.data>"u"||a.data.size!==0&&d&&d.send(a.data)}},J=()=>{b(o.INACTIVE),c.current.stop()},Q=()=>{const s=new re.w3cwebsocket("wss://callagentds.manaknightdigital.com/1");s.onopen=()=>{},s.onmessage=a=>{const n=new Blob([a.data],{type:R}),X=URL.createObjectURL(n);V(X)},s.onclose=()=>{},s.onerror=a=>{console.error("WebSocket error:",a)},w(s)},j=()=>{d&&(d.close(),w(null))};return Y.useEffect(()=>(x({type:"SETPATH",payload:{path:"voice"}}),async function(){m.setTable("numbers");const a=await m.callRestAPI({},"GETALL");a.error||L(a.list?a.list.map(n=>({number_id:n.id,name:n.number})):[])}(),async function(){m.setTable("assistants");const a=await m.callRestAPI({user_id:h.user,filter:[`user_id,eq,${h.user}`]},"GETALL");a.error||M(a.list?a.list.map(n=>({assistant_id:n.id,name:n.assistant_name})):[])}(),()=>{j()}),[]),e.jsx("div",{className:"mx-auto flex w-full justify-center rounded-md bg-[#1d2937] px-5 py-10 sm:w-[90%] md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl",children:e.jsxs(i.Group,{as:"div",className:"w-full max-w-xl rounded-xl bg-[#1d2937] px-0 py-0 text-[#ffffff]",children:[e.jsx(i.List,{className:"flex items-center justify-center",children:e.jsx(i,{className:({selected:s})=>` rounded-lg px-3 py-1 text-2xl ${s?" text-white":"text-white"} `,children:"Call your phone"})}),e.jsxs(i.Panels,{children:[e.jsxs(i.Panel,{as:"form",className:"max-w-xl",onSubmit:D(W),children:[e.jsxs("div",{className:"mt-3 flex max-w-xl flex-col items-center justify-center",children:[e.jsx("audio",{className:"h-[fit-content] w-[100%]",loop:!1,src:"https://via.placeholder.com/50?text=%20",controls:!1,muted:!1,autoPlay:!1}),null,g&&p===o.INACTIVE?e.jsxs(e.Fragment,{children:[e.jsx(E,{onClick:()=>{H(),Q()},className:"h-[50px] w-[50px] cursor-pointer"}),e.jsxs("div",{className:"my-4 flex items-center",children:[e.jsx("span",{className:"block  h-[15px] w-[15px] rounded-full bg-[red]"}),e.jsx("span",{className:"mx-2 block font-bold",children:"Start recording"})]})]}):null,p===o.IN_PROGRESS?e.jsxs(e.Fragment,{children:[e.jsx(E,{onClick:()=>{J(),j()},className:"h-[50px] w-[50px] cursor-pointer"}),e.jsx("div",{className:"my-4 flex items-center",children:e.jsx("span",{className:"block h-[15px] w-[15px] animate-pulse rounded-full bg-[red]"})})]}):null,f&&p===o.INACTIVE?e.jsx("div",{className:"audio-player",children:e.jsx("audio",{src:f,controls:!0})}):null]}),e.jsxs("div",{className:"bg-[#1d2937]",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Assistants"}),e.jsxs("select",{type:"dropdown",id:"assistant_id",...l("assistant_id"),className:`focus:shadow-outline w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base ${(S=r.assistant_id)!=null&&S.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Assistant"}),G.map((s,a)=>e.jsx("option",{value:s.assistant_id,children:s.name},a+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Phone Number"}),e.jsxs("select",{type:"dropdown",id:"number_id",...l("number_id"),className:`focus:shadow-outline flex h-[30px] w-full appearance-none items-center rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base lg:h-[38px] xl:h-[43px] xl:h-[43px] ${(N=r.number_id)!=null&&N.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Phone Number"}),$.map((s,a)=>e.jsx("option",{value:s.number_id,children:s.name},a+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-3 text-[15px] font-medium",children:"Phone to Call"}),e.jsxs("div",{className:"mt-2 flex",children:[e.jsxs("select",{id:"country_code",...l("country_code"),onChange:s=>F(s.target.value),className:`focus:shadow-outline h-[30px] appearance-none rounded-l border bg-[#1d2937] px-3 py-1 pr-6 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base md:text-base lg:h-[38px] xl:h-[43px] xl:h-[43px] ${(y=r.country_code)!=null&&y.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Country Code"}),e.jsx("option",{value:"+1",children:"+1 (USA)"}),e.jsx("option",{value:"+1",children:"+1 (CA)"}),e.jsx("option",{value:"+44",children:"+44 (UK)"}),e.jsx("option",{value:"+234",children:"+234 (Nigeria)"}),e.jsx("option",{value:"+40",children:"+40 (ROM)"}),e.jsx("option",{value:"+92",children:"+92 (PK)"})]}),e.jsx("input",{type:"text",placeholder:"Enter Number",...l("to_number"),className:`focus:shadow-outline h-[30px] w-full appearance-none rounded-r border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow placeholder:text-gray-300 focus:outline-none md:text-base lg:h-[38px] xl:h-[43px] ${(v=r.to_number)!=null&&v.message?"border-red-500":""}`})]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Language"}),e.jsxs("select",{type:"dropdown",id:"language",...l("language"),className:`focus:shadow-outline w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none md:text-base ${(_=r.language)!=null&&_.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Select Language"}),Object.keys(P).map(s=>e.jsx("option",{value:s,children:P[s]},s))]}),((C=r.language)==null?void 0:C.message)&&e.jsx("p",{className:"text-xs italic text-red-500",children:r.language.message})]}),e.jsx("div",{className:"flex w-full items-center justify-center",children:e.jsxs("button",{disabled:!B,className:"mt-4 flex w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90 disabled:cursor-not-allowed disabled:opacity-50",children:[z?e.jsx(ie,{color:"white",loading:!0,size:20}):null,e.jsx("span",{children:"Send Call"})]})})]}),e.jsxs(i.Panel,{as:"div",className:"flex h-[435px] flex-col items-center justify-center gap-4",children:[e.jsx(oe,{className:"h-12 w-12"}),e.jsx("h3",{children:"Coming soon"})]})]})]})})};export{Ge as default};
