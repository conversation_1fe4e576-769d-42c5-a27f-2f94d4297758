import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as n,L as l}from"./vendor-2ae44a2e.js";import{M as _,A as S,G as k,s as N}from"./index-d0a8f5da.js";import{_ as M}from"./qr-scanner-cf010ec4.js";import{g as F}from"./@mantine/core-1006e8cf.js";import{t as L}from"./tailwind-merge-05141ada.js";import{d as C,e as g,f as T,h as f,j as B}from"./index.esm-42944128.js";import{b as O,c as R,d as I}from"./index.esm-de9a80b6.js";import{g as j,h as E,i as A,U as z,B as P,L as V}from"./lucide-react-f66dbccf.js";import{q as w,_ as v}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@fullcalendar/core-a789a586.js";n.lazy(()=>M(()=>import("./BackButton-5d19a352.js"),["assets/BackButton-5d19a352.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-f2c2b086.js","assets/qr-scanner-cf010ec4.js"]));let y=new _;function b(...r){return L(F(r))}const D=({isOpen:r,onClose:t,onSubmit:s})=>{const{globalDispatch:i,state:a}=n.useContext(k),[x,h]=n.useState(""),[c,m]=n.useState(""),p=()=>{if(x.trim()===""){m("Query cannot be empty");return}d(x),s(x),h(""),m(""),t()},d=async o=>{console.log("Bug report submitted:",o);try{y.setTable("reports"),await y.callRestAPI({query:o,user_id:state.user},"POST"),setReportBug(!1),N(i,"Report submitted")}catch{N(i,"Unable to submit bug report")}};return e.jsx(w,{appear:!0,show:r,as:n.Fragment,children:e.jsxs(v,{as:"div",className:"relative z-10  bg-[#1d2937]",onClose:t,children:[e.jsx(w.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#0f1827] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(w.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(v.Panel,{className:"w-full max-w-md  transform overflow-hidden rounded-2xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsx(v.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white",children:"Report a Bug"}),e.jsxs("div",{className:"mt-2",children:[e.jsx("input",{type:"text",value:x,onChange:o=>h(o.target.value),className:"w-full rounded-md bg-transparent p-2 text-white placeholder:text-gray-300",placeholder:"Enter your query"}),c&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:c})]}),e.jsxs("div",{className:"mt-4 flex justify-end space-x-2",children:[e.jsx("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700",onClick:t,children:"Close"}),e.jsx("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent  bg-[#19b2f6]/80  px-4 py-2 text-sm font-medium text-white ",onClick:p,children:"Submit"})]})]})})})})]})})},me=()=>{const{state:r}=n.useContext(S);n.useContext(k);const[t,s]=n.useState(!1),[i,a]=n.useState("assistants"),[x,h]=n.useState(!1),[c,m]=n.useState(null);n.useRef(null);const p=n.useRef(),d=u=>{p.current&&clearTimeout(p.current),s(!0),m(u)},o=()=>{p.current=setTimeout(()=>{s(!1),m(null)},200)};return(r==null?void 0:r.role)=="user"?e.jsx("nav",{className:"border-bg-transparent fixed left-0 right-0 top-0 z-50 bg-[#0f1827] text-white",children:e.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex h-16 items-center justify-between",children:[e.jsx("div",{className:"flex flex-shrink-0 items-center",children:e.jsx("span",{className:"font-bold md:text-[13px] lg:text-xl",children:"AutomateIntel - Voice"})}),e.jsx("div",{className:"mx-8 hidden flex-1 items-center justify-center bg-[#0f1827] text-white md:flex",children:e.jsxs("div",{className:"relative flex  items-center rounded-full bg-[#1d2937] p-1",children:[e.jsxs("div",{className:"relative",onMouseEnter:()=>d("assistants"),onMouseLeave:o,children:[e.jsx(l,{to:"/user/voice",children:e.jsxs("button",{onClick:()=>{s(!t),a("assistants")},className:b("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="assistants"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Assistants",e.jsx(j,{className:"ml-1 h-4 w-4 2xl:h-6 2xl:w-6"})]})}),c==="assistants"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(l,{to:"/user/voice",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("assistants")},children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{children:"Voice Assistant Test"})]}),e.jsxs(l,{to:"/user/sms",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("assistants")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Assistant Test"})]}),e.jsxs(l,{to:"/user/assistants",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("assistants")},children:[e.jsx(T,{className:"h-4 w-4"}),e.jsx("span",{children:"Voice Assistants"})]}),e.jsxs(l,{to:"/user/voice_list",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("assistants")},children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{children:"Cloned Voices"})]})]})]}),e.jsxs("div",{className:"relative",onMouseEnter:()=>d("campaigns"),onMouseLeave:o,children:[e.jsx(l,{to:"/user/outbound_campaigns",children:e.jsxs("button",{onClick:()=>{s(!t),a("campaigns")},className:b("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="campaigns"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Campaigns",e.jsx(j,{className:"ml-1 h-4 w-4 2xl:w-6"})]})}),c==="campaigns"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(l,{to:"/user/outbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("campaigns")},children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{children:"OutBound Campaigns"})]}),e.jsxs(l,{to:"/user/inbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("campaigns")},children:[e.jsx(B,{className:"h-4 w-4"}),e.jsx("span",{children:"Inbound Campaigns"})]}),e.jsxs(l,{to:"/user/sms_outbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("campaigns")},children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Outbound Campaigns"})]}),e.jsxs(l,{to:"/user/sms_inbound_campaigns",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("campaigns")},children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Inbound Campaigns"})]})]})]}),e.jsxs("div",{className:"relative",onMouseEnter:()=>d("logs"),onMouseLeave:o,children:[e.jsx(l,{to:"/user/outbound_call_logs",children:e.jsxs("button",{onClick:()=>{s(!t),a("logs")},className:b("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="logs"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Logs",e.jsx(j,{className:"ml-1 h-4 w-4 2xl:w-6"})]})}),c==="logs"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-64 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(l,{to:"/user/outbound_call_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{children:"Outbound Call Logs"})]}),e.jsxs(l,{to:"/user/inbound_call_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(R,{className:"h-4 w-4"}),e.jsx("span",{children:"Inbound Call Logs"})]}),e.jsxs(l,{to:"/user/test_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(I,{className:"h-4 w-4"}),e.jsx("span",{children:"Sample Voice Call Log"})]}),e.jsxs(l,{to:"/user/test_sms_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"Sample SMS Followup Logs"})]}),e.jsxs(l,{to:"/user/email_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"Email Logs"})]}),e.jsxs(l,{to:"/user/sms_outbound_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Outbound Logs"})]}),e.jsxs(l,{to:"/user/sms_inbound_logs",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("logs")},children:[e.jsx(g,{className:"h-4 w-4"}),e.jsx("span",{children:"SMS Inbound Logs"})]})]})]}),e.jsx(l,{to:"/user/numbers",children:e.jsx("button",{onClick:()=>{a("phone"),s(!1),m(null)},className:b("relative z-10 rounded-full px-6 py-2 text-sm font-medium md:text-[12px] lg:text-sm",i==="phone"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:"Phone #"})}),e.jsxs("div",{className:"relative",onMouseEnter:()=>d("settings"),onMouseLeave:o,children:[e.jsxs("button",{onClick:()=>{s(!t),a("settings")},className:b("relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",i==="settings"?"bg-[#19b2f6]/80  text-white":"text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"),children:["Settings",e.jsx(j,{className:"ml-1 h-4 w-4 2xl:w-6"})]}),c==="settings"&&e.jsxs("div",{className:"absolute left-0 top-full mt-1 w-48 rounded-md bg-[#0f1827] py-1 shadow-lg",children:[e.jsxs(l,{to:"/user/settings",className:"block flex items-center space-x-3 px-4 py-2 text-sm  text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("settings")},children:[e.jsx(E,{className:"h-4 w-4"}),e.jsx("span",{children:"Settings"})]}),e.jsxs(l,{to:"/user/stripe_subscription",className:"block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]",onClick:()=>{s(!t),a("settings")},children:[e.jsx(A,{className:"h-4 w-4"}),e.jsx("span",{children:"Billing"})]})]})]})]})}),e.jsxs("div",{className:"hidden items-center space-x-6 md:flex",children:[e.jsx(l,{to:"/user/profile",className:"text-white hover:text-gray-600",children:e.jsx(z,{className:"h-5 w-5 2xl:w-7"})}),e.jsx("button",{className:"text-white hover:text-gray-600",onClick:()=>h(!0),children:e.jsx(P,{className:"h-5 w-5 2xl:w-7"})}),e.jsx(l,{to:"/user/logout",className:"text-white hover:text-gray-600",children:e.jsx(V,{className:"h-5 w-5 2xl:w-7"})})]}),e.jsx(D,{isOpen:x,onClose:()=>h(!1),onSubmit:u=>{onsubmit(u),console.log("Bug report submitted:",u)}}),e.jsx("div",{className:"flex items-center md:hidden",children:e.jsx("button",{className:"text-white hover:text-gray-600",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]})})}):e.jsx("nav",{className:"border-b border-gray-200 bg-white",children:e.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex h-16 items-center justify-between",children:e.jsx("span",{className:"text-xl font-bold",children:"AutomateIntel - Voice"})})})})};export{b as cn,me as default};
