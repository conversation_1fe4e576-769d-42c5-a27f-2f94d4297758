import"../@craftjs/core-9da1c17f.js";function o(n,e){if(!Object.prototype.hasOwnProperty.call(n,e))throw new TypeError("attempted to use private field on non-instance");return n}var u=0;function a(n){return"__private_"+u+++"_"+n}var i=a("uppy"),s=a("events");class p{constructor(e){Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:[]}),o(this,i)[i]=e}on(e,t){return o(this,s)[s].push([e,t]),o(this,i)[i].on(e,t)}remove(){for(const[e,t]of o(this,s)[s].splice(0))o(this,i)[i].off(e,t)}onFilePause(e,t){this.on("upload-pause",(r,l)=>{e===r&&t(l)})}onFileRemove(e,t){this.on("file-removed",r=>{e===r.id&&t(r.id)})}onPause(e,t){this.on("upload-pause",(r,l)=>{e===r&&t(l)})}onRetry(e,t){this.on("upload-retry",r=>{e===r&&t()})}onRetryAll(e,t){this.on("retry-all",()=>{o(this,i)[i].getFile(e)&&t()})}onPauseAll(e,t){this.on("pause-all",()=>{o(this,i)[i].getFile(e)&&t()})}onCancelAll(e,t){var r=this;this.on("cancel-all",function(){o(r,i)[i].getFile(e)&&t(...arguments)})}onResumeAll(e,t){this.on("resume-all",()=>{o(this,i)[i].getFile(e)&&t()})}}export{p as E};
