import{r as i,a as mr,b as Rt,e as hr,R as V}from"./vendor-2ae44a2e.js";import{j as f}from"./@react-google-maps/api-c55ecefa.js";import{R as vr,h as gr,c as yr,o as Cr,s as xr,f as wr,a as Er,b as br,d as tt,l as Pr,e as Sr}from"./@mantine/core-1006e8cf.js";function Y(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function nt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Nt(...e){return t=>{let n=!1;const r=e.map(o=>{const s=nt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():nt(e[o],null)}}}}function oe(...e){return i.useCallback(Nt(...e),e)}function Rr(e,t){const n=i.createContext(t),r=s=>{const{children:c,...a}=s,l=i.useMemo(()=>a,Object.values(a));return f.jsx(n.Provider,{value:l,children:c})};r.displayName=e+"Provider";function o(s){const c=i.useContext(n);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function Nr(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Or(o,...t)]}function Or(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var Ar=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},$r=mr["useId".toString()]||(()=>{}),Dr=0;function Tr(e){const[t,n]=i.useState($r());return Ar(()=>{e||n(r=>r??String(Dr++))},[e]),e||(t?`radix-${t}`:"")}function Ot(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Me({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=_r({defaultProp:t,onChange:n}),s=e!==void 0,c=s?e:r,a=Ot(n),l=i.useCallback(u=>{if(s){const d=typeof u=="function"?u(e):u;d!==e&&a(d)}else o(u)},[s,e,o,a]);return[c,l]}function _r({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=Ot(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}function de(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function rt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function At(...e){return t=>{let n=!1;const r=e.map(o=>{const s=rt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():rt(e[o],null)}}}}function $t(...e){return i.useCallback(At(...e),e)}var Dt=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Mr);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(ge,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(ge,{...r,ref:t,children:n})});Dt.displayName="Slot";var ge=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Lr(n);return i.cloneElement(n,{...Ir(r,n.props),ref:t?At(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});ge.displayName="SlotClone";var jr=({children:e})=>f.jsx(f.Fragment,{children:e});function Mr(e){return i.isValidElement(e)&&e.type===jr}function Ir(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Lr(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Wr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Tt=Wr.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Dt:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function kr(e,t){e&&Rt.flushSync(()=>e.dispatchEvent(t))}function _t(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Fr(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Vr(e,t=globalThis==null?void 0:globalThis.document){const n=Fr(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Br="DismissableLayer",ye="dismissableLayer.update",Ur="dismissableLayer.pointerDownOutside",Hr="dismissableLayer.focusOutside",ot,jt=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ie=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:c,onDismiss:a,...l}=e,u=i.useContext(jt),[m,d]=i.useState(null),p=(m==null?void 0:m.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=i.useState({}),g=$t(t,b=>d(b)),h=Array.from(u.layers),[C]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(C),E=m?h.indexOf(m):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,x=E>=y,S=Gr(b=>{const D=b.target,L=[...u.branches].some(A=>A.contains(D));!x||L||(o==null||o(b),c==null||c(b),b.defaultPrevented||a==null||a())},p),R=Yr(b=>{const D=b.target;[...u.branches].some(A=>A.contains(D))||(s==null||s(b),c==null||c(b),b.defaultPrevented||a==null||a())},p);return Vr(b=>{E===u.layers.size-1&&(r==null||r(b),!b.defaultPrevented&&a&&(b.preventDefault(),a()))},p),i.useEffect(()=>{if(m)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(ot=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(m)),u.layers.add(m),st(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=ot)}},[m,p,n,u]),i.useEffect(()=>()=>{m&&(u.layers.delete(m),u.layersWithOutsidePointerEventsDisabled.delete(m),st())},[m,u]),i.useEffect(()=>{const b=()=>v({});return document.addEventListener(ye,b),()=>document.removeEventListener(ye,b)},[]),f.jsx(Tt.div,{...l,ref:g,style:{pointerEvents:w?x?"auto":"none":void 0,...e.style},onFocusCapture:de(e.onFocusCapture,R.onFocusCapture),onBlurCapture:de(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:de(e.onPointerDownCapture,S.onPointerDownCapture)})});Ie.displayName=Br;var Kr="DismissableLayerBranch",zr=i.forwardRef((e,t)=>{const n=i.useContext(jt),r=i.useRef(null),o=$t(t,r);return i.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(Tt.div,{...e,ref:o})});zr.displayName=Kr;function Gr(e,t=globalThis==null?void 0:globalThis.document){const n=_t(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){Mt(Ur,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Yr(e,t=globalThis==null?void 0:globalThis.document){const n=_t(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&Mt(Hr,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function st(){const e=new CustomEvent(ye);document.dispatchEvent(e)}function Mt(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?kr(o,s):o.dispatchEvent(s)}function it(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function It(...e){return t=>{let n=!1;const r=e.map(o=>{const s=it(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():it(e[o],null)}}}}function Zr(...e){return i.useCallback(It(...e),e)}var Lt=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(qr);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(Ce,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(Ce,{...r,ref:t,children:n})});Lt.displayName="Slot";var Ce=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Qr(n);return i.cloneElement(n,{...Jr(r,n.props),ref:t?It(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Ce.displayName="SlotClone";var Xr=({children:e})=>f.jsx(f.Fragment,{children:e});function qr(e){return i.isValidElement(e)&&e.type===Xr}function Jr(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Qr(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var eo=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],to=eo.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Lt:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ct(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}var pe="focusScope.autoFocusOnMount",me="focusScope.autoFocusOnUnmount",at={bubbles:!1,cancelable:!0},no="FocusScope",Wt=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...c}=e,[a,l]=i.useState(null),u=ct(o),m=ct(s),d=i.useRef(null),p=Zr(t,h=>l(h)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let h=function(w){if(v.paused||!a)return;const x=w.target;a.contains(x)?d.current=x:F(d.current,{select:!0})},C=function(w){if(v.paused||!a)return;const x=w.relatedTarget;x!==null&&(a.contains(x)||F(d.current,{select:!0}))},y=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&F(a)};document.addEventListener("focusin",h),document.addEventListener("focusout",C);const E=new MutationObserver(y);return a&&E.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",C),E.disconnect()}}},[r,a,v.paused]),i.useEffect(()=>{if(a){ut.add(v);const h=document.activeElement;if(!a.contains(h)){const y=new CustomEvent(pe,at);a.addEventListener(pe,u),a.dispatchEvent(y),y.defaultPrevented||(ro(ao(kt(a)),{select:!0}),document.activeElement===h&&F(a))}return()=>{a.removeEventListener(pe,u),setTimeout(()=>{const y=new CustomEvent(me,at);a.addEventListener(me,m),a.dispatchEvent(y),y.defaultPrevented||F(h??document.body,{select:!0}),a.removeEventListener(me,m),ut.remove(v)},0)}}},[a,u,m,v]);const g=i.useCallback(h=>{if(!n&&!r||v.paused)return;const C=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,y=document.activeElement;if(C&&y){const E=h.currentTarget,[w,x]=oo(E);w&&x?!h.shiftKey&&y===x?(h.preventDefault(),n&&F(w,{select:!0})):h.shiftKey&&y===w&&(h.preventDefault(),n&&F(x,{select:!0})):y===E&&h.preventDefault()}},[n,r,v.paused]);return f.jsx(to.div,{tabIndex:-1,...c,ref:p,onKeyDown:g})});Wt.displayName=no;function ro(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(F(r,{select:t}),document.activeElement!==n)return}function oo(e){const t=kt(e),n=lt(t,e),r=lt(t.reverse(),e);return[n,r]}function kt(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function lt(e,t){for(const n of e)if(!so(n,{upTo:t}))return n}function so(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function io(e){return e instanceof HTMLInputElement&&"select"in e}function F(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&io(e)&&t&&e.select()}}var ut=co();function co(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ft(e,t),e.unshift(t)},remove(t){var n;e=ft(e,t),(n=e[0])==null||n.resume()}}}function ft(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ao(e){return e.filter(t=>t.tagName!=="A")}function dt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function lo(...e){return t=>{let n=!1;const r=e.map(o=>{const s=dt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():dt(e[o],null)}}}}var Ft=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(fo);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(xe,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(xe,{...r,ref:t,children:n})});Ft.displayName="Slot";var xe=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=mo(n);return i.cloneElement(n,{...po(r,n.props),ref:t?lo(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});xe.displayName="SlotClone";var uo=({children:e})=>f.jsx(f.Fragment,{children:e});function fo(e){return i.isValidElement(e)&&e.type===uo}function po(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function mo(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ho=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],vo=ho.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Ft:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),go=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},yo="Portal",Vt=i.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=i.useState(!1);go(()=>s(!0),[]);const c=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return c?hr.createPortal(f.jsx(vo.div,{...r,ref:t}),c):null});Vt.displayName=yo;var pt=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{};function Co(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var se=e=>{const{present:t,children:n}=e,r=xo(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=oe(r.ref,wo(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};se.displayName="Presence";function xo(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),c=e?"mounted":"unmounted",[a,l]=Co(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=Q(r.current);s.current=a==="mounted"?u:"none"},[a]),pt(()=>{const u=r.current,m=o.current;if(m!==e){const p=s.current,v=Q(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(m&&p!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),pt(()=>{if(t){let u;const m=t.ownerDocument.defaultView??window,d=v=>{const h=Q(r.current).includes(v.animationName);if(v.target===t&&h&&(l("ANIMATION_END"),!o.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",u=m.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},p=v=>{v.target===t&&(s.current=Q(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{m.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Q(e){return(e==null?void 0:e.animationName)||"none"}function wo(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Le=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(bo);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(we,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(we,{...r,ref:t,children:n})});Le.displayName="Slot";var we=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=So(n);return i.cloneElement(n,{...Po(r,n.props),ref:t?Nt(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});we.displayName="SlotClone";var Eo=({children:e})=>f.jsx(f.Fragment,{children:e});function bo(e){return i.isValidElement(e)&&e.type===Eo}function Po(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function So(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ro=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Z=Ro.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Le:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),he=0;function No(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??mt()),document.body.insertAdjacentElement("beforeend",e[1]??mt()),he++,()=>{he===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),he--}},[])}function mt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Bt="Dialog",[Ut,zi]=Nr(Bt),[Gi,j]=Ut(Bt),Ht="DialogTrigger",Oo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=j(Ht,n),s=oe(t,o.triggerRef);return f.jsx(Z.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Fe(o.open),...r,ref:s,onClick:Y(e.onClick,o.onOpenToggle)})});Oo.displayName=Ht;var We="DialogPortal",[Ao,Kt]=Ut(We,{forceMount:void 0}),zt=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=j(We,t);return f.jsx(Ao,{scope:t,forceMount:n,children:i.Children.map(r,c=>f.jsx(se,{present:n||s.open,children:f.jsx(Vt,{asChild:!0,container:o,children:c})}))})};zt.displayName=We;var ne="DialogOverlay",Gt=i.forwardRef((e,t)=>{const n=Kt(ne,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=j(ne,e.__scopeDialog);return s.modal?f.jsx(se,{present:r||s.open,children:f.jsx($o,{...o,ref:t})}):null});Gt.displayName=ne;var $o=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=j(ne,n);return f.jsx(vr,{as:Le,allowPinchZoom:!0,shards:[o.contentRef],children:f.jsx(Z.div,{"data-state":Fe(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),B="DialogContent",Yt=i.forwardRef((e,t)=>{const n=Kt(B,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=j(B,e.__scopeDialog);return f.jsx(se,{present:r||s.open,children:s.modal?f.jsx(Do,{...o,ref:t}):f.jsx(To,{...o,ref:t})})});Yt.displayName=B;var Do=i.forwardRef((e,t)=>{const n=j(B,e.__scopeDialog),r=i.useRef(null),o=oe(t,n.contentRef,r);return i.useEffect(()=>{const s=r.current;if(s)return gr(s)},[]),f.jsx(Zt,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Y(e.onCloseAutoFocus,s=>{var c;s.preventDefault(),(c=n.triggerRef.current)==null||c.focus()}),onPointerDownOutside:Y(e.onPointerDownOutside,s=>{const c=s.detail.originalEvent,a=c.button===0&&c.ctrlKey===!0;(c.button===2||a)&&s.preventDefault()}),onFocusOutside:Y(e.onFocusOutside,s=>s.preventDefault())})}),To=i.forwardRef((e,t)=>{const n=j(B,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return f.jsx(Zt,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var c,a;(c=e.onCloseAutoFocus)==null||c.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(c))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Zt=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...c}=e,a=j(B,n),l=i.useRef(null),u=oe(t,l);return No(),f.jsxs(f.Fragment,{children:[f.jsx(Wt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:f.jsx(Ie,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Fe(a.open),...c,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(_o,{titleId:a.titleId}),f.jsx(Mo,{contentRef:l,descriptionId:a.descriptionId})]})]})}),ke="DialogTitle",Xt=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=j(ke,n);return f.jsx(Z.h2,{id:o.titleId,...r,ref:t})});Xt.displayName=ke;var qt="DialogDescription",Jt=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=j(qt,n);return f.jsx(Z.p,{id:o.descriptionId,...r,ref:t})});Jt.displayName=qt;var Qt="DialogClose",en=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=j(Qt,n);return f.jsx(Z.button,{type:"button",...r,ref:t,onClick:Y(e.onClick,()=>o.onOpenChange(!1))})});en.displayName=Qt;function Fe(e){return e?"open":"closed"}var tn="DialogTitleWarning",[Yi,nn]=Rr(tn,{contentName:B,titleName:ke,docsSlug:"dialog"}),_o=({titleId:e})=>{const t=nn(tn),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},jo="DialogDescriptionWarning",Mo=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${nn(jo).contentName}}.`;return i.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Zi=zt,Xi=Gt,qi=Yt,Ji=Xt,Qi=Jt,ec=en;function Io(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ht(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function rn(...e){return t=>{let n=!1;const r=e.map(o=>{const s=ht(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():ht(e[o],null)}}}}function Lo(...e){return i.useCallback(rn(...e),e)}function Wo(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,ko(o,...t)]}function ko(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function on(e){const t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Fo=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{};function Ve(e){const[t,n]=i.useState(void 0);return Fo(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let c,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;c=u.inlineSize,a=u.blockSize}else c=e.offsetWidth,a=e.offsetHeight;n({width:c,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var sn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Bo);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(Ee,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(Ee,{...r,ref:t,children:n})});sn.displayName="Slot";var Ee=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Ho(n);return i.cloneElement(n,{...Uo(r,n.props),ref:t?rn(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Ee.displayName="SlotClone";var Vo=({children:e})=>f.jsx(f.Fragment,{children:e});function Bo(e){return i.isValidElement(e)&&e.type===Vo}function Uo(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Ho(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ko=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],cn=Ko.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?sn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),Be="Switch",[zo,tc]=Wo(Be),[Go,Yo]=zo(Be),an=i.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:o,defaultChecked:s,required:c,disabled:a,value:l="on",onCheckedChange:u,form:m,...d}=e,[p,v]=i.useState(null),g=Lo(t,w=>v(w)),h=i.useRef(!1),C=p?m||!!p.closest("form"):!0,[y=!1,E]=Me({prop:o,defaultProp:s,onChange:u});return f.jsxs(Go,{scope:n,checked:y,disabled:a,children:[f.jsx(cn.button,{type:"button",role:"switch","aria-checked":y,"aria-required":c,"data-state":fn(y),"data-disabled":a?"":void 0,disabled:a,value:l,...d,ref:g,onClick:Io(e.onClick,w=>{E(x=>!x),C&&(h.current=w.isPropagationStopped(),h.current||w.stopPropagation())})}),C&&f.jsx(Zo,{control:p,bubbles:!h.current,name:r,value:l,checked:y,required:c,disabled:a,form:m,style:{transform:"translateX(-100%)"}})]})});an.displayName=Be;var ln="SwitchThumb",un=i.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=Yo(ln,n);return f.jsx(cn.span,{"data-state":fn(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});un.displayName=ln;var Zo=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,s=i.useRef(null),c=on(n),a=Ve(t);return i.useEffect(()=>{const l=s.current,u=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(u,"checked").set;if(c!==n&&d){const p=new Event("click",{bubbles:r});d.call(l,n),l.dispatchEvent(p)}},[c,n,r]),f.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:s,style:{...e.style,...a,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function fn(e){return e?"checked":"unchecked"}var nc=an,rc=un;function dn(e,[t,n]){return Math.min(n,Math.max(t,e))}function H(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function vt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function pn(...e){return t=>{let n=!1;const r=e.map(o=>{const s=vt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():vt(e[o],null)}}}}function X(...e){return i.useCallback(pn(...e),e)}function Xo(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,qo(o,...t)]}function qo(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var Jo=i.createContext(void 0);function Qo(e){const t=i.useContext(Jo);return e||t||"ltr"}var mn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(ts);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(be,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(be,{...r,ref:t,children:n})});mn.displayName="Slot";var be=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=rs(n);return i.cloneElement(n,{...ns(r,n.props),ref:t?pn(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});be.displayName="SlotClone";var es=({children:e})=>f.jsx(f.Fragment,{children:e});function ts(e){return i.isValidElement(e)&&e.type===es}function ns(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function rs(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var os=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ie=os.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?mn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ss(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,is(o,...t)]}function is(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function gt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function hn(...e){return t=>{let n=!1;const r=e.map(o=>{const s=gt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():gt(e[o],null)}}}}function yt(...e){return i.useCallback(hn(...e),e)}var Pe=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(as);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(Se,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(Se,{...r,ref:t,children:n})});Pe.displayName="Slot";var Se=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=us(n);return i.cloneElement(n,{...ls(r,n.props),ref:t?hn(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Se.displayName="SlotClone";var cs=({children:e})=>f.jsx(f.Fragment,{children:e});function as(e){return i.isValidElement(e)&&e.type===cs}function ls(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function us(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function fs(e){const t=e+"CollectionProvider",[n,r]=ss(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=v=>{const{scope:g,children:h}=v,C=V.useRef(null),y=V.useRef(new Map).current;return f.jsx(o,{scope:g,itemMap:y,collectionRef:C,children:h})};c.displayName=t;const a=e+"CollectionSlot",l=V.forwardRef((v,g)=>{const{scope:h,children:C}=v,y=s(a,h),E=yt(g,y.collectionRef);return f.jsx(Pe,{ref:E,children:C})});l.displayName=a;const u=e+"CollectionItemSlot",m="data-radix-collection-item",d=V.forwardRef((v,g)=>{const{scope:h,children:C,...y}=v,E=V.useRef(null),w=yt(g,E),x=s(u,h);return V.useEffect(()=>(x.itemMap.set(E,{ref:E,...y}),()=>void x.itemMap.delete(E))),f.jsx(Pe,{[m]:"",ref:w,children:C})});d.displayName=u;function p(v){const g=s(e+"CollectionConsumer",v);return V.useCallback(()=>{const C=g.collectionRef.current;if(!C)return[];const y=Array.from(C.querySelectorAll(`[${m}]`));return Array.from(g.itemMap.values()).sort((x,S)=>y.indexOf(x.ref.current)-y.indexOf(S.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:c,Slot:l,ItemSlot:d},p,r]}var vn=["PageUp","PageDown"],gn=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],yn={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},z="Slider",[Re,ds,ps]=fs(z),[Cn,oc]=Xo(z,[ps]),[ms,ce]=Cn(z),xn=i.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:s=1,orientation:c="horizontal",disabled:a=!1,minStepsBetweenThumbs:l=0,defaultValue:u=[r],value:m,onValueChange:d=()=>{},onValueCommit:p=()=>{},inverted:v=!1,form:g,...h}=e,C=i.useRef(new Set),y=i.useRef(0),w=c==="horizontal"?hs:vs,[x=[],S]=Me({prop:m,defaultProp:u,onChange:N=>{var O;(O=[...C.current][y.current])==null||O.focus(),d(N)}}),R=i.useRef(x);function b(N){const _=ws(x,N);A(N,_)}function D(N){A(N,y.current)}function L(){const N=R.current[y.current];x[y.current]!==N&&p(x)}function A(N,_,{commit:O}={commit:!1}){const W=Ss(s),I=Rs(Math.round((N-r)/s)*s+r,W),$=dn(I,[r,o]);S((T=[])=>{const P=Cs(T,$,_);if(Ps(P,l*s)){y.current=P.indexOf($);const M=String(P)!==String(T);return M&&O&&p(P),M?P:T}else return T})}return f.jsx(ms,{scope:e.__scopeSlider,name:n,disabled:a,min:r,max:o,valueIndexToChangeRef:y,thumbs:C.current,values:x,orientation:c,form:g,children:f.jsx(Re.Provider,{scope:e.__scopeSlider,children:f.jsx(Re.Slot,{scope:e.__scopeSlider,children:f.jsx(w,{"aria-disabled":a,"data-disabled":a?"":void 0,...h,ref:t,onPointerDown:H(h.onPointerDown,()=>{a||(R.current=x)}),min:r,max:o,inverted:v,onSlideStart:a?void 0:b,onSlideMove:a?void 0:D,onSlideEnd:a?void 0:L,onHomeKeyDown:()=>!a&&A(r,0,{commit:!0}),onEndKeyDown:()=>!a&&A(o,x.length-1,{commit:!0}),onStepKeyDown:({event:N,direction:_})=>{if(!a){const I=vn.includes(N.key)||N.shiftKey&&gn.includes(N.key)?10:1,$=y.current,T=x[$],P=s*I*_;A(T+P,$,{commit:!0})}}})})})})});xn.displayName=z;var[wn,En]=Cn(z,{startEdge:"left",endEdge:"right",size:"width",direction:1}),hs=i.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:s,onSlideStart:c,onSlideMove:a,onSlideEnd:l,onStepKeyDown:u,...m}=e,[d,p]=i.useState(null),v=X(t,w=>p(w)),g=i.useRef(void 0),h=Qo(o),C=h==="ltr",y=C&&!s||!C&&s;function E(w){const x=g.current||d.getBoundingClientRect(),S=[0,x.width],b=Ue(S,y?[n,r]:[r,n]);return g.current=x,b(w-x.left)}return f.jsx(wn,{scope:e.__scopeSlider,startEdge:y?"left":"right",endEdge:y?"right":"left",direction:y?1:-1,size:"width",children:f.jsx(bn,{dir:h,"data-orientation":"horizontal",...m,ref:v,style:{...m.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:w=>{const x=E(w.clientX);c==null||c(x)},onSlideMove:w=>{const x=E(w.clientX);a==null||a(x)},onSlideEnd:()=>{g.current=void 0,l==null||l()},onStepKeyDown:w=>{const S=yn[y?"from-left":"from-right"].includes(w.key);u==null||u({event:w,direction:S?-1:1})}})})}),vs=i.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:s,onSlideMove:c,onSlideEnd:a,onStepKeyDown:l,...u}=e,m=i.useRef(null),d=X(t,m),p=i.useRef(void 0),v=!o;function g(h){const C=p.current||m.current.getBoundingClientRect(),y=[0,C.height],w=Ue(y,v?[r,n]:[n,r]);return p.current=C,w(h-C.top)}return f.jsx(wn,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:f.jsx(bn,{"data-orientation":"vertical",...u,ref:d,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:h=>{const C=g(h.clientY);s==null||s(C)},onSlideMove:h=>{const C=g(h.clientY);c==null||c(C)},onSlideEnd:()=>{p.current=void 0,a==null||a()},onStepKeyDown:h=>{const y=yn[v?"from-bottom":"from-top"].includes(h.key);l==null||l({event:h,direction:y?-1:1})}})})}),bn=i.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:s,onHomeKeyDown:c,onEndKeyDown:a,onStepKeyDown:l,...u}=e,m=ce(z,n);return f.jsx(ie.span,{...u,ref:t,onKeyDown:H(e.onKeyDown,d=>{d.key==="Home"?(c(d),d.preventDefault()):d.key==="End"?(a(d),d.preventDefault()):vn.concat(gn).includes(d.key)&&(l(d),d.preventDefault())}),onPointerDown:H(e.onPointerDown,d=>{const p=d.target;p.setPointerCapture(d.pointerId),d.preventDefault(),m.thumbs.has(p)?p.focus():r(d)}),onPointerMove:H(e.onPointerMove,d=>{d.target.hasPointerCapture(d.pointerId)&&o(d)}),onPointerUp:H(e.onPointerUp,d=>{const p=d.target;p.hasPointerCapture(d.pointerId)&&(p.releasePointerCapture(d.pointerId),s(d))})})}),Pn="SliderTrack",Sn=i.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=ce(Pn,n);return f.jsx(ie.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});Sn.displayName=Pn;var Ne="SliderRange",Rn=i.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=ce(Ne,n),s=En(Ne,n),c=i.useRef(null),a=X(t,c),l=o.values.length,u=o.values.map(p=>On(p,o.min,o.max)),m=l>1?Math.min(...u):0,d=100-Math.max(...u);return f.jsx(ie.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:a,style:{...e.style,[s.startEdge]:m+"%",[s.endEdge]:d+"%"}})});Rn.displayName=Ne;var Oe="SliderThumb",Nn=i.forwardRef((e,t)=>{const n=ds(e.__scopeSlider),[r,o]=i.useState(null),s=X(t,a=>o(a)),c=i.useMemo(()=>r?n().findIndex(a=>a.ref.current===r):-1,[n,r]);return f.jsx(gs,{...e,ref:s,index:c})}),gs=i.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...s}=e,c=ce(Oe,n),a=En(Oe,n),[l,u]=i.useState(null),m=X(t,E=>u(E)),d=l?c.form||!!l.closest("form"):!0,p=Ve(l),v=c.values[r],g=v===void 0?0:On(v,c.min,c.max),h=xs(r,c.values.length),C=p==null?void 0:p[a.size],y=C?Es(C,g,a.direction):0;return i.useEffect(()=>{if(l)return c.thumbs.add(l),()=>{c.thumbs.delete(l)}},[l,c.thumbs]),f.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[a.startEdge]:`calc(${g}% + ${y}px)`},children:[f.jsx(Re.ItemSlot,{scope:e.__scopeSlider,children:f.jsx(ie.span,{role:"slider","aria-label":e["aria-label"]||h,"aria-valuemin":c.min,"aria-valuenow":v,"aria-valuemax":c.max,"aria-orientation":c.orientation,"data-orientation":c.orientation,"data-disabled":c.disabled?"":void 0,tabIndex:c.disabled?void 0:0,...s,ref:m,style:v===void 0?{display:"none"}:e.style,onFocus:H(e.onFocus,()=>{c.valueIndexToChangeRef.current=r})})}),d&&f.jsx(ys,{name:o??(c.name?c.name+(c.values.length>1?"[]":""):void 0),form:c.form,value:v},r)]})});Nn.displayName=Oe;var ys=e=>{const{value:t,...n}=e,r=i.useRef(null),o=on(t);return i.useEffect(()=>{const s=r.current,c=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(c,"value").set;if(o!==t&&l){const u=new Event("input",{bubbles:!0});l.call(s,t),s.dispatchEvent(u)}},[o,t]),f.jsx("input",{style:{display:"none"},...n,ref:r,defaultValue:t})};function Cs(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,s)=>o-s)}function On(e,t,n){const s=100/(n-t)*(e-t);return dn(s,[0,100])}function xs(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function ws(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function Es(e,t,n){const r=e/2,s=Ue([0,50],[0,r]);return(r-s(t)*n)*n}function bs(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function Ps(e,t){if(t>0){const n=bs(e);return Math.min(...n)>=t}return!0}function Ue(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function Ss(e){return(String(e).split(".")[1]||"").length}function Rs(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var sc=xn,ic=Sn,cc=Rn,ac=Nn;function U(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Ct(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function An(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Ct(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Ct(e[o],null)}}}}function He(...e){return i.useCallback(An(...e),e)}function Ns(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Os(o,...t)]}function Os(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var te=typeof document<"u"?i.useLayoutEffect:i.useEffect;function re(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!re(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!re(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function $n(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function xt(e,t){const n=$n(e);return Math.round(t*n)/n}function ve(e){const t=i.useRef(e);return te(()=>{t.current=e}),t}function As(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:c}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[m,d]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,v]=i.useState(r);re(p,r)||v(r);const[g,h]=i.useState(null),[C,y]=i.useState(null),E=i.useCallback(P=>{P!==R.current&&(R.current=P,h(P))},[]),w=i.useCallback(P=>{P!==b.current&&(b.current=P,y(P))},[]),x=s||g,S=c||C,R=i.useRef(null),b=i.useRef(null),D=i.useRef(m),L=l!=null,A=ve(l),N=ve(o),_=ve(u),O=i.useCallback(()=>{if(!R.current||!b.current)return;const P={placement:t,strategy:n,middleware:p};N.current&&(P.platform=N.current),yr(R.current,b.current,P).then(M=>{const k={...M,isPositioned:_.current!==!1};W.current&&!re(D.current,k)&&(D.current=k,Rt.flushSync(()=>{d(k)}))})},[p,t,n,N,_]);te(()=>{u===!1&&D.current.isPositioned&&(D.current.isPositioned=!1,d(P=>({...P,isPositioned:!1})))},[u]);const W=i.useRef(!1);te(()=>(W.current=!0,()=>{W.current=!1}),[]),te(()=>{if(x&&(R.current=x),S&&(b.current=S),x&&S){if(A.current)return A.current(x,S,O);O()}},[x,S,O,A,L]);const I=i.useMemo(()=>({reference:R,floating:b,setReference:E,setFloating:w}),[E,w]),$=i.useMemo(()=>({reference:x,floating:S}),[x,S]),T=i.useMemo(()=>{const P={position:n,left:0,top:0};if(!$.floating)return P;const M=xt($.floating,m.x),k=xt($.floating,m.y);return a?{...P,transform:"translate("+M+"px, "+k+"px)",...$n($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:k}},[n,a,$.floating,m.x,m.y]);return i.useMemo(()=>({...m,update:O,refs:I,elements:$,floatingStyles:T}),[m,O,I,$,T])}const $s=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?tt({element:r.current,padding:o}).fn(n):{}:r?tt({element:r,padding:o}).fn(n):{}}}},Ds=(e,t)=>({...Cr(e),options:[e,t]}),Ts=(e,t)=>({...xr(e),options:[e,t]}),_s=(e,t)=>({...Pr(e),options:[e,t]}),js=(e,t)=>({...wr(e),options:[e,t]}),Ms=(e,t)=>({...Er(e),options:[e,t]}),Is=(e,t)=>({...br(e),options:[e,t]}),Ls=(e,t)=>({...$s(e),options:[e,t]});function wt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Ws(...e){return t=>{let n=!1;const r=e.map(o=>{const s=wt(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():wt(e[o],null)}}}}var Dn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Fs);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(Ae,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(Ae,{...r,ref:t,children:n})});Dn.displayName="Slot";var Ae=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Bs(n);return i.cloneElement(n,{...Vs(r,n.props),ref:t?Ws(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Ae.displayName="SlotClone";var ks=({children:e})=>f.jsx(f.Fragment,{children:e});function Fs(e){return i.isValidElement(e)&&e.type===ks}function Vs(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Bs(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Us=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Hs=Us.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Dn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),Ks="Arrow",Tn=i.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return f.jsx(Hs.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});Tn.displayName=Ks;var zs=Tn;function Et(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function _n(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Et(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Et(e[o],null)}}}}function jn(...e){return i.useCallback(_n(...e),e)}function Gs(e,t=[]){let n=[];function r(s,c){const a=i.createContext(c),l=n.length;n=[...n,c];const u=d=>{var y;const{scope:p,children:v,...g}=d,h=((y=p==null?void 0:p[e])==null?void 0:y[l])||a,C=i.useMemo(()=>g,Object.values(g));return f.jsx(h.Provider,{value:C,children:v})};u.displayName=s+"Provider";function m(d,p){var h;const v=((h=p==null?void 0:p[e])==null?void 0:h[l])||a,g=i.useContext(v);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,m]}const o=()=>{const s=n.map(c=>i.createContext(c));return function(a){const l=(a==null?void 0:a[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Ys(o,...t)]}function Ys(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const c=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var Mn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Xs);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx($e,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx($e,{...r,ref:t,children:n})});Mn.displayName="Slot";var $e=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Js(n);return i.cloneElement(n,{...qs(r,n.props),ref:t?_n(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});$e.displayName="SlotClone";var Zs=({children:e})=>f.jsx(f.Fragment,{children:e});function Xs(e){return i.isValidElement(e)&&e.type===Zs}function qs(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Js(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Qs=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],In=Qs.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Mn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ei(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}var bt=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},Ke="Popper",[Ln,Wn]=Gs(Ke),[ti,kn]=Ln(Ke),Fn=e=>{const{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return f.jsx(ti,{scope:t,anchor:r,onAnchorChange:o,children:n})};Fn.displayName=Ke;var Vn="PopperAnchor",Bn=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=kn(Vn,n),c=i.useRef(null),a=jn(t,c);return i.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||c.current)}),r?null:f.jsx(In.div,{...o,ref:a})});Bn.displayName=Vn;var ze="PopperContent",[ni,ri]=Ln(ze),Un=i.forwardRef((e,t)=>{var Ye,Ze,Xe,qe,Je,Qe;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:c=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:m=0,sticky:d="partial",hideWhenDetached:p=!1,updatePositionStrategy:v="optimized",onPlaced:g,...h}=e,C=kn(ze,n),[y,E]=i.useState(null),w=jn(t,G=>E(G)),[x,S]=i.useState(null),R=Ve(x),b=(R==null?void 0:R.width)??0,D=(R==null?void 0:R.height)??0,L=r+(s!=="center"?"-"+s:""),A=typeof m=="number"?m:{top:0,right:0,bottom:0,left:0,...m},N=Array.isArray(u)?u:[u],_=N.length>0,O={padding:A,boundary:N.filter(si),altBoundary:_},{refs:W,floatingStyles:I,placement:$,isPositioned:T,middlewareData:P}=As({strategy:"fixed",placement:L,whileElementsMounted:(...G)=>Sr(...G,{animationFrame:v==="always"}),elements:{reference:C.anchor},middleware:[Ds({mainAxis:o+D,alignmentAxis:c}),l&&Ts({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?_s():void 0,...O}),l&&js({...O}),Ms({...O,apply:({elements:G,rects:et,availableWidth:ur,availableHeight:fr})=>{const{width:dr,height:pr}=et.reference,J=G.floating.style;J.setProperty("--radix-popper-available-width",`${ur}px`),J.setProperty("--radix-popper-available-height",`${fr}px`),J.setProperty("--radix-popper-anchor-width",`${dr}px`),J.setProperty("--radix-popper-anchor-height",`${pr}px`)}}),x&&Ls({element:x,padding:a}),ii({arrowWidth:b,arrowHeight:D}),p&&Is({strategy:"referenceHidden",...O})]}),[M,k]=zn($),q=ei(g);bt(()=>{T&&(q==null||q())},[T,q]);const sr=(Ye=P.arrow)==null?void 0:Ye.x,ir=(Ze=P.arrow)==null?void 0:Ze.y,cr=((Xe=P.arrow)==null?void 0:Xe.centerOffset)!==0,[ar,lr]=i.useState();return bt(()=>{y&&lr(window.getComputedStyle(y).zIndex)},[y]),f.jsx("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:T?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ar,"--radix-popper-transform-origin":[(qe=P.transformOrigin)==null?void 0:qe.x,(Je=P.transformOrigin)==null?void 0:Je.y].join(" "),...((Qe=P.hide)==null?void 0:Qe.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:f.jsx(ni,{scope:n,placedSide:M,onArrowChange:S,arrowX:sr,arrowY:ir,shouldHideArrow:cr,children:f.jsx(In.div,{"data-side":M,"data-align":k,...h,ref:w,style:{...h.style,animation:T?void 0:"none"}})})})});Un.displayName=ze;var Hn="PopperArrow",oi={top:"bottom",right:"left",bottom:"top",left:"right"},Kn=i.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=ri(Hn,r),c=oi[s.placedSide];return f.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:f.jsx(zs,{...o,ref:n,style:{...o.style,display:"block"}})})});Kn.displayName=Hn;function si(e){return e!==null}var ii=e=>({name:"transformOrigin",options:e,fn(t){var C,y,E;const{placement:n,rects:r,middlewareData:o}=t,c=((C=o.arrow)==null?void 0:C.centerOffset)!==0,a=c?0:e.arrowWidth,l=c?0:e.arrowHeight,[u,m]=zn(n),d={start:"0%",center:"50%",end:"100%"}[m],p=(((y=o.arrow)==null?void 0:y.x)??0)+a/2,v=(((E=o.arrow)==null?void 0:E.y)??0)+l/2;let g="",h="";return u==="bottom"?(g=c?d:`${p}px`,h=`${-l}px`):u==="top"?(g=c?d:`${p}px`,h=`${r.floating.height+l}px`):u==="right"?(g=`${-l}px`,h=c?d:`${v}px`):u==="left"&&(g=`${r.floating.width+l}px`,h=c?d:`${v}px`),{data:{x:g,y:h}}}});function zn(e){const[t,n="center"]=e.split("-");return[t,n]}var ci=Fn,ai=Bn,li=Un,ui=Kn,Pt=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{};function fi(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var Gn=e=>{const{present:t,children:n}=e,r=di(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=He(r.ref,pi(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};Gn.displayName="Presence";function di(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),c=e?"mounted":"unmounted",[a,l]=fi(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=ee(r.current);s.current=a==="mounted"?u:"none"},[a]),Pt(()=>{const u=r.current,m=o.current;if(m!==e){const p=s.current,v=ee(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(m&&p!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Pt(()=>{if(t){let u;const m=t.ownerDocument.defaultView??window,d=v=>{const h=ee(r.current).includes(v.animationName);if(v.target===t&&h&&(l("ANIMATION_END"),!o.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",u=m.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},p=v=>{v.target===t&&(s.current=ee(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{m.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function ee(e){return(e==null?void 0:e.animationName)||"none"}function pi(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Yn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(mi);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(De,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(De,{...r,ref:t,children:n})});Yn.displayName="Slot";var De=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=vi(n);return i.cloneElement(n,{...hi(r,n.props),ref:t?An(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});De.displayName="SlotClone";var Zn=({children:e})=>f.jsx(f.Fragment,{children:e});function mi(e){return i.isValidElement(e)&&e.type===Zn}function hi(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function vi(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var gi=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],yi=gi.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Yn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function St(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Ci(...e){return t=>{let n=!1;const r=e.map(o=>{const s=St(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():St(e[o],null)}}}}var Xn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(wi);if(s){const c=s.props.children,a=o.map(l=>l===s?i.Children.count(c)>1?i.Children.only(null):i.isValidElement(c)?c.props.children:null:l);return f.jsx(Te,{...r,ref:t,children:i.isValidElement(c)?i.cloneElement(c,void 0,a):null})}return f.jsx(Te,{...r,ref:t,children:n})});Xn.displayName="Slot";var Te=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=bi(n);return i.cloneElement(n,{...Ei(r,n.props),ref:t?Ci(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Te.displayName="SlotClone";var xi=({children:e})=>f.jsx(f.Fragment,{children:e});function wi(e){return i.isValidElement(e)&&e.type===xi}function Ei(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function bi(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Pi=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Si=Pi.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...c}=r,a=s?Xn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),Ri="VisuallyHidden",qn=i.forwardRef((e,t)=>f.jsx(Si.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));qn.displayName=Ri;var Ni=qn,[ae,lc]=Ns("Tooltip",[Wn]),le=Wn(),Jn="TooltipProvider",Oi=700,_e="tooltip.open",[Ai,Ge]=ae(Jn),Qn=e=>{const{__scopeTooltip:t,delayDuration:n=Oi,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[c,a]=i.useState(!0),l=i.useRef(!1),u=i.useRef(0);return i.useEffect(()=>{const m=u.current;return()=>window.clearTimeout(m)},[]),f.jsx(Ai,{scope:t,isOpenDelayed:c,delayDuration:n,onOpen:i.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:i.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:i.useCallback(m=>{l.current=m},[]),disableHoverableContent:o,children:s})};Qn.displayName=Jn;var ue="Tooltip",[$i,fe]=ae(ue),er=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:c,delayDuration:a}=e,l=Ge(ue,e.__scopeTooltip),u=le(t),[m,d]=i.useState(null),p=Tr(),v=i.useRef(0),g=c??l.disableHoverableContent,h=a??l.delayDuration,C=i.useRef(!1),[y=!1,E]=Me({prop:r,defaultProp:o,onChange:b=>{b?(l.onOpen(),document.dispatchEvent(new CustomEvent(_e))):l.onClose(),s==null||s(b)}}),w=i.useMemo(()=>y?C.current?"delayed-open":"instant-open":"closed",[y]),x=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,C.current=!1,E(!0)},[E]),S=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,E(!1)},[E]),R=i.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{C.current=!0,E(!0),v.current=0},h)},[h,E]);return i.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),f.jsx(ci,{...u,children:f.jsx($i,{scope:t,contentId:p,open:y,stateAttribute:w,trigger:m,onTriggerChange:d,onTriggerEnter:i.useCallback(()=>{l.isOpenDelayed?R():x()},[l.isOpenDelayed,R,x]),onTriggerLeave:i.useCallback(()=>{g?S():(window.clearTimeout(v.current),v.current=0)},[S,g]),onOpen:x,onClose:S,disableHoverableContent:g,children:n})})};er.displayName=ue;var je="TooltipTrigger",tr=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=fe(je,n),s=Ge(je,n),c=le(n),a=i.useRef(null),l=He(t,a,o.onTriggerChange),u=i.useRef(!1),m=i.useRef(!1),d=i.useCallback(()=>u.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),f.jsx(ai,{asChild:!0,...c,children:f.jsx(yi.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:U(e.onPointerMove,p=>{p.pointerType!=="touch"&&!m.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),m.current=!0)}),onPointerLeave:U(e.onPointerLeave,()=>{o.onTriggerLeave(),m.current=!1}),onPointerDown:U(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:U(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:U(e.onBlur,o.onClose),onClick:U(e.onClick,o.onClose)})})});tr.displayName=je;var Di="TooltipPortal",[uc,Ti]=ae(Di,{forceMount:void 0}),K="TooltipContent",nr=i.forwardRef((e,t)=>{const n=Ti(K,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,c=fe(K,e.__scopeTooltip);return f.jsx(Gn,{present:r||c.open,children:c.disableHoverableContent?f.jsx(rr,{side:o,...s,ref:t}):f.jsx(_i,{side:o,...s,ref:t})})}),_i=i.forwardRef((e,t)=>{const n=fe(K,e.__scopeTooltip),r=Ge(K,e.__scopeTooltip),o=i.useRef(null),s=He(t,o),[c,a]=i.useState(null),{trigger:l,onClose:u}=n,m=o.current,{onPointerInTransitChange:d}=r,p=i.useCallback(()=>{a(null),d(!1)},[d]),v=i.useCallback((g,h)=>{const C=g.currentTarget,y={x:g.clientX,y:g.clientY},E=Li(y,C.getBoundingClientRect()),w=Wi(y,E),x=ki(h.getBoundingClientRect()),S=Vi([...w,...x]);a(S),d(!0)},[d]);return i.useEffect(()=>()=>p(),[p]),i.useEffect(()=>{if(l&&m){const g=C=>v(C,m),h=C=>v(C,l);return l.addEventListener("pointerleave",g),m.addEventListener("pointerleave",h),()=>{l.removeEventListener("pointerleave",g),m.removeEventListener("pointerleave",h)}}},[l,m,v,p]),i.useEffect(()=>{if(c){const g=h=>{const C=h.target,y={x:h.clientX,y:h.clientY},E=(l==null?void 0:l.contains(C))||(m==null?void 0:m.contains(C)),w=!Fi(y,c);E?p():w&&(p(),u())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[l,m,c,u,p]),f.jsx(rr,{...e,ref:s})}),[ji,Mi]=ae(ue,{isInside:!1}),rr=i.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:c,...a}=e,l=fe(K,n),u=le(n),{onClose:m}=l;return i.useEffect(()=>(document.addEventListener(_e,m),()=>document.removeEventListener(_e,m)),[m]),i.useEffect(()=>{if(l.trigger){const d=p=>{const v=p.target;v!=null&&v.contains(l.trigger)&&m()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[l.trigger,m]),f.jsx(Ie,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:d=>d.preventDefault(),onDismiss:m,children:f.jsxs(li,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[f.jsx(Zn,{children:r}),f.jsx(ji,{scope:n,isInside:!0,children:f.jsx(Ni,{id:l.contentId,role:"tooltip",children:o||r})})]})})});nr.displayName=K;var or="TooltipArrow",Ii=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=le(n);return Mi(or,n).isInside?null:f.jsx(ui,{...o,...r,ref:t})});Ii.displayName=or;function Li(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Wi(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function ki(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Fi(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,c=t.length-1;s<t.length;c=s++){const a=t[s].x,l=t[s].y,u=t[c].x,m=t[c].y;l>r!=m>r&&n<(u-a)*(r-l)/(m-l)+a&&(o=!o)}return o}function Vi(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Bi(t)}function Bi(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],c=t[t.length-2];if((s.x-c.x)*(o.y-c.y)>=(s.y-c.y)*(o.x-c.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],c=n[n.length-2];if((s.x-c.x)*(o.y-c.y)>=(s.y-c.y)*(o.x-c.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var fc=Qn,dc=er,pc=tr,mc=nr;export{qi as C,Qi as D,Xi as O,Zi as P,nc as R,Ji as T,ec as a,rc as b,sc as c,ic as d,cc as e,ac as f,mc as g,fc as h,dc as i,pc as j};
