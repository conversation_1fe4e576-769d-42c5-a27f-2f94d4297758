import React, { useState } from 'react';

const CalendarBooking = () => {
  const [username, setUsername] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [eventId, setEventId] = useState('');
  const [meetingType, setMeetingType] = useState('Link');

  return (
    <div className="calendar-booking-modal">
      <h3>Calendar Bookings</h3>
      <div className="calendar-options">
        <button>GHL</button>
        <button>Cal.com</button>
        <button>Google</button>
      </div>

      <form>
        <div className="form-group">
          <label>Username</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Username required"
          />
        </div>

        <div className="form-group">
          <label>API Key</label>
          <input
            type="text"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="API key required"
          />
        </div>

        <div className="form-group">
          <label>Event ID</label>
          <input
            type="text"
            value={eventId}
            onChange={(e) => setEventId(e.target.value)}
            placeholder="Event ID required"
          />
        </div>

        <div className="form-group">
          <label>Meeting Type</label>
          <select value={meetingType} onChange={(e) => setMeetingType(e.target.value)}>
            <option value="Link">Link</option>
            <option value="Zoom">Zoom</option>
            <option value="Google Meet">Google Meet</option>
            <option value="Cal Video">Cal Video</option>
          </select>
        </div>

        <button type="submit">Book Calendar</button>
      </form>
    </div>
  );
};

export default CalendarBooking;
