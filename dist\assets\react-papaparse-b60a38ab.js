import{P as S}from"./papaparse-2d1475f9.js";import{R as y,r as f}from"./vendor-2ae44a2e.js";var s=function(){return s=Object.assign||function(n){for(var r,a=1,o=arguments.length;a<o;a++)for(var t in r=arguments[a])Object.prototype.hasOwnProperty.call(r,t)&&(n[t]=r[t]);return n},s.apply(this,arguments)};function he(n,r){var a={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&r.indexOf(o)<0&&(a[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function"){var t=0;for(o=Object.getOwnPropertySymbols(n);t<o.length;t++)r.indexOf(o[t])<0&&Object.prototype.propertyIsEnumerable.call(n,o[t])&&(a[o[t]]=n[o[t]])}return a}function nn(n,r,a,o){return new(a||(a=Promise))(function(t,l){function c(d){try{h(o.next(d))}catch(i){l(i)}}function g(d){try{h(o.throw(d))}catch(i){l(i)}}function h(d){var i;d.done?t(d.value):(i=d.value,i instanceof a?i:new a(function(P){P(i)})).then(c,g)}h((o=o.apply(n,r||[])).next())})}function tn(n,r){var a,o,t,l,c={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return l={next:g(0),throw:g(1),return:g(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function g(h){return function(d){return function(i){if(a)throw new TypeError("Generator is already executing.");for(;l&&(l=0,i[0]&&(c=0)),c;)try{if(a=1,o&&(t=2&i[0]?o.return:i[0]?o.throw||((t=o.return)&&t.call(o),0):o.next)&&!(t=t.call(o,i[1])).done)return t;switch(o=0,t&&(i=[2&i[0],t.value]),i[0]){case 0:case 1:t=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,o=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(t=c.trys,!((t=t.length>0&&t[t.length-1])||i[0]!==6&&i[0]!==2)){c=0;continue}if(i[0]===3&&(!t||i[1]>t[0]&&i[1]<t[3])){c.label=i[1];break}if(i[0]===6&&c.label<t[1]){c.label=t[1],t=i;break}if(t&&c.label<t[2]){c.label=t[2],c.ops.push(i);break}t[2]&&c.ops.pop(),c.trys.pop();continue}i=r.call(n,c)}catch(P){i=[6,P],o=0}finally{a=t=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([h,d])}}}function we(n,r,a){if(a||arguments.length===2)for(var o,t=0,l=r.length;t<l;t++)!o&&t in r||(o||(o=Array.prototype.slice.call(r,0,t)),o[t]=r[t]);return n.concat(o||Array.prototype.slice.call(r))}function ue(n){return typeof n.isPropagationStopped=="function"?n.isPropagationStopped():n.cancelBubble!==void 0&&n.cancelBubble}function D(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return function(a){for(var o=[],t=1;t<arguments.length;t++)o[t-1]=arguments[t];return n.some(function(l){return!ue(a)&&l&&l.apply(void 0,we([a],o,!1)),ue(a)})}}function ce(n){return n.dataTransfer?Array.prototype.some.call(n.dataTransfer.types,function(r){return r==="Files"||r==="application/x-moz-file"}):!!n.target&&!!n.target.files}var rn=function(n){n=Array.isArray(n)&&n.length===1?n[0]:n;var r=Array.isArray(n)?"one of ".concat(n.join(", ")):n;return{code:"file-invalid-type",message:"File type must be ".concat(r)}};function on(n,r){var a=n.type==="application/x-moz-file"||function(o,t){if(o&&t){var l=Array.isArray(t)?t:t.split(","),c=o.name||"",g=(o.type||"").toLowerCase(),h=g.replace(/\/.*$/,"");return l.some(function(d){var i=d.trim().toLowerCase();return i.charAt(0)==="."?c.toLowerCase().endsWith(i):i.endsWith("/*")?h===i.replace(/\/.*$/,""):g===i})}return!0}(n,r);return[a,a?null:rn(r)]}function Z(n){return n!=null}var Ne=function(n){return{code:"file-too-large",message:"File is larger than ".concat(n," bytes")}},Ie=function(n){return{code:"file-too-small",message:"File is smaller than ".concat(n," bytes")}},an={code:"too-many-files",message:"Too many files"};function Ue(n){n.preventDefault()}var be={Link:"link",Button:"button"};function vn(){return{CSVDownloader:function(){var n=this,r=function(a){var o=a.children,t=a.data,l=t===void 0?{}:t,c=a.filename,g=a.type,h=g===void 0?be.Link:g,d=a.style,i=d===void 0?{}:d,P=a.className,M=P===void 0?"":P,$=a.bom,G=$!==void 0&&$,H=a.config,ee=H===void 0?{}:H,J=function(){return nn(n,void 0,void 0,function(){var Q,K,E,N,I,L;return tn(this,function(O){switch(O.label){case 0:return Q=G?"\uFEFF":"",K=null,E=null,typeof l!="function"?[3,2]:[4,l()];case 1:l=O.sent(),O.label=2;case 2:return K=typeof l=="object"?S.unparse(l,ee):l,N=new Blob(["".concat(Q).concat(K)],{type:"text/csv;charset=utf-8;"}),I=window.navigator,E=I.msSaveBlob?I.msSaveBlob(N,"".concat(c,".csv")):window.URL.createObjectURL(N),(L=document.createElement("a")).href=E,L.setAttribute("download","".concat(c,".csv")),L.click(),L.remove(),[2]}})})};return y.createElement(y.Fragment,null,h===be.Button?y.createElement("button",{onClick:function(){return J()},style:i,className:M},o):y.createElement("a",{onClick:function(){return J()},style:i,className:M},o))};return y.useMemo(function(){return r},[])}(),Type:be}}var _e={progressBar:{borderRadius:3,boxShadow:"inset 0 1px 3px rgba(0, 0, 0, .2)",bottom:14,width:"100%"},button:{position:"inherit",width:"100%"},fill:{backgroundColor:"#659cef",borderRadius:3,height:10,transition:"width 500ms ease-in-out"}};function cn(n){var r=n.style,a=n.className,o=n.display,t=f.useState(0),l=t[0],c=t[1];return f.useEffect(function(){c(n.percentage)},[n.percentage]),y.createElement("span",{style:Object.assign({},_e.progressBar,_e.fill,r,{width:"".concat(l,"%"),display:o}),className:a})}function un(n){var r=n.color,a=n.width,o=a===void 0?23:a,t=n.height,l=t===void 0?23:t;return y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:o,height:l,viewBox:"0 0 512 512"},y.createElement("path",{fill:r,d:"M504.1 256C504.1 119 393 7.9 256 7.9S7.9 119 7.9 256 119 504.1 256 504.1 504.1 393 504.1 256z"}),y.createElement("path",{fill:"#FFF",d:"M285 256l72.5-84.2c7.9-9.2 6.9-23-2.3-31-9.2-7.9-23-6.9-30.9 2.3L256 222.4l-68.2-79.2c-7.9-9.2-21.8-10.2-31-2.3-9.2 7.9-10.2 21.8-2.3 31L227 256l-72.5 84.2c-7.9 9.2-6.9 23 2.3 31 4.1 3.6 9.2 5.3 14.3 5.3 6.2 0 12.3-2.6 16.6-7.6l68.2-79.2 68.2 79.2c4.3 5 10.5 7.6 16.6 7.6 5.1 0 10.2-1.7 14.3-5.3 9.2-7.9 10.2-21.8 2.3-31L285 256z"}))}var ln="text/csv, .csv, application/vnd.ms-excel";function sn(){var n=function(r){var a=r.children,o=r.accept,t=o===void 0?ln:o,l=r.config,c=l===void 0?{}:l,g=r.minSize,h=g===void 0?0:g,d=r.maxSize,i=d===void 0?1/0:d,P=r.maxFiles,M=P===void 0?1:P,$=r.disabled,G=$!==void 0&&$,H=r.noClick,ee=H!==void 0&&H,J=r.noDrag,Q=J!==void 0&&J,K=r.noDragEventsBubbling,E=K!==void 0&&K,N=r.noKeyboard,I=N!==void 0&&N,L=r.multiple,O=L!==void 0&&L,De=r.required,We=De!==void 0&&De,Ee=r.preventDropOnDocument,le=Ee===void 0||Ee,ne=r.onUploadAccepted,se=r.validator,Fe=r.onUploadRejected,fe=r.onDragEnter,de=r.onDragOver,pe=r.onDragLeave,w=f.useRef(null),F=f.useRef(null),U=f.useRef([]),ke=f.useReducer(fn,qe),_=ke[0],b=ke[1],Ve=_.acceptedFile,$e=_.displayProgressBar,Ge=_.progressBarPercentage,He=_.draggedFiles,Ce=_.isFileDialogActive,Pe=function(e){F.current&&F.current.contains(e.target)||(e.preventDefault(),U.current=[])};f.useEffect(function(){return le&&(document.addEventListener("dragover",Ue,!1),document.addEventListener("drop",Pe,!1)),function(){le&&(document.removeEventListener("dragover",Ue),document.removeEventListener("drop",Pe))}},[F,le]);var q=function(e){return G?null:e},te=function(e){return Q?null:q(e)},Be=function(e){E&&e.stopPropagation()},re=function(e){e.preventDefault(e),e.persist(),Be(e)},xe=function(e){b({displayProgressBar:e,type:"setDisplayProgressBar"})},Oe=function(e){b({progressBarPercentage:e,type:"setProgressBarPercentage"})},Je=function(e){return y.createElement(cn,s({display:$e,percentage:Ge},e))},Qe=function(e){return y.createElement(un,s({},e))},ve=f.useCallback(function(){w.current&&_.displayProgressBar&&(b({type:"openDialog"}),w.current.value=null,w.current.click())},[b]),Ae=function(){Ce&&setTimeout(function(){w.current&&(w.current.files.length||b({type:"closeDialog"}))},300)};f.useEffect(function(){return window.addEventListener("focus",Ae,!1),function(){window.removeEventListener("focus",Ae,!1)}},[w,Ce]);var Re=f.useCallback(function(){var e;ee||(e===void 0&&(e=window.navigator.userAgent),function(u){return u.indexOf("MSIE")!==-1||u.indexOf("Trident/")!==-1}(e)||function(u){return u.indexOf("Edge/")!==-1}(e)?setTimeout(ve,0):ve())},[w,ee]),oe=f.useCallback(function(e){if(re(e),Oe(0),U.current=[],ce(e)){if(ue(e)&&!E)return;var u=[],p=[],k=e.target.files||e.dataTransfer&&e.dataTransfer.files;if(Array.from(k).forEach(function(v){var m=on(v,t),X=m[0],j=m[1],ie=function(z,W,V){if(Z(z.size))if(Z(W)&&Z(V)){if(z.size>V)return[!1,Ne(V)];if(z.size<W)return[!1,Ie(W)]}else{if(Z(W)&&z.size<W)return[!1,Ie(W)];if(Z(V)&&z.size>V)return[!1,Ne(V)]}return[!0,null]}(v,h,i),ae=ie[0],me=ie[1],Y=se?se(v):null;if(X&&ae&&!Y)u.push(v);else{var ye=[j,me];Y&&(ye=ye.concat(Y)),p.push({file:v,errors:ye.filter(function(z){return z})})}}),(!O&&u.length>1||O&&M>=1&&u.length>M)&&(u.forEach(function(v){p.push({file:v,errors:[an]})}),u.splice(0)),b({acceptedFiles:u,fileRejections:p,type:"setFiles"}),xe("block"),p.length>0&&Fe&&Fe(p,e),u.length>0&&ne){var B={},C=[],A=[],R=[],T=new window.FileReader,x=0;u.forEach(function(v){b({acceptedFile:v,type:"setFile"}),B={complete:c!=null&&c.complete||c!=null&&c.step?c.complete:function(){ne({data:C,errors:A,meta:R},v)},step:c!=null&&c.step?c.step:function(m){if(C.push(m.data),m.errors.length>0&&A.push(m.errors),m.length>0&&R.push(m[0].meta),c&&c.preview)x=Math.round(C.length/c.preview*100),C.length===c.preview&&ne({data:C,errors:A,meta:R},v);else{var X=m.meta.cursor,j=Math.round(X/v.size*100);if(j===x)return;x=j}Oe(x)}},B=Object.assign({},c,B),T.onload=function(m){S.parse(m.target.result,B)},T.onloadend=function(){setTimeout(function(){xe("none")},2e3)},T.readAsText(v,c.encoding||"utf-8")})}}},[O,t,h,i,M,se,ne]),Xe=f.useCallback(function(e){Be(e)},[]),ge=function(e){return I?null:q(e)},Se=f.useCallback(function(e){if(re(e),U.current=we(we([],U.current,!0),[e.target],!1),ce(e)){if(ue(e)&&!E)return;b({draggedFiles:He,isDragActive:!0,type:"setDraggedFiles"}),fe&&fe(e)}},[fe,E]),Le=f.useCallback(function(e){re(e);var u=ce(e);if(u&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch{}return u&&de&&de(e),!1},[de,E]),Te=f.useCallback(function(e){re(e);var u=U.current.filter(function(k){return F.current&&F.current.contains(k)}),p=u.indexOf(e.target);p!==-1&&u.splice(p,1),U.current=u,u.length>0||(b({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),ce(e)&&pe&&pe(e))},[F,pe,E]),je=f.useCallback(function(e){F.current&&F.current.isEqualNode(e.target)&&(e.key!=="Space"&&e.key!=="Enter"||(e.preventDefault(),ve()))},[F,w]),ze=f.useCallback(function(){b({type:"focus"})},[]),Me=f.useCallback(function(){b({type:"blur"})},[]),Ye=f.useMemo(function(){return function(e){e===void 0&&(e={});var u=e.onClick,p=u===void 0?function(){}:u,k=e.onDrop,B=k===void 0?function(){}:k,C=e.onDragOver,A=C===void 0?function(){}:C,R=e.onDragLeave,T=R===void 0?function(){}:R,x=e.onKeyDown,v=x===void 0?function(){}:x,m=e.onFocus,X=m===void 0?function(){}:m,j=e.onBlur,ie=j===void 0?function(){}:j,ae=e.onDragEnter,me=ae===void 0?function(){}:ae,Y=he(e,["onClick","onDrop","onDragOver","onDragLeave","onKeyDown","onFocus","onBlur","onDragEnter"]);return s({onClick:q(D(p,Re)),onDrop:te(D(B,oe)),onDragEnter:te(D(me,Se)),onDragOver:te(D(A,Le)),onDragLeave:te(D(T,Te)),onKeyDown:ge(D(v,je)),onFocus:ge(D(X,ze)),onBlur:ge(D(ie,Me))},Y)}},[F,je,ze,Me,Re,Se,Le,Te,oe,I,Q,G]),Ze=f.useMemo(function(){return function(e){var u;e===void 0&&(e={});var p=e.refKey,k=p===void 0?"ref":p,B=e.onChange,C=B===void 0?function(){}:B,A=e.onClick,R=A===void 0?function(){}:A,T=he(e,["refKey","onChange","onClick"]),x=((u={accept:t,multiple:O,required:We,type:"file",style:{display:"none"},onChange:q(D(C,oe)),onClick:q(D(R,Xe)),autoComplete:"off",tabIndex:-1})[k]=w,u);return s(s({},x),T)}},[w,t,oe,G]),Ke=f.useCallback(function(e){w.current.value="",b({type:"reset"}),e.stopPropagation()},[]),en=f.useMemo(function(){return function(e){e===void 0&&(e={});var u=e.onClick,p=u===void 0?function(){}:u,k=he(e,["onClick"]);return s({onClick:q(D(p,Ke))},k)}},[Ke]);return y.createElement(y.Fragment,null,y.createElement("input",s({},Ze())),a({getRootProps:Ye,acceptedFile:Ve,ProgressBar:Je,getRemoveFileProps:en,Remove:Qe}))};return f.useMemo(function(){return n},[])}function gn(){return{CSVReader:sn()}}var qe={displayProgressBar:"none",progressBarPercentage:0,isDragActive:!1,isFileDialogActive:!1,isFocused:!1,draggedFiles:[],acceptedFiles:[],acceptedFile:null};function fn(n,r){switch(r.type){case"openDialog":return s(s({},n),{isFileDialogActive:!0});case"closeDialog":return s(s({},n),{isFileDialogActive:!1});case"setFiles":return s(s({},n),{acceptedFiles:r.acceptedFiles,fileRejections:r.fileRejections});case"setFile":return s(s({},n),{acceptedFile:r.acceptedFile});case"setDisplayProgressBar":return s(s({},n),{displayProgressBar:r.displayProgressBar});case"setProgressBarPercentage":return s(s({},n),{progressBarPercentage:r.progressBarPercentage});case"setDraggedFiles":var a=r.isDragActive,o=r.draggedFiles;return s(s({},n),{draggedFiles:o,isDragActive:a});case"focus":return s(s({},n),{isFocused:!0});case"blur":return s(s({},n),{isFocused:!1});case"reset":return s({},qe);default:return n}}S.BAD_DELIMITERS;S.RECORD_SEP;S.UNIT_SEP;S.WORKERS_SUPPORTED;S.LocalChunkSize;S.DefaultDelimiter;export{vn as C,gn as z};
