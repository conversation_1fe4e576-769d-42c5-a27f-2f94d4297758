import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { tokenExpireError } from "Context/Auth";
import { InteractiveButton } from "Components/InteractiveButton";
import ModalPrompt from "Components/Modal/ModalPrompt";
import { useNavigate } from "react-router-dom";
import { Calendar, Split, Database, Settings } from "lucide-react";
let sdk = new MkdSDK();

function TabButton({ title, isActive, onClick, icon = null }) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center gap-2 border-b-2 px-6 py-3 text-sm font-medium transition-colors ${
        isActive
          ? "border-blue-500 text-white"
          : "border-transparent text-gray-400 hover:border-gray-400 hover:text-gray-300"
      }`}
    >
      {icon}
      {title}
    </button>
  );
}

const UserProfilePage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [oldEmail, setOldEmail] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isLastNameModalOpen, setIsLastNameModalOpen] = useState(false);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [isEventModalOpen, setIsEventModalOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("Profile");
  const [defaultValues, setDefaultValues] = useState({});
  const [active, setActive] = useState(false);
  const [load, setLoad] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  async function fetchData() {
    try {
      setLoad(true);
      const result = await sdk.getProfile();
      // const active = await sdk.callRawAPI("/v3/api/custom/voiceoutreach/user/calendar/check-integration",{},"GET");
      // setActive(active.active);
      setDefaultValues(result);

      setValue("email", result?.email);
      setValue("first_name", result?.first_name);
      setValue("last_name", result?.last_name);
      setOldEmail(result?.email);
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
    setLoad(false);
  }

  const onSubmit = async (data) => {
    setDefaultValues(data);
    try {
      setSubmitLoading(true);

      const result = await sdk.updateProfile({
        first_name: data.first_name || defaultValues?.first_name,
        last_name: data.last_name || defaultValues?.last_name,
      });

      if (!result.error) {
        showToast(dispatch, "Profile Updated", 4000);
        closeModal();
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
        closeModal();
      }
      if (oldEmail !== data.email) {
        const emailresult = await sdk.updateEmail(data.email);
        if (!emailresult.error) {
          showToast(dispatch, "Email Updated", 1000);
        } else {
          if (emailresult.validation) {
            const keys = Object.keys(emailresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: emailresult.validation[field],
              });
            }
          }
        }
        closeModal();
      }

      if (data.password.length > 0) {
        const passwordresult = await sdk.updatePassword(data.password);
        if (!passwordresult.error) {
          showToast(dispatch, "Password Updated", 2000);
        } else {
          if (passwordresult.validation) {
            const keys = Object.keys(passwordresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: passwordresult.validation[field],
              });
            }
          }
        }
      }
      await fetchData();
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });

    fetchData();
  }, []);

  const connectCalendar = async () => {
    setCalendarLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/calendar/auth",
        {},
        "GET"
      );
      console.log(result);
      if (!result.error) {
        setCalendarLoading(false);
        window.open(result.link);
      }
    } catch (e) {
      console.log(e);
    }
    setCalendarLoading(false);
  };
  const openModalEdit = () => {
    setIsEditModalOpen(true);
  };

  const openLastNModal = () => {
    setIsLastNameModalOpen(true);
  };

  const openEmailModal = () => {
    setIsEmailModalOpen(true);
  };

  const openEventsModal = () => {
    setIsEventModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsEditModalOpen(false);
    setIsLastNameModalOpen(false);
    setIsEmailModalOpen(false);
    setIsEventModalOpen(false);
  };

  const handleDelete = async () => {
    try {
      setSubmitLoading(true);

      const result = await sdk.updateProfile({
        first_name: defaultValues?.first_name,
        last_name: defaultValues?.last_name,
        photo: "",
      });

      if (!result.error) {
        showToast(dispatch, "Profile Picture Deleted", 1000);
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      await fetchData();
      setSubmitLoading(false);
      closeModal();
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
    }
  };

  return (
    <div className="mt-6 rounded-lg bg-[#1d2937]">
      <div className="mb-6">
        <div className="flex justify-center space-x-8 pt-5">
          <TabButton
            icon={<Calendar className="h-4 w-4" />}
            title="Profile"
            isActive={activeTab === "Profile"}
            onClick={() => setActiveTab("Profile")}
          />
          <TabButton
            icon={<Settings className="h-4 w-4" />}
            title="Security"
            isActive={activeTab === "Security"}
            onClick={() => setActiveTab("Security")}
          />
        </div>
      </div>

      <main className="p-8 pt-0">
        {/* Profile Tab */}
        {activeTab === "Profile" && (
          <div className="mx-auto mb-4 max-w-2xl">
            <h2 className="mb-6 text-xl font-semibold text-white">
              Personal Details
            </h2>

            <div className="space-y-4">
              {/* First Name */}
              <div className="overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]">
                <div className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-x-20">
                    <p className="text-sm font-medium text-gray-400">
                      First Name
                    </p>
                    <p className="text-base font-medium text-white">
                      {defaultValues?.first_name}
                    </p>
                  </div>
                  <button
                    onClick={openModalEdit}
                    className="text-sm font-medium text-blue-400 transition-colors hover:text-blue-300"
                  >
                    Edit
                  </button>
                </div>
              </div>

              {/* Last Name */}
              <div className="overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]">
                <div className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-x-20">
                    <p className="text-sm font-medium text-gray-400">
                      Last Name
                    </p>
                    <p className="text-base font-medium text-white">
                      {defaultValues?.last_name}
                    </p>
                  </div>
                  <button
                    onClick={openLastNModal}
                    className="text-sm font-medium text-blue-400 transition-colors hover:text-blue-300"
                  >
                    Edit
                  </button>
                </div>
              </div>

              {/* Email */}
              <div className="overflow-hidden rounded-lg bg-[#2a3441] transition-colors hover:bg-[#2f3b4b]">
                <div className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-x-20">
                    <p className="text-sm font-medium text-gray-400">Email</p>
                    <p className="text-base font-medium text-white">
                      {oldEmail}
                    </p>
                  </div>
                  <button
                    onClick={openEmailModal}
                    className="text-sm font-medium text-blue-400 transition-colors hover:text-blue-300"
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === "Security" && (
          <div className="mx-auto max-w-2xl">
            <h2 className=" mb-0 text-xl font-semibold text-white">
              Security Settings
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-2">
              <div className="py-2">
                <label className="mb-2 block text-sm font-medium text-gray-400">
                  Password
                </label>
                <input
                  {...register("password")}
                  type="password"
                  className="w-[300px] border border-gray-600 bg-[#1d2937] px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter new password"
                />
                {errors.password?.message && (
                  <p className="mt-2 text-sm text-red-400">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="flex justify-start">
                <InteractiveButton
                  type="submit"
                  loading={submitLoading}
                  disabled={submitLoading}
                  className="rounded-md bg-[#19b2f6]/80  px-6 py-2 text-white transition-colors hover:bg-blue-600 disabled:opacity-50"
                >
                  Update Password
                </InteractiveButton>
              </div>
            </form>
          </div>
        )}

        {isModalOpen && (
          <ModalPrompt
            actionHandler={handleDelete}
            closeModalFunction={closeModal}
            title={`Are you sure ? `}
            message={`Are you sure you want to delete profile picture ? `}
            acceptText={`DELETE`}
            rejectText={`CANCEL`}
          />
        )}
        {isEditModalOpen && (
          <EditInfoModal
            title="Edit information"
            label="First Name"
            buttonName="Save and close"
            isOpen={openModalEdit}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="first_name"
            submitLoading={submitLoading}
            errors={errors}
          />
        )}
        {isLastNameModalOpen && (
          <EditInfoModal
            title="Edit information"
            label="Last Name"
            buttonName="Save and close"
            isOpen={openLastNModal}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="last_name"
            submitLoading={submitLoading}
            errors={errors}
          />
        )}
        {isEmailModalOpen && (
          <EditInfoModal
            title="Change Email"
            label="Email"
            buttonName="Submit"
            isOpen={openEmailModal}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="email"
            submitLoading={submitLoading}
            errors={errors}
            defaultValues={defaultValues}
          />
        )}
        {isEventModalOpen && (
          <EventModal
            title="View Events"
            label="Events"
            buttonName="okay"
            isOpen={openEventsModal}
            onClose={closeModal}
          />
        )}
      </main>
    </div>
  );
};

export const EditInfoModal = (props) => {
  const {
    title,
    label,
    buttonName,
    isOpen,
    onClose,
    handleSubmit,
    onSubmit,
    register,
    id,
    submitLoading,
    errors,
    defaultValues,
  } = props;
  const [emailConfirm, setEmailConfirm] = useState(false);
  const [values, setValues] = useState({
    email: "",
  });

  const handleChange = (prop) => (event) => {
    if (prop === "email") {
      setValues({ ...values, [prop]: event.target.value });
    }
  };

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto">
      <div
        className={`fixed inset-0 z-10 overflow-y-auto ${
          isOpen ? "block" : "hidden"
        } `}
      >
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
          <span
            className="hidden sm:inline-block sm:h-screen sm:align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>
          <div className="inline-block transform overflow-hidden rounded-lg  bg-[#1d2937] px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold leading-6 text-white">
                {title}
              </div>
              <button
                className="text-gray-500 hover:text-white focus:outline-none"
                onClick={onClose}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmit(onSubmit)} className="max-w-lg">
              {emailConfirm === true && (
                <div className="mt-3 flex">
                  <div className="mr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z"
                        fill="transparent"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="mb-1 text-sm font-medium text-white">
                      We've send an email to:
                    </p>
                    <p className="mb-2 text-sm font-semibold text-white">
                      {values?.email}
                    </p>
                    <p className="mb-2 text-sm font-medium text-white">
                      In order to complete the email update click the
                      confirmation link.
                    </p>
                    <p className="mb-2 text-sm font-medium text-white">
                      (the link expires in 24 hours)
                    </p>
                  </div>
                </div>
              )}
              {emailConfirm === false &&
                (id === "first_name" || id === "last_name") && (
                  <div className="mt-3">
                    <label
                      htmlFor="firstName"
                      className="mb-1 block text-sm font-medium text-white"
                    >
                      {label}
                    </label>
                    <input
                      className="focus:shadow-outline placeholder-text-white w-full appearance-none rounded bg-transparent px-3 py-2 leading-tight text-white shadow focus:outline-none"
                      id={id}
                      type="text"
                      placeholder={`Enter ${label} `}
                      name={id}
                      {...register(id)}
                      // defaultValue={values[id]}
                      // onChange={id === "email" && handleChange("email")}
                    />
                    <p className="text-xs italic text-red-500">
                      {errors?.id?.message}
                    </p>
                  </div>
                )}
              {emailConfirm === false && id === "email" && (
                <div className="mt-3">
                  <label
                    htmlFor="firstName"
                    className="mb-1 block text-sm font-medium text-white"
                  >
                    {label}
                  </label>
                  <input
                    className="focus:shadow-outline placeholder-text-white w-full appearance-none rounded bg-transparent px-3 py-2 leading-tight text-white shadow focus:outline-none"
                    id={id}
                    type="text"
                    placeholder={`Enter ${label}`}
                    name={id}
                    {...register(id)}
                    onChange={handleChange("email")}
                  />
                  <p className="text-xs italic text-red-500">
                    {errors?.id?.message}
                  </p>
                </div>
              )}
              {emailConfirm === false && id === "events" && (
                <div className="mt-3">
                  <label
                    htmlFor="events"
                    className="mb-1 block text-sm font-medium text-white"
                  >
                    {label}
                  </label>
                </div>
              )}
              <div className="mt-4 flex justify-between">
                <button
                  className="-gray-300 mr-2 w-full rounded-md bg-red-600 px-4 py-2 text-white"
                  onClick={onClose}
                >
                  Cancel
                </button>
                {(id === "first_name" ||
                  id === "last_name" ||
                  emailConfirm === true) && (
                  <InteractiveButton
                    className="focus:shadow-outline hover:transparent w-full rounded-md bg-[#19b2f6]/80  px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed"
                    type="submit"
                    loading={submitLoading}
                    disabled={submitLoading}
                  >
                    {buttonName}
                  </InteractiveButton>
                )}
                {id === "email" && !emailConfirm && (
                  <InteractiveButton
                    className="focus:shadow-outline hover:transparent w-full rounded-md bg-[#19b2f6]/80  px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed"
                    type="submit"
                    loading={submitLoading}
                    disabled={submitLoading}
                    onClick={() => setEmailConfirm(true)}
                  >
                    Submit
                  </InteractiveButton>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
export const EventModal = (props) => {
  const { title, label, buttonName, isOpen, onClose } = props;

  const [events, setEvents] = useState([]);
  const [load, setLoad] = useState([]);
  async function getEvents() {
    setLoad(true);
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/calendar/events",
        {},
        "GET"
      );
      setEvents(result?.list);
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
    setLoad(false);
  }

  React.useEffect(() => {
    getEvents();
  }, []);

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto">
      <div
        className={`fixed inset-0 z-10 overflow-y-auto ${
          isOpen ? "block" : "hidden"
        } `}
      >
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
          <span
            className="hidden sm:inline-block sm:h-screen sm:align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>
          <div className="inline-block transform overflow-hidden rounded-lg  bg-[#1d2937] px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold leading-6 text-white">
                {title}
              </div>
              <button
                className="text-gray-500 hover:text-white focus:outline-none"
                onClick={onClose}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            {
              <div className="mt-3">
                <label
                  htmlFor="events"
                  className="mb-1 block text-sm font-medium text-white"
                >
                  {label}
                </label>
                {load ? "Fetching Events" : ""}
                <ul>
                  {events.map((event) => (
                    <div key={event.id}>
                      <li>
                        <div>
                          <strong>Event:</strong> {event.summary}
                        </div>
                        <div>
                          <strong>Start:</strong>{" "}
                          {new Date(
                            event.start.dateTime || event.start.date
                          ).toLocaleString()}
                        </div>
                        <div>
                          <strong>End:</strong>{" "}
                          {new Date(
                            event.end.dateTime || event.end.date
                          ).toLocaleString()}
                        </div>
                      </li>
                      <br></br>
                    </div>
                  ))}
                </ul>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;
