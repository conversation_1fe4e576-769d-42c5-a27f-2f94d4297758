import{_ as l}from"./qr-scanner-cf010ec4.js";import{r as e}from"./vendor-2ae44a2e.js";const s=e.lazy(()=>l(()=>import("./MkdFileUpload-7b3914e1.js"),["assets/MkdFileUpload-7b3914e1.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/papaparse-2d1475f9.js"]));function n({title:r,titleId:a,...o},t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),r?e.createElement("title",{id:a},r):null,e.createElement("path",{fillRule:"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"}))}const i=e.forwardRef(n),f=i;export{s as M,f as X};
