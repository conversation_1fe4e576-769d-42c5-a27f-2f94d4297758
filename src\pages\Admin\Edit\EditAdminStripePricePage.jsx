import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { FaLessThanEqual } from "react-icons/fa";

let sdk = new MkdSDK();

const EditAdminStripePricePage = ({ activeId, setSidebar }) => {
  const schema = yup
    .object({
      name: yup.string().required(),
      status: yup.boolean().required(),
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const params = useParams();
  const [id, setId] = useState(0);
  const [isEditingPlan, setIsEditingPlan] = useState(false);

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectStatus = [
    { key: "0", value: "Inactive" },
    { key: "1", value: "Active" },
  ];

  const onSubmit = async (data) => {
    setIsEditingPlan(true);
    try {
      const result = await sdk.updateStripePrice(activeId, data);
      if (!result.error) {
        showToast(globalDispatch, "Edited", 4000);
        // navigate("/admin/prices");
        setIsEditingPlan(false);
        if (closeSidebar) {
          closeSidebar();
        }
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
        setIsEditingPlan(false);
      }
    } catch (error) {
      console.log("Error", error);
      showToast(globalDispatch, error.message, 4000);
      tokenExpireError(dispatch, error.message);
    }
    setIsEditingPlan(false);
  };

  async function getPrices() {
    try {
      const result = await sdk.getStripePrice(activeId);

      if (!result.error) {
        const price = result.model.object;
        setValue("name", price.nickname);
        setValue("status", result.model.status);
        setId(result.model.id); //set local id
      }
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "prices",
      },
    });

    getPrices();
  }, [activeId]);

  return (
    <div className="mx-auto   rounded">
      {/* <button onClick={handleSubmit(onSubmit)}>try submiting</button> */}
      {/* <h4 className="text-2xl font-medium">Edit Product</h4> */}
      <div
        className={`flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3`}
      >
        <div className="flex items-center gap-3">
          <svg
            onClick={() => setSidebar(false)}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              stroke-width="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-lg font-semibold">Edit Plan</span>
        </div>
        <div className="flex items-center gap-4">
          <button
            className="flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]"
            onClick={() => setSidebar(false)}
          >
            Cancel
          </button>
          <button
            className="flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm"
            onClick={async () => {
              await handleSubmit(onSubmit)();
              setSidebar(false);
            }}
            disabled={isEditingPlan}
          >
            {isEditingPlan ? "Saving" : "Save"}
          </button>
        </div>
      </div>
      <form
        className="w-full max-w-lg p-4 text-left"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="mb-4">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="name"
          >
            Name
          </label>
          <input
            type="text"
            {...register("name")}
            className={`"appearance-none focus:shadow-outline w-full rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">{errors.name?.message}</p>
        </div>

        <div className="mb-4">
          <label className="mb-2 block text-sm font-bold text-gray-700">
            Status
          </label>
          <select
            className="focus:shadow-outline mb-3  w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none"
            {...register("status")}
          >
            {selectStatus.map((option) => (
              <option value={option.key} key={option.key}>
                {option.value}
              </option>
            ))}
          </select>
        </div>

        {/* <button type="submit" className="bg-[black] hover:opacity-95 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
            Submit
          </button> */}
      </form>
    </div>
  );
};

export default EditAdminStripePricePage;
