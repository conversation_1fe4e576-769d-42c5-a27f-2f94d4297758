import{j as s}from"./@react-google-maps/api-c55ecefa.js";import{R as o,f as D}from"./vendor-2ae44a2e.js";import{u as G}from"./react-hook-form-47c010f8.js";import{o as $}from"./yup-5abd4662.js";import{c as z,a as c}from"./yup-c2e87575.js";import{M as A,G as T,A as O,s as v,t as B}from"./index-d0a8f5da.js";import{M as m}from"./MkdInput-c12da351.js";import{I as H}from"./InteractiveButton-bff38983.js";import{b as J}from"./index.esm-4b383179.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";let u=new A;const ve=({closeSidebar:h})=>{var S;const{dispatch:d}=o.useContext(T),{state:_}=o.useContext(O),q=z({script:c().required("Script is required"),voicemail_script:c().required("Voicemail script is required"),assistant_name:c().required("Assistant name is required"),first_message:c().required("First message is required"),voice_id:c().required("Voice selection is required"),notes:c().required("Notes are required")}).required(),{dispatch:C}=o.useContext(T);o.useState({});const[p,P]=o.useState([]),[f,V]=o.useState([]),[i,y]=o.useState(null),[E,j]=o.useState(!1),[N,x]=o.useState(""),k=D(),{register:l,handleSubmit:I,setError:L,setValue:r,formState:{errors:n,isSubmitting:w}}=G({resolver:$(q)}),F=async e=>{let a=new A;try{const t={assistant_name:e.assistant_name,script:e.script,voicemail_script:e.voicemail_script,voice_id:e.voice_id,first_message:e.first_message,status:1,notes:e.notes};i&&i.assistant_settings?t.assistant_settings=i.assistant_settings:t.assistant_settings="",await a.callRawAPI("/v3/api/custom/voiceoutreach/user/create_assistant",t,"POST"),v(d,"Added"),k("/user/assistants"),h&&h()}catch(t){console.log("Error",t),B(C,t.message),v(d,t.message,5e3,"error"),L("script",{type:"manual",message:t.message})}},M=async()=>{j(!0);try{u.setTable("template_assistants");const e=await u.callRestAPI({},"GETALL");e.error||(console.log("templates",e),V(e.list||[]))}catch(e){console.log("Error fetching templates:",e),v(d,"Failed to load templates",5e3,"error")}finally{j(!1)}},R=e=>{var a;if(y(e),r("assistant_name",e.assistant_name||""),r("script",e.script||""),r("voicemail_script",e.voicemail_script||""),r("first_message",e.first_message||""),r("notes",e.notes||""),e.assistant_settings)try{const g=((a=JSON.parse(e.assistant_settings).llm_settings)==null?void 0:a.system_prompt)||"";console.log("systemPromptText",g),x(g)}catch(t){console.log("Error parsing assistant_settings:",t),x("")}else x("");e.voice_id&&(p.find(g=>g.voice_id===e.voice_id)?r("voice_id",e.voice_id):console.log("Template voice_id not found in user's voices:",e.voice_id))},b=()=>{y(null),x(""),r("assistant_name",""),r("script",""),r("voicemail_script",""),r("first_message",""),r("notes",""),r("voice_id","")};return o.useEffect(()=>{d({type:"SETPATH",payload:{path:"assistants"}}),async function(){u.setTable("voice_list");const a=await u.callRestAPI({user_id:_.user,filter:[`user_id,eq,${_.user}`]},"GETALL");a.error||(console.log("voices",a),P(a.list?a.list.map(t=>({voice_id:t.voice_id,name:t.name})):[]))}(),M()},[]),s.jsxs("div",{className:"mx-auto  bg-[#1d2937] px-4 py-5",children:[s.jsxs("div",{className:"flex flex-row justify-between",children:[s.jsx("h4",{className:"text-3xl font-semibold text-white",children:"Add Voice Assistant"}),s.jsx("button",{onClick:h,children:s.jsx(J,{className:"text-2xl text-white"})})]}),s.jsxs("div",{className:"mb-6 mt-6",children:[s.jsx("h5",{className:"mb-4 text-xl font-semibold text-white",children:"Choose Starting Point"}),s.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[s.jsxs("div",{className:`cursor-pointer rounded-lg border-2 p-4 transition-all ${i===null?"border-[#19b2f6] bg-[#19b2f6]/10":"border-gray-600 bg-[#1d2937] hover:border-gray-500"}`,onClick:b,children:[s.jsx("h6",{className:"mb-2 text-lg font-medium text-white",children:"Create from Scratch"}),s.jsx("p",{className:"text-sm text-gray-300",children:"Start with a blank assistant and customize everything yourself"})]}),f.length>0&&s.jsxs("div",{className:"rounded-lg border-2 border-gray-600 bg-[#1d2937] p-4",children:[s.jsx("h6",{className:"mb-2 text-lg font-medium text-white",children:"Use Template"}),E?s.jsx("p",{className:"text-sm text-gray-300",children:"Loading templates..."}):s.jsxs("select",{className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white",onChange:e=>{const a=f.find(t=>t.id===parseInt(e.target.value));a?R(a):b()},value:(i==null?void 0:i.id)||"",children:[s.jsx("option",{value:"",children:"Select a template"}),f.map(e=>s.jsxs("option",{value:e.id,children:[e.assistant_name," (",e.language||"en",") -"," ",e.voice_id||"No voice"]},e.id))]})]})]}),i&&s.jsx("div",{className:"mt-4 rounded-lg border border-[#19b2f6]/30 bg-[#19b2f6]/10 p-4",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("h6",{className:"mb-2 text-lg font-medium text-white",children:["Selected Template: ",i.assistant_name]}),s.jsxs("div",{className:"mb-2 text-sm text-gray-300",children:[s.jsxs("p",{children:[s.jsx("strong",{children:"Language:"})," ",i.language||"Not specified"]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Voice ID:"})," ",i.voice_id||"Not specified"]}),i.voice_id&&!p.find(e=>e.voice_id===i.voice_id)&&s.jsxs("p",{className:"mt-1 text-yellow-400",children:['⚠️ This template uses voice "',i.voice_id,`" which is not available in your account. You'll need to select a different voice.`]})]}),s.jsxs("p",{className:"mb-2 text-sm text-gray-300",children:[s.jsx("strong",{children:"Description:"})," ",(S=i.script)==null?void 0:S.substring(0,150),"..."]})]}),s.jsx("button",{onClick:b,className:"ml-4 text-sm text-gray-400 hover:text-white",children:"Clear Template"})]})})]}),s.jsxs("form",{className:"mt-5 w-full overflow-y-auto pb-10",onSubmit:I(F),children:[s.jsx(m,{type:"text",page:"edit",name:"assistant_name",errors:n,label:"Assistant Name",placeholder:"name",register:l,className:"bg-transparent text-white placeholder:text-gray-300"}),s.jsx(m,{type:"mapping",page:"add",name:"voice_id",errors:n,label:"Voice",placeholder:"voice_id",options:p.map(e=>e.voice_id),mapping:p.reduce((e,a)=>(e[a.voice_id]=a.name,e),{}),register:l,className:"bg-[#1d2937]  text-white placeholder:text-gray-300"}),s.jsx(m,{type:"text",page:"edit",name:"first_message",errors:n,label:"First Message",placeholder:"First Message",register:l,className:"bg-transparent text-white placeholder:text-gray-300"}),s.jsx(m,{type:"textarea",page:"edit",name:"script",errors:n,label:"script",placeholder:"script",register:l,className:"flex-grow bg-transparent text-white placeholder:text-gray-300",containerClassName:"flex flex-col ",rows:10}),s.jsx(m,{type:"textarea",page:"edit",name:"voicemail_script",errors:n,label:"Voicemail script",placeholder:"Voicemail script",register:l,className:"flex-grow bg-transparent text-white placeholder:text-gray-300",containerClassName:"flex flex-col flex-grow",rows:10}),s.jsx(m,{type:"textarea",page:"edit",name:"notes",errors:n,label:"Notes",placeholder:"Notes",register:l,className:"bg-transparent text-white placeholder:text-gray-300",rows:5}),N&&s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-medium text-white",children:"System Prompt (Template)"}),s.jsx("textarea",{value:N,readOnly:!0,className:"w-full resize-none rounded border border-gray-600 bg-[#1d2937] p-3 text-gray-300",rows:8,style:{fontFamily:"monospace",fontSize:"12px"}})]}),s.jsx(H,{type:"submit",loading:w,disabled:w,className:"focus:shadow-outline mt-6 w-full rounded bg-[#19b2f6]/80  px-4 py-2 font-semibold text-white focus:outline-none",children:"Submit"})]})]})};export{ve as default};
