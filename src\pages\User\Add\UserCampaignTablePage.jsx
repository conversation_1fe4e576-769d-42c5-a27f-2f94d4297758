import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { SkeletonLoader } from "Components/Skeleton";
import { AuthContext } from "Context/Auth";
let sdk = new MkdSDK();
const UserCampaignTablePage = ({ inbound, setSidebar }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const schema = yup
    .object({
      name: yup.string(),
      script_id: yup.string(),
      // status: yup.string(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [scripts, setScripts] = React.useState([]);
  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [pageDetails, setPageDetails] = React.useState([]);

  const getPageDetails = async () => {
    const result = await new TreeSDK()
      .getList("table")
      .catch((e) => console.error(object));
    setPageDetails(result.list);
  };

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    setIsSubmitLoading(true);
    try {
      sdk.setTable("campaign");
      const result = await sdk.callRestAPI(
        {
          name: _data.name,
          campaign_type: inbound ? 1 : 0,
          script_id: _data.script_id,
          user_id: state.user,
        },
        "POST"
      );
      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate(
          inbound ? "/user/inbound_campaigns" : "/user/outbound_campaigns"
        );
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: inbound ? "inbound_campaigns" : "outbound_campaigns",
      },
    });

    (async function getScripts() {
      sdk.setTable("scripts");
      console.log("state", state.user);
      const result = await sdk.callRestAPI(
        { user_id: state.user, filter: [`user_id,eq,${state.user}`] },
        "GETALL"
      );
      if (!result.error) {
        console.log("scripts", result);
        setScripts(
          result.list
            ? result.list.map((script) => {
                return {
                  script_id: script.id,
                  script: script.script,
                };
              })
            : []
        );
      }
    })();
  }, []);

  return (
    <div className="mx-auto rounded bg-[#1d2937] p-5 shadow-md">
      <h4 className="mb-6 text-2xl font-medium text-white">
        Add New {inbound ? "Inbound" : "Outbound"} Campaign
      </h4>
      <form className="w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <MkdInput
          type={"text"}
          page={"add"}
          name={"name"}
          errors={errors}
          label={"Campaign Name"}
          placeholder={"Name of the campaign"}
          register={register}
          className={"border-gray-700 bg-[#1d2937] text-white"}
          labelClassName="text-white"
        />

        <MkdInput
          type={"mapping"}
          page={"add"}
          name={"script_id"}
          errors={errors}
          label={"Script"}
          placeholder={"Select a script"}
          options={scripts.map((sc) => sc.script_id)}
          mapping={scripts.reduce((g, sc) => {
            g[sc.script_id] = sc.script;
            return g;
          }, {})}
          register={register}
          className={"border-gray-700 bg-[#1d2937] text-white"}
          labelClassName="text-white"
        />
        {/* <MkdInput
          type={'text'}
          page={'add'}
          name={'status'}
          errors={errors}
          label={'Status'}
          placeholder={'Status'}
          register={register}
          className={''}
        /> */}

        <InteractiveButton
          type="submit"
          loading={isSubmitLoading}
          disabled={isSubmitLoading}
          className="focus:shadow-outline rounded bg-[#19b2f6]/80  px-4 py-2 font-bold text-white hover:bg-[#0c99db] focus:outline-none"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default UserCampaignTablePage;
