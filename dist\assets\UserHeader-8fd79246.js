import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as a,R as p,f as T,L as A,l as R}from"./vendor-2ae44a2e.js";import{c as M}from"./index.esm-4b383179.js";import{M as _,A as j,t as C,G as F,s as y}from"./index-d0a8f5da.js";import{d as w,e as N,f as O,g as P,h as g,i as B}from"./index.esm-42944128.js";import{b as L,c as I,d as k}from"./index.esm-de9a80b6.js";import{a as u,q as f,_ as b}from"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";function $({title:o,titleId:r,...l},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":r},l),o?a.createElement("title",{id:r},o):null,a.createElement("path",{fillRule:"evenodd",d:"M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z",clipRule:"evenodd"}))}const U=a.forwardRef($),V=U,D=(o=!1)=>{const r=new _,{state:{profile:l},dispatch:n}=p.useContext(j),[x,i]=p.useState(null),d=p.useCallback(()=>{(async()=>{try{const s=await r.getProfile();console.log(s),s!=null&&s.error||(i(()=>s),n({type:"UPDATE_PROFILE",payload:s}))}catch(s){console.log(s.message),C(n,s.message)}})()},[x]);return p.useEffect(()=>{!l||o?d():i(l)},[l]),[x,i]};let v=new _;const Z=[{to:"/user/voice",text:"Voice Assistant Test",icon:e.jsx(w,{className:"text-xl text-[#A8A8A8]"}),value:"voice"},{to:"/user/sms",text:"SMS Assistant Test",icon:e.jsx(N,{className:"text-xl text-[#A8A8A8]"}),value:"sms"},{to:"/user/voice_list",text:"Voices",icon:e.jsx(w,{className:"text-xl text-[#A8A8A8]"}),value:"voice_list"},{to:"/user/assistants",text:"Voice Assistants",icon:e.jsx(O,{className:"text-xl text-[#A8A8A8]"}),value:"assistants"},{to:"/user/numbers",text:"Phone #",icon:e.jsx(P,{className:"text-xl text-[#A8A8A8]"}),value:"numbers"},{to:"/user/outbound_campaigns",text:"OutBound Campaigns",icon:e.jsx(g,{className:"text-xl text-[#A8A8A8]"}),value:"outbound_campaigns"},{to:"/user/inbound_campaigns",text:"Inbound Campaigns",icon:e.jsx(g,{className:"text-xl text-[#A8A8A8]"}),value:"inbound_campaigns"},{to:"/user/sms_outbound_campaigns",text:"SMS OutBound Campaigns",icon:e.jsx(g,{className:"text-xl text-[#A8A8A8]"}),value:"sms_outbound_campaigns"},{to:"/user/sms_inbound_campaigns",text:"SMS Inbound Campaigns",icon:e.jsx(g,{className:"text-xl text-[#A8A8A8]"}),value:"sms_inbound_campaigns"},{to:"/user/outbound_call_logs",text:"Outbound Call Logs",icon:e.jsx(L,{className:"text-xl text-[#A8A8A8]"}),value:"outbound_call_logs"},{to:"/user/inbound_call_logs",text:"Inbound Call Logs",icon:e.jsx(I,{className:"text-xl text-[#A8A8A8]"}),value:"inbound_call_logs"},{to:"/user/test_logs",text:"Sample Voice Call Log",icon:e.jsx(k,{className:"text-xl text-[#A8A8A8]"}),value:"test_call_logs"},{to:"/user/test_sms_logs",text:"Sample SMS Followup Logs",icon:e.jsx(N,{className:"text-xl text-[#A8A8A8]"}),value:"test_sms_logs"},{to:"/user/stripe_subscription",text:"Billing",icon:e.jsx(B,{className:"text-xl text-[#A8A8A8]"}),value:"subscription"},{to:"/user/profile",text:"Profile",icon:e.jsx(M,{className:"text-xl text-[#A8A8A8]"}),value:"profile"}],q=({isOpen:o,onClose:r,onSubmit:l})=>{const[n,x]=a.useState(""),[i,d]=a.useState(""),s=()=>{if(n.trim()===""){d("Query cannot be empty");return}l(n),x(""),d(""),r()};return e.jsx(f,{appear:!0,show:o,as:a.Fragment,children:e.jsxs(b,{as:"div",className:"relative z-10",onClose:r,children:[e.jsx(f.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-transparent bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(b.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[e.jsx(b.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Report a Bug"}),e.jsxs("div",{className:"mt-2",children:[e.jsx("input",{type:"text",value:n,onChange:h=>x(h.target.value),className:"w-full rounded-md border border-gray-300 p-2",placeholder:"Enter your query"}),i&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:i})]}),e.jsxs("div",{className:"mt-4 flex justify-end space-x-2",children:[e.jsx("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700",onClick:r,children:"Close"}),e.jsx("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-[#2cc9d5]/70",onClick:s,children:"Submit"})]})]})})})})]})})},me=()=>{const{state:{isOpen:o,path:r},dispatch:l}=a.useContext(F),{state:n}=p.useContext(j),{state:x,dispatch:i}=a.useContext(j),[d,s]=a.useState(!1),h=T(),[m]=D();let S=t=>{l({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};a.useEffect(()=>{async function t(){try{const c=await v.getProfile();i({type:"UPDATE_PROFILE",payload:c})}catch(c){console.log("Error",c),C(i,c.response.data.message?c.response.data.message:c.message)}}t()},[]);const E=async t=>{console.log("Bug report submitted:",t);try{v.setTable("reports"),await v.callRestAPI({query:t,user_id:n.user},"POST"),s(!1),y(l,"Report submitted")}catch{y(l,"Unable to submit bug report")}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${o?"fixed h-screen w-[18rem] min-w-[18rem] max-w-[18rem] md:relative":"relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-transparent text-white"} `,children:[e.jsxs("div",{className:`text-[#393939] ${o?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),o&&e.jsx("div",{className:"text-2xl font-bold",children:e.jsx(A,{to:"/",children:e.jsx("h4",{className:"flex cursor-pointer items-center px-4 pb-4 font-sans font-bold",children:"AutomateIntel - Voice"})})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:Z.map(t=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(R,{to:t.to,className:`${r==t.value?"active-nav":""} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,o&&e.jsx("span",{children:t.text})]})})},t.value))})})}),e.jsxs("div",{className:"flex justify-between pl-2",children:[e.jsxs(u,{as:"div",className:"relative inline-block text-left",children:[e.jsx("div",{children:e.jsxs(u.Button,{className:"inline-flex w-full items-center justify-center gap-4 rounded-sm border border-gray-200 bg-gray-50 px-4 py-2 text-base font-medium text-transparent hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[e.jsx(V,{className:"h-5 w-5"}),m==null?void 0:m.first_name," ",m==null?void 0:m.last_name]})}),e.jsx(f,{as:a.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsx(u.Items,{className:"absolute bottom-0 left-full mt-2 w-56 origin-bottom-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-transparent/5 focus:outline-none",children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsx(u.Item,{children:({active:t})=>e.jsx(A,{className:`${t?"bg-gray-500 text-white":"text-gray-900"} group flex w-full items-center rounded-md px-3 py-3`,to:"/user/profile",children:"Account"})}),e.jsx(u.Item,{children:({active:t})=>e.jsx("button",{className:`${t?"bg-gray-500 text-white":"text-gray-900"} group flex w-full items-center rounded-md px-3 py-3`,onClick:()=>{i({type:"LOGOUT"}),h("/user/login")},children:"Log out"})}),e.jsx(u.Item,{children:({active:t})=>e.jsx("button",{className:`${t?"bg-gray-500 text-white":"text-gray-900"} group flex w-full items-center rounded-md px-3 py-3`,onClick:()=>s(!0),children:"Report bug"})})]})})})]}),e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>S(!o),children:e.jsx("svg",{className:`transition-transform ${o?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})]})]}),e.jsx(q,{isOpen:d,onClose:()=>s(!1),onSubmit:E})]})};export{me as UserHeader,me as default};
