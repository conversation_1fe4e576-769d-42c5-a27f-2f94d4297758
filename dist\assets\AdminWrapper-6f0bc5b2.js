import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as l}from"./vendor-2ae44a2e.js";import{_ as s}from"./qr-scanner-cf010ec4.js";import{S as i}from"./index-f2c2b086.js";const o=l.lazy(()=>s(()=>import("./AdminHeader-05b9d280.js"),["assets/AdminHeader-05b9d280.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index.esm-4b383179.js","assets/react-icons-f29df01f.js","assets/index-d0a8f5da.js","assets/react-confirm-alert-783bc3ae.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-7bce1936.js","assets/lucide-react-f66dbccf.js","assets/react-loading-skeleton-f53ed7d1.js","assets/react-papaparse-b60a38ab.js","assets/papaparse-2d1475f9.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-9cc92aaf.js","assets/@fortawesome/react-fontawesome-27c5bed3.js","assets/@fortawesome/fontawesome-svg-core-294d29ff.js","assets/@fortawesome/free-solid-svg-icons-11dbc67c.js","assets/@fortawesome/free-regular-svg-icons-3e88f209.js","assets/@fortawesome/free-brands-svg-icons-2414b431.js","assets/index-f3f42218.css"])),a=({children:r})=>e.jsxs("div",{className:"flex flex-col w-full max-w-full min-h-screen bg-white",children:[e.jsx(o,{}),e.jsx("div",{className:"mt-[92px] flex min-h-screen w-full max-w-full",children:e.jsx("div",{className:"overflow-hidden mb-20 w-full",children:e.jsx(l.Suspense,{fallback:e.jsx("div",{className:"flex justify-center items-center w-full h-screen",children:e.jsx(i,{size:100,color:"#2CC9D5"})}),children:e.jsx("div",{className:"overflow-y-auto overflow-x-hidden px-6 w-full",children:r})})})})]}),d=l.memo(a);export{d as default};
