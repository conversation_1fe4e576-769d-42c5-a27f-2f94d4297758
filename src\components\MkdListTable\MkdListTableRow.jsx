import React from "react";
function formatDate(dateString) {
  const date = new Date(dateString);

  const options = { year: "numeric", month: "long", day: "numeric" };
  return date.toLocaleDateString("en-US", options);
}

function formatPhoneNumber(phoneNumber) {
  const cleaned = phoneNumber.toString().replace(/\D/g, ""); // Remove any non-digit characters
  const match = cleaned.match(/^(\d{3})(\d{4})(\d{4})$/); // Capture groups for formatting

  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }

  return null; // Return null if the phone number format is invalid
}

const MkdListTableRow = ({
  i,
  row,
  columns,
  actions,
  actionPosition,
  actionId = "id",
  handleTableCellChange,
  selectedIds = [],
  handleSelectRow,
  setDeleteId,
}) => {
  return (
    <>
      <tr>
        {columns.map((cell, index) => {
          if (cell.format) {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
                dangerouslySetInnerHTML={{
                  __html: cell.format(row[cell.accessor]),
                }}
              ></td>
            );
          }
          if (cell.accessor.indexOf("image") > -1) {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
              >
                <img
                  src={row[cell.accessor]}
                  className="h-[3.rem] w-[9.375rem]"
                  alt=""
                />
              </td>
            );
          }
          if (
            cell.accessor.indexOf("pdf") > -1 ||
            cell.accessor.indexOf("doc") > -1 ||
            // cell.accessor.indexOf('file') > -1 ||
            cell.accessor.indexOf("video") > -1
          ) {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
              >
                <a
                  className="text-white"
                  target="_blank"
                  href={row[cell.accessor]}
                  rel="noreferrer"
                >
                  {" "}
                  View
                </a>
              </td>
            );
          }
          if (cell.accessor === "create_at") {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
              >
                {formatDate(row[cell.accessor])}
              </td>
            );
          }
          if (cell.accessor === "number") {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
              >
                {formatPhoneNumber(row[cell.accessor])}
              </td>
            );
          }
          if (cell.accessor === "") {
            if (
              [
                actions?.select?.show,
                actions?.view?.show,
                actions?.edit?.show,
                actions?.delete?.show,
              ].includes(true)
            ) {
              return (
                <td
                  key={index}
                  className="flex !w-full whitespace-nowrap px-6 py-6 text-left text-white"
                >
                  <div className="items-center space-x-3 text-sm">
                    {actions?.select?.show && (
                      <span>
                        <input
                          className="mr-1"
                          type="checkbox"
                          name="select_item"
                          checked={selectedIds.includes(row[actionId])}
                          onChange={() => handleSelectRow(row[actionId])}
                        />
                      </span>
                    )}

                    {actionPosition === "onTable" && (
                      <>
                        {actions?.edit?.show && (
                          <button
                            title="Edit Entry"
                            className="rounded-[30px] bg-green-400/20 p-1.5 px-5 font-medium text-white "
                            onClick={() => {
                              if (actions?.edit?.action) {
                                actions.edit.action([row[actionId]]);
                              }
                              // navigate(
                              //  `/${ tableRole }/edit-${table}/` +
                              //     row[actionId],
                              //   {
                              //     state: row,
                              //   }
                              // );
                            }}
                          >
                            {/* <PencilIcon
                                          className={`text-white cursor-pointer h-[1rem] w-[1rem] group-hover:`}
                                          stroke={"#29282990"}
                                        /> */}
                            <span>Edit</span>
                          </button>
                        )}
                        {actions?.view?.show && (
                          <button
                            title="View Entry"
                            className="rounded-[30px] bg-blue-500/20 p-1.5 px-5 font-medium text-white "
                            onClick={() => {
                              if (actions?.view?.action) {
                                actions.view.action([row[actionId]]);
                              }
                              // navigate(
                              //   `/${tableRole}/view-${table}/` +
                              //     row[actionId],
                              //   {
                              //     state: row,
                              //   }
                              // );
                            }}
                          >
                            {/* <EyeIcon
                                          className={`text-white cursor-pointer h-[1rem] w-[1rem] group-hover:`}
                                        /> */}
                            <span>View</span>
                          </button>
                        )}
                        {actions?.delete?.show && (
                          <button
                            title="Delete Entry"
                            className="rounded-[30px] bg-red-500/20 p-1.5 px-5 font-medium text-white "
                            onClick={() => {
                              if (
                                window.confirm(
                                  "Are you sure you want to delete this entry?"
                                )
                              ) {
                                // if (actions?.delete?.action) {
                                // actions.delete.action([row[actionId]]);
                                setDeleteId(row[actionId]);
                                // }
                              }
                            }}
                          >
                            {/* <TrashIcon
                                          className={`text-white cursor-pointer h-[1rem] w-[1rem] group-hover:`}
                                        /> */}
                            <span>Delete</span>
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </td>
              );
            } else {
              return null;
            }
          }
          if (cell.mappingExist) {
            return (
              <td
                key={index}
                className="px-6 py-4 text-left text-white whitespace-nowrap"
              >
                {/* <select
                  onChange={(e) =>
                    handleTableCellChange(
                      row[actionId],
                      e.target.value,
                      i,
                      cell.accessor,
                    )
                  }
                >
                  {Object.keys(cell.mappings).map((cellDataKey, index) => (
                    <option
                      key={index}
                      value={cellDataKey}
                      selected={cellDataKey === row[cell.accessor]}
                    >
                      {cell.mappings[cellDataKey]}
                    </option>
                  ))}
                </select> */}
                {cell.mappings[row[cell.accessor]]}
              </td>
            );
          }
          if (
            !cell.mappingExist &&
            cell.accessor !== "id" &&
            cell.accessor !== "create_at" &&
            cell.accessor !== "update_at" &&
            cell.accessor !== "user_id"
          ) {
            return (
              <td
                title={row[cell.accessor].length > 40 ? row[cell.accessor] : ""}
                key={index}
                className="max-w-[200px] overflow-x-hidden truncate whitespace-nowrap px-6 py-4 text-white"
              >
                {cell.editable ? (
                  <input
                    placeholder={cell.accessor}
                    className="bg-transparent text-ellipsis"
                    type="text"
                    value={row[cell.accessor]}
                    onChange={(e) =>
                      handleTableCellChange(
                        row[actionId],
                        e.target.value,
                        i,
                        cell.accessor
                      )
                    }
                  />
                ) : (
                  row[cell.accessor]
                )}
              </td>
            );
          }
          return (
            <td
              key={index}
              title={row[cell.accessor].length > 40 ? row[cell.accessor] : ""}
              className="max-w-[200px] overflow-x-hidden truncate whitespace-nowrap px-6 py-4 text-white"
            >
              {row[cell.accessor] ?? "N/A"}
            </td>
          );
        })}
      </tr>
    </>
  );
};

export default MkdListTableRow;
