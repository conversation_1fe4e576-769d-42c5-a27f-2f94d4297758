import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as a,R as J}from"./vendor-2ae44a2e.js";import{u as Q}from"./react-hook-form-47c010f8.js";import{M as C,A as X,G as Y,s as v,t as Z}from"./index-d0a8f5da.js";import{o as ee}from"./yup-5abd4662.js";import{c as se,a as p}from"./yup-c2e87575.js";import{b as te}from"./websocket-791f88c1.js";import{_ as ae}from"./MoonLoader-62b0139a.js";import{M as R,S as re}from"./SpeakerWaveIcon-4df22e9a.js";import{$ as n}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";const o={INACTIVE:"inactive",IN_PROGRESS:"recording"},A="audio/webm";let m=new C;const Te=()=>{var N,S,y,_;const{state:h,dispatch:E}=a.useContext(X),{dispatch:u}=a.useContext(Y),[b,ne]=a.useState(!1),i=a.useRef(null),[x,f]=a.useState(o.INACTIVE),[P,oe]=a.useState(null),[I,k]=a.useState("+1");a.useState([]);const[T,$]=a.useState([]),[G,V]=a.useState([]),[g,L]=a.useState(null),[l,w]=a.useState(null),M=se({assistant_id:p().required("Please select an assistant"),number_id:p().required("Please select the inbound number"),to_number:p().required("Please enter the destination number")}),{register:c,handleSubmit:O,setError:U,setValue:ie,formState:{errors:d,isSubmitting:q,isValid:W},reset:F}=Q({mode:"onChange",resolver:ee(M),defaultValues:{assistant_id:"",number_id:"",to_number:""}}),D=async s=>{let t=new C;try{if(t.setTable("numbers"),!s.number_id||!s.assistant_id)throw new Error("Missing values");await t.callRawAPI("/v3/api/custom/voiceoutreach/user/test_sms",{from_number:s.number_id,assistant_id:s.assistant_id,to_number:I+s.to_number},"POST"),v(u,"Successful"),F()}catch(r){Z(E,r.message),v(u,r.message,1e4,"error"),U("assistant_id",{type:"manual",message:r.message})}},z=async()=>{f(o.IN_PROGRESS);const s=new MediaRecorder(P,{mimeType:A});i.current=s,i.current.start(),i.current.ondataavailable=t=>{typeof t.data>"u"||t.data.size!==0&&l&&l.send(t.data)}},B=()=>{f(o.INACTIVE),i.current.stop()},K=()=>{const s=new te.w3cwebsocket("wss://callagentds.manaknightdigital.com/1");s.onopen=()=>{},s.onmessage=t=>{const r=new Blob([t.data],{type:A}),H=URL.createObjectURL(r);L(H)},s.onclose=()=>{},s.onerror=t=>{console.error("WebSocket error:",t)},w(s)},j=()=>{l&&(l.close(),w(null))};return J.useEffect(()=>(u({type:"SETPATH",payload:{path:"sms"}}),async function(){m.setTable("numbers");const t=await m.callRestAPI({filter:["sms,eq,1"]},"GETALL");t.error||V(t.list?t.list.map(r=>({number_id:r.id,name:r.number})):[])}(),async function(){m.setTable("assistants");const t=await m.callRestAPI({user_id:h.user,filter:[`user_id,eq,${h.user}`]},"GETALL");t.error||$(t.list?t.list.map(r=>({assistant_id:r.id,name:r.assistant_name})):[])}(),()=>{j()}),[]),e.jsx("div",{className:"mx-auto flex w-full justify-center rounded-md bg-[#1d2937] px-5 py-10 md:max-w-2xl md:px-0 lg:max-w-2xl 2xl:max-w-2xl",children:e.jsxs(n.Group,{as:"div",className:"w-full max-w-xl rounded-xl bg-[#1d2937] px-0 py-0 text-[#ffffff]",children:[e.jsx(n.List,{className:"flex justify-center items-center",children:e.jsx(n,{className:({selected:s})=>` rounded-lg px-3 py-1 text-2xl ${s?" text-white":"text-white"} `,children:"Send an SMS"})}),e.jsxs(n.Panels,{children:[e.jsxs(n.Panel,{as:"form",className:"max-w-xl",onSubmit:O(D),children:[e.jsxs("div",{className:"flex flex-col justify-center items-center mt-5 max-w-xl",children:[e.jsx("audio",{className:"h-[fit-content] w-[100%]",loop:!1,src:"https://via.placeholder.com/50?text=%20",controls:!1,muted:!1,autoPlay:!1}),b?null:e.jsx(e.Fragment,{}),b&&x===o.INACTIVE?e.jsxs(e.Fragment,{children:[e.jsx(R,{onClick:()=>{z(),K()},className:"cursor-pointer h-[50px] w-[50px]"}),e.jsxs("div",{className:"flex items-center my-4",children:[e.jsx("span",{className:"block  h-[15px] w-[15px] rounded-full bg-[red]"}),e.jsx("span",{className:"block mx-2 font-bold",children:"Start recording"})]})]}):null,x===o.IN_PROGRESS?e.jsxs(e.Fragment,{children:[e.jsx(R,{onClick:()=>{B(),j()},className:"cursor-pointer h-[50px] w-[50px]"}),e.jsx("div",{className:"flex items-center my-4",children:e.jsx("span",{className:"block h-[15px] w-[15px] animate-pulse rounded-full bg-[red]"})})]}):null,g&&x===o.INACTIVE?e.jsx("div",{className:"audio-player",children:e.jsx("audio",{src:g,controls:!0})}):null]}),e.jsxs("div",{className:"",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Assistants"}),e.jsxs("select",{type:"dropdown",id:"assistant_id",...c("assistant_id"),className:`focus:shadow-outline h-[30px] w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px]  ${(N=d.assistant_id)!=null&&N.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Assistant"}),T.map((s,t)=>e.jsx("option",{value:s.assistant_id,children:s.name},t+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("h3",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Phone Number"}),e.jsxs("select",{type:"dropdown",id:"number_id",...c("number_id"),className:`focus:shadow-outline h-[30px] w-full appearance-none rounded border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px]  ${(S=d.number_id)!=null&&S.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Choose Phone Number"}),G.map((s,t)=>e.jsx("option",{value:s.number_id,children:s.name},t+1))]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-2 text-[13px] font-medium lg:text-[15px]",children:"Phone to Text"}),e.jsxs("div",{className:"flex mt-2",children:[e.jsxs("select",{id:"country_code",...c("country_code"),onChange:s=>k(s.target.value),className:`focus:shadow-outline h-[30px] appearance-none rounded-l border bg-[#1d2937] px-3 py-1 pr-[2rem] text-[12px] leading-tight text-white shadow focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px] ${(y=d.country_code)!=null&&y.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Country Code"}),e.jsx("option",{value:"+1",children:"+1 (USA)"}),e.jsx("option",{value:"+1",children:"+1 (CA)"}),e.jsx("option",{value:"+44",children:"+44 (UK)"}),e.jsx("option",{value:"+234",children:"+234 (Nigeria)"})]}),e.jsx("input",{type:"text",...c("to_number"),placeholder:"Enter Phone Number",className:`focus:shadow-outline h-[30px] w-full appearance-none rounded-r border bg-[#1d2937] px-3 py-2 text-[12px] leading-tight text-white shadow placeholder:text-gray-300 focus:outline-none lg:h-[38px] lg:text-base xl:h-[43px] ${(_=d.to_number)!=null&&_.message?"border-red-500":""}`})]})]}),e.jsx("div",{className:"flex justify-center items-center w-full",children:e.jsxs("button",{disabled:!W,className:"mt-4 flex h-[43px] w-full items-center justify-center gap-2 rounded-[3px] bg-[#19b2f6]/80 px-5 py-2 text-white shadow-md shadow-black/30 hover:bg-[#19b2f6]/90 disabled:cursor-not-allowed disabled:opacity-50",children:[q?e.jsx(ae,{color:"white",loading:!0,size:20}):null,"Send Sms"]})})]}),e.jsxs(n.Panel,{as:"div",className:"flex flex-col gap-4 justify-center items-center h-[435px]",children:[e.jsx(re,{className:"w-12 h-12"}),e.jsx("h3",{children:"Coming soon"})]})]})]})})};export{Te as default};
