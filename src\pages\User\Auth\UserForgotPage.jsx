import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { automateIcon } from "Assets/images";
import { bg } from "Assets/images";

const UserForgotPage = () => {
  const [submitLoading, setSubmitLoading] = useState(false);

  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { dispatch } = React.useContext(GlobalContext);

  const onSubmit = async (data) => {
    let sdk = new MkdSDK();
    try {
      setSubmitLoading(true);
      const result = await sdk.forgot(data.email, "user");

      if (!result.error) {
        showToast(dispatch, "Reset Code Sent", 4000, "success");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
      });
      tokenExpireError(
        dispatch,
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message
      );
    }
  };

  return (
    <div
      style={{
        display: "flex",
        height: "100vh",
        backgroundImage: `url(${bg})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      }}
      className=""
    >
      <div className="flex h-full w-full items-center justify-center bg-[rgb(255,255,255,0.01)] backdrop-blur-md">
        <div
          style={{
            flex: 1,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div
            style={{
              width: "600px",
              backgroundColor: "#1d2937",
              padding: "40px",
              borderRadius: "12px",
              boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
            }}
          >
            <img
              src={automateIcon}
              alt="Logo"
              style={{
                width: "50px",
                height: "50px",
                marginBottom: "15px",
                display: "block",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            />
            <h2
              style={{
                marginBottom: "20px",
                color: "#fff",
                textAlign: "center",
                fontSize: "1.5rem",
                fontWeight: "bold",
              }}
            >
              Forgot Password
            </h2>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div style={{ marginBottom: "15px" }}>
                <label
                  style={{
                    display: "block",
                    color: "white",
                    marginBottom: "15px",
                    fontWeight: "bold",
                  }}
                >
                  Email
                </label>
                <input
                  type="email"
                  className="h-[42px] bg-transparent"
                  {...register("email")}
                  style={{
                    width: "100%",
                    color: "white",
                    padding: "10px",
                    borderRadius: "5px",
                    border: "1px solid #ccc",
                    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                  }}
                />
                {errors.email && (
                  <p style={{ color: "red", marginTop: "5px" }}>
                    {errors.email.message}
                  </p>
                )}
              </div>

              <InteractiveButton
                type="submit"
                className={`hover: flex h-[42px] w-full items-center justify-center rounded-md bg-[#19b2f6]/80 py-2 tracking-wide text-white outline-none hover:bg-[#19b2f6]/60 focus:outline-none`}
                loading={submitLoading}
                disabled={submitLoading}
              >
                <span>Reset Password</span>
              </InteractiveButton>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginBottom: "15px",
                  marginTop: "15px",
                  textAlign: "center",
                }}
              >
                <Link
                  to="/user/login"
                  style={{ color: "white", textDecoration: "none" }}
                >
                  Back to Login
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserForgotPage;
