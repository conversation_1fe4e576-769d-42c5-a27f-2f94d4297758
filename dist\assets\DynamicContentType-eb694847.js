import{j as a}from"./@react-google-maps/api-c55ecefa.js";import{R as h}from"./vendor-2ae44a2e.js";import{M as v,z as f}from"./index-d0a8f5da.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const x=new v,b="https://via.placeholder.com/150?text=%20",B=({contentType:c,contentValue:o,setContentValue:d})=>{const[u,i]=h.useState(b),y=async m=>{const p=new FormData;p.append("file",m.target.files[0]);try{const e=await x.uploadImage(p);i(e.url),d(e.url)}catch(e){console.error(e)}};switch(c){case"text":return a.jsx(a.Fragment,{children:a.jsx("textarea",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",rows:15,placeholder:"Content",onChange:m=>d(m.target.value),defaultValue:o})});case"image":return a.jsxs(a.Fragment,{children:[a.jsx("img",{src:f(o)?u:o,alt:"preview",height:150,width:150}),a.jsx("input",{type:"file",onChange:y,className:"focus:shadow-outline mb-3 block w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]});case"number":return a.jsx("input",{type:"number",className:"focus:shadow-outline mb-3 block w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",onChange:m=>d(m.target.value),defaultValue:o});case"team-list":return a.jsx(N,{setContentValue:d,contentValue:o});case"image-list":return a.jsx(k,{setContentValue:d,contentValue:o});case"captioned-image-list":return a.jsx(j,{setContentValue:d,contentValue:o});case"kvp":return a.jsx(I,{setContentValue:d,contentValue:o})}},k=({contentValue:c,setContentValue:o})=>{let d=[{key:"",value_type:"image",value:null}];f(c)||(d=JSON.parse(c));const[u,i]=h.useState(d),y=async p=>{const e=p.target.getAttribute("listkey"),t=new FormData;t.append("file",p.target.files[0]);try{const s=await x.uploadImage(t);i(n=>n.map((l,g)=>(g==e&&(l.value=s.url),l))),o(JSON.stringify(u))}catch(s){console.error(s)}},m=p=>{const e=p.target.getAttribute("listkey");i(t=>t.map((n,r)=>(r==e&&(n.key=p.target.value),n))),o(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((p,e)=>a.jsxs("div",{children:[a.jsx("img",{src:p.value!==null?p.value:b,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"key",listkey:e,onChange:m,defaultValue:p.key}),a.jsx("input",{listkey:e,type:"file",accept:"image/*",onChange:y,className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]})]},e*.23)),a.jsx("button",{type:"button",className:"focus:shadow-outline my-4 rounded bg-blue-400 px-2 py-1 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",onClick:p=>i(e=>[...e,{key:"",value_type:"image",value:null}]),children:"+"})]})},j=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"image",value:null,caption:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const n=await x.uploadImage(s);i(r=>r.map((g,w)=>(w==t&&(g.value=n.url),g))),c(JSON.stringify(u))}catch(n){console.error(n)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,l)=>(l==t&&(r.key=e.target.value),r))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,l)=>(l==t&&(r.caption=e.target.value),r))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((e,t)=>a.jsxs("div",{children:[a.jsx("img",{src:e.value!==null?e.value:b,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"focus:shadow-outline mb-3 mr-2 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"Key",listkey:t,onChange:m,defaultValue:e.key}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]}),a.jsx("input",{className:"focus:shadow-outline mb-3 block w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"Caption",listkey:t,onChange:p,defaultValue:e.caption})]},t*.23)),a.jsx("button",{type:"button",className:"focus:shadow-outline my-4 rounded bg-blue-400 px-2 py-1 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",onClick:e=>i(t=>[...t,{key:"",value_type:"image",value:null,caption:""}]),children:"+"})]})},N=({setContentValue:c,contentValue:o})=>{let d=[{name:"",image:null,title:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const n=await x.uploadImage(s);i(r=>r.map((g,w)=>(w==t&&(g.image=n.url),g))),c(JSON.stringify(u))}catch(n){console.error(n)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,l)=>(l==t&&(r.name=e.target.value),r))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,l)=>(l==t&&(r.title=e.target.value),r))),c(JSON.stringify(u))};return a.jsxs("div",{className:"my-4 block",children:[u.map((e,t)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"focus:shadow-outline mb-3 block w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"Title",listkey:t,onChange:p,defaultValue:e.title}),a.jsx("input",{className:"focus:shadow-outline mb-3 mr-2 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"Name",listkey:t,onChange:m,defaultValue:e.name}),a.jsx("img",{src:e.image!==null?e.image:b,alt:"preview",height:150,width:150}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]},t*.23)),a.jsx("button",{type:"button",className:"focus:shadow-outline my-4 rounded bg-blue-400 px-2 py-1 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",onClick:e=>i(t=>[...t,{name:"",image:null,title:""}]),children:"+"})]})},I=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"text",value:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=[{key:"text",value:"Text"},{key:"number",value:"Number"},{key:"json",value:"JSON Object"},{key:"url",value:"URL"}],m=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((l,g)=>(g==s&&(l.key=t.target.value),l))),c(JSON.stringify(u))},p=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((l,g)=>(g==s&&(l.value=t.target.value),l))),c(JSON.stringify(u))},e=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((l,g)=>(g==s&&(l.value_type=t.target.value),l))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((t,s)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"focus:shadow-outline mb-3 mr-2 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",placeholder:"Key",listkey:s,onChange:m,defaultValue:t.key}),a.jsx("select",{className:"focus:shadow-outline mb-3 block w-full rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",listkey:s,onChange:e,defaultValue:t.value_type,children:y.map((n,r)=>a.jsx("option",{value:n.key,children:n.value},r*122))}),a.jsx("input",{className:"focus:shadow-outline mb-3 block w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",type:"text",required:!0,placeholder:"Value",listkey:s,onChange:p,defaultValue:t.value})]},s*.23)),a.jsx("button",{type:"button",className:"focus:shadow-outline my-4 rounded bg-blue-400 px-2 py-1 font-bold text-white hover:bg-[#2cc9d5]/70 focus:outline-none",onClick:t=>i(s=>[...s,{key:"",value_type:"text",value:""}]),children:"+"})]})};export{B as default};
