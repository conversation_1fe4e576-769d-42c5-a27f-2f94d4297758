import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{r as e}from"./vendor-2ae44a2e.js";import{_ as x}from"./MoonLoader-62b0139a.js";const j=e.memo(({loading:s=!1,disabled:r,children:o,type:f="button",className:a,loaderclasses:n,onClick:i,color:m="#ffffff"})=>{const c={borderColor:"#ffffff"},d=e.useId();return t.jsx("button",{type:f,disabled:r,className:`flex gap-5 justify-center items-center text-base ${a}`,onClick:i,children:t.jsxs(t.Fragment,{children:[t.jsx(x,{color:m,loading:s,cssOverride:c,size:20,className:n,"data-testid":d}),t.jsx("span",{children:o})]})})});export{j as I};
