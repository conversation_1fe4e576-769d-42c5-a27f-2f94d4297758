import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import MkdSDK from 'Utils/MkdSDK';
import { useNavigate } from 'react-router-dom';
import { tokenExpireError } from 'Context/Auth';
import { GlobalContext, showToast } from 'Context/Global';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { isImage, empty, isVideo, isPdf } from 'Utils/utils';
import { MkdInput } from 'Components/MkdInput';
import { InteractiveButton } from 'Components/InteractiveButton';
import { SkeletonLoader } from 'Components/Skeleton';

const AddKnowledgeBankPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      transcript: yup.string(),
      type: yup.string(),
      filename: yup.string(),
      status: yup.string(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const [fileObj, setFileObj] = React.useState({});
  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [pageDetails, setPageDetails] = React.useState([]);

  const getPageDetails = async () => {
    const result = await new TreeSDK()
      .getList('table')
      .catch((e) => console.error(object));
    setPageDetails(result.list);
  };

  const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
      file: target.files[0],
      tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
  };

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    setIsSubmitLoading(true);
    try {
      for (let item in fileObj) {
        let formData = new FormData();
        formData.append('file', fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
      }

      sdk.setTable('knowledge_bank');

      const result = await sdk.callRestAPI(
        {
          transcript: _data.transcript,
          type: _data.type,
          filename: _data.filename,
          status: _data.status,
        },
        'POST',
      );
      if (!result.error) {
        showToast(globalDispatch, 'Added');
        navigate('/user/knowledge_bank');
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: 'manual',
              message: result.validation[field],
            });
          }
        }
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log('Error', error);
      setError('transcript', {
        type: 'manual',
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: 'SETPATH',
      payload: {
        path: 'knowledge_bank',
      },
    });
  }, []);

  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <h4 className="text-2xl font-medium">Add Knowledge Bank</h4>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <MkdInput
          type={'text'}
          page={'add'}
          name={'transcript'}
          errors={errors}
          label={'Transcript'}
          placeholder={'Transcript'}
          register={register}
          className={''}
        />

        <MkdInput
          type={'text'}
          page={'add'}
          name={'type'}
          errors={errors}
          label={'type'}
          placeholder={'type'}
          register={register}
          className={''}
        />

        <MkdInput
          type={'text'}
          page={'add'}
          name={'filename'}
          errors={errors}
          label={'Filename'}
          placeholder={'Filename'}
          register={register}
          className={''}
        />

        <MkdInput
          type={'text'}
          page={'add'}
          name={'status'}
          errors={errors}
          label={'Status'}
          placeholder={'Status'}
          register={register}
          className={''}
        />

        <InteractiveButton
          type="submit"
          loading={isSubmitLoading}
          disabled={isSubmitLoading}
          className="focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default AddKnowledgeBankPage;
