import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { SkeletonLoader } from "Components/Skeleton";

const AddAdminStripeProductPage = ({ setSidebar, closeSidebar }) => {
  const schema = yup
    .object({
      name: yup.string().required(),
      description: yup.string().nullable(),
      // shippable: yup.boolean().nullable(),
    })
    .required();

  const { dispatch } = React.useContext(GlobalContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isUpdatingPrice, setIsUpdatingPrice] = useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // const selectShippable = [
  //   { key: "0", value: false },
  //   { key: "1", value: true },
  // ];
  const onSubmit = async (data) => {
    let sdk = new MkdSDK();

    setIsUpdatingPrice(true);
    try {
      const result = await sdk.addStripeProduct({
        name: data.name,
        description: data.description,
      });
      if (!result.error) {
        showToast(dispatch, "Added");
        setIsUpdatingPrice(false);
        if (closeSidebar) {
          closeSidebar();
        }
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            console.log(field);
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);

      showToast(dispatch, error.message);
      tokenExpireError(dispatch, error.message);
      setIsUpdatingPrice(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "products",
      },
    });
  }, []);
  return (
    <div className=" mx-auto rounded  p-5 shadow-md">
      <div
        className={`flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3`}
      >
        <div className="flex items-center gap-3">
          <svg
            onClick={() => setSidebar(false)}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              stroke-width="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-lg font-semibold">Add Product</span>
        </div>
        <div className="flex items-center gap-4">
          <button
            className="flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]"
            onClick={() => setSidebar(false)}
          >
            Cancel
          </button>
          <button
            className="flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm"
            onClick={async () => {
              await handleSubmit(onSubmit)();
              setSidebar(false);
            }}
            disabled={isUpdatingPrice}
          >
            {isUpdatingPrice ? "Saving" : "Save"}
          </button>
        </div>
      </div>
      <form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4 ">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="name"
          >
            Name
          </label>
          <input
            type="text"
            placeholder="Name"
            {...register("name")}
            className={`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">{errors.name?.message}</p>
        </div>
        <div className="mb-5">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="description"
          >
            Description
          </label>
          <input
            type="text"
            placeholder="Description"
            {...register("description")}
            className={`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.description?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">
            {errors.description?.message}
          </p>
        </div>

        {/* <div className="mb-5">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="interval">
              Shippable
            </label>
            <select
              className="shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              {...register("shippable")}
            >
              {selectShippable.map((option) => (
                <option value={option.value} key={`shippable_${option.key}`}>
                  {option.value}
                </option>
              ))}
            </select>
            <p className="text-red-500 text-xs italic">{errors.shippable?.message}</p>
          </div> */}
        {/*   
          <button type="submit" className="bg-[#2cc9d5] hover:bg-[#2cc9d5]/70 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
            Submit
          </button> */}
      </form>
    </div>
  );
};

export default AddAdminStripeProductPage;
