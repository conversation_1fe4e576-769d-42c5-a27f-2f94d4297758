import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as u,f as L,r as o}from"./vendor-2ae44a2e.js";import{u as M}from"./react-hook-form-47c010f8.js";import{o as q}from"./yup-5abd4662.js";import{c as F,a as k,b as R}from"./yup-c2e87575.js";import{M as T,A as G,G as H,t as E,s as S}from"./index-d0a8f5da.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";let C=new T;const ce=({activeId:l,setSidebar:n,closeSidebar:x})=>{var y,j,w,N;const A=F({name:k().required(),description:k().nullable(),status:R().required()}).required(),{dispatch:h}=u.useContext(G),{dispatch:c}=u.useContext(H);L();const[O,P]=o.useState(0);o.useState(""),o.useState(""),o.useState("");const[f,a]=o.useState(!1),{register:d,handleSubmit:g,setError:$,setValue:m,formState:{errors:r}}=M({resolver:q(A)}),D=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],b=async t=>{try{console.log(t),a(!0);const s=await C.updateStripeProduct(l,{name:t.name,description:t.description,status:t.status});if(!s.error)S(c,"Edited",4e3),a(!1),x&&x();else{if(s.validation){const i=Object.keys(s.validation);for(let p=0;p<i.length;p++){const v=i[p];$(v,{type:"manual",message:s.validation[v]})}}a(!1)}}catch(s){console.log("Error",s),S(c,s.message,4e3),E(h,s.message),a(!1)}};return u.useEffect(()=>{c({type:"SETPATH",payload:{path:"products"}}),async function(){try{const t=await C.getStripeProduct(l);if(!t.error){let s=t.model.object;try{s=JSON.parse(s)}catch(i){console.log("Error",i)}console.log(s),m("name",s.name),m("description",s.description),m("status",t.model.status),P(t.model.id)}}catch(t){console.log("Error",t),E(h,t.message)}}()},[l]),e.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>n(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Edit Product"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 text-black shadow-sm hover:bg-[#f4f4f4]",onClick:()=>n(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[black] px-3 py-2 text-white shadow-sm",onClick:async()=>{await g(b)(),n(!1)},disabled:f,children:f?"Saving":"Save"})]})]})," ",e.jsxs("form",{className:"w-full max-w-lg",onSubmit:g(b),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...d("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(y=r.name)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=r.name)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"description",children:"Description"}),e.jsx("input",{type:"text",placeholder:"Description",...d("description"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(w=r.description)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=r.description)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("status"),children:D.map(t=>e.jsx("option",{value:t.key,children:t.value},t.key))})]})]})]})};export{ce as default};
