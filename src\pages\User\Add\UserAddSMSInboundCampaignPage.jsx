import React, { Fragment, useMemo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { MkdInput } from "Components/MkdInput";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext } from "Context/Auth";
import {
  DocumentTextIcon,
  XMarkIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { Dialog, Transition } from "@headlessui/react";
import { useDropzone } from "react-dropzone";
import PizZip from "pizzip";
import { DOMParser } from "@xmldom/xmldom";
import pdfToText from "react-pdftotext";
import { ClipboardCheckIcon, ClipboardIcon } from "lucide-react";

let sdk = new MkdSDK();

const languages = {
  "English (USA)": "English (USA)",
  "English (UK)": "English (UK)",
  "English (Australia)": "English (Australia)",
  "English (Canada)": "English (Canada)",
  Japanese: "Japanese",
  Chinese: "Chinese",
  German: "German",
  Hindi: "Hindi",
  "French (France)": "French (France)",
  "French (Canada)": "French (Canada)",
  Korean: "Korean",
  "Portuguese (Brazil)": "Portuguese (Brazil)",
  "Portuguese (Portugal)": "Portuguese (Portugal)",
  Italian: "Italian",
  "Spanish (Spain)": "Spanish (Spain)",
  "Spanish (Mexico)": "Spanish (Mexico)",
  Indonesian: "Indonesian",
  Dutch: "Dutch",
  Turkish: "Turkish",
  Filipino: "Filipino",
  Polish: "Polish",
  Swedish: "Swedish",
  Bulgarian: "Bulgarian",
  Romanian: "Romanian",
  "Arabic (Saudi Arabia)": "Arabic (Saudi Arabia)",
  "Arabic (UAE)": "Arabic (UAE)",
  Czech: "Czech",
  Greek: "Greek",
  Finnish: "Finnish",
  Croatian: "Croatian",
  Malay: "Malay",
  Slovak: "Slovak",
  Danish: "Danish",
  Tamil: "Tamil",
  Ukrainian: "Ukrainian",
  Russian: "Russian",
};

const UserAddSMSInboundCampaignPage = ({ isOpen, closeSidebar }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const schema = yup
    .object({
      name: yup.string(),
      assistant_id: yup.string(),
      language: yup.string().required("Language is required"),
    })
    .required();

  const [assistants, setAssistants] = React.useState([]);
  const [documents, setDocuments] = React.useState(null);
  const [previewDocuments, setPreviewDocuments] = React.useState(false);
  const [numbers, setNumbers] = React.useState([]);
  const [knowledgeField, setKnowledgeField] = React.useState("");
  const [preview, showPreview] = React.useState(false);
  const [settings, setSettings] = React.useState([]);
  const [modelLimit, setModelLimit] = React.useState(2000);
  const limits = {
    claude: {
      totalTokens: 150000,
      usableTokensForPrompt: 112500,
      MAX_CONTENT_SIZE_LIMIT: 84375,
    },
    gpt_3: {
      totalTokens: 4096,
      usableTokensForPrompt: 3072,
      MAX_CONTENT_SIZE_LIMIT: 2304,
    },
    gpt_4: {
      totalTokens: 8192,
      usableTokensForPrompt: 6144,
      MAX_CONTENT_SIZE_LIMIT: 4608,
    },
    gpt_4_extended: {
      totalTokens: 32768,
      usableTokensForPrompt: 24576,
      MAX_CONTENT_SIZE_LIMIT: 18432,
    },
  };

  const DocumentPreview = () => {
    const totalSize =
      documents?.reduce((acc, doc) => acc + doc.wordCount, 0) || 0;
    const limitExceeded = totalSize > modelLimit;

    return (
      <div className="mt-4">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-xl font-bold text-white">Document Preview</h3>
          <p className="text-sm text-white/70">
            Max Words Allowed: {modelLimit.toLocaleString()} words
          </p>
        </div>

        {/* Summary Stats */}
        <div className="mb-4 grid grid-cols-3 gap-4">
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Total Files</div>
            <div className="text-lg font-semibold text-white">
              {documents?.length || 0}
            </div>
          </div>
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Total Words</div>
            <div className="text-lg font-semibold text-white">
              {totalSize.toLocaleString()}
            </div>
          </div>
          <div className="rounded-lg bg-[#2d3947] p-3">
            <div className="text-sm text-white/70">Status</div>
            <div
              className={`text-lg font-semibold ${
                limitExceeded ? "text-red-400" : "text-green-400"
              }`}
            >
              {limitExceeded ? "Limit Exceeded" : "Within Limit"}
            </div>
          </div>
        </div>

        {limitExceeded && (
          <div className="mb-4 rounded-lg border border-red-500/30 bg-red-500/20 p-4">
            <div className="mb-2 flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-red-400"></div>
              <h4 className="font-semibold text-red-400">
                Content Size Limit Exceeded
              </h4>
            </div>
            <p className="mb-1 text-sm text-red-300">
              Content size exceeds the limit of {modelLimit.toLocaleString()}{" "}
              words.
            </p>
            <p className="text-sm text-red-300">
              Please reduce the number of files or choose smaller documents.
            </p>
          </div>
        )}

        {/* Document List */}
        <div className="space-y-2">
          <h4 className="mb-3 text-lg font-semibold text-white">
            Uploaded Documents
          </h4>
          {documents?.length > 0 ? (
            <div className="space-y-2">
              {documents.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border border-white/10 bg-[#2d3947] p-3"
                >
                  <div className="flex items-center gap-3">
                    <DocumentTextIcon className="h-5 w-5 text-white/70" />
                    <div>
                      <div className="font-medium text-white">{doc.name}</div>
                      <div className="text-sm text-white/70">
                        {(doc.size / 1024).toFixed(2)} KB
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-white">
                      {doc.wordCount.toLocaleString()} words
                    </div>
                    <div className="text-sm text-white/70">
                      {((doc.wordCount / totalSize) * 100).toFixed(1)}% of total
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-white/70">
              <DocumentTextIcon className="mx-auto mb-2 h-12 w-12 text-white/50" />
              <p>No documents uploaded yet</p>
            </div>
          )}
        </div>

        {/* Usage Bar */}
        {totalSize > 0 && (
          <div className="mt-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm text-white/70">Usage</span>
              <span className="text-sm text-white/70">
                {totalSize.toLocaleString()} / {modelLimit.toLocaleString()}{" "}
                words
              </span>
            </div>
            <div className="h-2 w-full rounded-full bg-gray-700">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  limitExceeded
                    ? "bg-red-500"
                    : totalSize > modelLimit * 0.8
                    ? "bg-yellow-500"
                    : "bg-green-500"
                }`}
                style={{
                  width: `${Math.min((totalSize / modelLimit) * 100, 100)}%`,
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const DocumentUploader = ({ allow_preview }) => {
    const [copiedIndex, setCopiedIndex] = React.useState(null);

    const handleCopy = (text, index) => {
      navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    };

    const handleDocxFile = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = async (e) => {
          try {
            const c = e.target.result;
            const paragraphs = getParagraphs(c);
            const content = paragraphs;
            const wordCount = content.split(/\s+/).length;
            console.log(content, "from docx");
            resolve({ content, wordCount });
          } catch (error) {
            reject(error);
          }
        };

        reader.onerror = (err) => reject(err);

        reader.readAsArrayBuffer(file);
      });
    };

    const handleTxTFile = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = function () {
          try {
            const content = reader.result;
            const wordCount = content.split(/\s+/).length;
            console.log(content, "from docx");
            resolve({ content, wordCount });
          } catch (e) {
            reject(e);
          }
        };
        reader.readAsText(file);
      });
    };

    function str2xml(str) {
      if (str.charCodeAt(0) === 65279) {
        // BOM sequence
        str = str.substr(1);
      }
      return new DOMParser().parseFromString(str, "text/xml");
    }

    function getParagraphs(content) {
      const zip = new PizZip(content);
      const xml = str2xml(zip.files["word/document.xml"].asText());
      const paragraphsXml = xml.getElementsByTagName("w:p");
      const paragraphs = [];

      for (let i = 0, len = paragraphsXml.length; i < len; i++) {
        let fullText = "";
        const textsXml = paragraphsXml[i].getElementsByTagName("w:t");
        for (let j = 0, len2 = textsXml.length; j < len2; j++) {
          const textXml = textsXml[j];
          if (textXml.childNodes) {
            fullText += textXml.childNodes[0].nodeValue;
          }
        }
        if (fullText) {
          paragraphs.push(fullText);
        }
      }
      return paragraphs.join(" ");
    }

    const onDrop = useCallback(async (acceptedFiles) => {
      const updatedDocuments = [];
      let total_content = "";

      for (const file of acceptedFiles) {
        let wordCount = 0;
        let content = "";

        // Extract text content based on file type
        if (file.type === "application/pdf") {
          try {
            const text = await pdfToText(file);
            wordCount = text.split(/\s+/).length;
            content = text;
          } catch (error) {
            console.error("Error reading PDF file:", error);
          }
        } else if (
          file.type ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ) {
          const result = await handleDocxFile(file);
          content = result.content;
          wordCount = result.wordCount;
        } else if (file.type === "text/plain") {
          const result = await handleTxTFile(file);
          content = result.content;
          wordCount = result.wordCount;
          console.log("Word Count:", wordCount, content, "from txt");
        }
        console.log(wordCount, "wordCount");
        total_content += content;
        updatedDocuments.push({
          name: file.name,
          size: file.size,
          wordCount,
          content,
        });
      }

      const totalSize = updatedDocuments.reduce(
        (acc, doc) => acc + doc.wordCount,
        0
      );
      const limitExceeded = totalSize > modelLimit;
      if (!limitExceeded) {
        setKnowledgeField(total_content);
        showPreview(true);
      } else {
        setKnowledgeField("");
        showPreview(false);
        showToast(globalDispatch, "Word Limit Exceeded", 5000, "error");
      }

      setDocuments(updatedDocuments);
      setPreviewDocuments(true);
    }, []);

    const { getRootProps, getInputProps } = useDropzone({
      onDrop,
      accept: {
        "application/pdf": [".pdf"],
        "text/plain": [".txt"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          [".docx"],
      },
    });

    return (
      <div className="mt-5">
        <label className="mb-2 flex items-center gap-2 text-sm font-bold text-white">
          Upload Documents
          <span className="group relative">
            <InformationCircleIcon className="h-5 w-5 cursor-pointer text-white/70" />
            <div className="absolute bottom-full left-0 mb-2 hidden w-64 rounded-md bg-[#2d3947] p-2 text-sm text-white shadow-lg group-hover:block">
              Documents forming the knowledge base for the AI assistant
            </div>
          </span>
        </label>

        {/* File format info */}
        <div className="mb-4 flex gap-3 text-sm text-white/70">
          <span className="flex items-center gap-1">
            <DocumentTextIcon className="h-4 w-4" />
            Supported formats:
          </span>
          <span className="rounded bg-[#2d3947] px-2 py-0.5">PDF</span>
          <span className="rounded bg-[#2d3947] px-2 py-0.5">DOCX</span>
          <span className="rounded bg-[#2d3947] px-2 py-0.5">TXT</span>
        </div>

        <div
          {...getRootProps({
            className: "dropzone",
          })}
        >
          <input {...getInputProps()} />
          <div className="flex h-48 w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-white/30 p-8 transition-colors hover:border-[#19b2f6]/50">
            <div className="flex flex-col items-center text-center text-white">
              <DocumentTextIcon className="mb-2 h-8 w-8 text-white/70" />
              <p className="mb-2 text-lg">Drag & drop files here</p>
              <p className="text-sm text-white/70">or click to select files</p>
            </div>
          </div>
        </div>

        {/* File upload feedback */}
        {documents && documents.length > 0 && (
          <div className="mt-4 rounded-lg bg-[#2d3947] p-4">
            <h3 className="mb-3 text-sm font-semibold text-white">
              Uploaded Files ({documents.length})
            </h3>
            <div className="space-y-2">
              {documents.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded bg-[#1d2937] p-2"
                >
                  <div className="flex items-center gap-2">
                    <DocumentTextIcon className="h-4 w-4 text-white/70" />
                    <span className="text-sm text-white">{file.name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-white/70">
                      {(file.size / 1024).toFixed(2)} KB
                    </span>
                    <span className="text-sm text-white/70">
                      {file.wordCount} words
                    </span>
                    <button
                      onClick={() => handleCopy(file.name, index)}
                      className="rounded p-1 transition-colors hover:bg-[#2d3947]"
                      title="Copy filename"
                    >
                      {copiedIndex === index ? (
                        <ClipboardCheckIcon className="h-4 w-4 text-green-400" />
                      ) : (
                        <ClipboardIcon className="h-4 w-4 text-white/70 hover:text-white" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 flex justify-end">
              <button
                type="button"
                className="rounded-md bg-[#19b2f6]/80 px-4 py-2 text-sm text-white transition-colors hover:bg-[#19b2f6]"
                onClick={() => setPreviewDocuments(true)}
              >
                Preview Documents
              </button>
            </div>
          </div>
        )}

        {/* Word limit warning */}
        <div className="mt-2 text-sm text-white/70">
          Maximum {modelLimit.toLocaleString()} words allowed
        </div>
      </div>
    );
  };

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      from_number_id: "",
      assistant_id: "",
      language: "",
    },
  });

  const onSubmit = async (_data) => {
    let sdk = new MkdSDK();
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/sms_inbound_campaign/create",
        {
          name: _data.name,
          assistant_id: _data.assistant_id,
          from_number_id: _data.from_number_id,
          knowledgeField: knowledgeField,
          language: _data.language,
        },
        "POST"
      );
      if (!result.error) {
        showToast(globalDispatch, "Added");
        if (closeSidebar) {
          closeSidebar();
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("name", {
        type: "manual",
        message: error.message,
      });
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "sms_inbound_campaigns",
      },
    });

    (async function getAssistants() {
      sdk.setTable("assistants");
      const result = await sdk.callRestAPI(
        { user_id: authState.user, filter: [`user_id,eq,${authState.user}`] },
        "GETALL"
      );
      if (!result.error) {
        setAssistants(result.list);
      }
    })();

    (async function getNumbers() {
      sdk.setTable("numbers");
      const result = await sdk.callRestAPI(
        {
          user_id: authState.user,
          filter: [
            // `user_id,eq,${authState.user}`,
            `status,eq,1`,
          ],
        },
        "GETALL"
      );
      if (!result.error) {
        setNumbers(result.list);
      }
    })();
    (async function getSettings() {
      sdk.setTable("setting");
      const result = await sdk.callRestAPI({}, "GETALL");
      if (!result.error) {
        setSettings(result.list);
        const v = result.list.filter((s) => s.setting_key === "llm");
        if (v.length) {
          console.log("setting", [v[0]?.setting_value]);
          console.log("setting", limits[v[0]?.setting_value]);
          console.log(
            "setting",
            limits[v[0]?.setting_value].MAX_CONTENT_SIZE_LIMIT
          );
          setModelLimit(limits[v[0]?.setting_value].MAX_CONTENT_SIZE_LIMIT);
        }
      }
    })();
  }, []);

  return (
    <div className="">
      <Transition appear={false} show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[101]"
          onClose={() => {
            if (!previewDocuments) {
              closeSidebar();
            }
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-x-full"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className="h-[95vh] w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all">
                  <div className="p-5">
                    <h4 className="text-3xl font-medium text-white">
                      Add New SMS Inbound Campaign
                    </h4>
                    <form
                      className="mt-7 flex w-full flex-col gap-2"
                      onSubmit={handleSubmit(onSubmit)}
                    >
                      <MkdInput
                        type={"text"}
                        page={"add"}
                        name={"name"}
                        errors={errors}
                        label={"Campaign Name"}
                        placeholder={"Name of the SMS campaign"}
                        register={register}
                        className="bg-[#1d2937] placeholder:text-gray-300"
                      />

                      <MkdInput
                        type={"mapping"}
                        page={"add"}
                        name={"assistant_id"}
                        errors={errors}
                        label={"SMS Assistant"}
                        placeholder={"Select an Assistant"}
                        options={assistants.map((as) => as.id)}
                        mapping={assistants.reduce((a, c) => {
                          a[c.id] = c.assistant_name;
                          return a;
                        }, {})}
                        register={register}
                        className="bg-[#1d2937] placeholder:text-gray-300"
                      />

                      <MkdInput
                        type={"mapping"}
                        page={"add"}
                        name={"from_number_id"}
                        errors={errors}
                        label={"Inbound Receiving Phone #"}
                        placeholder={"Choose Phone Number"}
                        options={numbers.map((as) => as.id)}
                        mapping={numbers.reduce((a, c) => {
                          a[c.id] = c.number;
                          return a;
                        }, {})}
                        register={register}
                        className="bg-[#1d2937] placeholder:text-gray-300"
                      />

                      <MkdInput
                        type="mapping"
                        page="add"
                        name="language"
                        errors={errors}
                        label="Language"
                        placeholder="Select Language"
                        options={Object.keys(languages)}
                        mapping={languages}
                        register={register}
                        className="bg-[#1d2937] placeholder:text-gray-300"
                      />

                      <DocumentUploader allow_preview={preview} />

                      <InteractiveButton
                        type="submit"
                        loading={isSubmitting}
                        disabled={isSubmitting}
                        className="focus:shadow-outline mt-6 rounded bg-[#19b2f6]/80  px-4 py-2 font-semibold text-white focus:outline-none"
                      >
                        Submit
                      </InteractiveButton>
                    </form>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition appear={false} show={previewDocuments} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[102]"
          onClose={() => setPreviewDocuments(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="mx-auto  h-[95vh] w-full max-w-4xl transform overflow-y-auto rounded-xl bg-[#1d2937] p-6 text-left align-middle text-white shadow-xl transition-all">
                  <div className="flex items-center justify-between">
                    <Dialog.Title className="text-3xl font-medium">
                      Preview Document
                    </Dialog.Title>
                    <button
                      type="button"
                      onClick={() => setPreviewDocuments(false)}
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                  <DocumentPreview />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default UserAddSMSInboundCampaignPage;
