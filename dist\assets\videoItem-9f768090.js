import{j as r}from"./@react-google-maps/api-c55ecefa.js";import{r as c}from"./vendor-2ae44a2e.js";function o({videoStream:s,audioStream:t,muted:e}){const u=c.useRef(null),n=c.useRef(null);return c.useEffect(()=>{u.current&&(s==null||s.attach(u.current)),n.current&&(t==null||t.attach(n.current))},[s,t]),r.jsxs(r.Fragment,{children:[r.jsx("video",{autoPlay:!0,ref:u,className:"w-full"}),r.jsx("audio",{muted:e,autoPlay:!0,ref:n})]})}export{o as default};
