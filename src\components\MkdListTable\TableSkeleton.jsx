import Skeleton from "react-loading-skeleton";

export default function TableSkeleton({ columns }) {
  return (
    <table className="w-full rounded-lg border">
      <thead className="border-b text-left">
        {columns.map((column, i) => (
          <th key={i} className="px-6 py-2">
            {" "}
            <Skeleton
              width={`${column.header.length + 5}ch`}
              height={20}
              baseColor="#D2D5DB"
              highlightColor="#EAECF0"
            />
          </th>
        ))}
      </thead>
      <tbody>
        {Array(5)
          .fill("")
          .map((_, idx) => (
            <tr key={idx}>
              {columns.map((column, i) => {
                const width = column.header.length + Math.random() * 5;
                return (
                  <td key={`${idx}-${i}`} className="border-b px-6 py-4">
                    {" "}
                    <Skeleton width={`${width}ch`} height={20} />
                  </td>
                );
              })}
            </tr>
          ))}
      </tbody>
    </table>
  );
}
