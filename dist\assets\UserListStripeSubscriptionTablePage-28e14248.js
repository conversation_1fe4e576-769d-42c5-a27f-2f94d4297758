import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{u as Ce,R as u,r,f as _e}from"./vendor-2ae44a2e.js";import{M as Se,G as ke,A as Ee,B as Ae,a as Te,b as Fe,l as De,R as Pe,c as Be,s as O}from"./index-d0a8f5da.js";import{o as Ie}from"./yup-5abd4662.js";import{u as Oe}from"./react-hook-form-47c010f8.js";import{c as $e,a as S}from"./yup-c2e87575.js";import{S as Re}from"./index-a74110af.js";import{X}from"./lucide-react-f66dbccf.js";import{q as k,_ as $,C as E}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let p=new Se;const R=[{header:"Date",accessor:"datetime"},{header:"Campaign Name",accessor:"campaign_name"},{header:"Campaign Type",accessor:"campaign_type"},{header:"Call Id",accessor:"call_id"},{header:"Credits Used",accessor:"credits_used"},{header:"Total Duration (seconds)",accessor:"total_duration"}],J=R.filter(i=>![""].includes(i.accessor)),Le=["20","40","50","100","500"],Me=["5","10","15","20"],qe=[{id:4,currency:"usd",stripe_id:"price_1PeCvYBgOlWo0lDUiy42q15D",name:"Basic",amount:20,type:"one_time",status:1,product_name:"calls"}];function Ue(i){const n=new Date(i),d={year:"numeric",month:"long",day:"numeric"};return n.toLocaleDateString("en-US",d)}const Ve=async({priceId:i,priceCurrency:n,priceStripeId:d,priceName:b,productName:h,price:j,quantity:v=1})=>{let N={success_url:`${p.fe_baseurl}/user/stripe_subscription?success=true&session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${p.fe_baseurl}/user/stripe_subscription?success=false&session_id={CHECKOUT_SESSION_ID}`,mode:"payment",payment_method_types:["card"],shipping_address_collection:{allowed_countries:["CA","US"]},shipping_options:[{shipping_rate_data:{type:"fixed_amount",display_name:"Shipping fees",fixed_amount:{currency:n,amount:0}}}],locale:"en",line_items:[{price:d,quantity:v}],phone_number_collection:{enabled:!1},payment_intent_data:{metadata:{app_price_id:i,app_product_name:b,app_highlevel_product_name:h,is_order:"true"}},price:j};try{const{error:m,model:g,message:L}=await p.initCheckoutSession(N);if(m)return;g!=null&&g.url&&(location.href=g.url)}catch(m){console.error("Error",m)}},gt=()=>{Ce();const{dispatch:i}=u.useContext(ke);u.useContext(Ee);const[n,d]=u.useState([]),[b,h]=u.useState([]),[j,v]=u.useState(""),[N,m]=u.useState(!1),[g,L]=r.useState([]),[Q,Z]=r.useState(0),[ee,te]=r.useState(0);r.useState([]),r.useState(!1);const[ae,M]=r.useState(!1),[se,q]=r.useState(!1),[U,y]=u.useState({}),[A,re]=u.useState("eq");_e();const[V,f]=r.useState(!1),[T,le]=r.useState("20"),[Y,oe]=r.useState("5"),[x,ne]=r.useState(!1),[C,ie]=r.useState([]),[z,G]=r.useState(null),[ce,H]=r.useState(!1),[F,de]=r.useState(""),[D,ue]=r.useState(""),[P,pe]=r.useState(""),[B,he]=r.useState(""),xe=$e({start_date:S(),end_date:S(),campaign_name:S(),campaign_type:S()}),me=()=>{M(!0)},_=()=>{M(!1)},{register:Ye,handleSubmit:ze,formState:{errors:Ge},reset:ge}=Oe({resolver:Ie(xe)}),w=(t,a,o)=>{if(!o){h(I=>I.filter(K=>!K.startsWith(t+",")));return}const c=`${t},${a||"eq"},${o}`;h(I=>[...I.filter(Ne=>!Ne.startsWith(t+",")),c])};async function W(){q(!0);let t="?allow=true";b.length>0&&b.forEach(a=>{const[o,s,l]=a.split(",");t+=`&${o}=${l}`}),ge();try{const a=await p.callRawAPI("/v3/api/custom/voiceoutreach/user/usage"+t,{},"GET");a.error||(Z(a.total_credits),te(a.credits),L(a.usage))}catch(a){console.log(a)}q(!1)}const fe=()=>{d([]),h([]),y({}),W()};r.useEffect(()=>{i({type:"SETPATH",payload:{path:"subscription"}}),W()},[b]),r.useEffect(()=>{x&&be()},[x]);const be=async()=>{var t,a,o,s,l;H(!0);try{const c=await p.callRawAPI("/v2/api/lambda/stripe/customer/cards",{},"GET");c&&((t=c.data)!=null&&t.data)&&(ie((a=c.data)==null?void 0:a.data),((s=(o=c.data)==null?void 0:o.data)==null?void 0:s.length)>0&&G((l=c.data)==null?void 0:l.data[0].id))}catch(c){console.error("Error fetching cards:",c)}H(!1)},ye=t=>le(t),we=t=>oe(t),je=async()=>{if(f(!0),x){let t={autoload_amount:Y,credit_bundle:T};if(C.length>0)t.card_id=z;else{if(!F||!D||!P||!B){console.error("Please enter complete card details."),f(!1);return}const a=await p.callRawAPI("/v2/api/lambda/stripe/customer/card/create_token",{card_number:F,exp_month:D,exp_year:P,cvc:B},"POST");if(a.error){console.error("Error creating card token:",a.message),f(!1);return}const o=a.token,s=await p.callRawAPI("/v2/api/lambda/stripe/customer/card/",{sourceToken:o},"POST");if(s.error){console.error("Error adding card to user:",s.message),f(!1);return}const{model:l}=s;t.card_id=l.card_id||l.default_source}try{const a=await p.callRawAPI("/v3/api/custom/voiceoutreach/user/autoreload",t,"POST");a.error?(console.error("Auto reload setup failed:",a.message),O(i,"Error purchasing credits",4e3)):(console.log("Auto reload enabled:",a),O(i,"Credit added and auto reload enabled",4e3),window.location.reload())}catch(a){console.error("Error setting up auto reload:",a),O(i,"Error setting up auto reload",4e3)}finally{_(),f(!1)}}else{const t=qe.find(a=>a.amount==T);await Ve({priceId:t.id,priceCurrency:"usd",priceStripeId:t.stripe_id,priceName:t.name,productName:t.product_name,price:t.amount,quantity:1})}_(),f(!1)},ve=t=>{var l;const s=((l=t.target.value.replace(/\D+/g,"").match(/.{1,4}/g))==null?void 0:l.join(" "))||"";de(s)};return e.jsxs("div",{className:"p-8",style:{overflowY:"auto",maxHeight:"100vh"},children:[e.jsxs("div",{className:"flex items-center justify-between text-white",children:[e.jsx("h2",{children:"Automate Intel"}),e.jsxs("div",{children:[e.jsxs("span",{children:["Credits: ",ee.toFixed(2)]}),e.jsx("button",{onClick:me,className:"ml-4 rounded-md bg-[#19b2f6]/80 px-4 py-2 text-white transition-colors hover:bg-[#19b2f6]",children:"Buy Credits"})]})]}),e.jsx(k,{appear:!0,show:ae,as:r.Fragment,children:e.jsxs($,{as:"div",className:"relative z-[100]",onClose:_,children:[e.jsx(k.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(k.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs($.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx($.Title,{className:"text-xl font-medium text-white",children:"Buy Credits"}),e.jsx("button",{onClick:_,children:e.jsx(X,{className:"h-6 w-6 text-white/70 hover:text-white"})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Credit Bundles"}),e.jsx("div",{className:"grid grid-cols-5 gap-2",children:Le.map(t=>e.jsxs("button",{onClick:()=>ye(t),className:`rounded-lg border-2 px-4 py-2 transition-all ${T===t?"border-[#19b2f6] bg-[#19b2f6]/20 text-[#19b2f6]":"border-gray-600 text-white hover:border-[#19b2f6]/50"}`,children:["$",t]},t))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Autoload"}),e.jsx("div",{className:"flex items-center",children:e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",className:"peer sr-only",checked:x,onChange:t=>ne(t.target.checked)}),e.jsx("div",{className:"peer h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#19b2f6] peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#19b2f6]/30"})]})})]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Autoload if balance drops below"}),e.jsx("div",{className:"grid grid-cols-5 gap-2",children:Me.map(t=>e.jsxs("button",{onClick:()=>we(t),disabled:!x,className:`rounded-lg border-2 px-4 py-2 transition-all ${x?Y===t?"border-[#19b2f6] bg-[#19b2f6]/20 text-[#19b2f6]":"border-gray-600 text-white hover:border-[#19b2f6]/50":"cursor-not-allowed border-gray-600 text-gray-400 opacity-50"}`,children:["$",t]},t))}),x&&e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-white",children:C.length>0?"Select a Card for Autoload":"Enter Card Details"}),ce?e.jsx("p",{className:"text-gray-400",children:"Loading cards..."}):C.length>0?e.jsx("select",{value:z,onChange:t=>G(t.target.value),className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white focus:border-[#19b2f6] focus:outline-none",children:C.map(t=>e.jsxs("option",{value:t.id,children:[t.brand," ending in ",t.last4]},t.id))}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Card Number"}),e.jsx("input",{type:"text",placeholder:"1234 5678 9012 3456",value:F,maxLength:19,onChange:ve,className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Exp Month"}),e.jsx("input",{type:"text",placeholder:"MM",maxLength:2,value:D,onChange:t=>ue(t.target.value),className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Exp Year"}),e.jsx("input",{type:"text",placeholder:"YYYY",value:P,maxLength:4,onChange:t=>pe(t.target.value),className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"CVC"}),e.jsx("input",{type:"text",placeholder:"CVC",maxLength:3,value:B,onChange:t=>he(t.target.value),className:"w-full rounded border border-gray-600 bg-[#1d2937] p-2 text-white placeholder-gray-500 focus:border-[#19b2f6] focus:outline-none"})]})]})]})]})]})]}),e.jsx("div",{className:"mt-6 border-t border-gray-600 pt-6",children:e.jsx("button",{className:"w-full rounded-lg bg-[#19b2f6]/80 py-3 font-semibold text-white transition-colors hover:bg-[#19b2f6] disabled:cursor-not-allowed disabled:opacity-50",onClick:je,disabled:V,children:V?"Processing...":"Buy Credits"})})]})})})})]})}),se?e.jsx(Re,{}):e.jsxs("div",{className:"mt-8 rounded-lg bg-[#1d2937] p-5 px-12",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsx("div",{className:"mt-4 rounded-lg bg-[#1d2937] py-5",children:e.jsx("div",{className:"flex h-fit items-center justify-between gap-3",children:e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(E,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(E.Button,{className:"border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white  focus-visible:outline-0 focus-visible:outline-transparent",children:[e.jsx(Ae,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400",children:[e.jsx(Te,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"Search by campaign name",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:j,onChange:t=>{v(t.target.value),w("campaign_name","cs",t.target.value)}}),j&&e.jsx(Fe,{className:"cursor-pointer text-lg text-white",onClick:()=>{v(""),w("campaign_name","cs","")}})]})]}),e.jsx(k,{as:r.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(E.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(E.Button,{onClick:()=>{console.log("clicked"),d([]),h([]),y({})},children:e.jsx(X,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),n==null?void 0:n.map((t,a)=>{var o;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((o=J.find(s=>s.accessor===t))==null?void 0:o.header)||t}),e.jsxs("select",{className:"w-1/3 appearance-none rounded-md border-none bg-gray-100 capitalize text-gray-600 outline-0",value:A,onChange:s=>{re(s.target.value),w(t,s.target.value,U[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:U[t]||"",onChange:s=>{y(l=>({...l,[t]:s.target.value})),w(t,A,s.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(De,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",onReady:s=>w(t,A,s)}),e.jsx(Pe,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{d(s=>s.filter(l=>l!==t)),h(s=>s.filter(l=>!l.includes(t))),y(s=>{const l={...s};return delete l[t],l})}})]},a)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>m(!N),children:[e.jsx(Be,{}),"Add filter"]}),N&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:J.map(t=>e.jsx("li",{className:`${n.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{n.includes(t.accessor)||(d(a=>[...a,t.accessor]),y(a=>({...a,[t.accessor]:""}))),m(!1)},children:t.header},t.accessor))})}),n.length>0&&e.jsx("div",{onClick:fe,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})})})}),e.jsx("div",{className:"align-right text-white",children:e.jsxs("h4",{className:"text-right text-white",children:["Total: $",Q.toFixed(2)]})})]}),e.jsx("h4",{className:"my-4 text-2xl font-bold text-white",children:"Usage"}),e.jsx("div",{className:"w-full overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border  border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx("tr",{children:R.map((t,a)=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white",children:t.header},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:g.map((t,a)=>e.jsx("tr",{children:R.map((o,s)=>{var l;return o.accessor==="datetime"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-white",children:Ue(t[o.accessor])},s):o.accessor==="credits_used"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-white",children:(l=t[o.accessor]||0)==null?void 0:l.toFixed(2)},s):e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-white",children:t[o.accessor]??"N/A"},s)})},a))})]})})]})]})};export{gt as default};
