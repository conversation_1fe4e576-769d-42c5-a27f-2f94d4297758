import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as o}from"./vendor-2ae44a2e.js";import{A as e,G as i}from"./index-d0a8f5da.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const D=()=>{o.useContext(e);const{dispatch:r}=o.useContext(i);return o.useEffect(()=>{r({type:"SETPATH",payload:{path:"admin"}})},[]),t.jsx(t.Fragment,{children:t.jsx("div",{className:"w-full flex justify-center items-center text-7xl h-screen text-gray-700 ",children:"Dashboard"})})};export{D as default};
