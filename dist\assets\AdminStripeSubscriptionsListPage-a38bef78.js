import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as n,f as te}from"./vendor-2ae44a2e.js";import{M as se,G as ae,A as re,B as ne,a as ie,b as oe,R as ce,c as le,t as j,g}from"./index-d0a8f5da.js";import{o as de}from"./yup-5abd4662.js";import{u as pe}from"./react-hook-form-47c010f8.js";import{c as ue,a as x}from"./yup-c2e87575.js";import{P as me}from"./index-132fbad2.js";import"./index-e429b426.js";import{S as he}from"./index-a74110af.js";import"./index-9aa09a5c.js";import{X as ge}from"./lucide-react-f66dbccf.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let w=new se;const S=[{header:"Customer",accessor:"userEmail"},{header:"Plan",accessor:"planName"},{header:"Starts",accessor:"currentPeriodStart",type:"timestamp"},{header:"Ends",accessor:"currentPeriodEnd",type:"timestamp"},{header:"type",accessor:"planType",mapping:{recurring:"Recurring",life_time:"Lifetime"}},{header:"Usage Type",accessor:"isMetered",mapping:{0:"Upfront",1:"Metered"}},{header:"Price",accessor:"planAmount",type:"currency"},{header:"Has Trial",accessor:"trialDays"},{header:"Status",accessor:"status"},{header:"Action",accessor:""}],He=()=>{const{dispatch:l}=n.useContext(ae),{dispatch:y}=n.useContext(re);n.useState("");const[v,A]=n.useState([]),[o,N]=n.useState(10),[C,O]=n.useState(0),[xe,$]=n.useState(0),[d,L]=n.useState(0),[q,z]=n.useState(!1),[B,I]=n.useState(!1),[P,M]=n.useState(!1),[k,T]=n.useState(!1),[i,u]=n.useState([]),[R,m]=n.useState([]),[U,G]=n.useState(""),[_,H]=n.useState("eq"),[V,E]=n.useState(!1);te();const K=ue({customer_email:x(),plan_name:x(),sub_status:x(),plan_type:x()}),{register:ye,handleSubmit:X,formState:{errors:fe}}=pe({resolver:de(K)}),D=(t,a,s)=>{const r=a==="eq"&&isNaN(s)?`"${s}"`:s,p=`${t},${a},${r}`;m(f=>[...f.filter(h=>!h.includes(t)),p]),G(s)};function J(t){(async function(){N(t),await c(1,t)})()}function Q(){(async function(){await c(d-1>1?d-1:1,o)})()}function W(){(async function(){await c(d+1<=C?d+1:1,o)})()}async function c(t,a,s){E(!0);try{const r=await w.getStripeSubscriptions({page:t,limit:a},`filter=${s.toString()}`),{list:p,total:f,limit:F,num_pages:h,page:b}=r;A(p),N(+F),O(+h),L(+b),$(+f),z(+b>1),I(+b+1<=+h)}catch(r){console.log("ERROR",r),j(y,r.message)}E(!1)}const Y=t=>{const a=g(t.customer_email),s=g(t.plan_name),r=g(t.sub_status),p=g(t.plan_type);c(1,o,{customer_email:a,plan_name:s,sub_status:r,plan_type:p})},Z=async t=>{console.log(t);try{const a=await w.adminCancelStripeSubscription(t,{});showToast(l,a.message,3e3),c(1,o)}catch(a){console.log("ERROR",a),showToast(l,a.message),j(y,a.message)}},ee=async(t,a)=>{console.log(t);try{const s=await w.adminCreateUsageCharge(t,a);showToast(l,s.message,3e3),c(1,o)}catch(s){console.log("ERROR",s),showToast(l,s.message),j(y,s.message)}};return n.useEffect(()=>{l({type:"SETPATH",payload:{path:"subscriptions"}});const a=setTimeout(async()=>{await c(1,o,R)},700);return()=>{clearTimeout(a)}},[U,R,_]),console.log("data in subscriptions",v),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3 text-black",children:e.jsxs("form",{className:"relative rounded bg-white",onSubmit:X(Y),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>M(!P),children:[e.jsx(ne,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length>0?i.length:null})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ie,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var a;return D("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(oe,{className:"text-lg text-gray-200"})]})]}),P&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-700",children:"Filters"}),e.jsx(ge,{onClick:()=>{u([]),m([]),setFilterValues({})},className:"cursor-pointer text-lg text-gray-400 hover:text-gray-600"})]}),i==null?void 0:i.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:s=>{H(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>D(t,_,s.target.value)}),e.jsx(ce,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{u(s=>s.filter(r=>r!==t)),m(s=>s.filter(r=>!r.includes(t)))}})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex h-[40px] w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>{T(!k)},children:[e.jsx(le,{}),"Add filter"]}),k&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:S.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||u(a=>[...a,t.header]),T(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{u([]),m([])},className:"inline-block cursor-pointer  rounded py-2.5  pl-6 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]})}),V?e.jsx(he,{}):e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 text-black",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:S.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:v.map((t,a)=>e.jsx("tr",{children:S.map((s,r)=>{if(s.accessor=="")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[t.status!=="canceled"?e.jsx("button",{onClick:()=>Z(t.subId),type:"button",className:"mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Cancel"}):"",t.isMetered===1?e.jsx("button",{onClick:()=>ee(t.subId,1),type:"button",className:"mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Create Charge"}):""]},r);if(s.mapping)return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r);if(t.planType==="recurring"&&s.type==="timestamp")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(t.planType==="lifetime"&&s.type==="timestamp"){if(s.accessor==="currentPeriodStart")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t.createdAt*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(s.accessor==="currentPeriodEnd")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:"Infinity"},r)}else if(s.type=="currency")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",+(t[s.accessor]??0)]},r);return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r)})},a))})]})}),e.jsx(me,{currentPage:d,pageCount:C,pageSize:o,canPreviousPage:q,canNextPage:B,updatePageSize:J,previousPage:Q,nextPage:W})]})};export{He as default};
