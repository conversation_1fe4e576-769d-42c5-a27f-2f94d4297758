import React, { Fragment, useRef } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useLocation, useNavigate } from "react-router-dom";
import { MkdListTableV3 } from "Components/MkdListTable";
import { UserEditVoiceTablePage } from "Routes/LazyLoad";
import MkdSDK from "Utils/MkdSDK";
import { Dialog, Transition } from "@headlessui/react";

const filterConfig = {
  id: {
    operations: ["eq", "lt", "le", "gt", "gte"],
    type: "number",
    label: "ID",
  },
  create_at: {
    operations: ["eq", "lt", "le", "gt", "gte"],
    type: "string",
    label: "Created At",
  },
  name: {
    operations: ["eq", "cs", "sw", "ew"],
    type: "string",
    label: "Name",
  },
};

const columns = [
  {
    header: "Action",
    accessor: "",
  },
  {
    header: "Id",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    isSearchable: true,
  },

  {
    header: "Files",
    accessor: "files",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    format: (files) => {
      const voices = files.split(",");
      return voices.join("<br /><br />");
    },
  },
];

const UserListVoiceTablePage = () => {
  const { state, dispatch: authDispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const refreshRef = useRef(null);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "voice_list",
      },
    });
  }, []);

  async function deleteRow(id) {
    try {
      const sdk = new MkdSDK();
      sdk.setTable("voice_list");
      await sdk.callRestAPI({ id }, "DELETE");
      refreshRef.current?.click();
      showToast(globalDispatch, "Delete successful");
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      showToast(globalDispatch, err.message, 5000, "error");
    }
  }

  return (
    <>
      <>
        <div className="overflow-x-auto rounded-lg  bg-[#1d2937] pt-5 shadow md:p-5">
          <MkdListTableV3
            columns={columns}
            tableRole={"user"}
            table={"voice_list"}
            actionId={"id"}
            defaultFilter={[`user_id,eq,${state.user}`]}
            actions={{
              view: { show: false, action: null, multiple: false },
              edit: {
                show: false,
                multiple: false,
                action: (ids) => {
                  setActiveEditId(ids[0]);
                  setShowEditSidebar(true);
                },
              },
              delete: { show: false, action: deleteRow, multiple: false },
              select: { show: false, action: null, multiple: false },
              add: {
                show: false,
                action: () =>
                  navigate(`/user/onboarding?redirect_uri=${pathname}`),
                multiple: false,
                children: "Add New",
                showChildren: true,
              },
              export: { show: false, action: null, multiple: true },
            }}
            actionPosition={`onTable`}
            refreshRef={refreshRef}
            filterConfig={filterConfig}
          />
        </div>
      </>

      <Transition appear show={showEditSidebar} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-[100]"
          onClose={() => setShowEditSidebar(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-transparent/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center">
              <Transition.Child
                as={Fragment}
                enter="duration-300"
                enterFrom="opacity-0 translate-x-0"
                enterTo="opacity-100 translate-x-0"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-x-0"
                leaveTo="opacity-0 translate-x-full"
              >
                <Dialog.Panel className="h-fit w-full max-w-3xl transform overflow-y-auto bg-[#1d2937]  p-6 text-left align-middle shadow-xl transition-all">
                  <UserEditVoiceTablePage
                    activeId={activeEditId}
                    closeSidebar={() => {
                      setShowEditSidebar(false);
                      refreshRef.current?.click();
                    }}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default UserListVoiceTablePage;
