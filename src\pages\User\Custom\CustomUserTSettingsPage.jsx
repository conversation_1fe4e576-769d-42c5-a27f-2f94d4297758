import { useState, useEffect, useCallback } from "react";
import React from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Calendar, Split, Database, Settings, Phone, Mail } from "lucide-react";
import { Input } from "../ui/input";
import { Switch } from "../ui/switch";
import { Slider } from "../ui/slider";
import { HelpCircle, Eye, EyeOff } from "lucide-react";
import { ghl } from "Assets/images";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { X } from "lucide-react";

import MkdSDK from "Utils/MkdSDK";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { GlobalContext, showToast } from "Context/Global";

let sdk = new MkdSDK();
const timezones = [
  { value: "Pacific/Honolulu", label: "Hawaii-Aleutian Standard Time (HST)" },
  { value: "America/Anchorage", label: "Alaska Standard Time (AKST)" },
  { value: "America/Los_Angeles", label: "Pacific Standard Time (PST)" },
  { value: "America/Denver", label: "Mountain Standard Time (MST)" },
  { value: "America/Phoenix", label: "Mountain Standard Time (no DST)" },
  { value: "America/Chicago", label: "Central Standard Time (CST)" },
  { value: "America/Chihuahua", label: "America/Chihuahua" },
  { value: "America/New_York", label: "Eastern Standard Time (EST)" },
  { value: "America/Toronto", label: "Eastern Standard Time (Canada)" },
  { value: "America/Caracas", label: "Venezuela Standard Time (VET)" },
  { value: "America/Bogota", label: "Colombia Time (COT)" },
  { value: "America/Argentina/Buenos_Aires", label: "Argentina Time (ART)" },
  { value: "Atlantic/Bermuda", label: "Atlantic Standard Time (AST)" },
  { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
  { value: "Europe/Paris", label: "Central European Time (CET)" },
  { value: "Europe/Athens", label: "Eastern European Time (EET)" },
  { value: "Africa/Johannesburg", label: "South Africa Standard Time (SAST)" },
  { value: "Asia/Dubai", label: "Gulf Standard Time (GST)" },
  { value: "Asia/Kolkata", label: "Indian Standard Time (IST)" },
  { value: "Asia/Shanghai", label: "China Standard Time (CST)" },
  { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
  {
    value: "Australia/Sydney",
    label: "Australian Eastern Standard Time (AEST)",
  },
  { value: "Pacific/Auckland", label: "New Zealand Standard Time (NZST)" },
];

// Update validation schema to be conditional based on active tab
const calendarFormSchema = yup.object().shape({
  username: yup.string().when("activeTab", {
    is: "ghl",
    then: (schema) => schema.required("Calendar ID is required"),
    otherwise: (schema) => schema,
  }),
  apiKey: yup.string().when("activeTab", {
    is: (value) => value === "ghl" || value === "cal",
    then: (schema) => schema.required("API Key is required"),
    otherwise: (schema) => schema,
  }),
  timezone: yup.string().when("activeTab", {
    is: "ghl",
    then: (schema) => schema.required("Timezone is required"),
    otherwise: (schema) => schema,
  }),
  activeTab: yup.string().required(),
  // Email settings validation
  automate_emails: yup.boolean(),
  default_assistant_id: yup.number().when("activeTab", {
    is: "email",
    then: (schema) => schema.required("Default assistant ID is required"),
    otherwise: (schema) => schema,
  }),
  reply_signature: yup.string(),
  reply_delay_minutes: yup.number().min(0).max(60),
  notification_on_reply: yup.boolean(),
  cc_user_on_replies: yup.boolean(),
  max_auto_replies_per_thread: yup.number().min(1).max(10),
  working_hours_enabled: yup.boolean(),
  working_hours_timezone: yup.string(),
  out_of_office_enabled: yup.boolean(),
  out_of_office_message: yup.string(),
});

function CalendarForm() {
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [activeTab, setActiveTab] = useState("ghl");
  const [settings, setSettings] = useState(null);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
    setValue,
  } = useForm({
    mode: "onChange",
    resolver: yupResolver(calendarFormSchema),
    defaultValues: {
      username: "",
      apiKey: "",
      timezone: "",
      activeTab: "ghl",
      // Email settings defaults
      automate_emails: true,
      default_assistant_id: "",
      reply_signature: "Best regards,\nVoiceOutreach Team",
      reply_delay_minutes: 5,
      notification_on_reply: true,
      cc_user_on_replies: true,
      max_auto_replies_per_thread: 3,
      working_hours_enabled: true,
      working_hours_timezone: "America/New_York",
      out_of_office_enabled: false,
      out_of_office_message:
        "I'm currently out of the office and will respond when I return.",
    },
  });

  useEffect(() => {
    (async () => {
      try {
        await fetchSettings();
      } catch (error) {
        console.error("Error in useEffect:", error);
        if (globalDispatch) {
          showToast(globalDispatch, "Failed to load settings", 4000, "error");
        }
      }
    })();
  }, []);

  useEffect(() => {
    try {
      setValue("activeTab", activeTab);
      fetchSettings();
    } catch (error) {
      console.error("Error updating form:", error);
    }
  }, [activeTab]);

  const fetchSettings = async () => {
    console.log(authState);
    try {
      sdk.setTable("user_settings");
      const result = await sdk.callRestAPI(
        {
          user_id: authState.user,
          filter: [`user_id,eq,${authState.user}`],
        },
        "GETALL"
      );

      const result2 = await sdk.callRestAPI(
        {
          user_id: authState.user,
          filter: [`user_id,eq,23`],
        },
        "GETALL"
      );
      if (result?.list?.[0]) {
        setSettings(result.list[0]);
        console.log(result.list[0]);

        // Parse settings JSON if it exists
        let parsedSettings = {};
        try {
          parsedSettings = result.list[0].settings
            ? JSON.parse(result.list[0].settings)
            : {};
        } catch (e) {
          console.error("Error parsing settings JSON:", e);
        }

        // If on GHL tab and calendars exist in either place, populate the form
        if (activeTab === "ghl") {
          try {
            // Try both settings.calendars and direct calendars field
            const calendarsStr =
              parsedSettings.calendars || result.list[0].calendars;
            const calendars = calendarsStr ? JSON.parse(calendarsStr) : [];

            if (calendars?.[0]) {
              reset({
                username: calendars[0].calendarId,
                apiKey: calendars[0].GOHIGHLEVEL_TOKEN,
                timezone: calendars[0].timezone,
                activeTab: "ghl",
              });
            }
          } catch (parseError) {
            console.error("Error parsing calendars:", parseError);
            if (globalDispatch) {
              showToast(
                globalDispatch,
                "Failed to parse calendar settings",
                4000,
                "error"
              );
            }
          }
        }

        // If on Cal.com tab and cal_api_key exists in either place, populate the form
        if (activeTab === "cal") {
          const apiKey =
            parsedSettings.cal_api_key || result.list[0].cal_api_key;
          if (apiKey) {
            reset({
              apiKey: apiKey,
              activeTab: "cal",
            });
          }
        }

        // If on Email tab and email_settings exist, populate the form
        if (activeTab === "email") {
          try {
            const emailSettingsStr = result.list[0].email_settings;
            const emailSettings = emailSettingsStr
              ? JSON.parse(emailSettingsStr)
              : {};

            if (emailSettings && Object.keys(emailSettings).length > 0) {
              reset({
                activeTab: "email",
                automate_emails: emailSettings.automate_emails ?? true,
                default_assistant_id: emailSettings.default_assistant_id ?? "",
                reply_signature:
                  emailSettings.reply_signature ??
                  "Best regards,\nVoiceOutreach Team",
                reply_delay_minutes: emailSettings.reply_delay_minutes ?? 5,
                notification_on_reply:
                  emailSettings.notification_on_reply ?? true,
                cc_user_on_replies: emailSettings.cc_user_on_replies ?? true,
                max_auto_replies_per_thread:
                  emailSettings.max_auto_replies_per_thread ?? 3,
                working_hours_enabled:
                  emailSettings.working_hours?.enabled ?? true,
                working_hours_timezone:
                  emailSettings.working_hours?.timeZone ?? "America/New_York",
                out_of_office_enabled:
                  emailSettings.out_of_office?.enabled ?? false,
                out_of_office_message:
                  emailSettings.out_of_office?.message ??
                  "I'm currently out of the office and will respond when I return.",
              });
            }
          } catch (parseError) {
            console.error("Error parsing email settings:", parseError);
            if (globalDispatch) {
              showToast(
                globalDispatch,
                "Failed to parse email settings",
                4000,
                "error"
              );
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
      if (globalDispatch) {
        showToast(globalDispatch, "Failed to fetch settings", 4000, "error");
      }
    }
  };

  const onSubmit = async (data) => {
    try {
      setCalendarLoading(true);
      sdk.setTable("user_settings");

      if (activeTab === "ghl") {
        const ghlData = {
          calendarId: data.username,
          GOHIGHLEVEL_TOKEN: data.apiKey,
          timezone: data.timezone,
          campaign_id: "1",
        };

        // Parse existing settings to update
        const currentSettings = settings?.settings
          ? JSON.parse(settings.settings)
          : {};
        const updatedSettings = {
          ...currentSettings,
          calendars: JSON.stringify([ghlData]),
        };

        await sdk.callRestAPI(
          {
            id: settings?.id,
            calendars: JSON.stringify([ghlData]),
            settings: JSON.stringify(updatedSettings),
          },
          "PUT"
        );
      } else if (activeTab === "cal") {
        // Parse existing settings to update
        const currentSettings = settings?.settings
          ? JSON.parse(settings.settings)
          : {};
        const updatedSettings = {
          ...currentSettings,
          cal_api_key: data.apiKey,
        };

        await sdk.callRestAPI(
          {
            id: settings?.id,
            cal_api_key: data.apiKey,
            settings: JSON.stringify(updatedSettings),
          },
          "PUT"
        );
      } else if (activeTab === "email") {
        // Construct email settings object
        const emailSettings = {
          automate_emails: data.automate_emails,
          default_assistant_id: parseInt(data.default_assistant_id),
          reply_signature: data.reply_signature,
          reply_delay_minutes: parseInt(data.reply_delay_minutes),
          notification_on_reply: data.notification_on_reply,
          cc_user_on_replies: data.cc_user_on_replies,
          max_auto_replies_per_thread: parseInt(
            data.max_auto_replies_per_thread
          ),
          working_hours: {
            enabled: data.working_hours_enabled,
            timeZone: data.working_hours_timezone,
            monday: { start: "09:00", end: "17:00" },
            tuesday: { start: "09:00", end: "17:00" },
            wednesday: { start: "09:00", end: "17:00" },
            thursday: { start: "09:00", end: "17:00" },
            friday: { start: "09:00", end: "17:00" },
            saturday: { enabled: false },
            sunday: { enabled: false },
          },
          out_of_office: {
            enabled: data.out_of_office_enabled,
            start_date: null,
            end_date: null,
            message: data.out_of_office_message,
          },
        };

        await sdk.callRestAPI(
          {
            id: settings?.id,
            email_settings: JSON.stringify(emailSettings),
          },
          "PUT"
        );
      }

      if (globalDispatch) {
        showToast(globalDispatch, "Settings updated successfully!", 4000);
      }
      await fetchSettings(); // Refresh settings
    } catch (error) {
      console.error("Error saving settings:", error);
      if (globalDispatch) {
        showToast(globalDispatch, "Failed to update settings", 4000, "error");
      }
    } finally {
      setCalendarLoading(false);
    }
  };

  const connectCalendar = async () => {
    try {
      setCalendarLoading(true);
      const result = await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/calendar/auth",
        {},
        "GET"
      );
      if (!result.error && result.link) {
        window.open(result.link);
      } else {
        throw new Error("Invalid response from calendar auth");
      }
    } catch (error) {
      console.error("Error connecting calendar:", error);
      if (globalDispatch) {
        showToast(
          globalDispatch,
          "Failed to connect to Google Calendar",
          4000,
          "error"
        );
      }
    } finally {
      setCalendarLoading(false);
    }
  };

  // Watch form values for validation
  const watchedFields = watch();
  const isValidForm =
    activeTab === "ghl"
      ? watchedFields.username && watchedFields.apiKey && watchedFields.timezone
      : activeTab === "cal"
      ? watchedFields.apiKey
      : activeTab === "email"
      ? watchedFields.default_assistant_id
      : false;

  return (
    <div className="space-y-6">
      <div className="flex gap-2">
        <button
          type="button"
          className={`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${
            activeTab === "ghl" ? "bg-[#19b2f6]/20" : "hover:bg-gray-100/10"
          }`}
          onClick={() => setActiveTab("ghl")}
        >
          <span className="font-semibold text-white">GHL</span>
        </button>
        <button
          type="button"
          className={`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${
            activeTab === "cal" ? "bg-[#19b2f6]/20" : "hover:bg-gray-100/10"
          }`}
          onClick={() => setActiveTab("cal")}
        >
          <p className="font-semibold text-white">Cal.com</p>
        </button>
        <button
          type="button"
          className={`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 ${
            activeTab === "email" ? "bg-[#19b2f6]/20" : "hover:bg-gray-100/10"
          }`}
          onClick={() => setActiveTab("email")}
        >
          <span className="font-semibold text-white">Email</span>
        </button>
        <button
          type="button"
          className={`flex-1 rounded-md px-3 py-1.5 transition-colors duration-150 hover:bg-gray-100/10`}
          onClick={connectCalendar}
        >
          <span className="font-semibold text-white">Google</span>
        </button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex flex-col gap-4">
          {activeTab === "ghl" && (
            <>
              <div>
                <Input
                  {...register("username")}
                  placeholder="Calendar ID"
                  className={`bg-transparent text-white ${
                    errors.username ? "border-red-400" : "border-gray-300"
                  } placeholder:text-gray-300`}
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.username.message}
                  </p>
                )}
              </div>

              <div className="relative mt-2">
                <Input
                  {...register("apiKey")}
                  type={showApiKey ? "text" : "password"}
                  placeholder="API Key"
                  className={`bg-transparent text-white ${
                    errors.apiKey ? "border-red-400" : "border-gray-300"
                  } pr-10 placeholder:text-gray-300`}
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300"
                >
                  {showApiKey ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
                {errors.apiKey && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.apiKey.message}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="timezone"
                  className="mt-1 block text-sm font-medium text-white"
                >
                  Timezone
                </label>
                <select
                  {...register("timezone")}
                  className={`mt-1 block w-full rounded-md ${
                    errors.timezone ? "border-red-400" : "border-gray-300"
                  } bg-[#1d2937] text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm`}
                >
                  <option value="">Select your timezone</option>
                  {timezones.map((tz) => (
                    <option key={tz.value} value={tz.value}>
                      {tz.label}
                    </option>
                  ))}
                </select>
                {errors.timezone && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.timezone.message}
                  </p>
                )}
              </div>
            </>
          )}

          {activeTab === "cal" && (
            <div className="relative mt-2">
              <Input
                {...register("apiKey")}
                type={showApiKey ? "text" : "password"}
                placeholder="Cal.com API Key"
                className={`bg-transparent text-white ${
                  errors.apiKey ? "border-red-400" : "border-gray-300"
                } pr-10 placeholder:text-gray-300`}
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute right-2 top-[50%] z-10 -translate-y-1/2 text-white hover:text-gray-300"
              >
                {showApiKey ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
              {errors.apiKey && (
                <p className="mt-1 text-sm text-red-400">
                  {errors.apiKey.message}
                </p>
              )}
            </div>
          )}

          {activeTab === "email" && (
            <>
              {/* Email Automation Toggle */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">
                  Automate Emails
                </label>
                <Switch
                  checked={watch("automate_emails")}
                  onCheckedChange={(value) =>
                    setValue("automate_emails", value)
                  }
                  className="bg-gray-600 data-[state=checked]:bg-blue-500"
                />
              </div>

              {/* Default Assistant ID */}
              <div>
                <Input
                  {...register("default_assistant_id")}
                  type="number"
                  placeholder="Default Assistant ID"
                  className={`bg-transparent text-white ${
                    errors.default_assistant_id
                      ? "border-red-400"
                      : "border-gray-300"
                  } placeholder:text-gray-300`}
                />
                {errors.default_assistant_id && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.default_assistant_id.message}
                  </p>
                )}
              </div>

              {/* Reply Signature */}
              <div>
                <label className="mb-2 block text-sm font-medium text-white">
                  Reply Signature
                </label>
                <textarea
                  {...register("reply_signature")}
                  placeholder="Best regards,\nVoiceOutreach Team"
                  rows={3}
                  className="w-full rounded-md border border-gray-300 bg-transparent p-2 text-white placeholder:text-gray-300"
                />
              </div>

              {/* Reply Delay */}
              <div>
                <label className="mb-2 block text-sm font-medium text-white">
                  Reply Delay (minutes): {watch("reply_delay_minutes")}
                </label>
                <Slider
                  value={[watch("reply_delay_minutes") || 5]}
                  max={60}
                  min={0}
                  step={1}
                  className="w-full"
                  onValueChange={([val]) =>
                    setValue("reply_delay_minutes", val)
                  }
                />
              </div>

              {/* Notification Settings */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">
                  Notify on Reply
                </label>
                <Switch
                  checked={watch("notification_on_reply")}
                  onCheckedChange={(value) =>
                    setValue("notification_on_reply", value)
                  }
                  className="bg-gray-600 data-[state=checked]:bg-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">
                  CC User on Replies
                </label>
                <Switch
                  checked={watch("cc_user_on_replies")}
                  onCheckedChange={(value) =>
                    setValue("cc_user_on_replies", value)
                  }
                  className="bg-gray-600 data-[state=checked]:bg-blue-500"
                />
              </div>

              {/* Max Auto Replies */}
              <div>
                <label className="mb-2 block text-sm font-medium text-white">
                  Max Auto Replies per Thread:{" "}
                  {watch("max_auto_replies_per_thread")}
                </label>
                <Slider
                  value={[watch("max_auto_replies_per_thread") || 3]}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                  onValueChange={([val]) =>
                    setValue("max_auto_replies_per_thread", val)
                  }
                />
              </div>

              {/* Working Hours */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">
                  Enable Working Hours
                </label>
                <Switch
                  {...register("working_hours_enabled")}
                  className="bg-gray-600 data-[state=checked]:bg-blue-500"
                />
              </div>

              {/* Working Hours Timezone */}
              <div>
                <label className="mb-2 block text-sm font-medium text-white">
                  Working Hours Timezone
                </label>
                <select
                  {...register("working_hours_timezone")}
                  className="w-full rounded-md border border-gray-300 bg-[#1d2937] text-white"
                >
                  {timezones.map((tz) => (
                    <option key={tz.value} value={tz.value}>
                      {tz.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Out of Office */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">
                  Enable Out of Office
                </label>
                <Switch
                  {...register("out_of_office_enabled")}
                  className="bg-gray-600 data-[state=checked]:bg-blue-500"
                />
              </div>

              {/* Out of Office Message */}
              <div>
                <label className="mb-2 block text-sm font-medium text-white">
                  Out of Office Message
                </label>
                <textarea
                  {...register("out_of_office_message")}
                  placeholder="I'm currently out of the office and will respond when I return."
                  rows={3}
                  className="w-full rounded-md border border-gray-300 bg-transparent p-2 text-white placeholder:text-gray-300"
                />
              </div>
            </>
          )}

          <button
            disabled={!isValidForm || calendarLoading}
            type="submit"
            className="mt-3 inline-flex h-[41px] justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-3 py-2 text-sm font-medium text-white shadow-sm hover:border-[#19b2f6] hover:bg-[#19b2f6]/60 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {calendarLoading
              ? "Saving..."
              : activeTab === "ghl"
              ? "Connect GHL"
              : activeTab === "cal"
              ? "Save API Key"
              : activeTab === "email"
              ? "Save Email Settings"
              : "Save"}
          </button>
        </div>
      </form>
    </div>
  );
}

export function SettingRow({
  title,
  leftLabel,
  rightLabel,
  value,
  onChange,
  tooltipContent,
}) {
  return (
    <div className="mb-6">
      <div className="mb-2 flex items-center gap-2">
        <span className="text-lg text-white">{title}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-white">{leftLabel}</span>
        <Switch
          checked={value}
          onCheckedChange={onChange}
          className="bg-gray-200 data-[state=checked]:bg-blue-500"
        />
        <span className="text-white">{rightLabel}</span>
      </div>
    </div>
  );
}

export function SliderSetting({
  title,
  min,
  max,
  step,
  value,
  onChange,
  tooltipContent,
  leftLabel,
  rightLabel,
}) {
  return (
    <div className="mb-6">
      <div className="mb-2 flex items-center gap-2">
        <span className="text-lg text-white">{title}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipContent}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-blue-500">{leftLabel}</span>
        <Slider
          value={[value]}
          max={max}
          min={min}
          step={step}
          className="mx-4 w-[60%]"
          onValueChange={([val]) => onChange(val)}
        />
        <span className="text-blue-500">{rightLabel}</span>
      </div>
    </div>
  );
}

export function TransferSetting({ value, onChange }) {
  // Add state for each API key visibility toggle
  const [showElevenLabsKey, setShowElevenLabsKey] = useState(false);
  const [showDeepgramKey, setShowDeepgramKey] = useState(false);
  const [showAnthropicKey, setShowAnthropicKey] = useState(false);
  const [showTelnyxKey, setShowTelnyxKey] = useState(false);

  const ApiKeyInput = ({
    label,
    placeholder,
    showKey,
    setShowKey,
    value,
    onChange,
    tooltipText,
  }) => (
    <>
      <div className="mb-2 mt-2 flex items-center gap-2">
        <span className="text-lg text-white">{label}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="relative">
        <Input
          type={showKey ? "text" : "password"}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className="w-full bg-transparent pr-10 text-white"
        />
        <button
          type="button"
          onClick={() => setShowKey(!showKey)}
          className="absolute right-2 top-1/2 -translate-y-1/2 text-white hover:text-gray-300"
        >
          {showKey ? (
            <EyeOff className="h-5 w-5" />
          ) : (
            <Eye className="h-5 w-5" />
          )}
        </button>
      </div>
    </>
  );

  return (
    <div className="mb-6">
      <ApiKeyInput
        label="Elevenlabs Key"
        placeholder="Enter your Elevenlabs API key"
        showKey={showElevenLabsKey}
        setShowKey={setShowElevenLabsKey}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        tooltipText="API key for Elevenlabs text-to-speech service"
      />

      <ApiKeyInput
        label="Deepgram Key"
        placeholder="Enter your Deepgram API key"
        showKey={showDeepgramKey}
        setShowKey={setShowDeepgramKey}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        tooltipText="API key for Deepgram speech recognition service"
      />

      <ApiKeyInput
        label="Anthropic Key"
        placeholder="Enter your Anthropic API key"
        showKey={showAnthropicKey}
        setShowKey={setShowAnthropicKey}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        tooltipText="API key for Anthropic AI service"
      />

      <ApiKeyInput
        label="Telnyx Key"
        placeholder="Enter your Telnyx API key"
        showKey={showTelnyxKey}
        setShowKey={setShowTelnyxKey}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        tooltipText="API key for Telnyx telephony service"
      />

      <div className="mb-2 mt-2 flex items-center gap-2">
        <span className="text-lg text-white">Transfer Number</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-white" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Phone number to transfer calls to</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Enter phone number to transfer call to"
        className="w-full bg-transparent text-white"
      />
    </div>
  );
}

// Voice Dialer Settings Component
export function VoiceDialerSettings() {
  return (
    <div className="h-[600px] w-full">
      <iframe
        src="https://callagentds.manaknightdigital.com/voice-dialer"
        className="h-full w-full border-0"
        title="Voice Dialer"
      />
    </div>
  );
}

export function SettingSection({ icon, title, isActive, onClick }) {
  return (
    <div
      onClick={onClick}
      className={`flex cursor-pointer items-center justify-center space-x-3 px-4 py-2 ${
        isActive
          ? "text-center font-medium text-white"
          : "text-center text-white hover:text-white"
      }`}
    >
      {/* Icon */}
      <div
        className={`flex-shrink-0 ${isActive ? "text-white" : "text-white"}`}
      >
        {React.cloneElement(icon, { className: "w-6 h-6" })}{" "}
        {/* Adjusted size */}
      </div>
      {/* Title */}
      <span className="text-center text-sm">{title}</span>
    </div>
  );
}

import debounce from "lodash.debounce";

import { AuthContext } from "Context/Auth";
import { Spinner } from "Assets/svgs";

// Add this new component for the tab style
function TabButton({ title, isActive, onClick, icon = null }) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center gap-2 rounded-t-md px-6 py-3 text-sm font-medium transition-colors ${
        isActive
          ? "border-l border-r border-t border-gray-600 bg-[#2d3748] text-white"
          : "bg-transparent text-gray-400 hover:bg-[#2d3748]/50 hover:text-gray-300"
      }`}
    >
      {icon || null}
      {title}
    </button>
  );
}

export function SettingsModal({ open, onOpenChange }) {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [activeSection, setActiveSection] = useState("calendar");
  const [fetchSettings, setFetchSettings] = useState(false);
  const [settings, setSettings] = useState({
    interruptSensitivity: false,
    responseSpeed: false,
    doubleCall: false,
    vmDetection: false,
    initialDelay: 0,
    aiCreativity: 0.5,
    transferNumber: "",
    callerId: "",
    llm_settings: {
      provider: "groq",
      model_name: "llama-3.3-70b-versatile",
      temperature: "0.01",
      max_tokens: "250",
      top_p: "0.9",
      system_prompt: "",
    },
    tts_settings: {
      provider: "elevenlabs",
      model_name: "eleven_multilingual_v2",
    },
  });

  React.useEffect(() => {
    async function fetchSettings() {
      try {
        setFetchSettings(true);
        const result = await sdk.callRawAPI(
          "/v3/api/custom/voiceoutreach/user/settings",
          {},
          "GET"
        );
        console.log(result);
        if (result?.model) {
          // Parse the settings string
          const storedSettings = result.model.settings
            ? JSON.parse(result.model.settings)
            : {};

          // Parse llm_settings if it exists in the model
          let llmSettings = {
            provider: "groq",
            model_name: "llama-3.3-70b-versatile",
            temperature: "0.01",
            max_tokens: "250",
            top_p: "0.9",
            system_prompt: "",
          };

          try {
            if (result.model.llm_settings) {
              llmSettings = JSON.parse(result.model.llm_settings);
            }
          } catch (e) {
            console.error("Error parsing llm_settings:", e);
          }

          // Parse tts_settings if it exists in the model
          let ttsSettings = {
            provider: "elevenlabs",
            model_name: "eleven_multilingual_v2",
          };

          try {
            if (result.model.tts_settings) {
              ttsSettings = JSON.parse(result.model.tts_settings);
            }
          } catch (e) {
            console.error("Error parsing tts_settings:", e);
          }

          console.log(storedSettings);
          if (storedSettings) {
            setSettings({
              ...storedSettings,
              llm_settings: llmSettings,
              tts_settings: ttsSettings,
            });
          }
        }
      } catch (error) {
        console.log("Error", error);
      }
      setFetchSettings(false);
    }

    fetchSettings();
  }, []);

  // Debounced function to update settings
  const updateSettingsAPI = useCallback(
    debounce(async (newSettings) => {
      const storedSettings = JSON.stringify(newSettings);
      await sdk.callRawAPI(
        "/v3/api/custom/voiceoutreach/user/settings",
        { settings: storedSettings },
        "POST"
      );
    }, 500),
    []
  );

  // Update settings and debounce API call
  const handleSettingChange = (newSettings) => {
    setSettings(newSettings);
    updateSettingsAPI(newSettings); // Trigger debounced API call
  };

  const handleSaveSettings = async () => {
    const storedSettings = JSON.stringify(settings);
    await sdk.callRawAPI(
      "/v3/api/custom/voiceoutreach/user/settings",
      { settings: storedSettings },
      "POST"
    );
    showToast(globalDispatch, "Settings saved successfully!", 4000);
  };

  const handleSectionClick = (section) => {
    setActiveSection(section);
  };

  const tooltips = {
    interruptSensitivity:
      "Controls how easily the AI can be interrupted during conversations",
    responseSpeed: "Adjusts how quickly the AI responds to user input",
    initialDelay: "Sets the delay before the first AI response",
    doubleCall: "Enables double-call verification feature",
    vmDetection: "Activates voicemail detection capabilities",
    aiCreativity: "Controls the creativity level of AI responses",
    callerId: "Set the caller ID name for outbound calls",
    transferNumber: "Phone number to transfer calls to",
  };

  // Add model speed data
  const MODEL_SPEEDS = {
    groq: {
      "llama-3.3-70b-versatile": "Fast",
      "meta-llama/Llama-4-Scout-17B-16E-Instruct": "Medium",
      "meta-llama/llama-4-maverick-17b-128e-instruct": "Medium-Fast",
    },
    anthropic: {
      "claude-3-haiku-20240307": "Very Fast",
      "claude-3-5-haiku-20241022": "Fast",
      "claude-3-opus-20240229": "Slow",
      "claude-3-5-sonnet-20240620": "Medium",
    },
    "direct-openai": {
      "gpt-3.5-turbo": "Very Fast",
      "gpt-4o-mini": "Medium",
    },
    lambda_ai: {
      "llama-3.3-70b-instruct": "Very Fast",
    },
  };

  // Add provider options
  const PROVIDERS = {
    groq: {
      label: "Groq",
      models: [
        "llama-3.3-70b-versatile",
        "meta-llama/Llama-4-Scout-17B-16E-Instruct",
        "meta-llama/llama-4-maverick-17b-128e-instruct",
      ],
    },
    anthropic: {
      label: "Anthropic",
      models: [
        "claude-3-haiku-20240307",
        "claude-3-5-haiku-20241022",
        "claude-3-opus-20240229",
        "claude-3-5-sonnet-20240620",
      ],
    },
    "direct-openai": {
      label: "OpenAI",
      models: ["gpt-3.5-turbo", "gpt-4o-mini"],
    },
    lambda_ai: {
      label: "Lambda AI",
      models: ["llama-3.3-70B-instruct"],
    },
  };

  // Add speech recognition model speeds
  const SPEECH_RECOGNITION_SPEEDS = {
    deepgram: {
      "nova-3": "Very Fast",
      "nova-3-general": "Fast",
      "nova-3-medical": "Fast",
      "nova-2-phonecall": "Medium",
    },
    assemblyai: {
      "slam-1": "Very Fast",
      "universal-1": "Fast",
      "universal-2": "Medium",
    },
  };

  // Add speech recognition providers
  const SPEECH_RECOGNITION_PROVIDERS = {
    deepgram: {
      label: "Deepgram",
      models: [
        "nova-3",
        "nova-3-general",
        "nova-3-medical",
        "nova-2-phonecall",
      ],
    },
    assemblyai: {
      label: "AssemblyAI",
      models: ["slam-1", "universal-1", "universal-2"],
    },
  };

  // Add TTS providers
  const TTS_PROVIDERS = {
    elevenlabs: {
      label: "ElevenLabs",
      models: ["eleven_multilingual_v2", "eleven_flash_v2"],
    },
    orpheus_tts: {
      label: "Orpheus TTS",
      models: ["canopylabs/orpheus-tts-0.1-finetune-prod"],
    },
    kokoro_tts: {
      label: "Kokoro TTS",
      models: ["base"],
    },
  };

  // Update existing speech recognition options
  const speechRecognitionOptions = [
    { name: "Deepgram", value: "deepgram" },
    { name: "AssemblyAI", value: "assemblyai" },
  ];

  return (
    <>
      {fetchSettings ? (
        <div
          className={`flex max-h-fit min-h-fit w-full min-w-full max-w-full items-center justify-center py-5`}
        >
          <Spinner size={100} color="#0EA5E9" />
        </div>
      ) : (
        <div className="mx-auto max-w-3xl bg-[#1d2937] text-white">
          <div className="rounded-lg bg-[#1d2937] bg-opacity-100 p-12 shadow-lg">
            <div>
              <div className="flex justify-center">
                <TabButton
                  icon={<Calendar className="h-4 w-4" />}
                  title="Calendar & Email Booking"
                  isActive={activeSection === "calendar"}
                  onClick={() => handleSectionClick("calendar")}
                />
                <TabButton
                  icon={<Database className="h-4 w-4" />}
                  title="Advanced Options"
                  isActive={activeSection === "advanced"}
                  onClick={() => handleSectionClick("advanced")}
                />
                <TabButton
                  icon={<Phone className="h-4 w-4" />}
                  title="Voice Dialer"
                  isActive={activeSection === "voicedialer"}
                  onClick={() => handleSectionClick("voicedialer")}
                />
              </div>
            </div>

            <div className="rounded-b-md rounded-tr-md border border-gray-600 bg-[#2d3748] p-6">
              {activeSection === "calendar" && <CalendarForm />}

              {activeSection === "voicedialer" && <VoiceDialerSettings />}

              {activeSection === "advanced" && (
                <div className="mx-auto max-w-2xl space-y-6">
                  {/* Settings Rows */}
                  <div className="space-y-8">
                    {/* Interrupt Sensitivity */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-white">
                          Interrupt Sensitivity
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tooltips.interruptSensitivity}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-400">Low</span>
                        <Switch
                          checked={settings.interruptSensitivity}
                          onCheckedChange={(value) =>
                            handleSettingChange({
                              ...settings,
                              interruptSensitivity: value,
                            })
                          }
                          className="bg-gray-600 data-[state=checked]:bg-blue-500"
                        />
                        <span className="text-sm text-gray-400">High</span>
                      </div>
                    </div>

                    {/* Double Call */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-white">Double Call</span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tooltips.doubleCall}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-400">False</span>
                        <Switch
                          checked={settings.doubleCall}
                          onCheckedChange={(value) =>
                            handleSettingChange({
                              ...settings,
                              doubleCall: value,
                            })
                          }
                          className="bg-gray-600 data-[state=checked]:bg-blue-500"
                        />
                        <span className="text-sm text-gray-400">True</span>
                      </div>
                    </div>

                    {/* Response Speed */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-white">
                          Response Speed
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tooltips.responseSpeed}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-400">Auto</span>
                        <Switch
                          checked={settings.responseSpeed}
                          onCheckedChange={(value) =>
                            handleSettingChange({
                              ...settings,
                              responseSpeed: value,
                            })
                          }
                          className="bg-gray-600 data-[state=checked]:bg-blue-500"
                        />
                        <span className="text-sm text-gray-400">Sensitive</span>
                      </div>
                    </div>

                    {/* VM Detection */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-white">
                          VM Detection (Beta)
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tooltips.vmDetection}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-400">Off</span>
                        <Switch
                          checked={settings.vmDetection}
                          onCheckedChange={(value) =>
                            handleSettingChange({
                              ...settings,
                              vmDetection: value,
                            })
                          }
                          className="bg-gray-600 data-[state=checked]:bg-blue-500"
                        />
                        <span className="text-sm text-gray-400">On</span>
                      </div>
                    </div>

                    {/* Initial Message Delay */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-white">
                            Initial Message Delay
                          </span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="h-4 w-4 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{tooltips.initialDelay}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <span className="text-sm text-white">
                          {settings.initialDelay}s
                        </span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="whitespace-nowrap text-sm text-gray-400">
                          0 (sec)
                        </span>
                        <Slider
                          value={[settings.initialDelay]}
                          max={5}
                          min={0}
                          step={0.1}
                          className="w-full"
                          onValueChange={([val]) =>
                            handleSettingChange({
                              ...settings,
                              initialDelay: val,
                            })
                          }
                        />
                        <span className="whitespace-nowrap text-sm text-gray-400">
                          5 (sec)
                        </span>
                      </div>
                    </div>

                    {/* AI Creativity */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-white">
                            AI Creativity
                          </span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="h-4 w-4 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{tooltips.aiCreativity}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <span className="text-sm text-white">
                          {settings.aiCreativity}
                        </span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="pr-[28px] text-sm text-gray-400">
                          0{" "}
                        </span>
                        <Slider
                          value={[settings.aiCreativity]}
                          max={1}
                          min={0}
                          step={0.01}
                          className="w-full"
                          onValueChange={([val]) =>
                            handleSettingChange({
                              ...settings,
                              aiCreativity: val,
                            })
                          }
                        />
                        <span className="pl-[28px] text-sm text-gray-400">
                          1
                        </span>
                      </div>
                    </div>

                    {/* Caller ID */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-white">
                          Caller ID Name
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{tooltips.callerId}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Input
                        placeholder="Caller ID Name"
                        value={settings.callerId}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            callerId: e.target.value,
                          })
                        }
                        className="w-full border-gray-200 bg-transparent text-white placeholder:text-gray-300"
                      />
                    </div>
                  </div>

                  {/* LLM Settings Section */}
                  <div className="mt-8 space-y-4 border-t border-gray-700 pt-8">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-medium text-white">
                        LLM Settings
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Configure Language Model settings</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    {/* Provider Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">Provider</label>
                      <select
                        value={settings.llm_settings.provider}
                        onChange={(e) => {
                          const newProvider = e.target.value;
                          const firstModel = PROVIDERS[newProvider].models[0];
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              provider: newProvider,
                              model_name: firstModel,
                            },
                          });
                        }}
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {Object.entries(PROVIDERS).map(([value, { label }]) => (
                          <option
                            key={value}
                            value={value}
                            className="bg-[#1d2937]"
                          >
                            {label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Model Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">Model</label>
                      <select
                        value={settings.llm_settings.model_name}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              model_name: e.target.value,
                            },
                          })
                        }
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {PROVIDERS[settings.llm_settings.provider].models.map(
                          (model) => (
                            <option
                              key={model}
                              value={model}
                              className="bg-[#1d2937]"
                            >
                              {model} (
                              {
                                MODEL_SPEEDS[settings.llm_settings.provider][
                                  model
                                ]
                              }
                              )
                            </option>
                          )
                        )}
                      </select>
                    </div>

                    {/* Temperature */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Temperature</span>
                        <span className="text-sm text-white">
                          {settings.llm_settings.temperature}
                        </span>
                      </div>
                      <Slider
                        value={[parseFloat(settings.llm_settings.temperature)]}
                        max={1}
                        min={0}
                        step={0.01}
                        className="w-full"
                        onValueChange={([val]) =>
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              temperature: val.toString(),
                            },
                          })
                        }
                      />
                    </div>

                    {/* Max Tokens */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">Max Tokens</label>
                      <Input
                        type="number"
                        value={settings.llm_settings.max_tokens}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              max_tokens: e.target.value,
                            },
                          })
                        }
                        className="w-full border-gray-600 bg-transparent text-white"
                      />
                    </div>

                    {/* Top P */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Top P</span>
                        <span className="text-sm text-white">
                          {settings.llm_settings.top_p}
                        </span>
                      </div>
                      <Slider
                        value={[parseFloat(settings.llm_settings.top_p)]}
                        max={1}
                        min={0}
                        step={0.01}
                        className="w-full"
                        onValueChange={([val]) =>
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              top_p: val.toString(),
                            },
                          })
                        }
                      />
                    </div>

                    {/* System Prompt */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">
                        System Prompt
                      </label>
                      <textarea
                        value={settings.llm_settings.system_prompt}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            llm_settings: {
                              ...settings.llm_settings,
                              system_prompt: e.target.value,
                            },
                          })
                        }
                        className="h-24 w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                        placeholder="Enter system prompt..."
                      />
                    </div>
                  </div>

                  {/* Speech Recognition Settings Section */}
                  <div className="mt-8 space-y-4 border-t border-gray-700 pt-8">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-medium text-white">
                        Speech Recognition Settings
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Configure Speech Recognition settings</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    {/* Speech Recognition Provider Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">
                        Speech Recognition Provider
                      </label>
                      <select
                        value={settings.speech_recognition_settings?.provider}
                        onChange={(e) => {
                          const newProvider = e.target.value;
                          const firstModel =
                            SPEECH_RECOGNITION_PROVIDERS[newProvider].models[0];
                          handleSettingChange({
                            ...settings,
                            speech_recognition_settings: {
                              ...settings.speech_recognition_settings,
                              provider: newProvider,
                              model: firstModel,
                            },
                          });
                        }}
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {Object.entries(SPEECH_RECOGNITION_PROVIDERS).map(
                          ([value, { label }]) => (
                            <option
                              key={value}
                              value={value}
                              className="bg-[#1d2937]"
                            >
                              {label}
                            </option>
                          )
                        )}
                      </select>
                    </div>

                    {/* Speech Recognition Model Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">
                        Speech Recognition Model
                      </label>
                      <select
                        value={settings.speech_recognition_settings?.model}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            speech_recognition_settings: {
                              ...settings.speech_recognition_settings,
                              model: e.target.value,
                            },
                          })
                        }
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {SPEECH_RECOGNITION_PROVIDERS[
                          settings.speech_recognition_settings?.provider
                        ]?.models.map((model) => (
                          <option
                            key={model}
                            value={model}
                            className="bg-[#1d2937]"
                          >
                            {model} (
                            {
                              SPEECH_RECOGNITION_SPEEDS[
                                settings.speech_recognition_settings?.provider
                              ][model]
                            }
                            )
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* TTS Settings Section */}
                  <div className="mt-8 space-y-4 border-t border-gray-700 pt-8">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-medium text-white">
                        Text-to-Speech Settings
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Configure Text-to-Speech settings</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    {/* TTS Provider Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">TTS Provider</label>
                      <select
                        value={settings.tts_settings?.provider}
                        onChange={(e) => {
                          const newProvider = e.target.value;
                          const firstModel =
                            TTS_PROVIDERS[newProvider].models[0];
                          handleSettingChange({
                            ...settings,
                            tts_settings: {
                              ...settings.tts_settings,
                              provider: newProvider,
                              model_name: firstModel,
                            },
                          });
                        }}
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {Object.entries(TTS_PROVIDERS).map(
                          ([value, { label }]) => (
                            <option
                              key={value}
                              value={value}
                              className="bg-[#1d2937]"
                            >
                              {label}
                            </option>
                          )
                        )}
                      </select>
                    </div>

                    {/* TTS Model Selection */}
                    <div className="space-y-2">
                      <label className="text-sm text-white">TTS Model</label>
                      <select
                        value={settings.tts_settings?.model_name}
                        onChange={(e) =>
                          handleSettingChange({
                            ...settings,
                            tts_settings: {
                              ...settings.tts_settings,
                              model_name: e.target.value,
                            },
                          })
                        }
                        className="w-full rounded-md border border-gray-600 bg-transparent p-2 text-white"
                      >
                        {TTS_PROVIDERS[
                          settings.tts_settings?.provider
                        ]?.models.map((model) => (
                          <option
                            key={model}
                            value={model}
                            className="bg-[#1d2937]"
                          >
                            {model}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {activeSection == "advanced" && (
              <div className="mt-8 flex w-full justify-center">
                <button
                  className="focus:shadow-outline w-full rounded bg-[#19b2f6]/80 px-4 py-2 font-bold text-white hover:bg-[#19b2f6]/60 focus:outline-none"
                  onClick={handleSaveSettings}
                >
                  Save Settings
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export default SettingsModal;
