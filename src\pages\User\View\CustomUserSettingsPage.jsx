import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { InteractiveButton } from "Components/InteractiveButton";
// import {ProgressBar} from "Components/ProgressBar";
import ModalPrompt from "Components/Modal/ModalPrompt";

let sdk = new MkdSDK();

const CustomUserSettingsPage = () => {
  const schema = yup.object({}).required();

  const { dispatch } = React.useContext(GlobalContext);
  const [oldEmail, setOldEmail] = useState("");
  const [fileObj, setFileObj] = React.useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isLastNameModalOpen, setIsLastNameModalOpen] = useState(false);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [oldPhoto, setOldPhoto] = useState("");
  const [uploadedPhoto, setUploadedPhoto] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("Settings");
  const [defaultValues, setDefaultValues] = useState({});
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const previewImage = (field, target, multiple = false) => {
    let tempFileObj = fileObj;
    console.log(target);
    if (multiple) {
      if (tempFileObj[field]) {
        tempFileObj[field] = [
          ...tempFileObj[field],
          {
            file: target.files[0],
            tempFile: {
              url: URL.createObjectURL(target.files[0]),
              name: target.files[0].name,
              type: target.files[0].type,
            },
          },
        ];
      } else {
        tempFileObj[field] = [
          {
            file: target.files[0],
            tempFile: {
              url: URL.createObjectURL(target.files[0]),
              name: target.files[0].name,
              type: target.files[0].type,
            },
          },
        ];
      }
    } else {
      tempFileObj[field] = {
        file: target.files[0],
        tempURL: URL.createObjectURL(target.files[0]),
      };
    }
    setFileObj({ ...tempFileObj });
  };

  async function fetchData() {
    try {
      sdk.setTable("user_settings");
      const result = await sdk.callRestAPI(
        { user_id: state.user, filter: [`user_id,eq,${state.user}`] },
        "GETALL"
      );
      setDefaultValues(result?.list[0]);
      setValue("phone_service", result?.list[0]?.phone_service);
      setValue("voice_service", result?.list[0]?.voice_service);
      setValue("llm_service", result?.list[0]?.llm_service);
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(
        dispatch,
        error.response.message ? error.response.data.message : error.message
      );
    }
  }

  const handleImageChange = async (e) => {
    // console.log("starting image change");
    const formData = new FormData();
    // console.log(e[0]);
    formData.append("file", e[0]);
    try {
      const result = await sdk.uploadImage(formData);
      setUploadedPhoto(result.url);
    } catch (err) {
      console.error(err);
    }
  };

  const onSubmit = async (data) => {
    setDefaultValues(data);
    try {
      setSubmitLoading(true);

      const result = await sdk.updateProfile({
        first_name: data.first_name || defaultValues?.first_name,
        last_name: data.last_name || defaultValues?.last_name,
        photo: data.photo || oldPhoto,
      });

      if (!result.error) {
        showToast(dispatch, "Profile Updated", 4000);
        closeModal();
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
        closeModal();
      }
      if (oldEmail !== data.email) {
        const emailresult = await sdk.updateEmail(data.email);
        if (!emailresult.error) {
          showToast(dispatch, "Email Updated", 1000);
        } else {
          if (emailresult.validation) {
            const keys = Object.keys(emailresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: emailresult.validation[field],
              });
            }
          }
        }
        closeModal();
      }

      if (data.password.length > 0) {
        const passwordresult = await sdk.updatePassword(data.password);
        if (!passwordresult.error) {
          showToast(dispatch, "Password Updated", 2000);
        } else {
          if (passwordresult.validation) {
            const keys = Object.keys(passwordresult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: passwordresult.validation[field],
              });
            }
          }
        }
      }
      await fetchData();
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "settings",
      },
    });

    fetchData();
  }, []);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const openModalEdit = () => {
    setIsEditModalOpen(true);
  };

  const openLastNModal = () => {
    setIsLastNameModalOpen(true);
  };

  const openEmailModal = () => {
    setIsEmailModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsEditModalOpen(false);
    setIsLastNameModalOpen(false);
    setIsEmailModalOpen(false);
  };

  return (
    <div className="mx-auto mt-6 max-w-3xl rounded-md border">
      <div className="flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#ffffffd1]">
        <div className="flex items-center space-x-6">
          <div
            className={`cursor-pointer rounded-lg px-3 py-1 ${
              activeTab === "Settings" ? "bg-[#f4f4f4] text-[#525252]" : ""
            } `}
            onClick={() => setActiveTab("Settings")}
          >
            Settings
          </div>
        </div>
      </div>
      <main>
        {/* Profile Tab */}
        {activeTab === "Settings" && (
          <div className="rounded bg-white">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mx-10 max-w-lg">
                <p className="mb-3 text-base font-medium text-gray-900">
                  Model Details
                </p>
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-x-20">
                    <p className="text-base font-medium text-gray-600">
                      Phone service
                    </p>
                    <p className="text-base font-medium text-gray-900">
                      {defaultValues?.phone_service}
                    </p>
                  </div>
                  <p
                    className="cursor-pointer text-base font-semibold text-black"
                    onClick={openModalEdit}
                  >
                    Edit
                  </p>
                </div>
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-x-20">
                    <p className="text-base font-medium text-gray-600">
                      Voice service
                    </p>
                    <p className="text-base font-medium text-gray-900">
                      {defaultValues?.voice_service}
                    </p>
                  </div>
                  <p
                    className="cursor-pointer text-base font-semibold text-black"
                    onClick={openLastNModal}
                  >
                    Edit
                  </p>
                </div>
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-x-20">
                    <p className="text-base font-medium text-gray-600">
                      LLM service
                    </p>
                    <p className="text-base font-medium text-gray-900">
                      {defaultValues?.llm_service}
                    </p>
                  </div>
                  <p
                    className="cursor-pointer text-base font-semibold text-black"
                    onClick={openLastNModal}
                  >
                    Edit
                  </p>
                </div>
              </div>
            </form>
          </div>
        )}
        {isEditModalOpen && (
          <EditInfoModal
            title="Edit Phone Service"
            label="Phone service"
            buttonName="Save and close"
            isOpen={openModalEdit}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="phone_service"
            submitLoading={submitLoading}
            errors={errors}
          />
        )}
        {isLastNameModalOpen && (
          <EditInfoModal
            title="Edit Voice Service"
            label="Voice service"
            buttonName="Save and close"
            isOpen={openLastNModal}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="voice_service"
            submitLoading={submitLoading}
            errors={errors}
          />
        )}
        {isLastNameModalOpen && (
          <EditInfoModal
            title="Edit LLM service"
            label="LLM service"
            buttonName="Save and close"
            isOpen={openLastNModal}
            onClose={closeModal}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            id="llm_service"
            submitLoading={submitLoading}
            errors={errors}
          />
        )}
      </main>
    </div>
  );
};

export const EditInfoModal = (props) => {
  const {
    title,
    label,
    buttonName,
    isOpen,
    onClose,
    handleSubmit,
    onSubmit,
    register,
    id,
    submitLoading,
    errors,
    defaultValues,
  } = props;
  const [emailConfirm, setEmailConfirm] = useState(false);
  const [values, setValues] = useState({
    email: "",
  });

  const handleChange = (prop) => (event) => {
    if (prop === "email") {
      setValues({ ...values, [prop]: event.target.value });
    }
  };

  return (
    <div
      className="fixed inset-0 z-10 overflow-y-auto"
      // onClick={() => onClose()}
    >
      <div
        className={`fixed inset-0 z-10 overflow-y-auto ${
          isOpen ? "block" : "hidden"
        } `}
        // onClick={(e) => {
        //   if (e.target === e.currentTarget) {
        //     onClose();
        //   }
        // }}
      >
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
          <span
            className="hidden sm:inline-block sm:h-screen sm:align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>
          <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold leading-6 text-gray-900">
                {title}
              </div>
              {(id === "voice_service" ||
                id === "llm_service" ||
                id === "phone_service") && (
                <div className="mt-3">
                  <label
                    htmlFor="firstName"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    {label}
                  </label>
                  <input
                    className="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                    id={id}
                    type="text"
                    placeholder={`Enter ${label} `}
                    name={id}
                    {...register(id)}
                  />
                  <p className="text-xs italic text-red-500">
                    {errors?.id?.message}
                  </p>
                </div>
              )}
              <button
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                onClick={onClose}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomUserSettingsPage;
