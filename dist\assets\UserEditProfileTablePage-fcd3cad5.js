import{j as t}from"./@react-google-maps/api-c55ecefa.js";import{R as r,f as $,r as l,h as C}from"./vendor-2ae44a2e.js";import{u as L}from"./react-hook-form-47c010f8.js";import{o as M}from"./yup-5abd4662.js";import{c as G,a as p}from"./yup-c2e87575.js";import{M as O,A as B,G as q,t as H,s as K}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as V}from"./MkdInput-c12da351.js";import{I as z}from"./InteractiveButton-bff38983.js";import{S as J}from"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";let d=new O;const Te=()=>{var _,y,k,j,w,N;const{dispatch:S}=r.useContext(B),I=G({user_id:p(),fcm_token:p(),device_id:p(),device_type:p()}).required(),{dispatch:h}=r.useContext(q),[b,Q]=r.useState({}),[g,u]=r.useState(!1),[T,f]=r.useState(!1),E=$(),[W,D]=l.useState(0),[X,F]=l.useState(""),[Y,P]=l.useState(""),[Z,R]=l.useState(""),{register:c,handleSubmit:A,setError:v,setValue:m,formState:{errors:s}}=L({resolver:M(I)}),x=C();l.useEffect(function(){(async function(){try{f(!0),d.setTable("profile");const e=await d.callRestAPI({id:Number(x==null?void 0:x.id)},"GET");e.error||(m("user_id",e.model.user_id),m("fcm_token",e.model.fcm_token),m("device_id",e.model.device_id),m("device_type",e.model.device_type),D(e.model.user_id),F(e.model.fcm_token),P(e.model.device_id),R(e.model.device_type),setId(e.model.id),f(!1))}catch(e){f(!1),console.log("error",e),H(S,e.message)}})()},[]);const U=async e=>{u(!0);try{d.setTable("profile");for(let i in b){let a=new FormData;a.append("file",b[i].file);let n=await d.uploadImage(a);e[i]=n.url}const o=await d.callRestAPI({id,user_id:e.user_id,fcm_token:e.fcm_token,device_id:e.device_id,device_type:e.device_type},"PUT");if(!o.error)K(h,"Updated"),E("/user/profile");else if(o.validation){const i=Object.keys(o.validation);for(let a=0;a<i.length;a++){const n=i[a];v(n,{type:"manual",message:o.validation[n]})}}u(!1)}catch(o){u(!1),console.log("Error",o),v("user_id",{type:"manual",message:o.message})}};return r.useEffect(()=>{h({type:"SETPATH",payload:{path:"profile"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Profile"}),T?t.jsx(J,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(U),children:[t.jsx(V,{type:"number",page:"edit",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:c,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"fcm_token",children:"Fcm Token"}),t.jsx("textarea",{placeholder:"Fcm Token",...c("fcm_token"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(_=s.fcm_token)!=null&&_.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(y=s.fcm_token)==null?void 0:y.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"device_id",children:"Device Id"}),t.jsx("textarea",{placeholder:"Device Id",...c("device_id"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(k=s.device_id)!=null&&k.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=s.device_id)==null?void 0:j.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"device_type",children:"Device Type"}),t.jsx("textarea",{placeholder:"Device Type",...c("device_type"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(w=s.device_type)!=null&&w.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(N=s.device_type)==null?void 0:N.message})]}),t.jsx(z,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{Te as default};
