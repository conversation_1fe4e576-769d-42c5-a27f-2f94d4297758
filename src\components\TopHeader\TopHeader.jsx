import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  useContext,
} from "react";
import { GlobalContext } from "Context/Global";
import { Link, useLocation, useNavigate, NavLink } from "react-router-dom";
import { BackButton } from "Components/BackButton";
import { AuthContext } from "Context/Auth";
import { StringCaser } from "Utils/utils";
import { useProfile } from "Hooks/useProfile";
import MkdSDK from "Utils/MkdSDK";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";
import {
  MdAssistant,
  MdCampaign,
  MdFormatListNumbered,
  MdHandshake,
  MdHearing,
  MdOutlineAttachMoney,
  MdOutlineTextsms,
  MdRecordVoiceOver,
} from "react-icons/md";
import { PiUsersThreeFill } from "react-icons/pi";
import {
  BsTelephoneInbound,
  BsTelephoneOutboundFill,
  BsTelephone,
} from "react-icons/bs";
import {
  ChevronDown,
  Heart,
  Search,
  ShoppingCart,
  User,
  Bug,
  Coins,
  Settings2,
  LogOut,
} from "lucide-react";
import { Menu, Transition, Dialog } from "@headlessui/react";
import { UserCircleIcon } from "@heroicons/react/24/solid";
import { showToast } from "Context/Global";
import { automateIcon } from "Assets/images";

let sdk = new MkdSDK();

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}
const BugReportModal = ({ isOpen, onClose, onSubmit }) => {
  const { globalDispatch, state: globalState } = useContext(GlobalContext);
  const [query, setQuery] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (query.trim() === "") {
      setError("Query cannot be empty");
      return;
    }
    handleBugReportSubmit(query);
    onSubmit(query);
    setQuery("");
    setError("");
    onClose();
  };

  const handleBugReportSubmit = async (query) => {
    // Handle the bug report submission logic here
    console.log("Bug report submitted:", query);
    try {
      sdk.setTable("reports");
      await sdk.callRestAPI(
        {
          query,
          user_id: state.user,
        },
        "POST"
      );
      setReportBug(false);
      showToast(globalDispatch, "Report submitted");
    } catch (e) {
      showToast(globalDispatch, "Unable to submit bug report");
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10  bg-[#1d2937]"
        onClose={onClose}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#0f1827] bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md  transform overflow-hidden rounded-2xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-white"
                >
                  Report a Bug
                </Dialog.Title>
                <div className="mt-2">
                  <input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="w-full rounded-md bg-transparent p-2 text-white placeholder:text-gray-300"
                    placeholder="Enter your query"
                  />
                  {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                  )}
                </div>

                <div className="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
                    onClick={onClose}
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent  bg-[#19b2f6]/80  px-4 py-2 text-sm font-medium text-white "
                    onClick={handleSubmit}
                  >
                    Submit
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
const TopHeader = () => {
  const { state } = useContext(AuthContext);
  const { globalDispatch, state: globalState } = useContext(GlobalContext);
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState("assistants");
  const [isBugReportModalOpen, setIsBugReportModalOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null);
  const dropdownRef = useRef(null);
  const timeoutRef = useRef();

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
  //       setIsProductsOpen(false);
  //       setOpenDropdown(null);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => document.removeEventListener("mousedown", handleClickOutside);
  // }, []);

  const handleMouseEnter = (dropdown) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsProductsOpen(true);
    setOpenDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsProductsOpen(false);
      setOpenDropdown(null);
    }, 200);
  };

  return state?.role == "user" ? (
    <nav className="border-bg-transparent fixed left-0 right-0 top-0 z-50 bg-[#0f1827] text-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex flex-shrink-0 items-center">
            <span className="font-bold md:text-[13px] lg:text-xl">
              AutomateIntel - Voice
            </span>
          </div>
          {/* #1d2937 */}
          {/* Navigation Links */}
          <div className="mx-8 hidden flex-1 items-center justify-center bg-[#0f1827] text-white md:flex">
            <div className="relative flex  items-center rounded-full bg-[#1d2937] p-1">
              <div
                className="relative"
                onMouseEnter={() => handleMouseEnter("assistants")}
                onMouseLeave={handleMouseLeave}
              >
                <Link to="/user/voice">
                  <button
                    onClick={() => {
                      setIsProductsOpen(!isProductsOpen);
                      setSelectedItem("assistants");
                    }}
                    className={cn(
                      "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",
                      selectedItem === "assistants"
                        ? "bg-[#19b2f6]/80  text-white"
                        : "text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"
                    )}
                  >
                    Assistants
                    <ChevronDown className="ml-1 h-4 w-4 2xl:h-6 2xl:w-6" />
                  </button>
                </Link>

                {openDropdown === "assistants" && (
                  <div className="absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg">
                    {/* <Link
                      to="/user/onboarding"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("assistants");
                      }}
                    >
                      <MdHandshake className="w-4 h-4" />
                      <span>Onboarding</span>
                    </Link> */}
                    <Link
                      to="/user/voice"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("assistants");
                      }}
                    >
                      <MdRecordVoiceOver className="h-4 w-4" />
                      <span>Voice Assistant Test</span>
                    </Link>
                    <Link
                      to="/user/sms"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("assistants");
                      }}
                    >
                      <MdOutlineTextsms className="h-4 w-4" />
                      <span>SMS Assistant Test</span>
                    </Link>
                    <Link
                      to="/user/assistants"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("assistants");
                      }}
                    >
                      <MdAssistant className="h-4 w-4" />
                      <span>Voice Assistants</span>
                    </Link>
                    <Link
                      to="/user/voice_list"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("assistants");
                      }}
                    >
                      <MdRecordVoiceOver className="h-4 w-4" />
                      <span>Cloned Voices</span>
                    </Link>
                  </div>
                )}
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMouseEnter("campaigns")}
                onMouseLeave={handleMouseLeave}
              >
                <Link to="/user/outbound_campaigns">
                  <button
                    onClick={() => {
                      setIsProductsOpen(!isProductsOpen);
                      setSelectedItem("campaigns");
                    }}
                    className={cn(
                      "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",
                      selectedItem === "campaigns"
                        ? "bg-[#19b2f6]/80  text-white"
                        : "text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"
                    )}
                  >
                    Campaigns
                    <ChevronDown className="ml-1 h-4 w-4 2xl:w-6" />
                  </button>
                </Link>

                {openDropdown === "campaigns" && (
                  <div className="absolute left-0 top-full mt-1 w-56 rounded-md bg-[#0f1827] py-1 shadow-lg">
                    <Link
                      to="/user/outbound_campaigns"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("campaigns");
                      }}
                    >
                      <MdCampaign className="h-4 w-4" />
                      <span>OutBound Campaigns</span>
                    </Link>
                    <Link
                      to="/user/inbound_campaigns"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("campaigns");
                      }}
                    >
                      <MdHearing className="h-4 w-4" />
                      <span>Inbound Campaigns</span>
                    </Link>
                    <Link
                      to="/user/sms_outbound_campaigns"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("campaigns");
                      }}
                    >
                      <MdCampaign className="h-4 w-4" />
                      <span>SMS Outbound Campaigns</span>
                    </Link>
                    <Link
                      to="/user/sms_inbound_campaigns"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("campaigns");
                      }}
                    >
                      <MdCampaign className="h-4 w-4" />
                      <span>SMS Inbound Campaigns</span>
                    </Link>
                  </div>
                )}
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMouseEnter("logs")}
                onMouseLeave={handleMouseLeave}
              >
                <Link to="/user/outbound_call_logs">
                  <button
                    onClick={() => {
                      setIsProductsOpen(!isProductsOpen);
                      setSelectedItem("logs");
                    }}
                    className={cn(
                      "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",
                      selectedItem === "logs"
                        ? "bg-[#19b2f6]/80  text-white"
                        : "text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"
                    )}
                  >
                    Logs
                    <ChevronDown className="ml-1 h-4 w-4 2xl:w-6" />
                  </button>
                </Link>

                {openDropdown === "logs" && (
                  <div className="absolute left-0 top-full mt-1 w-64 rounded-md bg-[#0f1827] py-1 shadow-lg">
                    <Link
                      to="/user/outbound_call_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <BsTelephoneOutboundFill className="h-4 w-4" />
                      <span>Outbound Call Logs</span>
                    </Link>
                    <Link
                      to="/user/inbound_call_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <BsTelephoneInbound className="h-4 w-4" />
                      <span>Inbound Call Logs</span>
                    </Link>
                    <Link
                      to="/user/test_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <BsTelephone className="h-4 w-4" />
                      <span>Sample Voice Call Log</span>
                    </Link>
                    <Link
                      to="/user/test_sms_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <MdOutlineTextsms className="h-4 w-4" />
                      <span>Sample SMS Followup Logs</span>
                    </Link>
                    <Link
                      to="/user/email_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <MdOutlineTextsms className="h-4 w-4" />
                      <span>Email Logs</span>
                    </Link>
                    <Link
                      to="/user/sms_outbound_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <MdOutlineTextsms className="h-4 w-4" />
                      <span>SMS Outbound Logs</span>
                    </Link>
                    <Link
                      to="/user/sms_inbound_logs"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("logs");
                      }}
                    >
                      <MdOutlineTextsms className="h-4 w-4" />
                      <span>SMS Inbound Logs</span>
                    </Link>
                  </div>
                )}
              </div>

              <Link to="/user/numbers">
                <button
                  onClick={() => {
                    setSelectedItem("phone");
                    setIsProductsOpen(false);
                    setOpenDropdown(null);
                  }}
                  className={cn(
                    "relative z-10 rounded-full px-6 py-2 text-sm font-medium md:text-[12px] lg:text-sm",
                    selectedItem === "phone"
                      ? "bg-[#19b2f6]/80  text-white"
                      : "text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"
                  )}
                >
                  Phone #
                </button>
              </Link>

              <div
                className="relative"
                onMouseEnter={() => handleMouseEnter("settings")}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  onClick={() => {
                    setIsProductsOpen(!isProductsOpen);
                    setSelectedItem("settings");
                  }}
                  className={cn(
                    "relative z-10 flex items-center rounded-full px-6 py-2 text-sm font-medium transition-colors duration-300 ease-in md:text-[12px] lg:text-sm",
                    selectedItem === "settings"
                      ? "bg-[#19b2f6]/80  text-white"
                      : "text-white hover:bg-[#19b2f6]/60  hover:bg-opacity-90"
                  )}
                >
                  Settings
                  <ChevronDown className="ml-1 h-4 w-4 2xl:w-6" />
                </button>

                {openDropdown === "settings" && (
                  <div className="absolute left-0 top-full mt-1 w-48 rounded-md bg-[#0f1827] py-1 shadow-lg">
                    <Link
                      to="/user/settings"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm  text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("settings");
                      }}
                    >
                      <Settings2 className="h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                    <Link
                      to="/user/stripe_subscription"
                      className="block flex items-center space-x-3 px-4 py-2 text-sm text-white hover:bg-[#4CC1F8]"
                      onClick={() => {
                        setIsProductsOpen(!isProductsOpen);
                        setSelectedItem("settings");
                      }}
                    >
                      <Coins className="h-4 w-4" />
                      <span>Billing</span>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right side icons */}
          <div className="hidden items-center space-x-6 md:flex">
            <Link to="/user/profile" className="text-white hover:text-gray-600">
              <User className="h-5 w-5 2xl:w-7" />
            </Link>
            <button
              className="text-white hover:text-gray-600"
              onClick={() => setIsBugReportModalOpen(true)}
            >
              <Bug className="h-5 w-5 2xl:w-7" />
            </button>
            <Link to="/user/logout" className="text-white hover:text-gray-600">
              <LogOut className="h-5 w-5 2xl:w-7" />
            </Link>
          </div>
          <BugReportModal
            isOpen={isBugReportModalOpen}
            onClose={() => setIsBugReportModalOpen(false)}
            onSubmit={(query) => {
              // Handle bug report submission
              onsubmit(query);
              console.log("Bug report submitted:", query);
            }}
          />

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button className="text-white hover:text-gray-600">
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>
  ) : (
    <nav className="border-b border-gray-200 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <span className="text-xl font-bold">AutomateIntel - Voice</span>
        </div>
      </div>
    </nav>
  );
};

export default TopHeader;
