import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r}from"./vendor-2ae44a2e.js";import{_ as s}from"./qr-scanner-cf010ec4.js";import{S as a}from"./index-f2c2b086.js";import{b as l}from"./wbg-a13cc141.js";r.lazy(()=>s(()=>import("./UserHeader-8fd79246.js"),["assets/UserHeader-8fd79246.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index.esm-4b383179.js","assets/react-icons-f29df01f.js","assets/index-d0a8f5da.js","assets/react-confirm-alert-783bc3ae.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-7bce1936.js","assets/lucide-react-f66dbccf.js","assets/react-loading-skeleton-f53ed7d1.js","assets/react-papaparse-b60a38ab.js","assets/papaparse-2d1475f9.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-9cc92aaf.js","assets/@fortawesome/react-fontawesome-27c5bed3.js","assets/@fortawesome/fontawesome-svg-core-294d29ff.js","assets/@fortawesome/free-solid-svg-icons-11dbc67c.js","assets/@fortawesome/free-regular-svg-icons-3e88f209.js","assets/@fortawesome/free-brands-svg-icons-2414b431.js","assets/index-f3f42218.css","assets/index.esm-42944128.js","assets/index.esm-de9a80b6.js"]));const t=r.lazy(()=>s(()=>import("./TopHeader-81c61396.js"),["assets/TopHeader-81c61396.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js","assets/index-d0a8f5da.js","assets/react-confirm-alert-783bc3ae.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-7bce1936.js","assets/react-icons-f29df01f.js","assets/lucide-react-f66dbccf.js","assets/react-loading-skeleton-f53ed7d1.js","assets/react-papaparse-b60a38ab.js","assets/papaparse-2d1475f9.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-9cc92aaf.js","assets/@fortawesome/react-fontawesome-27c5bed3.js","assets/@fortawesome/fontawesome-svg-core-294d29ff.js","assets/@fortawesome/free-solid-svg-icons-11dbc67c.js","assets/@fortawesome/free-regular-svg-icons-3e88f209.js","assets/@fortawesome/free-brands-svg-icons-2414b431.js","assets/index-f3f42218.css","assets/@mantine/core-1006e8cf.js","assets/@fullcalendar/core-a789a586.js","assets/core-b9802b0d.css","assets/tailwind-merge-05141ada.js","assets/index.esm-42944128.js","assets/index.esm-de9a80b6.js"])),i=({children:o})=>e.jsx("div",{style:{backgroundImage:`url(${l})`,backgroundSize:"cover",backgroundRepeat:"no-repeat"},className:"flex min-h-screen w-full",children:e.jsxs("div",{className:"flex w-full flex-1 flex-col py-20",children:[e.jsx(t,{}),e.jsx("main",{className:"sm:px-400 w-full overflow-auto px-6 py-4 sm:overflow-y-auto",children:e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsx(a,{size:100,color:"#0EA5E9"})}),children:o})})]})}),x=r.memo(i);export{x as default};
