import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as l,f as se,i as ae,r as f}from"./vendor-2ae44a2e.js";import{A as re,G as ie,T as le,t as ne,s as oe,B as ce,a as de,b as he,l as xe,R as ue,c as pe,m as me,n as ge,P as fe,X as je}from"./index-d0a8f5da.js";import{u as be}from"./react-hook-form-47c010f8.js";import{C as ye}from"./react-papaparse-b60a38ab.js";import{X as we}from"./lucide-react-f66dbccf.js";import{C as j,q as b,_ as S}from"./@headlessui/react-7bce1936.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./papaparse-2d1475f9.js";const C=[{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Chat History",accessor:"chat_history",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Phone Number",accessor:"to_number",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Ended",accessor:"ended",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"True",1:"False"}},{header:"Credit Used",accessor:"cost",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"AI Response",accessor:"ai_response",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}}],R=C.filter(_=>![""].includes(_.accessor)),Ie=()=>{const{state:_,dispatch:F}=l.useContext(re),{dispatch:T}=l.useContext(ie);se();const[n,m]=l.useState([]);l.useState(4);const[y,c]=l.useState([]),[d,k]=l.useState(""),[P,$]=l.useState(!1),[A,D]=l.useState(!1),[w,q]=l.useState([]),[o,B]=ae(),{CSVDownloader:I}=ye(),[z,V]=l.useState(!1),[L,H]=l.useState(null),[W,X]=l.useState({currentPage:0,pageSize:0,totalNumber:0,totalPages:0});be({defaultValues:{}});const[E,h]=l.useState({}),[v,G]=l.useState("eq");l.useEffect(()=>{T({type:"SETPATH",payload:{path:"test_sms_logs"}}),O()},[y,d]);const x=(t,r,s)=>{if(!s){c(p=>p.filter(g=>!g.startsWith(t+",")));return}let a=s,i=r||"eq";t==="ended"&&(a=a.toLowerCase()==="true"?"0":"1"),t==="chat_history"&&(i="cs");const u=`${t},${i},${a}`;c(p=>[...p.filter(N=>!N.startsWith(t+",")),u])},J=({chatHistory:t})=>{const[r,s]=l.useState(4),a=()=>{s(i=>i+4)};return e.jsxs(e.Fragment,{children:[t.slice(0,r).map((i,u)=>e.jsxs("div",{className:`overflow-x-auto rounded-md p-3 ${i.role==="user"?"bg-blue-500/20 text-white":"bg-transparent text-white"}`,children:[e.jsxs("p",{className:"font-medium capitalize",children:[i.role,":"]}),e.jsx("p",{className:"mt-1",children:i.content})]},u)),r<t.length&&e.jsx("button",{onClick:a,className:"mt-4 text-[#19b2f6] hover:underline",children:"View More"})]})};function M(t){if(!t)return"N/A";const r=t.toString().replace(/(?!^\+)\D/g,""),a=r.startsWith("+1")?r.slice(2):r,i=a.match(/^(\d{3})(\d{3})(\d{4})$/);return i?`+1 (${i[1]}) ${i[2]}-${i[3]}`:(a.length===10,t)}async function O(){try{D(!0);const t=new le,r=`${t.getProjectId()}_sms_logs`;let s=["status,eq,3",o.get("campaign_id")?`${r}.campaign_id,eq,${o.get("campaign_id")}`:void 0].filter(Boolean);if(d&&s.push(`${r}.chat_history,cs,${d}`),y.length>0){const Q=y.map(Y=>{const[Z,ee,te]=Y.split(",");return`${r}.${Z},${ee},${te}`});s=[...s,...Q]}const a=await t.getPaginate("sms_logs",{size:o.get("limit")??50,page:o.get("page")??1,filter:s.filter(Boolean)}),{list:i,total:u,limit:p,num_pages:g,page:N}=a;q(i),X({currentPage:N,pageSize:p,totalNumber:u,totalPages:g})}catch(t){console.log("ERROR",t),ne(F,t.message),oe(T,t.message,5e3,"error")}D(!1)}const U=()=>{m([]),c([]),h({});for(const t of o.keys())t!=="campaign_id"&&o.delete(t);B(o),O()},K=({isOpen:t,onClose:r,row:s})=>{var a;return s?e.jsx(b,{appear:!0,show:t,as:f.Fragment,children:e.jsxs(S,{as:"div",className:"relative z-[100]",onClose:r,children:[e.jsx(b.Child,{as:f.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/50"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4",children:e.jsx(b.Child,{as:f.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(S.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-xl bg-[#1d2937] p-6 text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(S.Title,{className:"text-xl font-medium text-white",children:"SMS Details"}),e.jsx("button",{onClick:r,children:e.jsx(je,{className:"h-6 w-6 text-white/70 hover:text-white"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"ID"}),e.jsx("p",{className:"text-white",children:s.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Phone Number"}),e.jsxs("p",{className:"text-white",children:[console.log(s.to_number),s!=null&&s.to_number?M(s.to_number):"N/A"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Ended"}),e.jsx("p",{className:"text-white",children:s.ended===1?"False":s.ended===0?"True":"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-white/70",children:"Cost"}),e.jsx("p",{className:"text-white",children:s.cost?`$${(a=s.cost)==null?void 0:a.toFixed(2)}`:"N/A"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-white/70",children:"Chat History"}),s.chat_history?e.jsx(J,{chatHistory:JSON.parse(s.chat_history||"[]")}):e.jsx("p",{className:"text-white",children:"N/A"})]})]})]})})})})]})}):null};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"my-4 px-8 text-2xl font-bold text-white",children:"Test SMS Logs"}),e.jsxs("div",{className:"bg-[#1d2937] px-8 py-4",children:[e.jsxs("div",{className:"flex h-fit items-center justify-between gap-3",children:[e.jsx("div",{className:"flex w-[200px] min-w-[200px] items-center justify-between",children:e.jsx("div",{className:"relative z-10 rounded bg-[#1d2937]",children:e.jsxs(j,{children:[e.jsxs("div",{className:"flex items-center gap-4 bg-[#1d2937] text-white",children:[e.jsxs(j.Button,{className:"border-white/50ss flex w-[130px] cursor-pointer items-center justify-normal gap-3 rounded-md border border-white/50 bg-transparent px-3 py-1 text-white focus-visible:outline-0 focus-visible:outline-transparent ",children:[e.jsx(ce,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-white/50 bg-transparent px-2 py-1 text-white focus-within:border-gray-400",children:[e.jsx(de,{className:"text-xl text-white"}),e.jsx("input",{type:"text",placeholder:"Search in chat history",className:"border-none bg-transparent p-0 text-white placeholder:text-left placeholder:text-gray-300 focus:outline-none",style:{boxShadow:"0 0 transparent"},value:d,onChange:t=>{k(t.target.value),x("chat_history","cs",t.target.value)}}),d&&e.jsx(he,{className:"cursor-pointer text-lg text-white",onClick:()=>{k(""),x("chat_history","cs","")}})]})]}),e.jsx(b,{as:f.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(j.Panel,{children:e.jsxs("div",{className:"filter-form-holder absolute left-[-10px] top-[25px]  mt-4 min-w-[200%] rounded-md border border-[gray] bg-[#1d2937] p-5 pt-5 shadow-xl shadow-white/10",children:[e.jsx("span",{className:"absolute left-5 top-2 font-medium text-white",children:"Filters"}),e.jsx(j.Button,{onClick:()=>{console.log("clicked"),m([]),c([]),h({})},children:e.jsx(we,{className:"absolute right-2 top-2 cursor-pointer text-white"})}),n==null?void 0:n.map((t,r)=>{var s;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("button",{type:"button",className:"block h-[40px] w-1/3 cursor-pointer truncate rounded-md border border-gray-300 bg-[#1d2937] px-3 py-2 text-left leading-tight text-white outline-none",title:t,style:{WebkitTouchCallout:"none"},children:((s=R.find(a=>a.accessor===t))==null?void 0:s.header)||t}),e.jsxs("select",{className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-[8px] !leading-tight !text-gray-600 !outline-none",value:v,onChange:a=>{G(a.target.value),x(t,a.target.value,E[t])},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t==="status"?e.jsxs("select",{value:E[t]||"",onChange:a=>{h(i=>({...i,[t]:a.target.value})),x(t,v,a.target.value)},className:"h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",children:[e.jsx("option",{value:"",children:"Select Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]}):e.jsx(xe,{type:"text",labelClassName:"!mb-0",placeholder:"Enter value",setValue:()=>{},showIcon:!1,className:" h-[40px] w-1/3 !rounded-md !border !border-gray-700 !bg-gray-100 !px-3 !py-2 !leading-tight !text-gray-600 !outline-none",onReady:a=>x(t,v,a)}),e.jsx(ue,{className:"cursor-pointer text-2xl text-red-600",onClick:()=>{m(a=>a.filter(i=>i!==t)),c(a=>a.filter(i=>!i.includes(t))),h(a=>{const i={...a};return delete i[t],i})}})]},r)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out",onClick:()=>$(!P),children:[e.jsx(pe,{}),"Add filter"]}),P&&e.jsx("div",{className:"absolute top-11 z-10 border border-[gray] bg-[#1d2937] px-5 py-3",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:R.map(t=>e.jsx("li",{className:`${n.includes(t.accessor)?"cursor-not-allowed text-gray-100":"cursor-pointer text-gray-400 hover:text-white"}`,onClick:()=>{n.includes(t.accessor)||(m(r=>[...r,t.accessor]),h(r=>({...r,[t.accessor]:""}))),$(!1)},children:t.header},t.accessor))})}),n.length>0&&e.jsx("div",{onClick:U,className:"inline-block cursor-pointer rounded py-2.5 pl-6 font-medium leading-tight text-gray-400 transition duration-150 ease-in-out hover:text-white",children:"Clear all filter"})]})]})})})]})})}),e.jsx(I,{filename:"test_sms_logs",className:"relative flex h-9 cursor-pointer items-center justify-center rounded-md border border-transparent bg-[#19b2f6]/80 px-4 py-2 text-sm font-medium text-white shadow-md transition-colors hover:bg-[#19b2f6]",data:()=>w,children:"Download CSV"})]}),e.jsx("div",{className:"mt-4 overflow-x-auto  bg-[#1d2937]",children:e.jsx("div",{className:A?"":"overflow-x-auto border-b border-gray-400 shadow",children:A&&w.length===0?e.jsx(me,{columns:C}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-400 border border-b-0 border-gray-400 bg-[#1d2937]",children:[e.jsx("thead",{className:"bg-[#1d2937]",children:e.jsx(ge,{actionPosition:"onTable",onSort:()=>{},columns:C,actions:{view:{show:!0}}})}),e.jsx("tbody",{className:"divide-y divide-gray-400 bg-[#1d2937]",children:w.map(t=>{var r;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center justify-between gap-3 text-sm",children:e.jsx("button",{className:"rounded-[30px] bg-[#19b2f6]/20 p-1.5 px-5 font-medium text-white transition-colors hover:bg-[#19b2f6]/30",onClick:()=>{H(t),V(!0)},children:"View"})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.id}),e.jsx("td",{className:"whitespace-wrap overflow-x-auto px-6 py-4",children:t.chat_history?((r=JSON.parse(t.chat_history||"[]").find(s=>s.role==="assistant"))==null?void 0:r.content.slice(0,120))+"...":e.jsx("span",{className:"text-white",children:"N/A"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.to_number?M(t.to_number):"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.ended===1?"False":t.ended===0?"True":"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-white",children:t.cost?`$${t.cost.toFixed(2)}`:"N/A"})]},t.id)})})]})})}),e.jsx(fe,{paginationData:W})]})]}),e.jsx(K,{isOpen:z,onClose:()=>V(!1),row:L})]})};export{Ie as default};
