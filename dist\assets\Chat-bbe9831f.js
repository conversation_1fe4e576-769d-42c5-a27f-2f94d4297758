import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{r as o,R as r}from"./vendor-2ae44a2e.js";import{A,M as P,t as J}from"./index-d0a8f5da.js";import{h as N}from"./moment-55cb88ed.js";import{_ as Q}from"./qr-scanner-cf010ec4.js";import{I as Y}from"./react-input-emoji-6af2df68.js";import"./react-confirm-alert-783bc3ae.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const ee=o.lazy(()=>Q(()=>import("./ImagePreviewModal-e9fb9cbb.js"),["assets/ImagePreviewModal-e9fb9cbb.js","assets/@react-google-maps/api-c55ecefa.js","assets/vendor-2ae44a2e.js"])),S=a=>a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a==null?void 0:a.first_name} ${a.last_name}`:a!=null&&a.first_name||a!=null&&a.last_name?a!=null&&a.first_name?a==null?void 0:a.first_name:a==null?void 0:a.last_name:a==null?void 0:a.email,te=({roles:a,createNewRoom:n,setCreateRoom:m})=>{const[c,p]=r.useState(),[u,g]=r.useState(),[y,k]=r.useState(""),{state:h}=r.useContext(A);let _=new P;const v=async()=>{try{let s=await _.getAllUsers();if(!s.error){console.log(a);let f=[];a.length?f=s==null?void 0:s.list.filter(d=>a.includes(d.role)&&h.user!==(d==null?void 0:d.id)):f=s==null?void 0:s.list.filter(d=>h.user!==(d==null?void 0:d.id)),console.log("filtered >> ",f),p(f),g(s==null?void 0:s.list.filter(d=>d.id!==h.user))}}catch(s){console.log("Error",s)}},j=s=>{k(s),s.length>0?p([...u].filter(f=>`${f.first_name.toLowerCase()} ${f.last_name.toLowerCase()}`.includes(s.toLowerCase()))):p(u)};return r.useEffect(()=>{(async function(){await v()})()},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:[e.jsx("div",{className:"fixed inset-0 h-full w-full bg-black opacity-40",onClick:()=>m(!1)}),e.jsx("div",{className:"flex min-h-screen items-center px-4 py-8",children:e.jsx("div",{className:"relative mx-auto w-full max-w-lg rounded-md bg-white p-4 shadow-lg",children:e.jsx("div",{className:"mt-3 sm:flex",children:e.jsxs("div",{className:"mt-2 w-full px-2 text-center sm:ml-4 sm:text-left",children:[e.jsx("input",{type:"text",className:"block w-full border-b-2 border-gray-200 bg-transparent py-2 outline-none",placeholder:"Search",value:y,onChange:s=>j(s.target.value)}),e.jsx("ul",{className:"scrollbar-hide mt-4 h-[50vh] w-full overflow-y-scroll text-sm font-medium text-gray-900",children:c&&c.map(s=>e.jsx("li",{onClick:()=>n(s),className:`w-full cursor-pointer bg-white px-4 py-2 hover:bg-gray-200 user-${s.id}`,children:S(s)},s.id))})]})})})})]})})};function ae({title:a,titleId:n,...m},c){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":n},m),a?o.createElement("title",{id:n},a):null,o.createElement("path",{fillRule:"evenodd",d:"M17 10a.75.75 0 0 1-.75.75H5.612l4.158 3.96a.75.75 0 1 1-1.04 1.08l-5.5-5.25a.75.75 0 0 1 0-1.08l5.5-5.25a.75.75 0 1 1 1.04 1.08L5.612 9.25H16.25A.75.75 0 0 1 17 10Z",clipRule:"evenodd"}))}const le=o.forwardRef(ae),se=le;function ne({title:a,titleId:n,...m},c){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":n},m),a?o.createElement("title",{id:n},a):null,o.createElement("path",{d:"M3.105 2.288a.75.75 0 0 0-.826.95l1.414 4.926A1.5 1.5 0 0 0 5.135 9.25h6.115a.75.75 0 0 1 0 1.5H5.135a1.5 1.5 0 0 0-1.442 1.086l-1.414 4.926a.75.75 0 0 0 .826.95 28.897 28.897 0 0 0 15.293-*********** 0 0 0 0-1.114A28.897 28.897 0 0 0 3.105 2.288Z"}))}const ie=o.forwardRef(ne),re=ie;function oe({title:a,titleId:n,...m},c){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":n},m),a?o.createElement("title",{id:n},a):null,o.createElement("path",{fillRule:"evenodd",d:"M15.621 4.379a3 3 0 0 0-4.242 0l-7 7a3 3 0 0 0 4.241 4.243h.001l.497-.5a.75.75 0 0 1 1.064 1.057l-.498.501-.002.002a4.5 4.5 0 0 1-6.364-6.364l7-7a4.5 4.5 0 0 1 6.368 6.36l-3.455 3.553A2.625 2.625 0 1 1 9.52 9.52l3.45-3.451a.75.75 0 1 1 1.061 1.06l-3.45 3.451a1.125 1.125 0 0 0 1.587 1.595l3.454-3.553a3 3 0 0 0 0-4.242Z",clipRule:"evenodd"}))}const ce=o.forwardRef(oe),de=ce;function me({title:a,titleId:n,...m},c){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":n},m),a?o.createElement("title",{id:n},a):null,o.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-5.5-2.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10 12a5.99 5.99 0 0 0-4.793 2.39A6.483 6.483 0 0 0 10 16.5a6.483 6.483 0 0 0 4.793-2.11A5.99 5.99 0 0 0 10 12Z",clipRule:"evenodd"}))}const he=o.forwardRef(me),fe=he,Ae=({roles:a=[]})=>{const{state:n}=r.useContext(A),{dispatch:m}=r.useContext(A),[c,p]=r.useState([]),[u,g]=r.useState([]),[y,k]=r.useState(),h=r.useRef(),_=r.useRef(),v=r.useRef(null),[j,s]=r.useState(""),[f,d]=r.useState(),[R,M]=r.useState(null),[B,D]=r.useState(!1),[C,z]=r.useState(window.innerWidth),[T,$]=r.useState(!0),[Z,E]=r.useState(!1),[L,I]=r.useState([]);function O(t){t.currentTarget.innerWidth>1024&&$(!0),z(t.currentTarget.innerWidth)}let w=new P;const W=()=>{v.current.click()},H=()=>{D(!1),M(null),v.current.value=""},K=t=>{let l=N(new Date),i=N(t);return l.diff(i,"days")>1?N(i).format("Do MMMM"):N(i).format("hh:mm A")},U=async t=>{t.preventDefault();const l=new FormData;for(let i=0;i<R.length;i++)l.append("file",R[i]);try{const i=await w.uploadImage(l);await G(i)}catch(i){console.log(i)}};async function F(t){try{if(t)return I(c.filter(i=>`${S(i).toLowerCase()}`.includes(t.toLowerCase())));const l=await w.getMyRoom();l&&l.list&&l.list[0]&&(p(l.list),I(l.list),_.current=l.list)}catch(l){console.log("Error:",l)}}async function q(t){try{const l=await w.createRoom({user_id:n.user,other_user_id:t.id}),x=[{chat_id:l.chat_id,create_at:new Date,email:t.email,first_name:t.first_name,id:l.room_id,last_name:t.last_name,other_user_id:t.id,other_user_update_at:new Date,photo:t.photo,unread:0,update_at:new Date,user_id:n.user,user_update_at:new Date},...c];p(x),I(x),_.current=x,E(!1),document.getElementById(`user-${t.id}`)?document.getElementById(`user-${t.id}`).click():setTimeout(()=>{document.querySelector(".container-chat").firstChild.click()},200)}catch{E(!1),document.getElementById(`user-${t.id}`).click()}}async function V(t,l){try{d(t),k(l);let i=new Date().toISOString().split("T")[0];const x=await w.getChats(t,l,i);x&&x.model&&g(x.model.reverse())}catch(i){console.log("Error:",i)}}async function X(){try{let t=new Date().toISOString().split("T")[0];await w.postMessage({room_id:f,chat_id:y,user_id:n.user,message:j,date:t});let l={message:j,user_id:n.user,is_image:!1,timeStamp:new Date};const i=[...u,l];g(i),s("")}catch(t){console.log("Error:",t)}}async function G(t){try{let l=new Date().toISOString().split("T")[0];await w.postMessage({room_id:f,chat_id:y,user_id:n.user,message:t.url,date:l,is_image:!0});let i={message:t.url,user_id:n.user,is_image:!0,timeStamp:new Date};const x=[...u,i];g(x),D(!1),M(null),v.current.value="",s("")}catch(l){console.log("Error:",l)}}async function b(){try{const t=await w.startPooling(n.user);if(t.message){let l={message:t.message,user_id:t.user_id,is_image:!1,timeStamp:new Date};t.user_id===h.current?(g(i=>[...i,l]),setTimeout(async()=>{await b()},2e3)):setTimeout(async()=>{await b()},1e3)}else setTimeout(async()=>{b()},1e3)}catch(t){console.log(t.message),J(m,t.message),t.message==="TOKEN_EXPIRED"?window.location.replace(`/${n.role}/login/`):setTimeout(async()=>{b()},500)}}return r.useEffect(()=>{(async function(){await F(),await b()})()},[]),r.useEffect(()=>(window.addEventListener("resize",O),()=>{window.removeEventListener("resize",O)}),[C]),e.jsxs("div",{className:"h-full w-full flex-1 pt-4",children:[e.jsx("div",{className:"main-body container m-auto flex h-full w-11/12 flex-col",children:e.jsxs("div",{className:"main flex flex-1 flex-col",children:[e.jsx("div",{className:"heading flex-2 hidden lg:block",children:e.jsx("h1",{className:"mb-4 text-3xl text-gray-700",children:"Chat"})}),e.jsxs("div",{className:"flex h-full flex-1",children:[T&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{className:"inline-block h-10 w-10 rounded-full bg-blue-400 text-white",onClick:()=>E(!0),children:"+"})}),e.jsxs("div",{className:"flex-2 w-full flex-col pr-6 lg:flex lg:w-1/3",children:[e.jsx("div",{className:"flex-2 px-2 pb-6",children:e.jsx("input",{type:"text",className:"block w-full border-b-2 border-gray-200 bg-transparent py-2 outline-none",placeholder:"Search",onChange:t=>F(t.target.value)})}),e.jsx("div",{className:"container-chat h-full max-h-[70vh] flex-1 overflow-y-auto overflow-x-hidden px-2",children:L&&L.map((t,l)=>e.jsxs("div",{id:`user-${t.other_user_id}`,className:"entry mb-4 flex transform cursor-pointer items-center rounded bg-white p-4 shadow-md transition-transform duration-300 hover:scale-105",onClick:()=>{V(t.id,t.chat_id),h.current=t.other_user_id,h.currentRoom=t,C<1024&&$(!1)},children:[e.jsx("div",{className:"flex-2",children:e.jsx("div",{className:"relative h-12 w-12",children:t.photo?e.jsx("img",{className:"mx-auto h-12 w-12 rounded-full",src:t.photo,alt:"user-photo"}):e.jsx(fe,{className:"h-10 w-10"})})}),e.jsx("div",{className:"flex-1 px-2",children:e.jsx("div",{className:"w-32 truncate",children:e.jsx("span",{className:"text-gray-800",children:S(t)})})}),e.jsxs("div",{className:"flex-2 text-right",children:[e.jsx("div",{children:e.jsx("small",{className:"text-gray-500",children:K(t.update_at)})}),t.unread>0&&e.jsx("div",{children:e.jsx("small",{className:"inline-block h-6 w-6 rounded-full bg-green-500 text-center text-xs leading-6 text-white",children:t.unread})})]})]},l))})]})]}),C>1023||C<1024&&!T?e.jsx("div",{className:"flex max-h-[95vh] w-full flex-col lg:max-h-[60vh]",children:h!=null&&h.current?e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsx("div",{className:"flex-3",children:e.jsxs("h2",{className:"mb-8 flex border-b-2 border-gray-200 py-1 text-xl",children:[e.jsx("span",{className:"my-auto mr-4 lg:hidden",onClick:()=>$(!0),children:e.jsx(se,{className:"h-6 w-6"})}),"Chatting with"," ",e.jsx("b",{className:"ml-2",children:`${S(h.currentRoom)}`})]})}),u&&e.jsx("div",{className:"max-h-[95vh] min-h-[60vh] flex-1 overflow-y-auto lg:max-h-[60vh]",children:u.map((t,l)=>e.jsxs("div",{className:" mb-4 flex",children:[(t==null?void 0:t.user_id)!==n.user&&e.jsx("div",{className:"flex-2",children:e.jsx("div",{className:"relative h-12 w-12",children:e.jsx("span",{className:"absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white bg-gray-400"})})}),e.jsxs("div",{className:`flex-1 px-2 ${(t==null?void 0:t.user_id)===n.user&&"text-right"}`,children:[e.jsx("div",{className:"inline-block",children:t.is_image?e.jsx("img",{src:t==null?void 0:t.message,className:"h-40 md:h-52 lg:h-80"}):e.jsx("p",{className:`${(t==null?void 0:t.user_id)===n.user?"bg-gray-300 text-gray-700 ":"bg-blue-600 text-white"} whitespace-pre-line rounded-xl p-2 px-6 `,children:t==null?void 0:t.message})}),e.jsx("div",{className:"pl-4",children:e.jsx("small",{className:"text-gray-500",children:N(t.timestamp).format("hh:mm A")})})]})]},l))}),e.jsx("div",{className:"flex-2 items-end pb-10 pt-4",children:e.jsxs("div",{className:"write flex rounded-lg bg-white shadow",children:[e.jsx("div",{className:"flex-1",children:e.jsx(Y,{value:j,onChange:s,placeholder:"Type a message"})}),e.jsxs("div",{className:"flex-2 flex w-32 content-center items-center p-2",children:[e.jsx("div",{className:"flex-1 text-center",children:e.jsxs("span",{className:"text-gray-400 hover:text-gray-800",children:[e.jsx("input",{className:"hidden",ref:v,type:"file",accept:"image/png, image/gif, image/jpeg",name:"file",onChange:t=>{M(t.target.files),D(!0)}}),e.jsx("button",{onClick:W,className:"inline-block align-text-bottom",children:e.jsx(de,{className:"h-6 w-6 text-black"})})]})}),e.jsx("div",{className:"flex-1",children:e.jsx("button",{className:"inline-block h-10 w-10 rounded-full bg-blue-400",onClick:()=>X(),children:e.jsx("span",{className:"inline-block align-text-bottom",children:e.jsx(re,{className:"h-6 w-6 text-white"})})})})]})]})})]}):e.jsx("div",{className:"flex h-[70vh] w-full items-center justify-center text-7xl text-gray-700 ",children:"Select a Chat to view"})}):null]})]})}),B&&R?e.jsx(ee,{file:R,handleFileUpload:U,cancelFileUpload:H}):null,Z&&e.jsx(te,{roles:[...a],createNewRoom:q,setCreateRoom:E})]})};export{Ae as default};
