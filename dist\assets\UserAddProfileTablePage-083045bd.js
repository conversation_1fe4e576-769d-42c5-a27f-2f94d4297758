import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as T}from"./vendor-2ae44a2e.js";import{u as D}from"./react-hook-form-47c010f8.js";import{o as F}from"./yup-5abd4662.js";import{c as E,a as m}from"./yup-c2e87575.js";import{G as w,M as P,s as A,t as R}from"./index-d0a8f5da.js";import"./react-quill-d06fcfc9.js";import{M as $}from"./MkdInput-c12da351.js";import{I as M}from"./InteractiveButton-bff38983.js";import"./index-a74110af.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@craftjs/core-9da1c17f.js";import"./MoonLoader-62b0139a.js";const ue=()=>{var h,b,g,y,v,j;const{dispatch:p}=a.useContext(w),_=E({user_id:m(),fcm_token:m(),device_id:m(),device_type:m()}).required(),{dispatch:k}=a.useContext(w),[u,O]=a.useState({}),[f,c]=a.useState(!1),N=T(),{register:l,handleSubmit:S,setError:x,setValue:C,formState:{errors:s}}=D({resolver:F(_)});a.useState([]);const I=async r=>{let n=new P;c(!0);try{for(let i in u){let o=new FormData;o.append("file",u[i].file);let d=await n.uploadImage(o);r[i]=d.url}n.setTable("profile");const t=await n.callRestAPI({user_id:r.user_id,fcm_token:r.fcm_token,device_id:r.device_id,device_type:r.device_type},"POST");if(!t.error)A(p,"Added"),N("/user/profile");else if(t.validation){const i=Object.keys(t.validation);for(let o=0;o<i.length;o++){const d=i[o];x(d,{type:"manual",message:t.validation[d]})}}c(!1)}catch(t){c(!1),console.log("Error",t),x("user_id",{type:"manual",message:t.message}),R(k,t.message)}};return a.useEffect(()=>{p({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Profile"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(I),children:[e.jsx($,{type:"number",page:"add",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:l,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"fcm_token",children:"Fcm Token"}),e.jsx("textarea",{placeholder:"Fcm Token",...l("fcm_token"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=s.fcm_token)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(b=s.fcm_token)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"device_id",children:"Device Id"}),e.jsx("textarea",{placeholder:"Device Id",...l("device_id"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(g=s.device_id)!=null&&g.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=s.device_id)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"device_type",children:"Device Type"}),e.jsx("textarea",{placeholder:"Device Type",...l("device_type"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=s.device_type)!=null&&v.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=s.device_type)==null?void 0:j.message})]}),e.jsx(M,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ue as default};
