import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as x,r as o}from"./vendor-2ae44a2e.js";import{u as K}from"./react-hook-form-47c010f8.js";import{o as W}from"./yup-5abd4662.js";import{c as J}from"./yup-c2e87575.js";import{M as Q,G,A as X,t as q,s as O}from"./index-d0a8f5da.js";import"./InteractiveButton-bff38983.js";import"./index-f2c2b086.js";import"./@hookform/resolvers-d6373084.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./MoonLoader-62b0139a.js";let f=new Q;const ye=()=>{const N=J({}).required(),{dispatch:c}=x.useContext(G),[h,z]=o.useState("");x.useState({});const[P,y]=o.useState(!1),[T,S]=o.useState(!1),[u,r]=o.useState(!1),[F,d]=o.useState(!1),[A,w]=o.useState("");o.useState("");const[E,k]=o.useState(!1),[I,B]=o.useState("Settings"),[t,D]=o.useState({}),{dispatch:H}=x.useContext(G),{state:$}=x.useContext(X),{register:_,handleSubmit:b,setError:v,setValue:M,formState:{errors:L}}=K({resolver:W(N)});async function R(){var n,i,a;try{f.setTable("user_settings");const s=await f.callRestAPI({user_id:$.user,filter:[`user_id,eq,${$.user}`]},"GETALL");D(s==null?void 0:s.list[0]),M("phone_service",(n=s==null?void 0:s.list[0])==null?void 0:n.phone_service),M("voice_service",(i=s==null?void 0:s.list[0])==null?void 0:i.voice_service),M("llm_service",(a=s==null?void 0:s.list[0])==null?void 0:a.llm_service)}catch(s){console.log("Error",s),q(c,s.response.message?s.response.data.message:s.message)}}const g=async n=>{D(n);try{k(!0);const i=await f.updateProfile({first_name:n.first_name||(t==null?void 0:t.first_name),last_name:n.last_name||(t==null?void 0:t.last_name),photo:n.photo||A});if(!i.error)O(c,"Profile Updated",4e3),m();else{if(i.validation){const a=Object.keys(i.validation);for(let s=0;s<a.length;s++){const l=a[s];v(l,{type:"manual",message:i.validation[l]})}}m()}if(h!==n.email){const a=await f.updateEmail(n.email);if(!a.error)O(c,"Email Updated",1e3);else if(a.validation){const s=Object.keys(a.validation);for(let l=0;l<s.length;l++){const p=s[l];v(p,{type:"manual",message:a.validation[p]})}}m()}if(n.password.length>0){const a=await f.updatePassword(n.password);if(!a.error)O(c,"Password Updated",2e3);else if(a.validation){const s=Object.keys(a.validation);for(let l=0;l<s.length;l++){const p=s[l];v(p,{type:"manual",message:a.validation[p]})}}}await R(),k(!1)}catch(i){k(!1),console.log("Error",i),v("email",{type:"manual",message:i.response.data.message?i.response.data.message:i.message}),q(c,i.response.data.message?i.response.data.message:i.message)}};x.useEffect(()=>{H({type:"SETPATH",payload:{path:"settings"}}),R()},[]);const U=()=>{S(!0)},j=()=>{r(!0)},m=()=>{y(!1),S(!1),r(!1),d(!1)};return e.jsxs("div",{className:"mx-auto mt-6 max-w-3xl rounded-md border",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#ffffffd1]",children:e.jsx("div",{className:"flex items-center space-x-6",children:e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${I==="Settings"?"bg-[#f4f4f4] text-[#525252]":""} `,onClick:()=>B("Settings"),children:"Settings"})})}),e.jsxs("main",{children:[I==="Settings"&&e.jsx("div",{className:"rounded bg-white",children:e.jsx("form",{onSubmit:b(g),children:e.jsxs("div",{className:"mx-10 max-w-lg",children:[e.jsx("p",{className:"mb-3 text-base font-medium text-gray-900",children:"Model Details"}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base font-medium text-gray-600",children:"Phone service"}),e.jsx("p",{className:"text-base font-medium text-gray-900",children:t==null?void 0:t.phone_service})]}),e.jsx("p",{className:"cursor-pointer text-base font-semibold text-black",onClick:U,children:"Edit"})]}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base font-medium text-gray-600",children:"Voice service"}),e.jsx("p",{className:"text-base font-medium text-gray-900",children:t==null?void 0:t.voice_service})]}),e.jsx("p",{className:"cursor-pointer text-base font-semibold text-black",onClick:j,children:"Edit"})]}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base font-medium text-gray-600",children:"LLM service"}),e.jsx("p",{className:"text-base font-medium text-gray-900",children:t==null?void 0:t.llm_service})]}),e.jsx("p",{className:"cursor-pointer text-base font-semibold text-black",onClick:j,children:"Edit"})]})]})})}),T&&e.jsx(C,{title:"Edit Phone Service",label:"Phone service",buttonName:"Save and close",isOpen:U,onClose:m,handleSubmit:b,onSubmit:g,register:_,id:"phone_service",submitLoading:E,errors:L}),u&&e.jsx(C,{title:"Edit Voice Service",label:"Voice service",buttonName:"Save and close",isOpen:j,onClose:m,handleSubmit:b,onSubmit:g,register:_,id:"voice_service",submitLoading:E,errors:L}),u&&e.jsx(C,{title:"Edit LLM service",label:"LLM service",buttonName:"Save and close",isOpen:j,onClose:m,handleSubmit:b,onSubmit:g,register:_,id:"llm_service",submitLoading:E,errors:L})]})]})},C=N=>{var w;const{title:c,label:h,buttonName:z,isOpen:P,onClose:y,handleSubmit:T,onSubmit:S,register:u,id:r,submitLoading:F,errors:d,defaultValues:A}=N;return o.useState(!1),o.useState({email:""}),e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${P?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsx("div",{className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:c}),(r==="voice_service"||r==="llm_service"||r==="phone_service")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:h}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:r,type:"text",placeholder:`Enter ${h} `,name:r,...u(r)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=d==null?void 0:d.id)==null?void 0:w.message})]}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:y,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]})})]})})})};export{C as EditInfoModal,ye as default};
