import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{f as j,r as a,i as b}from"./vendor-2ae44a2e.js";import{A as f,G as N,M as y,s as m,t as v}from"./index-d0a8f5da.js";import{F as w,a as C,b as _,c as k,d as A}from"./index.esm-ae78e666.js";import{M as S,a as n,b as z,c as E}from"./index.esm-42944128.js";import{S as M}from"./index-a74110af.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const ee=()=>{j();const{dispatch:l}=a.useContext(f),{state:P,dispatch:r}=a.useContext(N),[x]=b(),d=x.get("campaign_id"),[s,h]=a.useState(null),[g,i]=a.useState(!0),[o,p]=a.useState(null),c=new y;return a.useEffect(()=>{r({type:"SETPATH",payload:{path:"outbound_campaigns"}});async function u(){try{i(!0),c.setTable("campaign");const t=await c.callRawAPI(`/v3/api/custom/voiceoutreach/user/campaign-summary/${d}`,{},"GET");h(t.model)}catch(t){console.log("ERROR",t),p(t.message),v(l,t.message),m(r,t.message,5e3,"error")}finally{i(!1)}}u()},[d,l,r]),g?e.jsx(M,{}):(o&&m(r,o,5e3,"error"),e.jsxs("div",{className:"overflow-x-auto rounded-lg bg-[#1d2937] pt-5 shadow md:p-5",children:[e.jsx("h3",{className:"px-8 mb-8 text-2xl font-bold text-white",children:"Campaign Summary Report"}),e.jsx("div",{className:"px-8 py-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(w,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Total Calls"}),e.jsx("p",{className:"text-gray-400",children:s.total_calls})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(S,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Pickups"}),e.jsx("p",{className:"text-gray-400",children:s.total_answered})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(n,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"No Answers"}),e.jsx("p",{className:"text-gray-400",children:s.total_calls-s.total_answered})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(C,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Retries"}),e.jsx("p",{className:"text-gray-400",children:s.retries})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(_,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Average Call Duration"}),e.jsxs("p",{className:"text-gray-400",children:[s.average_call_duration," seconds"]})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(k,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Usage Cost"}),e.jsxs("p",{className:"text-gray-400",children:["$",s.total_usage_cost]})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(z,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Appointments Booked"}),e.jsx("p",{className:"text-gray-400",children:s.appointments_booked})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(E,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Hangups"}),e.jsx("p",{className:"text-gray-400",children:s.hangups})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(n,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Callbacks"}),e.jsx("p",{className:"text-gray-400",children:s.callbacks})]})]}),e.jsxs("div",{className:"flex items-center rounded-lg border border-gray-400/50 bg-[#1d2937] p-4",children:[e.jsx(A,{className:"mr-4 text-[#19b2f6]",size:30}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Follow-ups"}),e.jsx("p",{className:"text-gray-400",children:s.follow_ups})]})]})]})})]}))};export{ee as default};
