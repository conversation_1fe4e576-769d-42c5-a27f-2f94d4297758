import{j as e}from"./@react-google-maps/api-c55ecefa.js";import{R as a,f as oe}from"./vendor-2ae44a2e.js";import{M as ne,G as le,A as I,B as ce,a as de,b as ue,R as xe,c as pe,u as me,v as he,t as ge,g as h}from"./index-d0a8f5da.js";import{o as fe}from"./yup-5abd4662.js";import{u as ye}from"./react-hook-form-47c010f8.js";import{c as je,a as g}from"./yup-c2e87575.js";import{P as be}from"./index-132fbad2.js";import{A as ve}from"./index-e429b426.js";import{S as we}from"./index-a74110af.js";import{M}from"./index-9aa09a5c.js";import{X as Se}from"./lucide-react-f66dbccf.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";import"./@hookform/resolvers-d6373084.js";let D=new ne;const v=[{header:"Email",accessor:"email"},{header:"Role",accessor:"role"},{header:"Status",accessor:"status",mapping:["Inactive","Active","Suspend"]},{header:"Action",accessor:""}],Ze=()=>{const{dispatch:O}=a.useContext(le),{dispatch:$}=a.useContext(I);a.useState("");const[w,q]=a.useState([]),[n,S]=a.useState(10),[N,B]=a.useState(0),[Ne,z]=a.useState(0),[c,L]=a.useState(0),[G,U]=a.useState(!1),[V,H]=a.useState(!1),[C,K]=a.useState(!1),[A,P]=a.useState(!1),[o,x]=a.useState([]),[E,p]=a.useState([]),[X,_]=a.useState(""),[k,J]=a.useState("eq"),[F,f]=a.useState(!0),[Q,y]=a.useState(!1),[W,d]=a.useState(!1),[Y,Z]=a.useState();oe(),a.useContext(I);const ee=je({id:g(),email:g(),role:g(),status:g()}),{register:Ce,handleSubmit:te,formState:{errors:Ae}}=ye({resolver:fe(ee)});function se(t){(async function(){S(t),await l(0,t)})()}function ae(){(async function(){await l(c-1>0?c-1:0,n)})()}function re(){(async function(){await l(c+1<=N?c+1:0,n)})()}const R=(t,r,s)=>{const i=r==="eq"&&isNaN(s)?`"${s}"`:s,u=`${t},${r},${i}`;p(j=>[...j.filter(m=>!m.includes(t)),u]),_(s)};async function l(t,r,s){f(!0);try{D.setTable("user");const i=await D.callRestAPI({payload:{...s,role:"admin"},page:t,limit:r,filter:[...E]},"PAGINATE");i&&f(!1);const{list:u,total:j,limit:T,num_pages:m,page:b}=i;q(u),S(T),B(m),L(b),z(j),U(b>1),H(b+1<=m)}catch(i){f(!1),console.log("ERROR",i),ge($,i.message)}}const ie=t=>{const r=h(t.email),s=h(t.role),i=h(t.status),u=h(t.id);l(0,n,{email:r,role:s,status:i,id:u})};return a.useEffect(()=>{O({type:"SETPATH",payload:{path:"users"}});const r=setTimeout(async()=>{await l(1,n)},700);return()=>{clearTimeout(r)}},[X,E,k]),e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between px-8 py-6",children:[e.jsxs("form",{className:"relative",onSubmit:te(ie),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 hover:border-gray-300",onClick:()=>K(!C),children:[e.jsx(ce,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-white",children:o.length})]}),e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-2 focus-within:border-gray-300",children:[e.jsx(de,{className:"text-xl text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search",className:"border-none p-0 placeholder:text-gray-400 focus:outline-none",style:{boxShadow:"none"},onInput:t=>{var r;return R("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(ue,{className:"text-lg text-gray-400 hover:text-gray-600"})]})]}),C&&e.jsxs("div",{className:"absolute left-0 z-20 mt-2 w-[760px] rounded-md border border-gray-200 bg-white p-5 shadow-lg",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-700",children:"Filters"}),e.jsx(Se,{onClick:()=>{x([]),p([]),setFilterValues({})},className:"cursor-pointer text-lg text-gray-400 hover:text-gray-600"})]}),o==null?void 0:o.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"h-[40px] w-1/3 appearance-none rounded-md border border-gray-300 outline-0",onChange:s=>{J(s.target.value)},children:[e.jsx("option",{value:"eq",children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" h-[40px] w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>R(t,k,s.target.value)}),e.jsx(xe,{className:"cursor-pointer text-2xl text-red-500 hover:text-red-600",onClick:()=>{x(s=>s.filter(i=>i!==t)),p(s=>s.filter(i=>!i.includes(t)))}})]},r)),e.jsxs("div",{className:"relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"flex cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 text-gray-600 hover:bg-gray-200",onClick:()=>{P(!A)},children:[e.jsx(pe,{}),"Add filter"]}),A&&e.jsx("div",{className:"absolute top-11 z-10 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg",children:e.jsx("ul",{className:"flex flex-col",children:v.slice(0,-1).map(t=>e.jsx("li",{className:`px-4 py-2 ${o.includes(t.header)?"cursor-not-allowed text-gray-700":"cursor-pointer text-gray-400   "}`,onClick:()=>{o.includes(t.header)||x(r=>[...r,t.header]),P(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("button",{type:"button",onClick:()=>{x([]),p([])},className:"py-2 pl-4 text-gray-600 hover:text-gray-800",children:"Clear all filter"})]})]})]}),e.jsx(ve,{onClick:()=>d(!0)})]}),F?e.jsx(we,{}):e.jsx("div",{className:"px-8",children:e.jsxs("div",{className:"overflow-x-auto rounded-lg border border-gray-200 bg-white shadow",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:v.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:w.map((t,r)=>e.jsx("tr",{className:"hover:bg-gray-50",children:v.map((s,i)=>s.accessor===""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{Z(t.id),y(!0)},children:"Edit"})},i):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]===1?e.jsx("span",{className:"rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800",children:s.mapping[t[s.accessor]]})},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-900",children:t[s.accessor]},i))},r))})]}),!F&&w.length===0&&e.jsx("div",{className:"px-6 py-4 text-center text-sm text-gray-500",children:"No admins found"})]})}),e.jsx("div",{className:"px-8 py-4",children:e.jsx(be,{currentPage:c,pageCount:N,pageSize:n,canPreviousPage:G,canNextPage:V,updatePageSize:se,previousPage:ae,nextPage:re})}),e.jsx(M,{isModalActive:W,closeModalFn:()=>d(!1),children:e.jsx(me,{setSidebar:d,closeSidebar:()=>{d(!1),l(1,n)}})}),e.jsx(M,{isModalActive:Q,closeModalFn:()=>y(!1),children:e.jsx(he,{activeId:Y,setSidebar:y,closeSidebar:()=>{d(!1),l(1,n)}})})]})};export{Ze as default};
