import{j as p}from"./@react-google-maps/api-c55ecefa.js";import"./vendor-2ae44a2e.js";import{S as n}from"./index-d0a8f5da.js";import{A as m}from"./index-e429b426.js";import"./react-confirm-alert-783bc3ae.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-7bce1936.js";import"./react-icons-f29df01f.js";import"./lucide-react-f66dbccf.js";import"./react-loading-skeleton-f53ed7d1.js";import"./react-papaparse-b60a38ab.js";import"./papaparse-2d1475f9.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-9cc92aaf.js";import"./@fortawesome/react-fontawesome-27c5bed3.js";import"./@fortawesome/fontawesome-svg-core-294d29ff.js";import"./@fortawesome/free-solid-svg-icons-11dbc67c.js";import"./@fortawesome/free-regular-svg-icons-3e88f209.js";import"./@fortawesome/free-brands-svg-icons-2414b431.js";const P=({actions:i,selectedItems:o})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var a,l;if(i[r].show&&!["select","add","export"].includes(r)){if(o&&(o==null?void 0:o.length)===1&&!((a=i[r])!=null&&a.multiple))return p.jsx(m,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r);if(o&&(o==null?void 0:o.length)>=1&&((l=i[r])!=null&&l.multiple))return p.jsx(m,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{P as default};
